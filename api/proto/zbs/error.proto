syntax = "proto2";

package zbs;

enum ErrorCode {
    EOK                                 = 0;

    // ==== system defined

    SYSEPERM                            = 1;       /* Operation not permitted */
    SYSENOENT                           = 2;       /* No such file or directory */
    SYSESRCH                            = 3;       /* No such process */
    SYSEINTR                            = 4;       /* Interrupted system call */
    SYSEIO                              = 5;       /* I/O error */
    SYSENXIO                            = 6;       /* No such device or address */
    SYSE2BIG                            = 7;       /* Argument list too long */
    SYSENOEXEC                          = 8;       /* Exec format error */
    SYSEBADF                            = 9;       /* Bad file number */
    SYSECHILD                           = 10;      /* No child processes */
    SYSEAGAIN                           = 11;      /* Try again */
    SYSENOMEM                           = 12;      /* Out of memory */
    SYSEACCES                           = 13;      /* Permission denied */
    SYSEFAULT                           = 14;      /* Bad address */
    SYSENOTBLK                          = 15;      /* Block device required */
    SYSEBUSY                            = 16;      /* Device or resource busy */
    SYSEEXIST                           = 17;      /* File exists */
    SYSEXDEV                            = 18;      /* Cross-device link */
    SYSENODEV                           = 19;      /* No such device */
    SYSENOTDIR                          = 20;      /* Not a directory */
    SYSEISDIR                           = 21;      /* Is a directory */
    SYSEINVAL                           = 22;      /* Invalid argument */
    SYSENFILE                           = 23;      /* File table overflow */
    SYSEMFILE                           = 24;      /* Too many open files */
    SYSENOTTY                           = 25;      /* Not a typewriter */
    SYSETXTBSY                          = 26;      /* Text file busy */
    SYSEFBIG                            = 27;      /* File too large */
    SYSENOSPC                           = 28;      /* No space left on device */
    SYSESPIPE                           = 29;      /* Illegal seek */
    SYSEROFS                            = 30;      /* Read-only file system */
    SYSEMLINK                           = 31;      /* Too many links */
    SYSEPIPE                            = 32;      /* Broken pipe */
    SYSEDOM                             = 33;      /* Math argument out of domain of func */
    SYSERANGE                           = 34;      /* Math result not representable */
    SYSEDEADLK                          = 35;      /* Resource deadlock would occur */
    SYSENAMETOOLONG                     = 36;      /* File name too long */
    SYSENOLCK                           = 37;      /* No record locks available */
    SYSENOSYS                           = 38;      /* Function not implemented */
    SYSENOTEMPTY                        = 39;      /* Directory not empty */
    SYSELOOP                            = 40;      /* Too many symbolic links encountered */
    SYSENOMSG                           = 42;      /* No message of desired type */
    SYSEIDRM                            = 43;      /* Identifier removed */
    SYSECHRNG                           = 44;      /* Channel number out of range */
    SYSEL2NSYNC                         = 45;      /* Level 2; not synchronized */
    SYSEL3HLT                           = 46;      /* Level 3; halted */
    SYSEL3RST                           = 47;      /* Level 3; reset */
    SYSELNRNG                           = 48;      /* Link number out of range */
    SYSEUNATCH                          = 49;      /* Protocol driver not attached */
    SYSENOCSI                           = 50;      /* No CSI structure available */
    SYSEL2HLT                           = 51;      /* Level 2; halted */
    SYSEBADE                            = 52;      /* Invalid exchange */
    SYSEBADR                            = 53;      /* Invalid request descriptor */
    SYSEXFULL                           = 54;      /* Exchange full */
    SYSENOANO                           = 55;      /* No anode */
    SYSEBADRQC                          = 56;      /* Invalid request code */
    SYSEBADSLT                          = 57;      /* Invalid slot */
    SYSEBFONT                           = 59;      /* Bad font file format */
    SYSENOSTR                           = 60;      /* Device not a stream */
    SYSENODATA                          = 61;      /* No data available */
    SYSETIME                            = 62;      /* Timer expired */
    SYSENOSR                            = 63;      /* Out of streams resources */
    SYSENONET                           = 64;      /* Machine is not on the network */
    SYSENOPKG                           = 65;      /* Package not installed */
    SYSEREMOTE                          = 66;      /* Object is remote */
    SYSENOLINK                          = 67;      /* Link has been severed */
    SYSEADV                             = 68;      /* Advertise error */
    SYSESRMNT                           = 69;      /* Srmount error */
    SYSECOMM                            = 70;      /* Communication error on send */
    SYSEPROTO                           = 71;      /* Protocol error */
    SYSEMULTIHOP                        = 72;      /* Multihop attempted */
    SYSEDOTDOT                          = 73;      /* RFS specific error */
    SYSEBADMSG                          = 74;      /* Not a data message */
    SYSEOVERFLOW                        = 75;      /* Value too large for defined data type */
    SYSENOTUNIQ                         = 76;      /* Name not unique on network */
    SYSEBADFD                           = 77;      /* File descriptor in bad state */
    SYSEREMCHG                          = 78;      /* Remote address changed */
    SYSELIBACC                          = 79;      /* Can not access a needed shared library */
    SYSELIBBAD                          = 80;      /* Accessing a corrupted shared library */
    SYSELIBSCN                          = 81;      /* .lib section in a.out corrupted */
    SYSELIBMAX                          = 82;      /* Attempting to link in too many shared libraries */
    SYSELIBEXEC                         = 83;      /* Cannot exec a shared library directly */
    SYSEILSEQ                           = 84;      /* Illegal byte sequence */
    SYSERESTART                         = 85;      /* Interrupted system call should be restarted */
    SYSESTRPIPE                         = 86;      /* Streams pipe error */
    SYSEUSERS                           = 87;      /* Too many users */
    SYSENOTSOCK                         = 88;      /* Socket operation on non-socket */
    SYSEDESTADDRREQ                     = 89;      /* Destination address required */
    SYSEMSGSIZE                         = 90;      /* Message too long */
    SYSEPROTOTYPE                       = 91;      /* Protocol wrong type for socket */
    SYSENOPROTOOPT                      = 92;      /* Protocol not available */
    SYSEPROTONOSUPPORT                  = 93;      /* Protocol not supported */
    SYSESOCKTNOSUPPORT                  = 94;      /* Socket type not supported */
    SYSEOPNOTSUPP                       = 95;      /* Operation not supported on transport endpoint */
    SYSEPFNOSUPPORT                     = 96;      /* Protocol family not supported */
    SYSEAFNOSUPPORT                     = 97;      /* Address family not supported by protocol */
    SYSEADDRINUSE                       = 98;      /* Address already in use */
    SYSEADDRNOTAVAIL                    = 99;      /* Cannot assign requested address */
    SYSENETDOWN                         = 100;     /* Network is down */
    SYSENETUNREACH                      = 101;     /* Network is unreachable */
    SYSENETRESET                        = 102;     /* Network dropped connection because of reset */
    SYSECONNABORTED                     = 103;     /* Software caused connection abort */
    SYSECONNRESET                       = 104;     /* Connection reset by peer */
    SYSENOBUFS                          = 105;     /* No buffer space available */
    SYSEISCONN                          = 106;     /* Transport endpoint is already connected */
    SYSENOTCONN                         = 107;     /* Transport endpoint is not connected */
    SYSESHUTDOWN                        = 108;     /* Cannot send after transport endpoint shutdown */
    SYSETOOMANYREFS                     = 109;     /* Too many references: cannot splice */
    SYSETIMEDOUT                        = 110;     /* Connection timed out */
    SYSECONNREFUSED                     = 111;     /* Connection refused */
    SYSEHOSTDOWN                        = 112;     /* Host is down */
    SYSEHOSTUNREACH                     = 113;     /* No route to host */
    SYSEALREADY                         = 114;     /* Operation already in progress */
    SYSEINPROGRESS                      = 115;     /* Operation now in progress */
    SYSESTALE                           = 116;     /* Stale NFS file handle */
    SYSEUCLEAN                          = 117;     /* Structure needs cleaning */
    SYSENOTNAM                          = 118;     /* Not a XENIX named type file */
    SYSENAVAIL                          = 119;     /* No XENIX semaphores available */
    SYSEISNAM                           = 120;     /* Is a named type file */
    SYSEREMOTEIO                        = 121;     /* Remote I/O error */
    SYSEDQUOT                           = 122;     /* Quota exceeded */
    SYSENOMEDIUM                        = 123;     /* No medium found */
    SYSEMEDIUMTYPE                      = 124;     /* Wrong medium type */
    SYSECANCELED                        = 125;     /* Operation Canceled */
    SYSENOKEY                           = 126;     /* Required key not available */
    SYSEKEYEXPIRED                      = 127;     /* Key has expired */
    SYSEKEYREVOKED                      = 128;     /* Key has been revoked */
    SYSEKEYREJECTED                     = 129;     /* Key was rejected by service */
    SYSEOWNERDEAD                       = 130;     /* Owner died */
    SYSENOTRECOVERABLE                  = 131;     /* State not recoverable */
    SYSERFKILL                          = 132;     /* Operation not possible due to RF-kill */
    SYSEHWPOISON                        = 133;     /* Memory page has hardware error */
    SYSEUNKNOWN                         = 999;

    // ==== common
    EUNKNOWN                            = 100001;
    EBadArgument                        = 100002;
    ELevelDb                            = 100003;
    EMongoDb                            = 100004;  /* No longer used */
    EBoot                               = 100005;
    EAgain                              = 100006;
    EMaxLimit                           = 100007;
    EOther                              = 100009;
    EProto                              = 100010;
    EAllocSpace                         = 100011;
    ETimedOut                           = 100012;
    EShutDown                           = 100013;
    ENoSpace                            = 100014;
    EProfiler                           = 100015;
    EKilled                             = 100016;
    ECGroup                             = 100017;
    ENIOError                           = 100019;
    ETimerFd                            = 100020;
    EInvalidPath                        = 100021;
    ENotSupport                         = 100022;
    EAlreadyStarted                     = 100023;
    EInvalidArgument                    = 100025;
    EMock                               = 100026;
    EAsyncEventQueue                    = 100027;
    EBadHexFormat                       = 100028;
    EBadLicense                         = 100029;
    EBadKeyFile                         = 100030;
    EBadSign                            = 100031;
    EBlkDev                             = 100032;
    EPythonException                    = 100033;
    ELicenseNotPermitted                = 100034;
    ELicenseExpired                     = 100035;
    ENotDir                             = 100036;
    ENotEmpty                           = 100037;
    EIsDir                              = 100038;
    ENameTooLong                        = 100039;
    ENameEmpty                          = 100040;
    EFCNTL                              = 100041;
    EHeapProfiler                       = 100042;
    EOnlyOneChunkRemoving               = 100043;
    ENotImplemented                     = 100044;
    EChunkNotFound                      = 100045;
    EDBCorrupt                          = 100046;
    EDBIOError                          = 100047;
    ENoMemory                           = 100048;
    ESPDK                               = 100049;
    EQueueFull                          = 100050;
    ESystemModeNotPermitted             = 100051;
    ENotInitialized                     = 100052;
    EInvalidRequestVersion              = 100053;

    // ==== rpc common

    EBadHeaderFormat                    = 100101;
    EUnknownSeviceId                    = 100102;
    EUnknownMethodId                    = 100103;
    EBadMessageFormat                   = 100104;
    ETooLargeMessage                    = 100105;

    // ==== rpc client

    EUnknowMessageId                    = 100151;
    ERpcClientClosed                    = 100152;

    // ==== proto async client

    EDuplicateMessageId                 = 100161;

    // ==== async server

    EAsyncServer                        = 100170;

    // ==== db

    EDbNotOpen                          = 100201;
    EConfigConflict                     = 100202;

    // ==== socket
    ESocket                             = 100300;
    ESocketConnect                      = 100301;
    ESocketBind                         = 100302;
    ESocketListen                       = 100303;
    ESocketAccept                       = 100304;
    ESocketSelect                       = 100305;
    ESocketClosed                       = 100306;
    ESocketEOF                          = 100307;
    ESocketPoll                         = 100308;
    ESocketShutdown                     = 100309;
    ESocketDisconnect                   = 100310;

    // ==== epoll

    EEpoll                              = 100320;
    EEpollCtl                           = 100321;
    EEpollTimerFd                       = 100322;
    EEpollWait                          = 100323;
    EEpollAlreadyExsist                 = 100324;
    EEpollNotActive                     = 100325;
    EEpollTooMuchYield                  = 100326;

    EProtoAsyncClient                   = 100330;

    EBadRequest                         = 100400;
    EForbidden                          = 100403;
    ENotFound                           = 100404;
    EMethodNotAllowed                   = 100405;
    EDuplicate                          = 100409;
    EConnectError                       = 100420;
    EChunksLessThanReplicas             = 100421;
    EChunkConnectUnavailable            = 100422;
    EChunksNotEnoughFreeSpace           = 100423;

    EInternalServerError                = 100500;
    EServiceUnavailable                 = 100503;

    EMongoException                     = 100602;  /* No longer used */
    EMongoError                         = 100603;  /* No longer used */
    EMongoConnect                       = 100604;  /* No longer used */

    // ==== consensus

    EZKConnectError                     = 100701;
    EZKNoNode                           = 100702;
    EZKError                            = 100703;
    EZKStopped                          = 100704;
    EZKSessionExpired                   = 100705;
    EZKNodeExists                       = 100706;
    EZKAPIError                         = 100707;
    EZKInvalidCallback                  = 100708;
    EBadNodeAddress                     = 100710;
    EBadClusterStatus                   = 100711;
    EZKAlreadyRegistered                = 100712;
    EInvalidDb                          = 100713;
    EInvalidDbOp                        = 100714;
    EZKCommit                           = 100715;
    ENotInDbCluster                     = 100716;
    ETooManyPendingJournals             = 100717;
    EBadElectionPathFound               = 100718;
    ENotLeader                          = 100719;
    EDbReplay                           = 100720;
    EDbClusterCommit                    = 100721;
    EZKNotEmpty                         = 100722;
    EBadZNodeVersion                    = 100723;
    EIncompatibleZkJournal              = 100724;
    ESessionExpired                     = 100750;
    EBadSessionEpoch                    = 100751;
    ESessionReconnecting                = 100752;
    EZkMarshallingError                 = 100753;
    EZkSystemError                      = 100754;
    ELocalDbTooOld                      = 100755;
    ENonConsecutiveZkJournal            = 100756;

    // ==== io

    EOpen                               = 100901;
    EIOVCountTooBig                     = 100903;
    EDiskEOF                            = 100904;
    EPathExist                          = 100905;
    EPathNotFound                       = 100906;
    EPathsRangeError                    = 100907;
    EFillZero                           = 100908;
    ECAWMiscompare                      = 100909;
    ETooFewReplica                      = 100910;
    EInvaildAccessPoint                 = 100911;
    EDuringSpecialRecover               = 100912;
    EDuringRecover                      = 100913;
    EDuringSink                         = 100914;
    EUnsupportedType                    = 100999;

    // ==== meta

    EMetaCorrupt                        = 1001;
    EVolumeBroken                       = 1003;
    ERecover                            = 1004;
    ESnapshotNotHealthy                 = 1007;
    EDumpMeta                           = 1008;
    ENoCmdOwner                         = 1009;
    ETooManyReplica                     = 1010;
    EVolumeShrinked                     = 1011;
    ELastReplica                        = 1012;
    EModVerfMismatch                    = 1013;
    EGuardCheck                         = 1014;
    EUpgrade                            = 1015;
    ERevokeLeaseFail                    = 1016;
    EHasStoragePool                     = 1017;
    EVolumeEOF                          = 1018;
    ENoHeathyChunk                      = 1019;
    EUpgradeTimeout                     = 1020;
    EMetaRemoveSlowReplica              = 1021;
    ENoPrioSpace                        = 1022;
    EAlreadySet                         = 1023;

    // ==== chunk

    ECNoVolume                          = 2001;
    ECUnknowOpCode                      = 2004;
    ECAllReplicaFail                    = 2005;
    ECRejectRecover                     = 2006;
    ECAllocExtent                       = 2009;
    ECReadOnly                          = 2010;
    ECBadLocationInfo                   = 2011;
    ECSyncGeneration                    = 2013;
    ECGenerationNotMatch                = 2014;
    ECRebalance                         = 2015;
    ECBadExtentStatus                   = 2016;
    EChunkDataChannelServer             = 2020;
    EPartitionWorker                    = 2021;
    EOriginExtentBroken                 = 2022;
    ENotOwner                           = 2023;
    ENotAlloc                           = 2024;
    ENoNeedRecover                      = 2025;

    EMetaDisconnect                     = 2030;
    EMetaAddReplica                     = 2031;
    EMetaRemoveReplica                  = 2032;
    EMetaReplaceReplica                 = 2033;
    ELeaseExpired                       = 2034;
    ENodeMonitorInit                    = 2035;
    ENoStatInfo                         = 2036;
    ELocalIOFull                        = 2037;
    ELSMCanceled                        = 2038;
    ELSMIOSlow                          = 2039;
    ERetryImmediately                   = 2040;
    EUnmapNeedRetry                     = 2041;
    EPerfNotAlloc                       = 2042;

    // ==== lsm

    ELSMInit                            = 3001;
    EInvalidedChecksumType              = 3006;
    EChecksum                           = 3007;
    EPartitionType                      = 3008;
    EInvalidPartitionType               = 3009;
    EInvalidExtentStatus                = 3010;
    EIOQueueGet                         = 3011;
    EIOQueuePut                         = 3012;
    ENotFoundExtent                     = 3013;
    EInvalidBIOCB                       = 3014;
    EInvalidUIOCB                       = 3015;
    EReadInvalid                        = 3016;
    ENotEnoughPartitionSpace            = 3017;
    EThreadError                        = 3018;
    EIOCTL                              = 3019;
    EBadDevice                          = 3020;
    EMount                              = 3021;
    EFormat                             = 3022;
    EExist                              = 3023;
    EJournalBoundary                    = 3024;
    EAllocateMem                        = 3025;
    EReadSuperBlock                     = 3026;
    EAllJournalsFull                    = 3027;
    EReplayJournals                     = 3028;
    EUmount                             = 3029;
    ELoadJournalEntry                   = 3030;
    EWriteSuperBlock                    = 3031;
    EInvalidDeviceSize                  = 3032;
    EJournal                            = 3033;
    EPartition                          = 3034;
    EMMap                               = 3035;
    EMUnmap                             = 3036;
    ENoJournal                          = 3037;
    EUnknowCacheVersion                 = 3038;
    EAlreadyInCheck                     = 3039;
    EJournalBusy                        = 3040;
    EPartitionWorkerBusy                = 3041;
    EBadExtentEpoch                     = 3042;
    ENotFoundOrigin                     = 3043;
    ENoAvailableDevID                   = 3044;
    ELSMBusy                            = 3045;
    EPromotion                          = 3046;
    EWriteback                          = 3047;
    EDeviceStatus                       = 3048;
    EExtentEOF                          = 3049;
    ELSMNotAllocData                    = 3050;
    ELSMFullWriteAttrs                  = 3051;

    // ==== libzbs
    ENotReady                           = 5001;
    EStopped                            = 5002;
    EBadHandle                          = 5003;
    EIOError                            = 5004;
    EDataChannelManager                 = 5005;
    EIOThrottle                         = 5010;
    ECancelled                          = 5011;
    EResetVolume                        = 5012;
    EAbortTask                          = 5013;
    EDirtyBlockTrackError               = 5014;

    // ==== elf
    EVmNotMigrate                       = 7001;
    EConnectLibvirtFail                 = 7002;

    // ==== metric
    EMetricWrongDate                    = 8001;

    // ==== iscsi
    EInitiatorReservationConflict       = 9001;
    EInitiatorInvalidURL                = 9002;
    EInitiatorConnectFail               = 9003;
    EInitiatorTimeOut                   = 9004;
    EInitiatorLogoutFail                = 9005;
    EInitiatorDisconnectFail            = 9006;
    EInitiatorDiscoveryFail             = 9007;
    EInitiatorLoginFail                 = 9008;
    EInitiatorPollError                 = 9009;
    EInitiatorServiceError              = 9010;
    EInitiatorContextCreate             = 9011; /* deprecated */
    EInitiatorAlreadyLoggedIn           = 9012;
    EInitiatorReportLun                 = 9013;
    EInitiatorUnmarshall                = 9014;
    EInitiatorResetLun                  = 9015;
    EInitiatorWarmReset                 = 9016;
    EInitiatorColdReset                 = 9017;
    EInitiatorInquiry                   = 9018;

    // ==== iscsi redirector
    ERedirectorTargetProbe              = 9050;
    ERedirectorService                  = 9051;
    ERedirectorConnection               = 9052;

    // ==== sunrpc
    ESunRpc                             = 10001;
    ERpcBind                            = 10002;
    ECreateXprt                         = 10003;

    // ==== task
    EWrongRunner                        = 10101;
    ERunnerInterrupt                    = 10102;
    ETaskCanceled                       = 10103;
    EInvalidOperation                   = 10104;
    ETaskPaused                         = 10105;

    // ==== Network
    ENetLink                            = 10201;
    EInterFace                          = 10202;
    EBadAddress                         = 10203;
    EARPBroadCastFailed                 = 10204;
    // ====  rdma, unstable yet
    ERDMACreateEventChannel             = 10301;
    ERDMACreateID                       = 10302;
    ERDMABind                           = 10303;
    ERDMAListen                         = 10304;
    ERDMAAccept                         = 10305;
    ERDMAReject                         = 10306;
    ERDMAConnect                        = 10307;
    ERDMADisconnect                     = 10308;
    ERDMACreateQP                       = 10309;
    ERDMAEventMissingID                 = 10310;
    ERDMAEventMissingVerbs              = 10311;
    ERDMARegisterMessages               = 10320;
    ERDMANotMemory                      = 10321;
    ERDMAQueryDevice                    = 10330;
    ERDMAPostRecv                       = 10331;
    ERDMAPostSend                       = 10332;
    ERDMAPostRead                       = 10333;
    ERDMAPostWrite                      = 10334;
    ERDMADeviceRemoved                  = 10335;

    // ==== regular expression
    EInvalidReString                    = 10401;
    EReNotMatch                         = 10402;
    EReInternalError                    = 10403;

    // ==== compression
    ECompress                           = 10501;
    EDecompress                         = 10502;

    // ==== compare lextent & volume
    EResiliencyTypeNotMatch             = 10601;
    EECAlgorithmParamNotMatch           = 10602;
    EObjectVersionNotMatch              = 10603;
    EChunkCannotComparePExtent          = 10604;
    ENoProxyToCompare                   = 10605;
    ECompareTwoInvalidPExtent           = 10606;

    // === temporary replica
    ETemporaryResponseV1ToV3             = 10701;
    ETemporaryResponseV2ToV3             = 10702;
    ETemporaryReplicaRetryWrite          = 10703;
    ETemporaryWriteParamInvalid          = 10704;

    // === KMS CONFIGS
    EKMSConnectFailed                   = 10801;
    EKMSClusterNotFound                 = 10802;
    EKMSClusterExist                    = 10803;
    EKMSClusterNoServer                 = 10804;
    EKMSClusterNoAuth                   = 10805;
    EKMSServerNotFound                  = 10806;
    EKMSAuthNotFound                    = 10807;
    EKMSAuthExist                       = 10808;
    EKMSGetKeyFailed                    = 10809;
    EKMSCreateKeyFailed                 = 10810;
    EKMSRequestBusy                     = 10811;
    EKMSLastServer                      = 10812;
    EKMSLastHealthyServer               = 10813;
    EKMSServerExist                     = 10814;


    ECipherNotSupport                   = 10830;
    ECipherKeyCorrupt                   = 10831;
    ECipherInvalidMethod                = 10832;
    ECipherNotEncryptVolume             = 10833;
    ECipherHasEncryptObject             = 10834;
    ECipherEmptyEncryptVolume           = 10835;
    ECipherKeyRotationBusy              = 10836;
    ECipherMetadataNotFound             = 10837;

    // ==== recycle bin
    ERecycleBinConfigError               = 10901;
    ETrashPoolError                      = 10902;
    ERestoreTrashVolumeError             = 10903;
    ESweepTrashVolumeError               = 10904;
    ECheckTrashVolumeError               = 10905;
}


enum UserCode {
    UOK                                 = 0;
    UAllocFail                          = 1;
    UNoEnoughSpace                      = 2;

    /* meta user code: 8xxx */
    /* meta user code common */
    UNameTooLong                        = 1001;
    UNameEmpty                          = 1002;
    UDescriptionTooLong                 = 1003;
    UBadReplicaNum                      = 1004;
    USpaceMaxLimit                      = 1005;
    UPExtentNotFound                    = 1006;
    UNameEncoding                       = 1007;
    /* meta pool related */
    UPoolMaxLimit                       = 1100;
    UPoolDuplicate                      = 1101;
    UPoolPropertyCorrupt                = 1102;
    UPoolNotEmpty                       = 1103;
    UPoolNotFound                       = 1104;
    UPoolIdChanged                      = 1105;
    UPoolCreatedDateChanged             = 1106;
    UDefaultPoolCannotBeDeleted         = 1107;
    /* meta volume related */
    UVolumeDuplicate                      = 1200;
    UVolumeBadSize                        = 1201;
    UVolumePropertyCorrupt                = 1202;
    UVolumeOpened                         = 1203;
    UVolumeShrinked                       = 1204;
    UVolumeNotFound                       = 1205;
    UVolumeMaxLimit                       = 1206;
    UVolumeUpdateNotAllowedWhenOpened     = 1207;
    UVolumePoolIdChangeNotAllowed         = 1208;
    UVolumeIdChangeNotAllowed             = 1209;
    UVolumeCreatedDateChangeNotAllowed    = 1210;
    UVolumeStatusChangeNotAllowed         = 1211;
    UVolumeOwnerChangeNotAllowed          = 1212;
    UVolumeThinProvisionChangeNotAllowed  = 1213;
    UVolumeResizeError                    = 1214;
    UVolumeHasSnapshot                    = 1215;
    UVolumeNotOnline                      = 1216;
    UVolumeStripeNumTooBig                = 1217;
    UVolumeStripeSizeTooBig               = 1218;
    UVolumeStripeSizeTooSmall             = 1219;
    UVolumeStripeSizeNotPowerOfTwo        = 1220;
    UVolumeStripeSizeNotAlignToVolumeSize   = 1221;
    UVolumeMaxSizeLimit                   = 1230;
    UVolumeStripeNumConflicted            = 1231;
    UVolumeSizeUnspecified                = 1232;
    UVolumeConvertingResiliencyConflicted = 1233;
    UVolumeResiliencyCompatibilityConflicted = 1234;

    /* meta chunk related */
    UChunkMaxLimit                      = 1300;
    UChunkInvalidRpcIP                  = 1301;
    UChunkInvalidRpcPort                = 1302;
    UChunkInvalidDataIP                 = 1303;
    UChunkInvalidDataPort               = 1304;
    UChunkInvalidHeartbeatIP            = 1305;
    UChunkInvalidHeartbeatPort          = 1306;
    UChunkRegistered                    = 1307;
    UChunkNotRegistered                 = 1308;
    UChunkStillOwnVolume                = 1309;
    UChunkNoSlotInGroup                 = 1310;
    UZoneMaxLimit                       = 1311;
    UPodMaxLimit                        = 1312;
    URackMaxLimit                       = 1313;
    UBrickMaxLimit                      = 1314;
    UZoneNotEmpty                       = 1315;
    UPodNotEmpty                        = 1316;
    URackNotEmpty                       = 1317;
    UBrickNotEmpty                      = 1318;
    UZoneDuplicate                      = 1319;
    UPodDuplicate                       = 1320;
    URackDuplicate                      = 1321;
    UBrickDuplicate                     = 1322;
    URackNotFound                       = 1323;
    UBrickNotFound                      = 1324;
    UZoneNotFound                       = 1325;
    UPodNotFound                        = 1326;
    UOnlyOneChunkRemoving               = 1327;
    UTopoNameDuplicate                  = 1328;
    UCapacityOverflow                   = 1329;
    UPositionConflict                   = 1330;
    UMaxPrioRatio                       = 1331;
    UInsufficientTotalPlannedPRS        = 1332;
    UPriorNodePerZone                   = 1333;
    UPriorInStretched                   = 1334;
    UPriorTooManyOverloadChunk          = 1335;

    /* meta snapshot related */
    USnapshotNotFound                   = 1401;
    USnapshotPropertyCorrupt            = 1402;
    USnapshotDuplicate                  = 1403;
    USnapshotNotHealthy                 = 1404;
    USnapshotSecondaryIDDuplicate       = 1405;

    /* nfs related */
    UInodeMaxLimit                      = 1501;
    UNoParentId                         = 1502;
    UParentNotFound                     = 1503;
    UDirNotEmpty                        = 1504;
    UAlreadyExists                      = 1505;
    UNotDir                             = 1506;
    UExportMaxLimit                     = 1507;
    UPoolExportAttributeChanged         = 1508;
    UNotFile                            = 1509;
    UInodeNotFound                      = 1510;

    /* chunk related */
    UNotFormatted                       = 1601;
    UJournalNotEmpty                    = 1602;

    /* iscsi related */
    UIQNNameMissing                     = 1701;
    ULUNIDMissing                       = 1702;
    ULUNIDDuplicate                     = 1703;
    ULUNMaxLimit                        = 1704;
    UTargetNotFound                     = 1705;
    ULUNNotFound                        = 1706;
    UIQNNameTooLong                     = 1707;
    UEmptyLunPath                       = 1708;
    UISCSIInvalidIQNDate                = 1709;
    UISCSIInvalidIQNNamingAuth          = 1710;
    UISCSIInvalidStorageName            = 1711;
    UISCSIInvalidName                   = 1712;
    UISCSIInvalidInitiatorChapNameLen   = 1713;
    UISCSIInvalidInitiatorChapName      = 1714;
    UISCSIInvalidInitiatorChapSecLen    = 1715;
    UISCSIInvalidInitiatorChapSec       = 1716;
    UISCSIInvalidTargetChapNameLen      = 1717;
    UISCSIInvalidTargetChapName         = 1718;
    UISCSIInvalidTargetChapSecLen       = 1719;
    UISCSIInvalidTargetChapSec          = 1720;
    UISCSIInvalidIQNPrefix              = 1721;
    UISCSIInvalidIQNRegExpr             = 1722;
    UISCSICHAPSecretDuplicate           = 1723;
    UISCSICHAPInitiatorIQNDuplicate     = 1724;
    UISCSIWhitelistEntryTooShort        = 1733;
    UISCSILUNSizeShrink                 = 1734;
    UISCSIWhiteListInvaildRegex         = 1735;
    UISCSISingeAccessTooManyInitiator   = 1736;
    UTargetMaxLimit                     = 1737;
    UISCSISetStretchedReplicaNumFailed  = 1738;
    UISCSIAdaptiveIQNWhitelistConflicted = 1739;
    UISCSIInvalidTargetDriverName       = 1740;
    UISCSINotEmptyTarget                = 1741;
    UISCSIUpdateTargetReplicaNumFailed  = 1742;
    UISCSITargetRequirementConflicted   = 1743;
    UISCSIInvalidLunUuid                = 1744;
    UISCSILunPathUuidConflicted         = 1745;
    UISCSILunNameDuplicated             = 1746;
    UISCSILunFromInodeNotFile           = 1747;
    UISCSILunFromInodeInConsistencyGroup = 1748;
    UISCSILunFromInodeHasHardLink       = 1749;
    UISCSILunFromInodeHasNoVolume       = 1750;
    UISCSILunFromVolumeInConsistencyGroup = 1751;
    UISCSILunFromVolumeStoragePoolNotSame = 1752;
    UISCSILunFromVolumeReplicaNumConflicted = 1753;
    UISCSILunNameDuplicate              = 1754;
    UISCSILunFromVolumeSecondaryIdConflicted = 1755;
    UISCSILunCloneStoragePoolNotSame    = 1756;
    UISCSILunDeleteInConsistencyGroup   = 1757;
    UISCSILunDeleteWithAllowedInitiator = 1758;
    UISCSILunMoveInConsistencyGroup     = 1759;
    UISCSILunInodeConflicted            = 1760;
    UISCSIIQNNotFoundInRemovingCHAP     = 1761;
    UISCSIIQNNotFoundInUpdatdingCHAP    = 1762;
    UISCSILunMissSize                   = 1763;
    UISCSILunFromInodeStoragePoolNotSame = 1764;
    UISCSILunFromInodeReplicaNumConflicted = 1765;
    UISCSIModVerfFailed                 = 1766;
    UISCSILunMoveStoragePoolNotSame     = 1767;
    UISCSILunNotFoundByName             = 1768;
    UISCSIReserveIdConflicted           = 1769;
    UISCSIActiveSessionEmpty            = 1770;
    UISCSIActiveSecondarySessionEmpty   = 1771;
    UISCSIVaildAccessPointNotFound      = 1772;
    UISCSIAccessRecordInitiatorEmpty    = 1773;
    UISCSIAccessRecordNotExternalUse    = 1774;
    UISCSIAccessReocrdInvalidCid        = 1775;
    UISCSIAccessRecordSelectUnhealthyPoint = 1776;
    UISCSIAccessRecordNeedUpdate        = 1777;

    /* nvmf related */
    UNVMFDistSubsystemNotFound          = 1801;
    UNVMFWhitelistEntryTooShort         = 1802;
    UNVMFInvalidNQNPrefix               = 1803;
    UNVMFInvalidNQNDate                 = 1804;
    UNVMFInvalidNQNNamingAuth           = 1805;
    UNVMFInvalidStorageName             = 1806;
    UNVMFNQNNameTooLong                 = 1807;
    UNVMFInvalidName                    = 1808;
    UNVMFNamespaceMaxLimit              = 1809;
    UNVMFNamespaceIDDuplicate           = 1810;
    UNVMFDistNamespacePathEmpty         = 1811;
    UNVMFDistNamespaceSizeShrink        = 1812;
    UNVMFDistNamespaceIDMissing         = 1813;
    UNVMFDistNamespaceNotFound          = 1814;
    UNVMFDistNamespaceGroupNotFound     = 1815;
    UNVMFDistNamespaceFromVolumeSecondaryIdConflicted = 1816;
    UNVMFDistSubsystemNotEmpty          = 1817;

    /* CDP related */
    UCDPBadJobStage                     = 1901;
    UCDPJobsInGroupMaxLimit             = 1902;
    UCDPGroupNotSpecified               = 1903;

    /* User Account related */
    UUserAuthenticationFail             = 2001;
    UUserLoginFail                      = 2002;
    UUserLogoutFail                     = 2003;
    UUserWrongOldPassword               = 2004;
    UUserChangePasswordFail             = 2005;
    UUserCreateFail                     = 2006;
    UUserDeleteFail                     = 2007;
    UUserAddRoleFail                    = 2008;
    UUserDeleteRoleFail                 = 2009;
    UUserNotAdmin                       = 2010;
    UEmailDuplicate                     = 2011;
    UUserNotFound                       = 2012;


    /* Operation logs related */
    ULoadOperationsFail                 = 2101;

    /* Volume related */
    UVolumeDeleteFail                     = 2201;
    UVolumeCreateFail                     = 2202;
    UVolumeUpdateFail                     = 2203;
    UVolumeDetailFail                     = 2204;
    UVolumeCloneFail                      = 2205;
    UVolumeUploadFail                     = 2206;

    /* Pool related */
    UPoolVolumeListFail                   = 2301;
    UPoolDetailFail                     = 2302;
    UPoolListFail                       = 2303;
    UPoolNameFail                       = 2304;
    UPoolUpdateFail                     = 2305;
    UPoolDeleteFail                     = 2306;
    UPoolCreateFail                     = 2307;
    UPoolNotInWhitelist                 = 2308;

    /* Node related */
    UNodeInfoFail                       = 2401;
    UNodeListFail                       = 2403;
    UNodeDetailFail                     = 2404;
    UNodeDeleteFail                     = 2405;
    UNodeAddFail                        = 2406;

    /* snap related */
    USnapListFail                       = 2501;
    USnapCreateFail                     = 2502;
    USnapRollbackFail                   = 2503;
    USnapDeleteFail                     = 2504;
    USnapUpdateFail                     = 2505;
    USnapAddSameTask                    = 2506;


    /* Compute related */
    UVMInstanceSummaryFail              = 2601;
    UComputeResourceSummaryFail         = 2602;
    UVMListFail                         = 2603;
    UVMDeleteFail                       = 2604;
    UVMDetailFail                       = 2605;
    UVMNewFail                          = 2606;
    UVMConfigFail                       = 2607;
    UVMShutdownFail                     = 2608;
    UVMStartFail                        = 2609;
    UVMResetFail                        = 2610;
    UVMNodeListFail                     = 2611;
    UConnectNodeFail                    = 2612;
    UDiskHasBeenUsed                    = 2613;
    UVMNotMigrate                       = 2614;
    UDiskAlreadyAttached                = 2615;
    UVMNotFound                         = 2616;
    UVMCannotMigrateToSameNode          = 2617;
    UVMNotAllowedRebuild                = 2618;
    UVMMigrateFailed                    = 2619;
    URequestTimeOut                     = 2620;
    UVMNameUsed                         = 2621;
    UVMSetMemFailed                     = 2622;
    UVlanTagInvalidValue                = 2623;
    UVlanExist                          = 2624;
    UVlanChangeDefault                  = 2625;
    UVlanNotExist                       = 2626;
    UDeleteNotStopVM                    = 2627;
    UVMNotRunning                       = 2629;
    UVMRebuildFailed                    = 2630;
    UVMTokenCreateFail                  = 2632;
    UDiskNotAttached                    = 2633;
    UMaxDeviceLimit                     = 2634;
    UAccessDenied                       = 2635;
    UNodeIpNotSpecified                 = 2636;
    UBootPathNotSpecified               = 2637;
    UNameNotSpecified                   = 2638;
    UVCPUNotSpecified                   = 2639;
    UMemoryNotSpecified                 = 2640;
    UBadRequest                         = 2641;
    UDestIpNotSpecified                 = 2642;
    UKeyNotFound                        = 2643;
    UMissingParameter                   = 2644;
    UConnectLibvirtFail                 = 2645;
    UConnectMongoFail                   = 2646;  /* No longer used */

    /* license */
    ULicenseNotPermitted                = 2701;
    ULicenseExpired                     = 2702;
    ULicenseMaxNodeNumber               = 2703;
    ULicenseBadFormat                   = 2704;
    ULicenseSerialNotMatch              = 2705;
    ULicenseDowngradeSoftwareEdition    = 2706;
    ULicenseChangeLicenseType           = 2707;
    ULicenseMetroX                      = 2708;
    ULicenseCapacityLimit               = 2709;
    ULicenseNVMFDistSubsystem           = 2710;

    /* metric */
    UGetMetricFail                      = 2801;

    /* ippattern */
    UIPPatternEmpty                     = 2901;
    UIPPatternInvalid                   = 2902;

    /* io throttle config */
    UIOThrottleLimitConflict            = 3001;
    UIOThrottleIOPSLimitNotSpecfied     = 3002;
    UIOThrottleInvalidRateLimit         = 3003;
    UIOThrottleInvalidBurstLength       = 3004;
    UIOThrottleBurstRateNotSpecified    = 3005;
    UIOThrottleAverageNotSpecified      = 3006;
    UIOThrottleAverageTooLarge          = 3007;
    UIOThrottleLowLimitConflict         = 3008;

    /* extent allocation */
    UAllocPrioReplicaFail               = 3101;

    /* regular expression */
    UReNullPtr                          = 4001;
    UReBadOption                        = 4002;
    UReBadMagic                         = 4003;
    UReNoMemory                         = 4004;
    UReUnknown                          = 4005;

    UTrashPoolProtected                 = 5001;
    UTrashPoolInitFailed                = 5002;
    URecycleBinConfigInitFailed         = 5003;
    URecycleBinConfigUpdateFailed       = 5004;

    UTrashVolumeTypeMismatch            = 5010;
    UBadRestoreRequest                  = 5011;
    UTrashVolumeUpdateFailed            = 5012;
    UTrashVolumeProtected               = 5013;

    /* interface limit */
    UBatchCountMaxLimit                 = 5101;

    UOtherError                         = 9999;
}
