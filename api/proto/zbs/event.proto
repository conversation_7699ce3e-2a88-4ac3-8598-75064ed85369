syntax = "proto2";

package zbs;

enum EventCode {
    // ==== chunk server

    LOCAL_IO_START                     = 10001;
    LOCAL_IO_END                       = 10002;
    LSM_START_HANDLE                   = 10003;
    LSM_COW                            = 10004;
    LSM_CACHE_READ_HIT                 = 10005;
    LSM_CACHE_READ_MISS                = 10006;
    LSM_CACHE_READ_PROMOTE             = 10007;
    LSM_CACHE_WRITE_HIT                = 10008;
    LSM_CACHE_WRITE_MISS               = 10009;
    LSM_CACHE_WRITE_PROMOTE            = 10010;

    LSM_WRITE_DURING_RECOVER           = 10011;

    PARTITION_WORKER_ENQUEUE           = 10101;
    PARTITION_WORKER_IO_START          = 10102;
    PARTITION_WORKER_IO_END            = 10103;
    PARTITION_WORKER_ALLOC_WRITE       = 10104;
    PARTITION_WORKER_WRITE_WITH_ORIGIN = 10105;
    PARTITION_WORKER_EVICT             = 10106;
    PARTITION_WORKER_UPDATE_INODE      = 10107;
    PARTITION_WORKER_WAIT_START        = 10111;
    PARTITION_WORKER_WAIT_END          = 10112;
}

message Event {
    required EventCode code     = 1;
    optional uint64    abs_ms   = 2;
    optional uint64    rel_ms   = 3;
}