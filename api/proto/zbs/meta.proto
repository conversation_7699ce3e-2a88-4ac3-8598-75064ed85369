syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

import "common.proto";
import "session.proto";
import "cdp.proto";
import "options.proto";
package zbs.meta;

enum ProtocolObjType {
    ZBS_Volume = 0;
    ISCSI_Lun = 1;
    NVMF_Namespace = 2;
    NFS_File = 3;
}

message VersionInfo {
    optional uint64 major_num = 1 [default = 1];
    optional uint64 minor_num = 2 [default = 0];
    optional bytes meta_id = 3 [(zbs.labels).as_str = true];
}

message TrashProtocolObj {
    optional ISCSILun lun = 1;
    optional NVMFDistNamespace ns = 2;
    optional NFSInode file = 3;
}

message TrashInfo {
    required TimeSpec deleted_time = 1;
    required TimeSpec expired_time = 2;
    required string volume_id = 3;
    required string origin_pool_id = 4;
    // used to confirm protocol type
    optional ProtocolObjType protocol_obj_type = 5;
    optional TrashProtocolObj protocol_obj = 6;
    optional string snapshot_origin_volume_id = 7;
    optional string origin_file_path = 8;
}

message RecycleBinConfig {
    required bool enable_recycle_bin = 1 [ default = true ];
    required uint32 default_vol_expired_hours = 2 [ default = 72 ];
}

message RestoreTrashVolumeRequest {
    required string trash_volume_id = 1;
    optional VolumePath dst_volume_path = 2;
    optional InodeId dst_dir_id = 3;
    optional LunPath dst_lun_path = 4;
    optional DistNamespacePath dst_ns_path = 5;
    optional DistNamespaceGroupPath dst_ns_group_path = 6;
    // SnapshotPath is only allowed to set new_name and dst_pool_path
    optional SnapshotPath dst_snapshot_path = 7;
    optional bool dst_ns_is_shared = 8;
}

message RestoreTrashVolumeResponse {
    required Volume volume = 1;
    required ProtocolObjType protocol_obj_type = 2;
    optional ISCSILun lun = 3;
    optional NFSInode file = 4;
    optional NVMFDistNamespace ns = 5;
}

message BatchSweepTrashVolumesRequest {
    repeated string trash_volume_id = 1;
}

message ShowTrashVolumeRequest {
    required string trash_volume_id = 1;
}

message ListTrashVolumeRequest {
    optional Pagination pagination = 1;
}

message ListTrashVolumeResponse {
    repeated Volume trash_volumes = 1;
    optional uint32 total_num = 2;
}

message ListTrashSnapshotRequest {
    optional Pagination pagination = 1;
}

message ListTrashSnapshotResponse {
    repeated Volume trash_snapshots = 1;
    optional uint32 total_num = 2;
}

// for the description of the leaky bucket alogorithm
// refer to https://blogs.igalia.com/berto/2016/05/24/io-bursts-with-qemu-2-6/
message IOThrottleConfig {
    optional uint64 iops = 1 [default = 0];
    optional uint64 iops_rd = 2 [default = 0];
    optional uint64 iops_wr = 3 [default = 0];
    optional uint64 iops_max = 4 [default = 0];
    optional uint64 iops_rd_max = 5 [default = 0];
    optional uint64 iops_wr_max = 6 [default = 0];
    optional uint64 iops_max_length = 7 [default = 1];
    optional uint64 iops_rd_max_length = 8 [default = 1];
    optional uint64 iops_wr_max_length = 9 [default = 1];

    optional uint64 io_size = 10 [default = 4096]; // 4K
    optional uint64 bps = 11 [default = 0];
    optional uint64 bps_rd = 12 [default = 0];
    optional uint64 bps_wr = 13 [default = 0];
    optional uint64 bps_max = 14 [default = 0];
    optional uint64 bps_rd_max = 15 [default = 0];
    optional uint64 bps_wr_max = 16 [default = 0];
    optional uint64 bps_max_length = 17 [default = 1];
    optional uint64 bps_rd_max_length = 18 [default = 1];
    optional uint64 bps_wr_max_length = 19 [default = 1];
}

message Pool {
    required bytes name = 1 [(zbs.labels).as_str = true];  // < 255B
    optional uint32 id_v1 = 2 [deprecated=true];
    optional bytes id = 3 [(zbs.labels).as_str = true];
    optional TimeSpec created_time = 4;

    // if not specified, the system storage pool is used
    optional bytes storage_pool_id = 5 [(zbs.labels).as_str = true];

    optional uint32 replica_num = 11;
    optional bool thin_provision = 13;

    optional bytes description = 21 [default = "", (zbs.labels).as_str = true];  // 256B

    // deprecated: (v1 needed it)
    optional bool nfs_export = 42 [default = false, deprecated = true];

    // mod_verf is a guard against unintended modification/removal of
    // a pool, if set any future requestis attempting to alter the pool
    // needs to provide a match verification number
    optional uint32 mod_verf = 43;

    // ip v4: *************
    // ip/net-mask: ************/*************
    // ip/mask: ************/24
    // */*
    optional bytes whitelist = 44 [default = "*/*", (zbs.labels).as_str = true];

    // copied to every volume
    optional IOThrottleConfig throttling = 50;

    // copied to every volume
    optional uint32 stripe_num = 61 [default = 1];
    optional uint32 stripe_size = 62 [default = 262144];  // 256K

    optional bool prioritized = 70 [default = false];
    optional ResiliencyType resiliency_type = 71 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 72;
    optional EncryptMethod encrypt_method = 73 [default = ENCRYPT_PLAIN_TEXT];
}

message PoolPath {
    // either pool_id or pool_name is specified
    // if pool_id is specified, pool_name is ignored
    optional bytes pool_name = 1 [(zbs.labels).as_str = true];
    optional bytes pool_id = 2 [(zbs.labels).as_str = true];

    optional uint32 mod_verf = 12;
}

message PoolsResponse {
    repeated Pool pools = 2;
    optional uint32 total_num = 10;
}

message UpdatePoolRequest {
    required PoolPath path = 1;

    optional bytes name = 10 [(zbs.labels).as_str = true];
    optional uint32 replica_num = 11;
    optional bool thin_provision = 12;
    optional bytes description = 13 [(zbs.labels).as_str = true];  // 256B
    optional bytes user = 14 [(zbs.labels).as_str = true];
    optional uint32 mod_verf = 15;
    optional bytes whitelist = 21 [(zbs.labels).as_str = true];

    optional IOThrottleConfig throttling = 50;

    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;

    optional bool prioritized = 70 [default = false];
    optional ResiliencyType resiliency_type = 71;
    optional ECAlgorithmParam ec_param      = 72;
    optional EncryptMethod encrypt_method = 73 [default = ENCRYPT_PLAIN_TEXT];
}

message ListPoolRequest {
    optional Pagination pagination = 1;

    // user id. Not set or empty means root user.
    optional bytes user = 11 [default = "", (zbs.labels).as_str = true];
}

enum VolumeStatus {
    VOLUME_ONLINE = 0;  // the volume is online/ok.
    VOLUME_OFFLINE = 1;  // the volume is not available for read or write
    VOLUME_BROKEN = 2;  // the volume is broken
}

enum PExtentStatus {
    PEXTENT_HEALTHY = 0;
    PEXTENT_DEAD = 1;
    PEXTENT_BROKEN = 2;
    PEXTENT_NEED_RECOVER = 3;
    PEXTENT_GARBAGE = 4;
    PEXTENT_MAY_RECOVER = 5;
    PEXTENT_NEED_DRAIN = 6;
}

message AccessPoint {
    optional uint32 cid = 1;
}

message Volume {
    optional bytes name = 1 [(zbs.labels).as_str = true];  // < 255B
    optional uint64 size = 2; //Bytes
    optional uint32 id_v1 = 3 [deprecated=true];
    optional uint32 parent_id_v1 = 4 [deprecated=true];
    optional TimeSpec created_time = 5;
    optional bytes id = 6 [(zbs.labels).as_str = true];
    optional bytes parent_id = 7 [(zbs.labels).as_str = true];  // parent id
    // where this volume is from: either a snapshot or a source volume (when
    // clone from a volume)
    optional bytes origin_id = 8 [(zbs.labels).as_str = true];

    optional uint32 replica_num = 12;
    optional bool thin_provision = 14;

    // The following fields are used in encrypted volumes only.
    optional EncryptMethod encrypt_method = 16 [default = ENCRYPT_PLAIN_TEXT];
    optional uint64 encrypt_metadata_id = 18 [default = 0];

    // read only volume could be opened by multiple readers and can only be open
    // for read
    optional bool read_only = 21 [default = false];

    optional VolumeStatus status = 33 [default = VOLUME_ONLINE];

    optional bytes description = 41 [default = "", (zbs.labels).as_str = true];  // 256B
    optional uint64 iops = 51 [default = 0, deprecated=true];
    optional uint64 iops_burst = 52 [default = 0, deprecated=true];
    optional uint64 bps = 53 [default = 0, deprecated=true];
    optional uint64 bps_burst = 54 [default = 0, deprecated=true];
    optional IOThrottleConfig throttling = 55;

    optional uint32 stripe_num = 61 [default = 1];
    optional uint32 stripe_size = 62 [default = 262144];  // 256K

    // used by snapshot or cloned volume
    optional bool is_snapshot = 70 [default = false];
    optional bytes snapshot_pool_id = 500 [(zbs.labels).as_str = true];  // snapshot's pool id

    // diff size when cloning or snapshotting. Note this value is only created
    // when the volume is created. So it doesn't reflects the diff (unique) size of
    // the system later.
    optional uint64 diff_size = 71 [default = 0];
    optional NFSAttr nfs_meta = 72;

    // unique size means data size only used by this volume
    // unique_size == -1 means it has never be scaned
    // shared size means data size shared with other volume
    // shared_size == -1 means it has never be scaned
    // capacity layer
    optional int64 unique_size = 73 [default = -1];
    optional int64 shared_size = 74 [default = -1];
    // performance layer
    optional int64 perf_unique_size = 75 [default = -1];
    optional int64 perf_shared_size = 76 [default = -1];

    optional int64 logical_used_size = 77 [default = -1];

    repeated AccessPoint access_points = 80;

    optional bool alloc_even = 81 [default = false];
    // how many time has volume be cloned
    optional uint64 clone_count = 82 [default = 0];

    optional bool skip_all_zero_first_write = 83 [default = false];

    optional bytes secondary_id = 84 [(zbs.labels).as_str = true];
    optional uint32 prefer_cid  = 85 [default = 0];

    repeated uint32 vextent_id = 100;

    optional bool prioritized = 110 [default = false];
    optional uint64 downgraded_prioritized_space = 111 [default = 0];  // Bytes

    optional bool file = 300 [default = false];

    // allocated virtual size
    optional uint64 alloced_virtual_bytes = 400 [deprecated = true];

    // allocated physical size = virtual size x replica num - missing
    // replicas size
    optional uint64 alloced_physical_bytes = 401 [deprecated = true];

    optional ResiliencyType resiliency_type = 402 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 403;

    optional uint64 chunk_instances_bits = 404 [default = 0];

    // only trash volume should set TrashInfo
    optional TrashInfo trash_info = 501;
}

message ListVolumeRequest {
    required PoolPath pool_path = 1;
    optional Pagination pagination = 2;
}

message VolumesResponse {
    repeated Volume volumes = 2;
    optional uint32 total_num = 10;
}

message ShowVolumeRequest {
    optional PoolPath pool_path = 1;
    optional bytes volume_name = 2 [(zbs.labels).as_str = true];
    optional bytes volume_id = 3 [(zbs.labels).as_str = true];
    optional bytes secondary_id = 4 [(zbs.labels).as_str = true];
    // should be set to false in most scenarios when you only want volume basic info,
    // use true only for forward compatible
    optional bool show_lextent = 5 [ default = true];
    optional bool show_pextent = 6 [ default = false];
    optional bool show_alive_pextents_only = 7 [ default = false];
}

message VolumePath {
    optional PoolPath pool_path = 1;
    optional bytes volume_name = 2 [(zbs.labels).as_str = true];
    optional bytes volume_id = 3 [(zbs.labels).as_str = true];
    optional bytes secondary_id = 4 [(zbs.labels).as_str = true];
}

message DeleteVolumeRequest {
    optional PoolPath pool_path = 1;
    optional bytes volume_name = 2 [ (zbs.labels).as_str = true ];
    optional bytes volume_id = 3 [ (zbs.labels).as_str = true ];
    optional bytes secondary_id = 4 [ (zbs.labels).as_str = true ];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 5 [ default = false ];
}

message VolumeId {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
}

message CreateVolumeRequest {
    required PoolPath pool_path = 1;
    required Volume volume = 2;
    optional uint32 preferred_cid = 3 [ default = 0 ];
}

message MoveVolumeRequest {
    required VolumePath volume_path = 1;
    required PoolPath dst_pool_path = 2;
}

enum FindVolumeType {
    VOLUME_EVEN = 0;
    VOLUME_NEED_ELEVATE = 1;
}

message FindVolumeRequest {
    optional FindVolumeType type = 1;
}

message KMIPServer {
    required string id = 1;
    required string name = 2;
    required string host = 3;
    required uint32 port = 4;
}

message KMIPAuth {
    required string auth_id = 1;
    required bytes client_cert = 2;
    required bytes client_key = 3;
    optional bytes root_cert = 4;
    optional string username = 5;
    optional string password = 6;
}

message KMIPCluster {
    required string cluster_id = 1;
    required string cluster_name = 2;
    optional string vendor = 3;
    repeated KMIPServer servers = 4;
    optional KMIPAuth auth = 5;
    // the current master key id of this kmip cluster
    optional bytes master_key_id = 6;
    optional uint64 last_key_rotate_time = 7;
    // 31536000 seconds is 1 year
    optional uint64 key_rotate_period_seconds = 8 [default = 31536000];
    optional uint64 last_all_key_backup_time = 9;
    // it will be set true when begin rotate master key.
    optional bool is_rotating_master_key = 10 [default = false];
    optional bytes encrypted_master_key_id = 11;
}

message ClusterKmipServer {
    required string server_name = 1;
    required string host = 2;
    required uint32 port = 3;
}

message ClusterKmipAuth {
    // ClusterKmipAuth is compatible with AddKmipAuthReqeust, so here reserve field 1.
    required bytes client_cert = 2;
    required bytes client_key = 3;
    optional bytes root_cert = 4;
    optional string username = 5;
    optional string password = 6;
}

message CreateKmipClusterRequest {
    required string cluster_name = 1;
    optional string vendor = 2;
    repeated ClusterKmipServer servers = 3;
    optional ClusterKmipAuth auth = 4;
    optional uint64 key_rotate_period_seconds = 5;
}

message DeleteKmipClusterRequest {
    required string cluster_id = 1;
}

enum KmipServerStatus {
    KMIP_SERVER_CONNECTED = 0;
    KMIP_SERVER_DISCONNECTED = 1;
}

message KmipServerTaskStatus {
    optional int64 epoch_sec = 1;
    optional string key_id = 2;
    optional string res_detail = 3;
}

message KMIPServerResponse {
    optional string id = 1;
    optional string name = 2;
    optional string host = 3;
    optional uint32 port = 4;
    optional KmipServerStatus status = 5;
}

message KMIPClusterResponse {
    required string cluster_id = 1;
    required string cluster_name = 2;
    optional string vendor = 3;
    repeated KMIPServerResponse servers = 4;
    optional KMIPAuth auth = 5;
    optional TimeSpec last_key_rotate_time = 6;
    optional uint64 key_rotate_period_seconds = 7;
    optional TimeSpec last_all_key_backup_time = 8;
    optional bool is_rotating_master_key = 9;
}

message ListKmipClusterResponse {
    repeated KMIPClusterResponse cluster = 1;
}

message UpdateKmipClusterRequest {
    required string cluster_id = 1;
    optional string cluster_name = 2;
    optional uint64 key_rotate_period_seconds = 3;
}

message RefreshKmipClusterRequest {
    required string cluster_id = 1;
    optional string cluster_name = 2;
    optional string vendor = 3;
    repeated ClusterKmipServer servers = 4;
    optional ClusterKmipAuth auth = 5;
}

message AddKmipServerRequest {
    required string cluster_id = 1;
    required string server_name = 2;
    required string host = 3;
    required uint32 port = 4;
    optional bool force = 5 [default = false];
}

message UpdateKmipServerRequest {
    required string cluster_id = 1;
    required string server_id = 2;
    optional string server_name = 3;
    optional string host = 4;
    optional uint32 port = 5;
    optional bool force = 6 [default = false];
}

message DeleteKmipServerRequest {
    required string cluster_id = 1;
    required string server_id = 2;
    optional bool force = 3 [default = false];
}

message AddKmipAuthRequest {
    optional string cluster_id = 1;
    required bytes client_cert = 2;
    required bytes client_key = 3;
    optional bytes root_cert = 4;
    optional string username = 5;
    optional string password = 6;
}

message UpdateKmipAuthRequest {
    required string cluster_id = 1;
    required string auth_id = 2;
    optional bytes client_cert = 3;
    optional bytes client_key = 4;
    optional bytes root_cert = 5;
    optional string username = 6;
    optional string password = 7;
    optional bool force = 8 [default = false];
}

message DeleteKmipAuthRequest {
    required string cluster_id = 1;
    required string auth_id = 2;
}

message RotateKeyRequest {
    required string cluster_id = 1;
}

enum KeyEncryptMethod {
    ENCRYPT_KEY_UNKNOWN = 0;
    ENCRYPT_AES256_GCM = 1;
}

message EncryptKey {
    required uint64 metadata_id = 1;
    required bytes encrypted_key = 2;
}

message ExportAllEncryptKeyRequest {
    required string dek_encrypt_password = 1;
    optional KeyEncryptMethod encrypt_method = 2;
}

message ExportAllEncryptKeyResponse {
    repeated EncryptKey export_keys = 1;
}

message ImportEncryptKeyRequest {
    required string dek_encrypt_password = 1;
    repeated EncryptKey import_keys = 2;
    optional KeyEncryptMethod encrypt_method = 3;
}

message ListKmipServersConnectivityRequest {
    required string cluster_id = 1;
}

message KmipServerConnectivity {
    required string server_id = 1;
    required KmipServerStatus server_status = 2;
    optional KmipServerTaskStatus get_status = 3;
    optional KmipServerTaskStatus create_status = 4;
    optional KmipServerTaskStatus hb_status = 5;
}

message ListKmipServersConnectivityResponse {
    repeated KmipServerConnectivity servers_conn = 1;
}

message GetVExtentLeaseRequest {
    required bytes vtable_id = 1 [(zbs.labels).as_str = true];
    required uint32 vextent_no = 2;
    optional bytes preferred_session = 3 [default = "", (zbs.labels).as_str = true];

    // if it is a write request
    optional bool is_write = 4 [default = false];

    // if resize as needed
    optional bool resize = 5 [default = false];
    optional uint32 preferred_cid = 6;
    optional bool no_alloc = 7 [default = false];

    optional bool for_route = 8 [default = false];
}

message GetLeaseRequest {
    required uint32 pid = 1;
    optional bytes preferred_session = 2 [default = "", (zbs.labels).as_str = true];
    optional bool for_sink = 3 [default = false];
}

message ShowVolumeResponse {
    optional Volume    volume       = 2;
    repeated LExtentResp lextents = 3;
    repeated PExtentResp pextents = 4;
}

message VolumeInfo {
    required VolumePath path = 1;
    optional bool exist = 2;
    optional Volume volume = 3;
    repeated LExtentResp lextents = 4;
    repeated PExtentResp pextents = 5;
}

message ShowVolumesRequest {
    repeated VolumePath volumes = 1;
    optional bool show_lextent = 2 [default = false];
    optional bool show_pextent = 3 [default = false];
}

message ShowVolumesResponse {
    repeated VolumeInfo  volumes = 1;
}

message UpdateVolumeRequest {
    required VolumePath path = 1;

    optional bytes name = 10 [(zbs.labels).as_str = true];  // < 255B
    optional uint32 replica_num = 12;
    optional bool thin_provision = 13;
    optional bytes description = 14 [(zbs.labels).as_str = true];  // 256B

    optional uint64 iops = 15 [deprecated=true];
    optional uint64 iops_burst = 16 [deprecated=true];
    optional uint64 bps = 17 [deprecated=true];
    optional uint64 bps_burst = 18 [deprecated=true];
    optional IOThrottleConfig throttling = 19;

    optional bool alloc_even = 20;
    optional bool skip_all_zero_first_write = 21 [default = false];
    optional uint32 prefer_cid              = 22 [default = 0];
    optional bool read_only                 = 23;
    optional bool prioritized               = 24 [default = false];
    optional uint64 new_size                = 25;
    optional uint64 chunk_instances_bits = 26 [default = 0];
}

message ResizeVolumeRequest {
    required VolumePath path = 1;
    required uint64 size = 2;
}

message VolumeSizeResponse {
    required uint64 size = 1;
}

message GetVTableResponse {
    repeated VExtent vextents = 12;
}

message ListPExtentRequest {
    required uint32 pid_start = 1;
    required uint32 pid_end = 2;
    optional bool show_non_exist = 3 [default = false];
    optional bool show_cap_only = 4 [default = true];
    optional bool show_perf_only = 5 [default = false];
}

message PExtentsResponse {
    repeated PExtent pextents = 2;
}

message ListSnapshotRequest {
    optional VolumePath volume_path = 1;
    optional Pagination pagination = 2;
}

message UpdateEpochRequest {
    required bool enable = 1 [default = false];
    optional bool reset = 2 [default = false];
}

message UpdateEpochResponse {
    required bool enable = 1 [default = false];
    required uint64 epoch = 2;
}

message SnapshotPath {
    optional VolumePath volume_path = 1;
    optional bytes snapshot_name = 2 [(zbs.labels).as_str = true];
    optional bytes snapshot_id = 3 [(zbs.labels).as_str = true];
    optional bytes secondary_id = 4 [(zbs.labels).as_str = true];
}

message DeleteSnapshotRequest {
    optional VolumePath volume_path = 1;
    optional bytes snapshot_name = 2 [ (zbs.labels).as_str = true ];
    optional bytes snapshot_id = 3 [ (zbs.labels).as_str = true ];
    optional bytes secondary_id = 4 [ (zbs.labels).as_str = true ];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 5 [ default = false ];
}

// notice: the value of the type cannot be changed.
enum NFSType {
    NONE = 0;
    FILE = 1;
    DIR = 2;
    BLK = 3;  // not used
    CHR = 4;  // not used
    LNK = 5;
    SOCK = 6; // not used
    FIFO = 7; // not used
}

message NFSAttr {
    optional NFSType type = 1;
    optional uint32 mode = 2 [default = 0x1B0];
    optional uint32 uid = 3 [default = 0];
    optional uint32 gid = 4 [default = 0];
    optional uint64 size = 5;
    optional TimeSpec ctime = 6;
    optional TimeSpec mtime = 7;
    optional TimeSpec atime = 8;
    optional uint32 nlink = 9 [default = 1];
}

// notice: the value of the type cannot be changed.
enum ISCSIType {
    SCSI_TYPE_DISK = 0;
    SCSI_TYPE_TAPE = 1;
    SCSI_TYPE_PRINTER = 2;
    SCSI_TYPE_PROCESSOR = 3;
    SCSI_TYPE_WORM = 4;
    SCSI_TYPE_MMC = 5;
    SCSI_TYPE_SCANNER = 6;
    SCSI_TYPE_MOD = 7;

    SCSI_TYPE_MEDIUM_CHANGER = 8;
    SCSI_TYPE_COMM = 9;

    SCSI_TYPE_RAID = 12;
    SCSI_TYPE_ENCLOSURE = 13;
    SCSI_TYPE_RBC = 14;
    SCSI_TYPE_OSD = 15;
    SCSI_TYPE_NO_LUN = 127;
    SCSI_TYPE_PT = 255; // 0xff
}

message TargetChapInfo {
    required bytes secret           = 1 [(zbs.labels).as_str = true];
    required bytes chap_name        = 2 [(zbs.labels).as_str = true];
    optional bool  enable           = 3 [default = true];
}

message InitiatorChapInfo {
    required bytes iqn              = 1 [(zbs.labels).as_str = true];
    required bytes chap_name        = 2 [(zbs.labels).as_str = true];
    required bytes secret           = 3 [(zbs.labels).as_str = true];
    optional bool enable            = 4 [default = true];
}

message ISCSIAccessRecord {
    optional string initiator = 1;
    optional uint32 cid = 2;
}

message ISCSIAccessRecords {
    // target uuid
    required bytes target_id = 1 [(zbs.labels).as_str = true];
    // access login record for balance target & external use target
    repeated ISCSIAccessRecord accesses = 2;
}

message ISCSITarget {
    required bytes id = 1 [(zbs.labels).as_str = true]; // pool_id
    required bytes name = 2 [(zbs.labels).as_str = true]; // same with pool_name, used in iqn_name

    // iqn name rfc 3721
    // https://www.ietf.org/rfc/rfc3721.txt
    // iSCSI Qualified Name (IQN)
    // Format: The iSCSI Qualified Name is documented in RFC 3720, with further
    // examples of names in RFC 3721. Briefly, the fields are:
    // literal iqn (iSCSI Qualified Name)
    // date (yyyy-mm) that the naming authority took ownership of the domain
    // reversed domain name of the authority (e.g. org.alpinelinux, com.example,
    // to.yp.cr)
    // Optional ":" prefixing a storage target name specified by the naming
    // authority.
    // From the RFC:
    //                   Naming     String defined by
    //      Type  Date    Auth      "example.com" naming authority
    //     +--++-----+ +---------+ +-----------------------------+
    //     |  ||     | |         | |                             |
    //
    //     iqn.1992-01.com.example:storage:diskarrays-sn-a8675309
    //     iqn.1992-01.com.example
    //     iqn.1992-01.com.example:storage.tape1.sys1.xyz
    //     iqn.1992-01.com.example:storage.disk2.sys1.xyz[10]
    required bytes iqn_name = 3 [(zbs.labels).as_str = true]; // used in iscsi INQUIRY, maxmium 223
    optional TimeSpec created_time = 4;

    // attributes for iSCSI Qualified Name (IQN)
    // our rule for iqn_name:
    // iqn.$iqn_date.$iqn_naming_auth:$storage_pool_name:$name
    optional bytes iqn_date = 5 [(zbs.labels).as_str = true];
    optional bytes iqn_naming_auth = 6 [(zbs.labels).as_str = true];
    optional bytes description = 7 [default = "", (zbs.labels).as_str = true];
    optional bytes driver_name = 8 [default = "iscsi", (zbs.labels).as_str = true];  // "iscsi" or "iser"

    optional Pool pool = 10;  // pool of this target

    optional TargetChapInfo target_chap = 11;  // mutual chap
    // acceptable chaps, non-zero size means CHAP is required
    repeated InitiatorChapInfo initiator_chap = 12;

    repeated bytes iqn_whitelist = 13 [deprecated = true, (zbs.labels).as_str = true];

    optional bool external_use = 14 [default = false];

    optional bytes iqn_whitelist_v2 = 15 [default = "*/*", (zbs.labels).as_str = true];
    optional bool adaptive_iqn_whitelist = 16 [default = false];

    // this field is available from ListTarget, but not guaranteed for other rpc
    repeated ISCSILun luns = 20;
    // use for quick compare target config
    optional uint32 config_ver = 21 [default = 0];

    optional Labels labels = 30;
    optional bool prioritized = 31 [default = false];
}

message CreateISCSITargetRequest {
    required bytes name = 1 [(zbs.labels).as_str = true];

    // if not specified, the system storage pool is used
    optional bytes storage_pool_id = 2 [(zbs.labels).as_str = true];

    optional bytes driver_name = 3 [default = "iscsi", (zbs.labels).as_str = true];  // "iscsi" or "iser"

    optional uint32 replica_num = 11;
    optional bool thin_provision = 13;

    // attributes for iSCSI Qualified Name (IQN)
    optional bytes iqn_date = 14 [(zbs.labels).as_str = true];
    optional bytes iqn_naming_auth = 15 [(zbs.labels).as_str = true];
    optional bool external_use = 16 [default = false];

    optional bytes description = 21 [(zbs.labels).as_str = true]; // 256B
    optional bytes whitelist = 22 [default = "*/*", (zbs.labels).as_str = true];

    optional TargetChapInfo target_chap = 31;
    repeated InitiatorChapInfo initiator_chap = 32;

    repeated bytes iqn_whitelist = 33 [deprecated=true, (zbs.labels).as_str = true];

    optional bytes iqn_whitelist_v2 = 34 [default = "*/*", (zbs.labels).as_str = true];
    optional bool adaptive_iqn_whitelist = 35;

    optional IOThrottleConfig throttling = 50;

    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;

    optional Labels  labels = 70;
    optional bool prioritized = 71 [default = false];
    optional ResiliencyType resiliency_type = 72 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 73;
    optional EncryptMethod encrypt_method = 74 [default = ENCRYPT_PLAIN_TEXT];
}

message UpdateISCSITargetRequest {
    required PoolPath pool_path = 1;

    optional bytes name = 10 [(zbs.labels).as_str = true];
    optional uint32 replica_num = 11;
    optional bool thin_provision = 13;

    // attributes for iSCSI Qualified Name (IQN)
    optional bytes iqn_date = 14 [(zbs.labels).as_str = true];
    optional bytes iqn_naming_auth = 15 [(zbs.labels).as_str = true];

    optional bool external_use = 16 [default = false];

    optional bytes description = 21 [(zbs.labels).as_str = true];  // 256B
    optional bytes whitelist = 22 [(zbs.labels).as_str = true];

    // target chap
    optional bytes chap_name = 31 [(zbs.labels).as_str = true];
    optional bytes secret = 32 [(zbs.labels).as_str = true];
    optional bool enable_target_chap = 33;
    optional bool remove_target_chap = 34 [default = false];

    // initiator chaps, processing seq: remove, update then create
    repeated bytes remove_initiator_chap = 35 [(zbs.labels).as_str = true];
    repeated InitiatorChapInfo update_initiator_chap = 36;
    repeated InitiatorChapInfo create_initiator_chap = 37;

    // prefix is either of: 1. "iqn." for an iqn
    //                      2. "reg." for a regular expression
    repeated bytes iqn_whitelist = 38 [deprecated = true, (zbs.labels).as_str = true];
    optional bool remove_iqn_whitelist = 39 [deprecated = true];
    optional bytes iqn_whitelist_v2 = 40 [(zbs.labels).as_str = true];
    optional bool adaptive_iqn_whitelist = 41;

    optional IOThrottleConfig throttling = 50;

    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;
    optional bool prioritized = 63 [default = false];

    optional ResiliencyType resiliency_type = 64;
    optional ECAlgorithmParam ec_param      = 65;

    optional ResetLabelsParams reset_labels_params = 66;
    optional EncryptMethod encrypt_method = 67 [default = ENCRYPT_PLAIN_TEXT];
}

message ISCSITargetsResponse {
    repeated ISCSITarget targets = 2;
    optional uint32 total_num = 10;
}

message ISCSILun {
    required uint32 lun_id = 1; // lun_id, used in scsi REPORTLUNS
    required bytes pool_id = 2 [(zbs.labels).as_str = true]; // same with underlying pool_id
    required bytes volume_id = 3 [(zbs.labels).as_str = true]; // same with underlying volume_id
    optional TimeSpec created_time = 4;

    optional ISCSIType type = 5 [default = SCSI_TYPE_DISK, deprecated=true];
    // copied from volume
    optional uint64 size = 6;
    optional bytes description = 7 [(zbs.labels).as_str = true];
    optional uint32 replica_num = 11;
    optional bool thin_provision = 13;
    optional uint64 iops = 15 [deprecated=true];
    optional uint64 iops_burst = 16 [deprecated=true];
    optional uint64 bps = 17 [deprecated=true];
    optional uint64 bps_burst = 18 [deprecated=true];
    optional IOThrottleConfig throttling = 19;

    // capacity layer
    optional int64 unique_size = 20 [default = -1];
    optional int64 shared_size = 21 [default = -1];
    // performance layer
    optional int64 perf_unique_size = 22 [default = -1];
    optional int64 perf_shared_size = 23 [default = -1];

    optional int64 logical_used_size = 24 [default = -1];

    // spc2 reservation
    optional uint64 reserve_id = 30 [default = 0];

    // for openstack volume name
    optional bytes name = 41 [(zbs.labels).as_str = true];
    // for scsi id in host
    optional bytes naa = 42 [(zbs.labels).as_str = true];
    optional bytes allowed_initiators = 51 [default = "*/*", (zbs.labels).as_str = true];
    optional bool single_access = 52 [default = false];
    // stripe num
    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;

    optional PRInfo pr_info = 70;
    optional uint32 queue_num = 71;
    optional bool prioritized = 72 [default = false];
    optional uint64 downgraded_prioritized_space = 73 [default = 0];  // Bytes

    optional ResiliencyType resiliency_type = 74 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 75;

    optional uint64 chunk_instances_bits = 76 [default = 0];
    optional EncryptMethod encrypt_method = 77 [default = ENCRYPT_PLAIN_TEXT];
}

message LunPath {
    required PoolPath pool_path = 1;
    optional uint32 lun_id = 2 [default = 0];
    optional bytes lun_name = 3 [(zbs.labels).as_str = true];

    optional bytes lun_uuid = 4 [(zbs.labels).as_str = true];  // pool_path/lun_id/lun_name are ignored
    optional bytes storage_pool_id = 5 [(zbs.labels).as_str = true];  // with lun_uuid only
    optional bytes secondary_id = 6 [(zbs.labels).as_str = true];
}

message DeleteLunRequest {
    required PoolPath pool_path = 1;
    optional uint32 lun_id = 2 [default = 0];
    optional bytes lun_name = 3 [(zbs.labels).as_str = true];

    optional bytes lun_uuid = 4 [(zbs.labels).as_str = true];  // pool_path/lun_id/lun_name are ignored
    optional bytes storage_pool_id = 5 [(zbs.labels).as_str = true];  // with lun_uuid only
    optional bytes secondary_id = 6 [(zbs.labels).as_str = true];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 7 [ default = false ];
}

message BatchDeleteLunsRequest {
    required PoolPath pool_path = 1;
    repeated uint32 lun_ids = 2;
    optional bool delete_snapshots = 3 [default = false];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 4 [ default = false ];
}

message BatchDeleteDistNamespacesRequest {
    required PoolPath pool_path = 1;
    repeated uint32 ns_ids = 2;
    optional bool delete_snapshots = 3 [default = false];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 4 [ default = false ];
}

message TargetRequirement {
    optional bool  external_use = 1;
    optional Labels labels = 2;
    optional uint32 replica_num = 3;
    optional bool adaptive_iqn_whitelist = 4;
    optional ResiliencyType resiliency_type = 5 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param = 6;
    optional bool prioritized = 7;
    optional EncryptMethod encrypt_method = 8 [default = ENCRYPT_PLAIN_TEXT];
}

message CreateISCSILunRequest {
    required LunPath lun_path = 1;
    optional uint64 size = 3;

    optional ISCSIType type = 4 [default = SCSI_TYPE_DISK];
    optional bytes description = 5 [(zbs.labels).as_str = true];

    optional uint32 replica_num = 11;
    optional bool thin_provision = 13;
    optional uint64 iops = 15 [deprecated=true];
    optional uint64 iops_burst = 16 [deprecated=true];
    optional uint64 bps = 17 [deprecated=true];
    optional uint64 bps_burst = 18 [deprecated=true];
    optional IOThrottleConfig throttling = 19;

    optional LunPath src_lun_path = 21; // clone from another lun
    optional bytes src_snapshot_id = 23 [(zbs.labels).as_str = true]; // clone from another snapshot

    optional bytes allowed_initiators = 31 [(zbs.labels).as_str = true];
    optional bool single_access = 32 [default = false];

    // stripe num
    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;

    optional bytes inode_path = 71 [(zbs.labels).as_str = true];  // create from nfs inode

    optional uint32 preferred_cid = 72 [ default = 0 ];

    optional TargetRequirement target_requirement = 73;
    optional uint32 queue_num = 74;
    optional bool prioritized = 75 [default = false];

    optional ResiliencyType resiliency_type = 76 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 77;

    optional uint64 chunk_instances_bits = 78 [default = 0];
    optional EncryptMethod encrypt_method = 79 [default = ENCRYPT_PLAIN_TEXT];
}

message ConvertVolumeIntoLunRequest {
    required LunPath lun_path = 1;
    required VolumePath volume_path = 2;

    optional ISCSIType type = 3 [default = SCSI_TYPE_DISK];
    optional bytes description = 4 [(zbs.labels).as_str = true];

    optional uint32 replica_num = 11;
    optional bool thin_provision = 12;
    optional uint64 iops = 15 [deprecated=true];
    optional uint64 iops_burst = 16 [deprecated=true];
    optional uint64 bps = 17 [deprecated=true];
    optional uint64 bps_burst = 18 [deprecated=true];
    optional IOThrottleConfig throttling = 19;

    // stripe num
    optional uint32 stripe_num = 21;
    optional uint32 stripe_size = 22;
}

message UpdateISCSILunRequest {
    required LunPath lun_path = 1;

    optional uint64 size = 3;
    optional bytes description = 5 [(zbs.labels).as_str = true];

    optional uint32 replica_num = 12;
    optional bool thin_provision = 13;
    optional uint64 iops = 15 [deprecated=true];
    optional uint64 iops_burst = 16 [deprecated=true];
    optional uint64 bps = 17 [deprecated=true];
    optional uint64 bps_burst = 18 [deprecated=true];
    optional IOThrottleConfig throttling = 19;

    optional bytes new_name = 31 [(zbs.labels).as_str = true];
    optional bool new_alloc_even = 32;
    optional bytes new_allowed_initiators = 33 [(zbs.labels).as_str = true];
    optional bool skip_all_zero_first_write = 34 [default = false];
    optional uint32 prefer_cid = 35 [default = 0];

    optional bool single_access = 60 [default = false];
    optional uint32 queue_num = 61;
    optional bool read_only = 62;
    optional bool prioritized = 63 [default = false];
    optional uint64 chunk_instances_bits = 64 [default = 0];
}

message AddLunAllowedInitiatorsRequest {
    required LunPath lun_path = 1;
    required bytes new_allowed_initiators = 2 [(zbs.labels).as_str = true];
}

message RemoveLunAllowedInitiatorsRequest {
    required LunPath lun_path = 1;
    required bytes initiators = 2 [(zbs.labels).as_str = true];
}

message ResetLunPrRequest {
    required LunPath lun_path = 1;
}

message MoveISCSILunRequest {
    required LunPath lun_path = 1;
    required LunPath src_lun_path = 2;
}

message ISCSILunsResponse {
    repeated ISCSILun luns = 2;
    optional uint32 total_num = 10;
}

message ISCSISpc2ReserveRequest {
    required LunPath lun_path = 1;
    required uint64 reserve_id = 2;
}

message ISCSISpc2ReleaseRequest {
    required LunPath lun_path = 1;
    required uint64 reserve_id = 2;
    required bool force = 3;
}

message ISCSISnapshotPath {
    optional LunPath lun_path = 1;
    optional bytes snapshot_name = 2 [(zbs.labels).as_str = true];

    optional bytes snapshot_id = 5 [(zbs.labels).as_str = true];
    optional bytes secondary_id = 6 [(zbs.labels).as_str = true];
}

message DeleteISCSISnapshotRequest {
    optional LunPath lun_path = 1;
    optional bytes snapshot_name = 2 [ (zbs.labels).as_str = true ];

    optional bytes snapshot_id = 5 [ (zbs.labels).as_str = true ];
    optional bytes secondary_id = 6 [ (zbs.labels).as_str = true ];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 7 [ default = false ];
}

message CreateISCSISnapshotRequest {
    required LunPath lun_path = 1 [(zbs.labels).as_str = true];
    required bytes snapshot_name = 2 [(zbs.labels).as_str = true];

    optional bytes snapshot_desc = 3 [default = "", (zbs.labels).as_str = true];
    optional bytes secondary_id = 4 [default = "", (zbs.labels).as_str = true];
}

message UpdateISCSISnapshotRequest {
    required ISCSISnapshotPath snapshot_path = 1;

    optional bytes new_name = 10 [(zbs.labels).as_str = true];
    optional bytes new_description = 11 [(zbs.labels).as_str = true];
    optional bool new_alloc_even = 12;
}

message MoveISCSISnapshotRequest {
    required ISCSISnapshotPath snapshot_path = 1;
    required PoolPath dst_target = 2;
}

message TargetAndLunInfo {
    required ISCSITarget target = 1;
    required ISCSILun lun = 2;
}

message ListTargetPortalsRequest {
    required bytes iqn_name = 1 [(zbs.labels).as_str = true];
    optional uint32 source_ip = 2; // report portals for given source IP
}

message PRReg {
    optional uint64 key = 1;         // scsi specification defined the key
    optional uint32 pr_scope = 2;    // only need 8bit
    optional uint32 pr_type = 3;     // only need 8bit
    optional bytes it_nexus = 4 [(zbs.labels).as_str = true];     // this is a session link identifier
}

message PRInfo {
    optional uint32 gen = 11 [default = 0];    // scsi pr generation
    repeated PRReg regs = 13;                  // registrants per lun
    optional bytes pr_holder = 14 [(zbs.labels).as_str = true];             // scsi-3 reservation
    optional bytes scsi2_pr_holder = 15 [(zbs.labels).as_str = true];       // scsi-2 reservation
}

message PRRequest {
    required LunPath lun_path = 1;      // lun identifier
    required PRInfo old_pr_info = 2;    // the current pr information
    required PRInfo new_pr_info = 3;    // the next pr information
}

message PRResponse {
    required LunPath lun_path = 1;       // lun identifier
    optional PRInfo meta_pr_info = 2;    // the record of meta pr information
}

message TargetPortals {
    repeated bytes portal = 1 [(zbs.labels).as_str = true]; // "IP:Port,TPGT"
}

// deprecated v1 snapshot
message SnapshotV1 {
    required bytes name = 1 [(zbs.labels).as_str = true];  // < 64B
    required uint64 size = 2; //B
    required uint64 diff_size = 3; //B
    optional uint32 volume_id = 4;
    optional TimeSpec created_time = 5;
    optional bytes description = 6 [default = "", (zbs.labels).as_str = true];  // 256B
    repeated uint32 pextents = 7;  // pextents in the snapshot
    optional NFSAttr nfs_meta = 8; // snapshot for nfs volatile meta info
}

enum ConsistencyVolumeType {
    CONSISTENCY_NONE = 0;
    CONSISTENCY_VOLUME = 1;
    CONSISTENCY_LUN = 2;
    CONSISTENCY_FILE = 3;
    CONSISTENCY_NAMESPACE = 4;
}

message ConsistencyVolume {
    required bytes id = 1 [(zbs.labels).as_str = true];
    required bytes pool_id = 2 [(zbs.labels).as_str = true];
    required ConsistencyVolumeType type = 3;
    extensions 10000 to max;  // for specified volume attribute
}

message ConsistencyFile {
    extend ConsistencyVolume {
        optional ConsistencyFile volume = 10001;
    }
    required bytes inode_id = 1 [(zbs.labels).as_str = true];
}

message ConsistencyLUN {
    extend ConsistencyVolume {
        optional ConsistencyLUN volume = 10002;
    }
    required uint32 lun_id = 1;
}

message ConsistencyNamespace {
    extend ConsistencyVolume {
        optional ConsistencyNamespace volume = 10003;
    }
    required uint32 ns_id = 1;
}

message ConsistencyGroup {
    required bytes id = 1 [(zbs.labels).as_str = true];
    optional bytes name = 2 [(zbs.labels).as_str = true];
    optional bytes description = 3 [(zbs.labels).as_str = true];
    repeated ConsistencyVolume volumes = 4;
    optional TimeSpec created_time = 5;
    optional int64 logical_size_bytes = 6 [default = -1];
    optional int64 physical_size_bytes = 7 [default = -1]; // cap layer
    optional int64 perf_physical_size_bytes = 8 [default = -1]; // perf layer
}

message ListConsistencyGroupRequest {}

message ListConsistencyGroupResponse {
    repeated ConsistencyGroup groups = 1;
    optional uint32 total_num = 2;
}

message ShowConsistencyGroupRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
}

message CreateConsistencyGroupRequest {
    required bytes name = 1 [(zbs.labels).as_str = true];
    optional bytes description = 2 [(zbs.labels).as_str = true];
    repeated ConsistencyVolume volumes = 3;
}

message DeleteConsistencyGroupRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    optional bool remain_volume_snapshot = 2 [default = false];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 3 [ default = false ];
}

message UpdateConsistencyGroupRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    optional bytes new_name = 2 [(zbs.labels).as_str = true];
    optional bytes new_description = 3 [(zbs.labels).as_str = true];
    repeated ConsistencyVolume add_volumes = 4;
    repeated ConsistencyVolume remove_volumes = 5;
    optional bool remain_volume_snapshot = 6 [default = false];
}

message ConsistencyVolumeSnapshot {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    required bytes snapshot_id = 2 [(zbs.labels).as_str = true];
    required bytes snapshot_pool_id = 3 [(zbs.labels).as_str = true];
}

message ConsistencyGroupSnapshot {
    required bytes id = 1 [(zbs.labels).as_str = true];
    required bytes group_id = 2 [(zbs.labels).as_str = true];
    optional bytes name = 3 [(zbs.labels).as_str = true];
    optional bytes description = 4 [(zbs.labels).as_str = true];
    optional TimeSpec created_time = 5;
    repeated ConsistencyVolumeSnapshot snapshots = 6;
    optional int64 logical_size_bytes = 7 [default = -1];
    optional int64 physical_size_bytes = 8 [default = -1]; // cap layer
    optional int64 perf_physical_size_bytes = 9 [default = -1]; // perf layer
}

message CreateConsistencyGroupSnapshotRequest {
    required bytes group_id = 1 [(zbs.labels).as_str = true];
    optional bytes name = 2 [(zbs.labels).as_str = true];
    optional bytes description = 3 [(zbs.labels).as_str = true];
}

message UpdateConsistencyGroupSnapshotRequest {
    required bytes snapshot_id = 1 [(zbs.labels).as_str = true];
    optional bytes new_name = 2 [(zbs.labels).as_str = true];
    optional bytes new_description = 3 [(zbs.labels).as_str = true];
}

message ListConsistencyGroupSnapshotRequest {
    required bytes group_id = 1 [(zbs.labels).as_str = true];
}

message ListConsistencyGroupSnapshotResponse {
    repeated ConsistencyGroupSnapshot snapshots = 1;
}

message ShowConsistencyGroupSnapshotRequest {
    required bytes snapshot_id = 1 [(zbs.labels).as_str = true];
}

message RollbackConsistencyGroupSnapshotRequest {
    required bytes snapshot_id = 1 [(zbs.labels).as_str = true];
}

message DeleteConsistencyGroupSnapshotRequest {
    required bytes snapshot_id = 1 [(zbs.labels).as_str = true];
    optional bool remain_volume_snapshot = 2 [default = false];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 3 [ default = false ];
}

message CreateSnapshotRequest {
    required SnapshotPath path = 1;
    optional bytes description = 21 [default = "", (zbs.labels).as_str = true];  // description for this snapshot
    optional NFSAttr nfs_meta = 22; // right now nfs inode and volume are
                                    // seperated so put attr as an arg
}

message UpdateSnapshotRequest {
    required SnapshotPath path = 1;

    optional bytes new_name = 21 [(zbs.labels).as_str = true];
    optional bytes new_description = 22 [(zbs.labels).as_str = true];
    optional bool new_alloc_even = 23 [default = false];
}

message SnapshotsResponse {
    repeated Volume snapshots = 1;
}

message MoveSnapshotRequest {
    required SnapshotPath path = 1;
    required PoolPath dst_pool_path= 2;
}

message CloneVolumeRequest {
    required SnapshotPath snapshot = 1;
    required CreateVolumeRequest volume_request = 2;
}

message GetExtentRequest {
    required uint32 extent_id = 1;
    optional bool only_lextent = 2 [default = false];
}

message GetLExtentRequest {
    required uint32 lid = 1;
}

message FindPExtentRequest {
    required PExtentStatus pextent_status = 1;
    optional uint32 start = 2 [ default = 1 ];
    optional uint32 length = 3 [ default = 1024 ];
}

message ExtentRef {
    required bytes pool_name = 1 [(zbs.labels).as_str = true];
    required bytes volume_name = 2 [(zbs.labels).as_str = true];
    optional bytes snapshot_name = 3 [(zbs.labels).as_str = true];
    optional bytes volume_id = 4 [(zbs.labels).as_str = true];
    optional bool volume_is_snapshot = 5 [default = false];
    optional bytes volume_snapshot_pool_id = 6 [(zbs.labels).as_str = true];
    optional int64 offset = 7 [default = -1]; // pextent index offset in volume
}

message GetExtentRefResponse {
    repeated ExtentRef refs = 2;
    optional LExtent lextent = 3;
}

message CowPExtentRequest {
    required bytes session = 1 [(zbs.labels).as_str = true];
    required bytes volume_id = 2 [(zbs.labels).as_str = true];
    required uint32 vextent_no = 3;
}

message GetAllocPExtentRequest {  // deprecated
    required uint32 chunk_id = 1;
    required uint32 pid = 2;
    required bool alloc = 3;
}

message RemoveReplicaRequest {
    required bytes session = 1 [(zbs.labels).as_str = true];  // the chunk sending this request
    required uint32 pid = 2;
    repeated uint32 replicas = 3;
    optional uint64 generation = 4;  // replica gen: if set, the meta recorded gen will be updated to this value
    optional uint64 epoch = 5;
    optional bool alloc_temporary_replica = 6 [default = false];
    repeated TemporaryReplica remove_temporary_replicas = 7;
    optional bool removing_slow = 8 [default = false]; // do not remove replica if no healthy chunk to recover extent.
}

message AddReplicaRequest {
    required bytes session = 1 [(zbs.labels).as_str = true];  // the chunk sending this request
    required uint32 pid = 2;
    required uint32 replica_chunk = 3;  // replica to be added
    optional uint64 epoch = 4;
}

message ReplaceReplicaRequest {
    required bytes session = 1 [(zbs.labels).as_str = true];
    required uint32 pid = 2;
    required uint32 src_chunk = 3;
    required uint32 dst_chunk = 4;
    optional uint64 epoch = 5;
    optional bool reset_location_to_dst = 6 [default = false];
    optional uint64 reset_generation = 7;
    optional int32 segment_idx = 8 [default = -1];
}

message ChunksResponse {
    repeated Chunk chunks = 2;
}

message ObjectVersion {
    // lextent version 1 for ZBS version <= v5.5.x
    //                 2 for ZBS verison >= 5.6.0, tiering
    optional uint32 lextent_version = 1;
}

message ClusterInfo {
    optional bytes uuid = 1 [(zbs.labels).as_str = true];
    optional bytes name = 2 [(zbs.labels).as_str = true];
    optional bytes desc = 3 [(zbs.labels).as_str = true];
    optional bool is_stretched = 4 [default = false];
    optional bytes preferred_zone_id = 5 [default = "", (zbs.labels).as_str = true];
    optional bool zk_uuid_recorded = 6 [default = false];
    optional NegotiatedConfig negotiated_config = 7;
    optional uint32 default_prio_space_ratio = 8 [default = 0];
    optional ObjectVersion object_version = 9;
    optional bool no_performance_layer = 10 [default = false];
    optional bool replica_capacity_only = 11 [default = false];
    optional bool access_write_compress_enabled = 12 [default = false];
    repeated EncryptMethod encrypt_support_array = 13;
    optional bool internal_kms_support = 14 [default = false];
    optional RecycleBinConfig recycle_bin_config = 15;
}

message ZoneSummary {
    optional bytes id = 1 [(zbs.labels).as_str = true];
    optional StorageSpace space_info = 2;
    optional StoragePerf read_perf             = 3 [deprecated = true];
    optional StoragePerf write_perf            = 4 [deprecated = true];
    optional StoragePerf total_perf            = 5 [deprecated = true];
    optional StoragePerf cross_zone_read_perf  = 6 [deprecated = true];
    optional StoragePerf cross_zone_write_perf = 7 [deprecated = true];
    optional RecoverSummary recover_info = 8;
    optional bytes name = 9 [(zbs.labels).as_str = true];
    optional ChunkPerf chunk_perf              = 10;
}

message ClusterSummary {
    optional uint64 valid_data_space = 3 [default = 0];
    optional uint64 provisioned_data_space = 4 [default = 0];
    optional uint64 used_data_space = 5 [default = 0];
    optional uint64 total_data_capacity = 70 [default = 0];
    optional uint64 failure_data_space = 71 [default = 0];
    optional uint64 allocated_data_space = 74 [default = 0];

    optional uint64 perf_valid_data_space     = 76 [default = 0];
    optional uint64 perf_used_data_space      = 77 [default = 0];
    optional uint64 perf_total_data_capacity  = 78 [default = 0];
    optional uint64 perf_failure_data_space   = 79 [default = 0];
    optional uint64 perf_allocated_data_space = 80 [default = 0];
    optional uint64 perf_planned_space        = 81 [default = 0];

    optional uint64 valid_cache_space = 8 [default = 0];
    optional uint64 valid_free_cache_space = 82 [default = 0];
    optional uint64 used_cache_space = 9 [default = 0];
    optional uint64 dirty_cache_space = 10 [default = 0];
    optional uint64 failure_cache_space = 72 [default = 0];
    optional uint64 total_cache_capacity = 73 [default = 0];
    optional uint64 total_prio_volume_size = 75 [default = 0];

    optional uint32 total_nodes = 11 [default = 0];
    optional uint32 healthy_nodes = 12 [default = 0];
    optional uint32 connecting_nodes = 13 [default = 0];  // nodes are being connecting
    optional uint32 warning_nodes = 14 [default = 0];
    optional uint32 error_nodes = 15 [default = 0];
    optional uint32 removing_nodes = 16 [default = 0];   // nodes are being removed
    optional uint32 idle_nodes = 17 [default = 0];   // nodes idle
    optional uint32 in_use_nodes = 18 [default = 0];   // nodes in used

    optional uint32 total_chunks = 43 [default = 0];
    optional uint32 healthy_chunks = 44 [default = 0];
    optional uint32 connecting_chunks = 45 [default = 0];  // chunks are being connecting
    optional uint32 warning_chunks = 46 [default = 0];
    optional uint32 error_chunks = 47 [default = 0];
    optional uint32 removing_chunks = 48 [default = 0];   // chunks are being removed
    optional uint32 idle_chunks = 49 [default = 0];   // chunks idle
    optional uint32 in_use_chunks = 50 [default = 0];   // chunks in used

    optional string leader = 22;  // meta leader known by this node.
    repeated Address alive_meta_hosts = 23;

    optional ClusterInfo cluster_info = 24;

    optional bool migrate_enabled = 31;
    optional bool recover_enabled = 32;
    optional uint64 upgrade_mode_duration = 33; // second

    optional uint32 alloced_pids = 41;
    optional uint32 alloced_lids = 42;
    optional string serial = 101;

    optional uint64 recover_migrate_speed = 51 [default = 0, deprecated = true];
    optional uint32 num_ongoing_recovers = 52 [default = 0, deprecated = true];
    optional uint32 num_pending_recovers = 53 [default = 0, deprecated = true];
    optional uint32 num_ongoing_migrates = 54 [default = 0, deprecated = true];
    optional uint32 num_pending_migrates = 55 [default = 0, deprecated = true];
    optional uint32 num_pending_recycles = 56 [default = 0, deprecated = true];
    optional uint64 recover_speed = 57 [default = 0, deprecated = true];
    optional uint64 migrate_speed = 58 [default = 0, deprecated = true];

    optional RecoverSummary recover_info = 59;

    repeated StoragePool storage_pools = 61;
    repeated Chunk unallocated_chunks = 62;
    optional StorageSpace space_info = 63;
    repeated ZoneSummary zone_infos = 64;

    optional ChunkPerf cluster_perf = 65;
    optional DataReductionInfo data_reduction_info = 66;
    optional OverprovisionInfo overprovision_info = 67;

    optional bool enable_encryption = 68;
    optional bool exist_encrypted_objects = 69;

    optional RecycleBinSizeInfo recycle_bin_size_info = 83;
}

enum TracePolicy {
    DISABLE_TRACE = 0;
    ENABLE_TRACE = 1;
    ADAPTIVE_TRACE = 2;
}

message PerfConfig {
    optional bool enable_metric = 1 [default = true];
    optional bool enable_trace = 2 [default = false, deprecated = true];
    optional TracePolicy trace_policy = 3 [default = ADAPTIVE_TRACE];
}

enum MetaStatus {
    META_BOOTING = 1;
    META_RUNNING = 2;
    META_STOPPED = 3;

    META_CONNECTING_CHUNK = 10;
    META_SCANNING_VOLUME = 11;  // deprecated
}

message SerializedMetaInfo {
    optional MetaStatus meta_status = 1;
}

message MetaSummary {
    optional MetaStatus meta_status = 2;
    optional bool is_leader = 3;
    optional string leader = 4;
    repeated Address alive_meta_hosts = 5;
}

message ClusterPerf {
    // deprecated soon
    optional float iops = 2;  // io per second
    optional float bandwidth = 3;  // Bytes per second
    optional float latency = 4;  // nano seconds

    optional StoragePerf total = 11;
    optional StoragePerf read = 12;
    optional StoragePerf write = 13;
    optional StoragePerf cross_zone_read = 14;
    optional StoragePerf cross_zone_write = 15;

    optional ChunkPerf chunk_perf = 16;
}

message ListDbResponse {
    optional VersionInfo version = 1;
    required bytes pool_name = 2 [(zbs.labels).as_str = true];
    repeated string db_name = 3 [(zbs.labels).as_str = true];
}

message DumpDbRequest {
    required string db_name = 1;
    required Pagination pagination = 2;
}

message DbItem {
    required bytes key = 1 [(zbs.labels).as_str = true];
    required bytes value = 2 [(zbs.labels).as_str = true];
}

message DumpDbResponse {
    required int64 op_seq = 2;
    repeated DbItem items = 3;
}

message CreateConsistenceMetaDBSnapshotRequest {
    required string addr = 1; // <ip:port> of caller
    required bool refresh_snapshot = 2 [default = false];
}

message CreateConsistenceMetaDBSnapshotResponse {
    required string consistence_snapshot_id = 1;
    required bool new_created = 2 [default = false];
}

message DumpMetaDBSnapshotRequest {
    required string db_name = 1;
    required string consistence_snapshot_id = 2;
    optional Pagination pagination = 3;
}

message DumpMetaDBSnapshotResponse {
    required int64 op_seq = 1;
    repeated DbItem items = 2;
}

message SwitchRequest {
    required bool enable = 1;
}

message SwitchTraceRequest {
    required TracePolicy trace_policy = 1;
}

message RecoverCmds {
    repeated RecoverCmd cmds = 2;
}

message DrainCmds {
    repeated DrainCmd cmds = 1;
}

message KeyRequest {
    required string key = 1;
    optional string value = 2;
}

message KeyResponse {
    optional string value = 2;
}

message NFSInode {
    optional uint32 id_v1 = 1 [deprecated=true];
    optional uint32 parent_id_v1 = 2 [deprecated=true];
    optional bytes id = 6 [(zbs.labels).as_str = true];
    optional bytes parent_id = 7 [(zbs.labels).as_str = true];
    optional bytes name = 3 [(zbs.labels).as_str = true];
    optional uint32 pool_id_v1 = 4 [deprecated=true];
    optional bytes pool_id = 5 [(zbs.labels).as_str = true];  // the pool id corresponding to the export

    optional NFSAttr attr = 10;

    optional bool preallocate = 100 [deprecated=true];
    optional uint32 volume_id_v1 = 101 [default = 0, deprecated = true];
    optional bytes volume_id = 102 [(zbs.labels).as_str = true];  // volume id in zbs. used when it is a file
    // used size. used when it is a file
    // capacity layer
    optional int64 unique_size = 103 [default = -1];
    optional int64 shared_size = 104 [default = -1];
    // performance layer
    optional int64 perf_unique_size = 109 [default = -1];
    optional int64 perf_shared_size = 110 [default = -1];

    optional int64 logical_used_size = 111 [default = -1];

    optional bool prioritized  = 105 [default = false];
    optional uint64 downgraded_prioritized_space = 106 [default = 0];  // Bytes

    optional ResiliencyType resiliency_type = 107 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 108;
    optional EncryptMethod encrypt_method = 129 [default = ENCRYPT_PLAIN_TEXT];

    optional bytes createverf3 = 200 [(zbs.labels).as_str = true];  // to store EXCLUSIVE create verifier
    optional uint32 xid = 201; // to store xid as a creation verifier
                               // note there is a low possibility that two
                               // requests from different clients have the same
                               // xid. However since a) possibility is very low
                               // b) hard to differentiate the case where nfs
                               // server was reset and request retried, so just
                               // take xid here
    optional bytes symlink_path = 400 [(zbs.labels).as_str = true];   // used for symlink path
}

message NFSInodes {
    repeated NFSInode inodes = 1;
}

message InodeId {
    required bytes id = 1 [(zbs.labels).as_str = true];
}

message MountPoint {
    required bytes client   = 1 [(zbs.labels).as_str = true];
    required bytes dir      = 2 [(zbs.labels).as_str = true];
}

message MountTable {
    repeated MountPoint entries    = 1;
}

enum time_how {
    DONT_CHANGE        = 0;
    SET_TO_SERVER_TIME = 1;
    SET_TO_CLIENT_TIME = 2;
}

message SAttr3 {
    optional uint32 mode = 11;
    optional uint32 uid = 12;
    optional uint32 gid = 13;
    optional uint64 size = 14;
    optional time_how atime_how = 15 [default = DONT_CHANGE];
    optional TimeSpec atime = 16;
    optional time_how mtime_how = 17 [default = DONT_CHANGE];
    optional TimeSpec mtime = 18;
    optional time_how ctime_how = 19 [default = DONT_CHANGE];
    optional TimeSpec ctime = 20;
}

message SetAttrRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    required SAttr3 sattr3 = 2;
    optional TimeSpec guard = 3;
    optional uint32 xid = 4;
}

message BatchedSetAttrRequest {
    repeated SetAttrRequest requests = 1;
}

message BatchedSetAttrResponse {
    repeated bytes success_ids = 1 [(zbs.labels).as_str = true];
    repeated bytes removed_ids = 2 [(zbs.labels).as_str = true];
    repeated bytes failed_ids = 3 [(zbs.labels).as_str = true];
}

enum CreateHow {
    UNCHECKED = 0;
    GUARDED = 1;
    EXCLUSIVE = 2;
}

message CreateInodeRequest {
    required bytes parent_id = 1 [(zbs.labels).as_str = true];
    required bytes name = 2 [(zbs.labels).as_str = true];
    required NFSType type = 3;

    optional CreateHow how = 11 [default = UNCHECKED];
    optional SAttr3 sattr3 = 12;  // used for GUARD and UNCHECKED
    optional bytes createverf3 = 13 [(zbs.labels).as_str = true];  // create verifier
    optional uint32 xid = 14;  // internal creation verifier

    optional bytes src_inode_id = 21 [(zbs.labels).as_str = true];  // clone from another inode
    optional bytes src_snapshot_id = 22 [(zbs.labels).as_str = true];  // clone from a snapshot

    optional bool preallocate = 30;

    optional bytes symlink_path = 40 [(zbs.labels).as_str = true];
}

// Delete an inode or a hardlink
message DeleteInodeRequest {
    required bytes parent_id = 1 [(zbs.labels).as_str = true];
    optional bool recursive = 2 [default = false];
    required bytes name = 3 [(zbs.labels).as_str = true];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 4 [ default = false ];
}

message DeleteInodeResponse {
    optional bytes inode_id = 1 [(zbs.labels).as_str = true]; // inode_id that has been deleted
}

message ListInodeRequest {
    required bytes id  = 1 [(zbs.labels).as_str = true];
    optional Pagination pagination = 2;
}

message TruncateRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    required uint64 size = 2;

    // only change the file size when it is increasing
    optional bool increase_only = 3 [default = false];
}

message LookupInodeRequest {
    required bytes dir_id = 1 [(zbs.labels).as_str = true];
    required bytes name = 2 [(zbs.labels).as_str = true];
}

message FindInodeRequest {
    required bytes path = 1 [(zbs.labels).as_str = true];  // e.g. /export/dir1/dir2/file1
}

message RenameInodeRequest {
    required bytes from_parent_id = 1 [(zbs.labels).as_str = true];
    required bytes from_name = 2 [(zbs.labels).as_str = true];
    required bytes to_parent_id = 3 [(zbs.labels).as_str = true];
    required bytes to_name = 4 [(zbs.labels).as_str = true];
    optional uint32 xid = 5; // operation verifier
}

message RenameInodeResponse {
    required NFSInode inode = 1;
    optional bytes deleted_inode_id = 2 [(zbs.labels).as_str = true]; // inode_id that has been deleted if any
}

// move a file inode between two exports
message MoveFileRequest {
    required bytes parent_id = 1 [(zbs.labels).as_str = true];
    required bytes name = 2 [(zbs.labels).as_str = true];
    required bytes src_inode_path = 3 [(zbs.labels).as_str = true];
}

message ConvertLunIntoFileRequest {
    required LunPath lun_path = 1;
    required bytes parent_id = 2 [(zbs.labels).as_str = true];
    required bytes name = 3 [(zbs.labels).as_str = true];
    optional SAttr3 sattr3 = 4;
}

message ConvertVolumeIntoFileRequest {
    required VolumePath volume_path = 1;
    required bytes parent_id = 2 [(zbs.labels).as_str = true];
    required bytes name = 3 [(zbs.labels).as_str = true];
    optional SAttr3 sattr3 = 4;
}

message SizeInfo {
    optional uint64 size = 1;
    optional uint64 volume_size = 2 [default = 0];
}

message NFSSnapshotPath {
    optional bytes inode_id = 1 [(zbs.labels).as_str = true];
    optional bytes snapshot_name = 2 [(zbs.labels).as_str = true];

    optional bytes export_name = 5 [(zbs.labels).as_str = true];  // for list operation only
    optional bytes snapshot_id = 6 [(zbs.labels).as_str = true];
}

message DeleteNFSSnapshotRequest {
    optional bytes inode_id = 1 [(zbs.labels).as_str = true];
    optional bytes snapshot_name = 2 [(zbs.labels).as_str = true];

    optional bytes export_name = 5 [(zbs.labels).as_str = true];  // for list operation only
    optional bytes snapshot_id = 6 [(zbs.labels).as_str = true];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 7 [ default = false ];
}

message CreateNFSSnapshotRequest {
    required bytes inode_id = 1 [(zbs.labels).as_str = true];
    required bytes snapshot_name = 2 [(zbs.labels).as_str = true];
    optional bytes snapshot_desc = 3 [default = "", (zbs.labels).as_str = true];
}

message UpdateNFSSnapshotRequest {
    required NFSSnapshotPath snapshot_path = 1;
    optional bytes new_name = 2 [(zbs.labels).as_str = true];
    optional bytes new_description = 3 [default = "", (zbs.labels).as_str = true];
    optional bool new_alloc_even = 4;
}

message UpdateNFSFileRequest {
    required bytes inode_id = 1 [(zbs.labels).as_str = true];
    optional bool skip_all_zero_first_write = 2 [default = false];
    optional bool alloc_even = 3;
    optional uint32 replica_num = 4;
    optional bool read_only = 5;
    optional bool prioritized = 6 [default = false];
}

message RollbackNFSSnapshotRequest {
    required bytes inode_id = 1 [(zbs.labels).as_str = true];
    optional bytes snapshot_name = 2 [(zbs.labels).as_str = true];
    optional bytes snapshot_id = 3 [(zbs.labels).as_str = true];
}

message MoveNFSSnapshotRequest {
    required NFSSnapshotPath snapshot_path = 1;
    required bytes dst_export_name = 2 [(zbs.labels).as_str = true];
}

message CreateHardlinkRequest {
    required bytes parent_id = 1 [(zbs.labels).as_str = true];
    required bytes name = 2 [(zbs.labels).as_str = true];
    required bytes target_id = 3 [(zbs.labels).as_str = true];
    optional uint32 xid = 13;  // creation verifier
}

message CreateTopoObjRequest {
    required TopoType type = 1;
    optional bytes parent_id = 2 [default="topo", (zbs.labels).as_str = true];
    optional bytes name = 3 [(zbs.labels).as_str = true];  // name of the rack, not unique
    optional bytes desc = 4 [(zbs.labels).as_str = true];

    optional Position position = 12;
    optional Capacity capacity = 13;
    optional Dimension dimension = 14;
}

message UpdateTopoObjRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    optional bytes new_parent_id = 2 [(zbs.labels).as_str = true];
    optional bytes new_name = 3 [(zbs.labels).as_str = true];  // new name
    optional bytes new_desc = 4 [(zbs.labels).as_str = true];

    optional Position new_position = 12;
    optional Capacity new_capacity = 13;
    optional Dimension new_dimension = 14;
    optional uint64 new_ring_id = 15 [default = 0];
}

message CreateStoragePoolRequest {
    optional bytes name = 1 [(zbs.labels).as_str = true];

    // the chunks to be included in the storage pool
    repeated uint32 chunk_ids = 2;
}

message UpdateStoragePoolRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    optional bytes new_name = 2 [(zbs.labels).as_str = true];
}

message AddChunkToStoragePoolRequest {
    required bytes storage_pool_id = 1 [(zbs.labels).as_str = true];
    required uint32 chunk_id = 2;
}

message RemoveChunkFromStoragePoolRequest {
    required bytes storage_pool_id = 1 [(zbs.labels).as_str = true];
    required uint32 chunk_id = 2;
    optional bool skip_capacity_check = 3 [default = false];
}

message CancelChunkRemovingRequest {
    required uint32 chunk_id = 1;
}

message MoveDataAndDeleteVolumeRequest {
    required VolumePath src_volume = 1;
    required VolumePath dst_volume = 2;
}

message RevokeClientCmd {
    repeated bytes client_ips = 1 [(zbs.labels).as_str = true];
}

message ISCSIConnection {
    required bytes initiator = 1 [(zbs.labels).as_str = true];
    required bytes initiator_ip = 2 [(zbs.labels).as_str = true];
    required bytes target_id = 3 [(zbs.labels).as_str = true];
    required uint32 client_port = 4;
    optional bytes target_addr = 5 [(zbs.labels).as_str = true];
    optional bytes target_name = 6 [(zbs.labels).as_str = true];
    optional uint64 survival_time = 7;
    optional bytes tr_type = 8 [(zbs.labels).as_str = true];
}

message ISCSIConnections {
    repeated ISCSIConnection conns = 1;
}

message ISCSIConnectionRecord {
    required ISCSIConnection conn = 1;
    required bytes session_id = 2 [(zbs.labels).as_str = true];
    optional uint32 cid = 3;
}

message ListISCSIConnectionRequest {
    optional bytes session_id = 1 [(zbs.labels).as_str = true];
}

message ListISCSIConnectionResponse {
    repeated ISCSIConnectionRecord records = 1;
}

message ElevatingRecord {
    optional string volume_id = 1;
    repeated uint32 vextent_no = 2;
}

message AccessKeepAliveRequest {
    extend consensus.KeepAliveRequest {
        optional AccessKeepAliveRequest request = 10001;
    }
    optional StoragePerf read_perf             = 1 [deprecated = true];  // moved to AccessDataReportRequest
    optional StoragePerf write_perf            = 2 [deprecated = true];  // moved to AccessDataReportRequest
    optional StoragePerf total_perf            = 7 [deprecated = true];  // moved to AccessDataReportRequest
    optional StoragePerf cross_zone_read_perf  = 8 [deprecated = true];  // moved to AccessDataReportRequest
    optional StoragePerf cross_zone_write_perf = 9 [deprecated = true];  // moved to AccessDataReportRequest

    optional uint64 recover_migrate_speed            = 3 [deprecated = true];  // moved to AccessDataReportRequest
    repeated uint32 in_recover_pids                  = 4 [deprecated = true];
    optional uint64 recover_speed                    = 5 [deprecated = true];   // moved to AccessDataReportRequest
    optional uint64 migrate_speed                    = 6 [deprecated = true];   // moved to AccessDataReportRequest
    optional uint64 cross_zone_recover_speed         = 10 [deprecated = true];  // moved to AccessDataReportRequest
    optional uint64 cross_zone_migrate_speed         = 11 [deprecated = true];  // moved to AccessDataReportRequest
    optional uint64 cross_zone_recover_migrate_speed = 12 [deprecated = true];  // moved to AccessDataReportRequest
    optional uint64 recover_speed_limit = 13 [deprecated = true];
    optional uint64 migrate_speed_limit = 14 [deprecated = true];
    optional ISCSIConnections iscsi_conns = 15;
    optional bool need_refresh_iscsi_config = 16;
    optional bool need_refresh_nvmf_config = 17;
    optional uint64 max_recover_speed_limit = 18 [deprecated = true];
    optional uint64 max_migrate_speed_limit = 19 [deprecated = true];
    optional bool enable_iscsi_config_push = 20;
    optional NVMFConnections nvmf_conns = 21;

    optional AccessPerf access_perf   = 22;
    optional RecoverPerf recover_perf = 23;

    optional bool nvmf_rdma_enabled = 24;
    optional bool nvmf_tcp_enabled  = 25;

    optional CDPJobUpdateDone cdp_job_updated = 26;
    optional ChunkConnectivity chunk_connectivity = 27;
    optional ChunkIsolateFlag real_isolate_status = 28;
    optional bool enable_unmap = 29;
    optional bool enable_thick_extent = 30;
    optional bool enable_temporary_replica = 31;
    optional bool enable_tiering = 32;

    optional AccessPerf access_cap_replica_app_perf = 33;
    optional AccessPerf access_perf_replica_app_perf = 34;
    optional AccessPerf access_cap_ec_app_perf = 35;

    // The timing for sinking perf extents in the perf chain and without the COW flag is determined by Access. However,
    // sinking perf extents of this type may violate the parent capacity read-only constraint. If Access obtains some
    // extents' vextent leases with the sinkable flag set to false, then Access can actively query the latest sinkable
    // flag of these extents.
    repeated uint32 potential_sinkable_lids = 36;

    optional uint64 cap_internal_io_speed_limit = 37;
    optional uint64 max_cap_internal_io_speed_limit = 38;

    optional uint64 perf_internal_io_speed_limit = 39;
    optional uint64 max_perf_internal_io_speed_limit = 40;

    repeated FailedDrainCmd failed_drain_cmds = 45;

    optional AccessPerf access_cap_replica_sink_perf = 46;
    optional AccessPerf access_perf_replica_sink_perf = 47;
    optional AccessPerf access_cap_ec_sink_perf = 48;

    optional bool enable_metric = 50  [deprecated = true];
    optional bool enable_trace = 51  [deprecated = true];

    optional AccessPerf access_cap_replica_reposition_perf = 52;
    optional AccessPerf access_perf_replica_reposition_perf = 53;
    optional AccessPerf access_cap_ec_reposition_perf = 54;

    optional bool enable_offload_unmap = 55;
    optional bool write_compress_enabled = 56 [default = false];

    optional ElevatingRecord elevating_record = 57;

    repeated RepositionConcurrencyInfo reposition_concurrency_infos = 58;

    optional bool enable_internal_flow_control = 59;
    optional bool enable_dc_multipath = 60;

    optional bool enable_auto_special_recover = 61;
    optional bool enable_encryption = 62;
}

message DropISCSIConnCmd {
    // ipv4:port
    repeated bytes conn_client_addrs = 1 [(zbs.labels).as_str = true];
}

enum ISCSIConfigOption {
    UNKNOWN = 0;
    TARGET_ADD = 1;
    TARGET_REMOVE = 2;
    TARGET_WHITELIST = 3;
    TARGET_IQN_WHITELIST_V2 = 4;
    TARGET_CHAP = 5;
    TARGET_INITIATOR_CHAP = 6;
    TARGET_NAME = 7;
    LUN_ADD = 21;
    LUN_REMOVE = 22;
    LUN_SIZE = 23;
    LUN_ALLOWED_INITIATORS = 24;
    LUN_PR_INFO = 25;
    LUN_SNAPSHOT_ROLLBACK = 26;
    LUN_THIN_PROVISION_CHANGE = 27;
    LUN_CHUNK_INSTANCES_UPDATE = 28;
}

message NegotiatedConfig {
    optional bool enable_config_push = 1 [default = false];
    optional bool enable_data_report_channel = 2 [default = false];
    optional bool deprecated__enable_thick_extent = 3 [default = false, deprecated = true];
    optional bool enable_unmap = 4 [default = false];
    optional bool chunk_zk_session = 5 [default = false];
    optional bool enable_temporary_replica = 6 [default = false];
    optional bool enable_thick_extent = 7 [default = false];
    optional bool enable_tiering = 8 [default = false];
    optional bool enable_offload_unmap = 9 [default = false];
    optional bool enable_lextent_lease = 10 [default = false];
    optional bool enable_internal_flow_control = 11 [default = false];
    optional bool enable_dc_multipath = 12 [default = false];
    optional bool enable_auto_special_recover = 13 [default = false];
    optional bool enable_encryption = 14 [default = false];
}

enum ConfigUpdateType {
    UNKNOWN_TYPE = 0;
    ISCSI = 1;
    NVMF = 2;
    VHOST = 3;
}

message ConfigUpdate {
    required ConfigUpdateType update_type = 1;
    extensions 10000 to max;  // for specified config update
}

message ISCSIConfigUpdate {
    extend ConfigUpdate {
        optional ISCSIConfigUpdate update = 10001;
    }
    required bytes target_id = 1 [(zbs.labels).as_str = true];
    required uint32 config_ver = 2;
    repeated ISCSIConfigOption op = 3;
    optional ISCSITarget target = 4;
    optional ISCSILun lun = 5;
}

message NVMFVolumeReset {
    required string host_nqn = 1;
    repeated bytes nvmf_volumes = 2 [(zbs.labels).as_str = true];
}

message ExtentPriorityChange {
    required uint32 pid = 1;
    required bool new_prioritized = 2;
}

message AccessKeepAliveResponse {
    extend consensus.KeepAliveResponse {
        optional AccessKeepAliveResponse response = 10001;
    }
    repeated RecoverCmd recover_cmd = 1;
    repeated RevokeCmd revoke_cmd = 2;
    optional RevokeClientCmd revoke_client_cmd = 3;
    repeated bytes volume_need_update = 4 [(zbs.labels).as_str = true];
    optional RecoverMode recover_mode = 5;
    optional MigrateMode migrate_mode = 6;
    optional uint64 static_cap_recover_speed_limit = 7 [deprecated = true];
    optional uint64 static_cap_migrate_speed_limit = 8 [deprecated = true];
    repeated PRResponse luns_need_update = 10;
    optional DropISCSIConnCmd conn_need_drop = 11;
    repeated ConfigUpdate config_updates = 12;
    repeated ISCSITarget all_iscsi_config = 13;
    repeated NVMFDistSubsystem all_nvmf_config = 14;
    optional NegotiatedConfig negotiated_config = 15;
    optional uint32 nvmf_target_id = 16;
    optional NVMFVolumeReset nvmf_volume_reset = 17;
    optional CDPJobUpdate cdp_updates = 18;
    repeated MaintenanceCmd maintenance_cmd = 19;
    optional ChunkIsolateInfo isolate_info = 20;
    repeated CleanChunkInfoCmd clean_chunk_info_cmd = 21;
    repeated RecoverCmd special_recover_cmd = 22;
    optional RepositionMode reposition_mode = 23;
    optional uint64 static_cap_reposition_concurrency_limit = 24;
    repeated DrainCmd drain_cmd = 25;
    repeated SinkablePair sinkable_pairs = 26;
    optional SinkCmd sink_cmd = 27;
    optional uint64 static_cap_internal_io_speed_limit = 28;
    optional uint64 static_perf_internal_io_speed_limit = 29;
    optional uint64 static_perf_reposition_concurrency_limit = 30;
    optional bool enable_metric = 31;
    optional bool enable_trace = 32;
    repeated ElevateCmd elevate_cmd = 33;
    optional InternalIOMode internal_io_mode = 34;
    optional InternalIOPriorities internal_io_priorities = 35;
    optional uint64 max_cap_reposition_concurrency_limit = 36;
    optional uint64 max_perf_reposition_concurrency_limit = 37;
    optional MPathInfo mpath_info = 38;
    repeated CancelRepositionCmd cancel_reposition_cmd = 39;
    optional TracePolicy trace_policy = 40;
}

message ChunkKeepAliveRequest {
    extend consensus.KeepAliveRequest {
        optional ChunkKeepAliveRequest request = 10002;
    }
    optional ChunkStatus status = 1 [default = CHUNK_STATUS_CONNECTED_HEALTHY];  // moved to ChunkDataReportRequest
    optional ChunkSpaceInfo space_info = 2;
    repeated PExtentInfo pextents_info = 3;  // moved to ChunkDataReportRequest
    optional StoragePerf read_perf  = 4 [deprecated = true];  // chunk local read perf, moved to ChunkDataReportRequest
    optional StoragePerf write_perf = 5 [deprecated = true];  // chunk local write perf, moved to ChunkDataReportRequest
    optional StoragePerf total_perf = 7 [deprecated = true];  // chunk local total perf, moved to ChunkDataReportRequest
    optional NodePerf node_perf     = 6 [deprecated = true];  // moved to ChunkDataReportRequest
    optional bytes host_name = 8 [(zbs.labels).as_str = true];
    optional LSMVersion lsm_version = 9;
    optional LSMPerf lsm_perf       = 10;
}

message ChunkKeepAliveResponse {
    extend consensus.KeepAliveResponse {
        optional ChunkKeepAliveResponse response = 10002;
    }
    repeated GcCmd gc_cmd = 1;
    repeated uint32 thick_pids = 2;
    optional uint64 reserve_space = 3;
    repeated uint32 thin_pids = 4;
    optional uint32 report_start_pid = 5;
    // the last report pid should be the biggest pid, not need to roll back to kPExtentIdStart
    optional uint32 max_report_size = 6;
    optional uint64 planned_perf_trs = 7;
    repeated ExtentPriorityChange extent_priority_changes = 8;

    optional uint32 pextents_info_struct_version = 9 [default = 0];
}

message ReleaseLeaseRequest {
    required bytes session_id = 1 [(zbs.labels).as_str = true];
    repeated uint32 pids = 2;
}

message ReleaseBadLeaseRequest {
    required uint32 pid = 1;
    required bytes my_session_id = 2 [(zbs.labels).as_str = true];
    repeated uint32 replica_cids = 3;
}

message LidPidPair {
    optional uint32 lid = 1;
    optional uint32 pid = 2;
    optional uint64 lepoch = 3;
    optional uint64 pepoch = 4;
}

message DeallocPerfExtentsRequest {
    required bytes session_id = 1 [(zbs.labels).as_str = true];
    repeated LidPidPair lid_pid_pairs = 2;
}

message DeallocCapExtentsRequest {
    required string session_id = 1;
    required string volume_id = 2;
    repeated LidPidPair lid_cap_pairs = 3;
}

message UpdateClusterInfoRequest {
    optional bytes new_name = 1 [(zbs.labels).as_str = true];
    optional bytes new_desc = 2 [(zbs.labels).as_str = true];
    optional bool is_stretched = 3;
    optional bytes preferred_zone_id = 4 [(zbs.labels).as_str = true];
    optional bool no_performance_layer = 5;
    optional bool replica_capacity_only = 6;
    optional bool enable_recycle_bin = 7;
    optional uint32 default_vol_expired_hours = 8;
}

message RerouteStatus {
    optional bool enable_reroute = 1;
    // whether secondary data channel could provide data
    // service when data channel was broken.
    optional bool enable_secondary_data_channel = 2;
}

message AbilityState {
    optional bool upgrade_thin_provision = 1 [default = false];
    optional bool upgrade_for_tiering    = 2 [default = false];
}

message ReportVolumeAccessRequest {
    repeated bytes volume_ids = 1 [(zbs.labels).as_str = true];
    optional uint32 cid = 2;
    optional bool force = 3 [default = false];
    optional bool include_cow = 4 [default = false];
}

message ChunkAccess {
    // The field cid is deprecated since introducing multi chunk instances mode,
    // this field will be filled with the first cid of the field cids.
    required uint32 cid = 1;
    required uint32 report_count = 2;
    required uint64 last_report_s = 3;
    optional string node_ip = 4;
    repeated uint32 cids = 5;
}

message AccessRecord {
    required uint32 report_count = 1;
    required uint64 last_report_s = 2;
    required bytes volume_id = 3 [ (zbs.labels).as_str = true ];
    repeated uint32 cids = 4;
}

message VolumeAccess {
    required uint32 cid = 1; // deprecated, this field will be filled with the cid in ListVolumeAccessRequest
    required uint32 record_num = 2;
    repeated AccessRecord records = 3;
    optional string node_ip = 4;
}

message ShowVolumeAccessRequest {
    required bytes volume_id = 1 [ (zbs.labels).as_str = true ];
}

message ShowVolumeAccessResponse {
    repeated ChunkAccess accesses = 1;
}

message ListVolumeAccessRequest {
    required uint32 cid = 1 [ default = 0 ];
    optional uint32 page_index = 2[ default = 0 ];
    optional uint32 page_size = 3 [ default = 1024 ];
}

message ListVolumeAccessResponse {
    required uint32 total_record_num = 1;
    repeated VolumeAccess accesses = 2;
}

message ListTargetsRequest {
    optional bool show_lun = 1 [default = false];
}

message ISCSITargetShortInfo {
    required bytes id = 1 [(zbs.labels).as_str = true]; // pool_id
    required uint32 config_ver = 2;
}

message SyncTargetsRequest {
    repeated ISCSITargetShortInfo targets = 1;
}

message ReserveVolumeSpaceRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
}

// for child vextent
message RefreshChildExtentLocationRequest {
    required bytes vtable_id = 1 [(zbs.labels).as_str = true];
    required uint32 vextent_no = 2;
    required bytes session_id =3 [(zbs.labels).as_str = true];
}

// for child lextent
message RefreshChildLExtentLocationRequest {
    optional uint32 lid = 1;
    optional bytes session_id = 2 [(zbs.labels).as_str = true];
}

message SetUpgradeModeRequest {
    required uint64 duration = 1; // second
}

message GetMetaLeaderDurationMSResponse {
    required uint64 duration_ms = 1;
}

message SessionRepositionLimit {
    required string session_id = 1;
    required string ip = 2;

    optional uint32 cid = 3;
    repeated RepositionConcurrencyInfo reposition_concurrency_infos = 4;
}

// deprecated
message SessionRecoverLimit {
    required bytes session_id = 1 [(zbs.labels).as_str = true];
    required bytes ip = 2 [(zbs.labels).as_str = true];

    required uint64 current_recover_speed_limit = 3;                      // MiB/s
    optional uint64 current_max_recover_speed_limit = 4;                  // MiB/s
}

// deprecated
message SessionMigrateLimit {
    required bytes session_id = 1 [(zbs.labels).as_str = true];
    required bytes ip = 2 [(zbs.labels).as_str = true];

    required uint64 current_migrate_speed_limit = 3;                      // MiB/s
    optional uint64 current_max_migrate_speed_limit = 4;                  // MiB/s
}

// deprecated
message RecoverModeInfo {
    required RecoverMode recover_mode = 1 [default = RECOVER_INVALID];
    optional uint64 static_recover_speed_limit = 2;
    repeated SessionRecoverLimit session_recover_limits = 3;
    optional bool enable_recover = 4;

    required MigrateMode migrate_mode = 5 [default = MIGRATE_INVALID];
    optional uint64 static_migrate_speed_limit = 6;
    repeated SessionMigrateLimit session_migrate_limits = 7;
    optional bool enable_migrate = 8;

    optional uint64 cluster_max_recover_speed_limit = 9;
    optional uint64 cluster_max_migrate_speed_limit = 10;
}

message UpdatableRepositionParams {
    // updatable params affecting both recover and migrate
    optional RepositionMode reposition_mode = 1 [default = REPOSITION_AUTO];

    optional uint64 static_generate_cmds_per_round_limit = 2;

    optional uint64 static_cap_distribute_cmds_per_chunk_limit = 3;
    optional uint64 static_cap_reposition_concurrency_limit = 4;

    optional uint64 static_perf_distribute_cmds_per_chunk_limit = 5;
    optional uint64 static_perf_reposition_concurrency_limit = 6;
}

message NonUpdatableRepositionParams {
    // non-updatable params affecting both recover and migrate
    optional uint64 reposition_trigger_interval_ms = 1;
    optional uint64 max_cap_reposition_concurrency_limit = 2;
    optional uint64 max_perf_reposition_concurrency_limit = 3;
}

message UpdatableRecoverParams {
}

message NonUpdatableRecoverParams {
    optional uint64 recover_scan_interval_ms = 1;
}

message UpdatableMigrateParams {
}

message NonUpdatableMigrateParams {
    optional uint64 migrate_scan_interval_ms = 1;
    optional SpaceLoad perf_thick_load = 2;
    optional SpaceLoad perf_thin_load = 3;
    optional SpaceLoad cap_load = 4;
    optional double perf_thick_load_ratio = 5;
    optional double perf_thin_load_ratio = 6;
    optional double cap_load_ratio = 7;
}

message UpdatableGeneralRepositionParams {
    // general reposition including reposition, recover and migrate
    optional UpdatableRepositionParams updatable_reposition_params = 1;
}

message RecoverParamsResponse {
    optional bool enable_recover = 1;
    optional NonUpdatableRecoverParams non_updatable_recover_params = 2;
}

message MigrateParamsResponse {
    optional bool enable_migrate = 1;
    optional NonUpdatableMigrateParams non_updatable_migrate_params = 2;
}

message RepositionParamsResponse {
    optional uint64 current_generate_cmds_per_round_limit = 1;
    optional uint64 current_cap_distribute_cmds_per_chunk_limit = 2;
    optional uint64 current_perf_distribute_cmds_per_chunk_limit = 8;

    optional UpdatableRepositionParams updatable_reposition_params = 3;
    optional NonUpdatableRepositionParams non_updatable_reposition_params = 4;
    repeated SessionRepositionLimit session_reposition_limits = 5;

    optional RecoverParamsResponse recover_params = 6;
    optional MigrateParamsResponse migrate_params = 7;
}

message UpdatableDrainParams {
    optional uint32 drain_distribute_cmds_per_chunk_limit = 1;
    optional uint32 drain_generate_cmds_per_round_limit = 2;
    optional uint64 drain_cmd_timeout_ms = 3;
}

message NonUpdatableDrainParams {
    optional bool cluster_enable_drain = 1;
    optional bool allow_drain = 2;
    optional uint64 scan_interval_ms = 3;
    optional uint64 generate_cmds_interval_ms = 4;
    optional uint64 drain_no_lease_timeout_ms = 5;
    optional bool allow_drain_parent = 6;
    optional bool allow_drain_idle = 7;
}

message DrainParamsResponse {
    optional UpdatableDrainParams updatable_drain_params = 1;
    optional NonUpdatableDrainParams non_updatable_drain_params = 2;
}

message SinkCapDirectWriteInfo {
    optional SinkMode mode = 1;
    optional CapDirectWritePolicy policy = 2;
}

message SinkModeRelatedIntervalInfo {
    optional SinkMode mode = 1;
    optional uint64 interval = 2;
}

message UpdatableSinkParams {
    repeated SinkModeRelatedIntervalInfo sink_inactive_lease_interval_map = 1;
    repeated SinkModeRelatedIntervalInfo sink_no_lease_timeout_map = 2;
    repeated SinkCapDirectWriteInfo sink_cap_direct_write_map = 3;
}

message NonUpdatableSinkParams {
    optional float sink_mid_load_ratio = 1;
    optional float sink_high_load_ratio = 2;
    optional float sink_very_high_load_ratio = 3;
    optional uint32 sink_scan_interval_s = 4;
    optional SinkMode mode = 5;
    optional float load = 6;
    optional uint64 access_reserve_block_num = 7;
    optional SinkMode drain_parent_start_mode = 8;
    optional SinkMode drain_idle_start_mode = 9;
}

message SinkParamsResponse {
    optional UpdatableSinkParams updatable_sink_params = 1;
    optional NonUpdatableSinkParams non_updatable_sink_params = 2;
}

message InternalIOPriorities {
    optional InternalIOPriority recover = 1 [default = INTERNAL_HIGH];
    optional InternalIOPriority sink  = 2 [default = INTERNAL_MID];
    optional InternalIOPriority migrate = 3 [default = INTERNAL_LOW];
}

message SessionInternalIOLimit {
    required string session_id = 1;
    required string ip = 2;
    optional uint64 current_cap_internal_io_speed_limit = 3; // MiB/s
    optional uint64 current_max_cap_internal_io_speed_limit = 4; // MiB/s
    optional uint64 current_perf_internal_io_speed_limit = 5; // MiB/s
    optional uint64 current_max_perf_internal_io_speed_limit = 6; // MiB/s
    optional uint32 cid = 7;
}

message InternalIOParamsResponse {
    optional UpdatableInternalIOParams updatable_params = 1;
    repeated SessionInternalIOLimit session_limits = 2;
}

message UpdatableInternalIOParams {
    optional InternalIOMode mode = 1;
    optional uint64 static_cap_internal_io_speed_limit = 2;
    optional uint64 static_perf_internal_io_speed_limit = 3;
    optional InternalIOPriorities internal_io_priorities = 4;
}

message ClusterStatus {
    enum ClusterStatusFlags {
        CLUSTER_HEALTHY = 0x01;
        CLUSTER_LICENSE_EXPIRED = 0x02;
        CLUSTER_SPACE_FULL = 0x04;
        CLUSTER_NO_ALIVE_CHUNK = 0x08;
    }
    optional uint64 flags = 1;
}

message SetMaintenanceModeRequest {
    required ChunkId id = 1;
    required bool mode = 2;
    optional uint32 expire_duration_s = 3;  // second
    // if single_chunk is true, only the chunk with specified cid will enter maintenance mode
    optional bool single_chunk = 4 [default = false];
}

message GetMaintenanceInfoResponse {
    required uint32 cid = 1;
    required uint32 expire_duration_s = 2;
    optional uint32 node_data_ip = 3 [default = 0];
    repeated uint32 all_cids = 4;
}

message GetServicePortalRequest {
    required string initiator = 1;
    required string target_name = 2;
    required string initiator_ip = 3;
    // which address was redirector get login request
    required string conn_server_ip = 4;
    // data ip address in redirectord node who send request
    required string local_access_ip = 5;
}

message ISCSIServicePortal {
    optional string portal = 1;  // ip:port as iscsi target access point
}

message GetAccessRecordRequest {
    required string target_id = 1;
}

message GetAccessRecordResponse {
    optional ISCSIAccessRecords record = 1;
}

message SetAccessRecordRequest {
    required string target_id = 1;
    required string initiator = 2;
    required uint32 cid = 3;
}

message SetAccessRecordResponse {
    optional string target_id = 1;
    optional string initiator = 2;
    optional uint32 cid = 3;
}

message CheckLeaseRequest {
    required uint32 pid = 1;
}

message CheckLeaseResponse {
    required bool allocated = 1;
}

message AccessDataReportRequest {
    extend consensus.DataReportRequest {
        optional AccessDataReportRequest request = 10001;
    }
    optional StoragePerf read_perf             = 1 [deprecated = true];
    optional StoragePerf write_perf            = 2 [deprecated = true];
    optional StoragePerf total_perf            = 3 [deprecated = true];
    optional StoragePerf cross_zone_read_perf  = 4 [deprecated = true];
    optional StoragePerf cross_zone_write_perf = 5 [deprecated = true];

    optional uint64 recover_migrate_speed            = 6 [deprecated = true];
    optional uint64 recover_speed                    = 7 [deprecated = true];
    optional uint64 migrate_speed                    = 8 [deprecated = true];
    optional uint64 cross_zone_recover_speed         = 9 [deprecated = true];
    optional uint64 cross_zone_migrate_speed         = 10 [deprecated = true];
    optional uint64 cross_zone_recover_migrate_speed = 11 [deprecated = true];
    optional ISCSIConnections iscsi_conns = 12;
    optional NVMFConnections nvmf_conns = 13;

    optional AccessPerf access_perf   = 14;
    optional RecoverPerf recover_perf = 15;

    optional AccessPerf access_cap_replica_app_perf = 16;
    optional AccessPerf access_perf_replica_app_perf = 17;
    optional AccessPerf access_cap_ec_app_perf = 18;

    optional AccessPerf access_cap_replica_sink_perf = 19;
    optional AccessPerf access_perf_replica_sink_perf = 20;
    optional AccessPerf access_cap_ec_sink_perf = 21;

    optional AccessPerf access_cap_replica_reposition_perf = 22;
    optional AccessPerf access_perf_replica_reposition_perf = 23;
    optional AccessPerf access_cap_ec_reposition_perf = 24;
}

message DataReportPExtentInfos {
    required uint32 version = 1;
    optional uint32 count = 2;
    optional bytes pextents_info_raw = 3;
}

message ChunkDataReportRequest {
    extend consensus.DataReportRequest {
        optional ChunkDataReportRequest request = 10002;
    }
    repeated PExtentInfo pextents_info = 3; // use pextents_info_struct instead
    optional StoragePerf read_perf     = 4 [deprecated = true];  // chunk local read perf
    optional StoragePerf write_perf    = 5 [deprecated = true];  // chunk local write perf
    optional StoragePerf total_perf    = 6 [deprecated = true];  // chunk local total perf
    optional NodePerf node_perf        = 7 [deprecated = true];
    optional LSMVersion lsm_version = 8;

    optional LSMPerf lsm_perf = 9;
    optional bool report_complete = 10 [default = true];
    optional uint64 thin_used_data_space = 11 [default = 0];

    optional uint64 planned_prioritized_space = 15;
    optional uint64 allocated_prioritized_space = 16;

    optional DataReportPExtentInfos pextents_info_struct = 17;
}

message ChunkDataReportResponse {
    extend consensus.DataReportResponse {
        optional ChunkDataReportResponse response = 10001;
    }

    optional uint32 report_start_pid = 1;
    // the last report pid should be the biggest pid, not need to roll back to kPExtentIdStart
    optional uint32 max_report_size = 2;
    repeated uint32 query_lease_lids = 3;
}

message NVMFConnection {
    // TODO(jiewei): remove unused fileds
    required bytes host_nqn = 1 [(zbs.labels).as_str = true];
    required bytes host_ip = 2 [(zbs.labels).as_str = true];
    required bytes subsystem_id = 3 [(zbs.labels).as_str = true];
    required uint32 client_port = 4;
    optional bytes subsystem_addr = 5 [(zbs.labels).as_str = true];
    optional bytes subsystem_name = 6 [(zbs.labels).as_str = true];
    optional uint64 survival_time = 7;
    optional bytes tr_type = 8 [(zbs.labels).as_str = true];
}

message NVMFConnections {
    repeated NVMFConnection conns = 1;
}

message NVMFConnectionRecord {
    required NVMFConnection conn = 1;
    required bytes session_id = 2 [(zbs.labels).as_str = true];
    optional uint32 cid = 3;
}

message ListNVMFConnectionRequest {
    optional bytes session_id = 1 [(zbs.labels).as_str = true];
}

message ListNVMFConnectionResponse {
    repeated NVMFConnectionRecord records = 1;
}

message NVMFAccessRecord {
    optional string host_nqn = 1;
    optional uint32 cid = 2;
}

message NVMFAccessRecords {
    // subsystem uuid
    required bytes subsystem_id = 1 [(zbs.labels).as_str = true];
    // access login record for balance subsystem
    repeated NVMFAccessRecord accesses = 2;
}

message NVMFVolumeAccessRecords {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    repeated NVMFAccessRecord accesses = 2;
}

message GetNVMFOptimizedAccessRequest {
    required string subsystem_name = 1;
    required uint32 ns_id = 2;
}

message SetNVMFOptimizedAccessRequest {
    repeated string host_nqns = 1;
    required string subsystem_name = 2;
    required uint32 ns_id = 3;
}

message NVMFOptimizedPaths {
    repeated NVMFOptimizedPath paths = 1;
}

message GetNVMFAccessRecordRequest {
    required string subsystem_id = 1;
}

message GetNVMFAccessRecordResponse {
    optional NVMFAccessRecords record = 1;
}

message SetNVMFAccessRecordRequest {
    required string subsystem_id = 1;
    required string host_nqn = 2;
    required uint32 cid = 3;
}

message SetNVMFAccessRecordResponse {
    required string subsystem_id = 1;
    required string host_nqn = 2;
    optional uint32 cid = 3;
}

message AcquireNVMFIOPermissionRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    required string host_nqn = 2;
    required uint32 cid = 3;
}

enum NVMFAccessPolicy {
    INHERIT_POLICY = 1;
    BALANCE_POLICY = 2;
}

message NVMFDistSubsystem {
    required bytes id = 1 [(zbs.labels).as_str = true];              // pool_id
    required bytes name = 2 [(zbs.labels).as_str = true];            // same with pool_name, used in nqn_name
    required bytes nqn_name = 3 [(zbs.labels).as_str = true];        // maximum 223
    optional TimeSpec created_time = 4;

    // attributes for NVMe Qualified Name (NQN)
    // our rule for nqn_name:
    // nqn.$nqn_date.$nqn_naming_auth:$storage_pool_name:$name
    optional bytes nqn_date = 5 [(zbs.labels).as_str = true];
    optional bytes nqn_naming_auth = 6 [(zbs.labels).as_str = true];
    optional bytes description = 7 [default = "", (zbs.labels).as_str = true];

    optional Pool pool = 8;  // pool of this subsystem

    optional NVMFAccessPolicy policy = 10 [default = INHERIT_POLICY];
    repeated NVMFDistNamespace namespaces = 11;

    // use for quick compare subsystem config
    optional uint32 config_ver = 21 [default = 0];

    optional bytes nqn_whitelist = 31 [default = "*/*", (zbs.labels).as_str = true];
    optional bytes ipv4_whitelist = 32 [default = "*/*", (zbs.labels).as_str = true];

    optional Labels labels = 41;
    optional Labels attributes = 42;
    optional bool prioritized = 43 [default = false];
    optional bool adaptive_nqn_whitelist = 44 [default = false];
}

message CreateNVMFDistSubsystemRequest {
    required bytes name = 1 [(zbs.labels).as_str = true];

    // if not specified, the system storage pool is used
    optional bytes storage_pool_id = 2 [(zbs.labels).as_str = true];
    optional NVMFAccessPolicy policy = 3;

    optional uint32 replica_num = 11;
    optional bool thin_provision = 13;

    // attributes for NVMe Qualified Name (NQN)
    optional bytes nqn_date = 14 [(zbs.labels).as_str = true];
    optional bytes nqn_naming_auth = 15 [(zbs.labels).as_str = true];
    optional bool adaptive_nqn_whitelist = 16;

    optional bytes description = 21 [(zbs.labels).as_str = true]; // 256B

    optional IOThrottleConfig throttling = 50;

    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;

    optional bytes nqn_whitelist = 71 [default = "*/*", (zbs.labels).as_str = true];
    optional bytes ipv4_whitelist = 72 [default = "*/*", (zbs.labels).as_str = true];

    optional Labels labels = 81;
    optional Labels attributes = 82;
    optional bool prioritized = 83 [default = false];

    optional ResiliencyType resiliency_type = 84 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 85;
    optional EncryptMethod encrypt_method = 86 [default = ENCRYPT_PLAIN_TEXT];
}

message UpdateNVMFDistSubsystemRequest {
    required PoolPath pool_path = 1;

    optional uint32 replica_num = 11;
    optional bool thin_provision = 13;
    optional bool adaptive_nqn_whitelist = 14;

    optional bytes description = 21 [(zbs.labels).as_str = true];  // 256B

    optional IOThrottleConfig throttling = 50;

    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;

    optional bytes nqn_whitelist = 71 [(zbs.labels).as_str = true];
    optional bytes ipv4_whitelist = 72 [(zbs.labels).as_str = true];
    optional bool prioritized = 73 [default = false];
    optional ResiliencyType resiliency_type = 74;
    optional ECAlgorithmParam ec_param      = 75;

    optional ResetLabelsParams reset_labels_params = 76;
    optional EncryptMethod encrypt_method = 77 [default = ENCRYPT_PLAIN_TEXT];
}

message NVMFDistSubsystemsResponse {
    repeated NVMFDistSubsystem dist_subsystems = 2;
    optional uint32 total_num = 10;
}

message ListNVMFDistSubsystemsRequest {
    optional bool show_ns = 1 [default = false];
}

message NVMFDistNamespaceGroup {
    required bytes group_id = 1 [(zbs.labels).as_str = true];
    required bytes pool_id = 2 [(zbs.labels).as_str = true];
    required bytes name = 3 [(zbs.labels).as_str = true];
    optional TimeSpec created_time = 4;
}

message DistNamespaceGroupPath {
    required PoolPath pool_path = 1;
    optional bytes group_id = 2 [(zbs.labels).as_str = true];
    optional bytes group_name = 3 [(zbs.labels).as_str = true];
}

message UpdateNVMFDistNamespaceGroupRequest {
    required DistNamespaceGroupPath group_path = 1;
    optional bytes new_name = 2 [(zbs.labels).as_str = true];
}

message NVMFDistNamespaceGroupsResponse {
    repeated NVMFDistNamespaceGroup ns_groups = 1;
    optional uint32 total_num = 2;
}

message NVMFOptimizedPath {
    required string host_nqn = 1;
    required uint32 optimized_cid = 2;
    optional uint32 expected_optimized_cid = 3 [default = 0];
    optional uint32 current_location = 4 [default = 0];
    optional uint32 expected_location = 5 [default = 0];
}

message NVMFDistNamespace {
    required uint32 ns_id = 1;
    required bytes pool_id = 2 [(zbs.labels).as_str = true]; // same with underlying pool_id
    required bytes volume_id = 3 [(zbs.labels).as_str = true]; // same with underlying volume_id
    optional TimeSpec created_time = 4;

    // copied from volume
    optional uint64 size = 5;
    optional bytes description = 6 [(zbs.labels).as_str = true];
    optional uint32 replica_num = 7;
    optional bool thin_provision = 8;

    optional IOThrottleConfig throttling = 9;

    // capacity layer
    optional int64 unique_size = 10 [default = -1];
    optional int64 shared_size = 11 [default = -1];
    // performance layer
    optional int64 perf_unique_size = 13 [default = -1];
    optional int64 perf_shared_size = 14 [default = -1];

    optional int64 logical_used_size = 15 [default = -1];

    optional bytes name = 12 [(zbs.labels).as_str = true];

    optional bytes group_id = 22 [(zbs.labels).as_str = true]; // namespace group id
    optional uint32 current_location = 23 [default = 0];  // deprecated
    optional uint32 expected_location = 24 [default = 0];  // deprecated
    repeated NVMFOptimizedPath optimized_paths = 25;
    optional uint32 preferred_cid = 26 [default = 0];

    // stripe num
    optional uint32 stripe_num = 31;
    optional uint32 stripe_size = 32;

    optional bool is_shared = 33 [default = false]; // shared namespace, only valid for INHERIT_POLICY
    optional bytes nqn_whitelist = 41 [(zbs.labels).as_str = true];
    optional bool single_access = 42 [default = false];
    optional bool prioritized = 43 [default = false];
    optional uint64 downgraded_prioritized_space = 44 [default = 0];  // Bytes

    optional ResiliencyType resiliency_type = 45 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param      = 46;

    optional uint64 chunk_instances_bits = 47 [default = 0];
    optional EncryptMethod encrypt_method = 48 [default = ENCRYPT_PLAIN_TEXT];
}

enum NVMFConfigOption {
    NVMF_UNKNOWN = 0;

    DIST_SUBSYSTEM_ADD = 1;
    DIST_SUBSYSTEM_REMOVE = 2;
    DIST_SUBSYSTEM_NQN_WHITELIST = 3;
    DIST_SUBSYSTEM_IPV4_WHITELIST = 4;

    DIST_NAMESPACE_ADD = 21;
    DIST_NAMESPACE_REMOVE = 22;
    DIST_NAMESPACE_UPDATE_CURRENT_LOC = 23;  // deprecated
    DIST_NAMESPACE_UPDATE_ANA_STATE   = 24;  // deprecated
    DIST_NAMESPACE_UPDATE_SIZE = 25;
    DIST_NAMESPACE_NQN_WHITELIST = 26;
    DIST_NAMESPACE_UPDATE_CHUNK_INSTANCES = 27;
}

message NVMFConfigUpdate {
    extend ConfigUpdate {
        optional NVMFConfigUpdate update = 10002;
    }
    required bytes subsystem_id = 1 [(zbs.labels).as_str = true];
    required uint32 config_ver = 2;
    repeated NVMFConfigOption op = 3;
    optional NVMFDistSubsystem subsystem = 4;
    optional NVMFDistNamespace ns = 5;
}

message VhostConfigUpdate {
    extend ConfigUpdate {
        optional VhostConfigUpdate update = 10003;
    }
    optional VhostIOPermissionRecord record = 1;
    optional ISCSILun lun = 2;
}

message DistNamespacePath {
    required PoolPath pool_path = 1;
    optional uint32 ns_id = 2 [default = 0];
    optional bytes ns_name = 3 [(zbs.labels).as_str = true];
    optional bytes secondary_id = 4 [(zbs.labels).as_str = true];
    optional bytes volume_id = 5 [(zbs.labels).as_str = true];
}

message DeleteDistNamespaceRequest {
    required PoolPath pool_path = 1;
    optional uint32 ns_id = 2 [ default = 0 ];
    optional bytes ns_name = 3 [ (zbs.labels).as_str = true ];
    optional bytes secondary_id = 4 [ (zbs.labels).as_str = true ];
    optional bytes volume_id = 5 [ (zbs.labels).as_str = true ];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 6 [ default = false ];
}

message SubsystemRequirement {
    optional Labels labels = 1;
    optional Labels attributes = 2;
    optional NVMFAccessPolicy access_policy = 3 [default = INHERIT_POLICY];
    optional uint32 replica_num = 4;
    optional ResiliencyType resiliency_type = 5 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param = 6;
    optional bool adaptive_nqn_whitelist = 7;
    optional bool prioritized = 8;
    optional EncryptMethod encrypt_method = 9 [default = ENCRYPT_PLAIN_TEXT];
}

message CreateNVMFDistNamespaceRequest {
    required DistNamespacePath ns_path = 1;
    optional uint64 size = 2;
    optional bytes description = 3 [(zbs.labels).as_str = true];

    optional uint32 replica_num = 11;
    optional bool thin_provision = 12;
    optional IOThrottleConfig throttling = 13;

    optional bytes group_id = 22 [(zbs.labels).as_str = true]; // namespace group id

    optional DistNamespacePath src_ns_path = 31; // clone from another ns
    optional bytes src_snapshot_id = 32 [(zbs.labels).as_str = true]; // clone from another snapshot

    // stripe num
    optional uint32 stripe_num = 61;
    optional uint32 stripe_size = 62;
    optional bool is_shared = 63 [default = false]; // shared namespace, only valid for INHERIT_POLICY

    optional bytes nqn_whitelist = 71 [(zbs.labels).as_str = true];
    optional bool single_access = 72;

    // for test purpose
    optional bool alloc_ns = 81 [default = true];

    optional SubsystemRequirement subsystem_requirement = 91;
    optional bool prioritized = 92 [default = false];

    optional ResiliencyType resiliency_type = 93;
    optional ECAlgorithmParam ec_param      = 94;

    optional uint64 chunk_instances_bits = 95 [default = 0];
    optional EncryptMethod encrypt_method = 96 [default = ENCRYPT_PLAIN_TEXT];
}

message UpdateNVMFDistNamespaceRequest {
    required DistNamespacePath ns_path = 1;
    optional uint64 size = 2;
    optional bytes description = 3 [(zbs.labels).as_str = true];

    optional uint32 replica_num = 11;
    optional bool thin_provision = 12;
    optional IOThrottleConfig throttling = 13;

    optional bytes new_name = 31 [(zbs.labels).as_str = true];
    optional bool new_alloc_even = 32;
    optional bool skip_all_zero_first_write = 34 [default = false];
    optional uint32 expected_location = 35;
    repeated NVMFOptimizedPath optimized_paths = 36;

    optional bytes nqn_whitelist = 41 [(zbs.labels).as_str = true];
    optional bool single_access = 42;
    optional bool read_only = 51;
    optional bool prioritized = 52 [default = false];
    optional uint64 chunk_instances_bits = 53 [default = 0];
}

message NVMFDistNamespacesResponse {
    repeated NVMFDistNamespace namespaces = 2;
    optional uint32 total_num = 10;
}

message NVMFSnapshotPath {
    optional DistNamespacePath ns_path = 1;
    optional bytes snapshot_name = 2 [(zbs.labels).as_str = true];
    optional bytes snapshot_id = 5 [(zbs.labels).as_str = true];
    optional bytes secondary_id = 6 [(zbs.labels).as_str = true];
}

message DeleteNVMFSnapshotRequest {
    optional DistNamespacePath ns_path = 1;
    optional bytes snapshot_name = 2 [ (zbs.labels).as_str = true ];
    optional bytes snapshot_id = 5 [ (zbs.labels).as_str = true ];
    optional bytes secondary_id = 6 [ (zbs.labels).as_str = true ];

    // this field will be checked only if recycle_bin is enabled.
    optional bool delete_permanently = 7 [ default = false ];
}

message CreateNVMFSnapshotRequest {
    required DistNamespacePath ns_path = 1;
    required bytes snapshot_name = 2 [(zbs.labels).as_str = true];

    optional bytes snapshot_desc = 3 [default = "", (zbs.labels).as_str = true];
    optional bytes secondary_id = 4 [(zbs.labels).as_str = true];
}

message UpdateNVMFSnapshotRequest {
    required NVMFSnapshotPath snapshot_path = 1;

    optional bytes new_name = 10 [(zbs.labels).as_str = true];
    optional bytes new_description = 11 [(zbs.labels).as_str = true];
    optional bool new_alloc_even = 12;
}

message RebalanceAccessPointStatus {
    optional bool enable_rebalance = 1;
}

enum NVMFTransportType {
    NVMF_TR_TYPE_RDMA = 0;
    NVMF_TR_TYPE_TCP  = 1;
}

message NVMFTarget {
    required bytes address = 1 [(zbs.labels).as_str = true];
    required int32 port = 2 [default = 4420];
    required NVMFTransportType tr_type = 3 [default = NVMF_TR_TYPE_RDMA];
}

message NVMFTargetsResponse {
    repeated NVMFTarget targets = 1;
}

message NVMFNamespaceAllowedHost {
    required DistNamespacePath ns_path = 1;
    required bytes host_nqn = 2 [(zbs.labels).as_str = true];
}

message GetNVMFVolumeAccessRecordsRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
}

// deprecated
message RecoverParameter {
    required uint64 migrate_scan_interval_s = 1;
    required uint64 recover_scan_interval_s = 2;
    required uint64 recover_trigger_inteval_s = 3;
    required uint64 max_migrate_cmds = 4;
    required uint64 max_recover_cmds = 5;
    required uint64 max_migrate_cmds_per_chunk = 6;
    required uint64 max_recover_cmds_per_chunk = 7;
}

message VhostIOPermissionEntry {
    required string vm_uuid = 1;
    required string machine_uuid = 2;
}

message VhostIOPermissionRecord {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    optional bool single_access = 2 [ default = false ];
    repeated VhostIOPermissionEntry entries = 3;
}

message UpdateVhostIOPermissionRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    repeated VhostIOPermissionEntry entries = 2;
}

message VhostIOPermissionRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    required string vm_uuid = 2;
    required string machine_uuid = 3;
    optional bool override = 4 [ default = false ];
    optional bool single_access = 5 [ default = false ];
}

message ClearVhostIOPermissionRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    optional bool reset_to_allow_all = 2 [ default = false ];
}

message GetVhostIOPermissionResponse {
    optional VhostIOPermissionRecord record = 1;
}

message BanChunkRequest {
    // fields 1-3 are extracted from ChunkId for compatibility
    optional uint32 id = 1;
    optional uint32 rpc_ip = 2;
    optional uint32 rpc_port = 3;
    // if single_chunk is true, only the chunk with specified cid will be banned
    optional bool single_chunk = 4 [default = false];
}

message UnbanChunkRequest {
    // fields 1-3 are extracted from ChunkId for compatibility
    optional uint32 id = 1;
    optional uint32 rpc_ip = 2;
    optional uint32 rpc_port = 3;
    // if single_chunk is true, only the chunk with specified cid will be unbanned
    optional bool single_chunk = 4 [default = false];
}

message SetChunkIsolatePolicyRequest {
    optional ChunkId id = 1;
    optional ChunkIsolateFlag flag = 2;
    optional string name = 3 [deprecated=true];
}

message GetChunkIsolateInfoResponseItem {
    optional ChunkIsolateRecord record = 1;
    optional ChunkIsolateFlag chunk_real_status = 2;
}

message GetChunkIsolateInfoResponse {
    repeated GetChunkIsolateInfoResponseItem items = 1;
}

message SetDefaultPrioSpaceRatioRequest {
    optional uint32 percentage = 1;
}

message SetChunkPrioSpaceRatioRequest {
    message CidPercentage {
        optional ChunkId cid = 2;
        optional uint32 percentage = 1;
    }
    repeated CidPercentage chunk_ratios = 1;
}

message ChunkPrioSpaceInfo {
    optional ChunkId cid = 1;
    optional uint32 percentage = 2;
    optional uint64 planned_prioritized_space = 3;
    optional uint64 allocated_prioritized_space = 4;
    optional uint64 downgraded_prioritized_space = 5;
}

message ChunkPrioSpaceInfoList {
    repeated ChunkPrioSpaceInfo chunk_infos = 1;
}

message DefaultPrioSpaceInfo {
    optional uint32 percentage = 1;
}

message SpecialRecoverRequest {
    required uint32 pid = 1;
    required uint64 epoch = 2;
    required uint32 temporary_pid = 3;
    required uint64 temporary_epoch = 4;
    optional bool rollback_failed_replica = 5;
    optional bool force_recover_from_temporary_replica = 6;
    optional uint32 failed_cid = 7 [default = 0];
}

message ListTemporaryReplicaRequest {
   optional uint32 start_temporary_pid = 1 [default = 0];
   optional uint32 max_num = 2 [default = 2048];
}

message ListTemporaryReplicaResponse {
    repeated TemporaryReplica temporary_replicas = 1;
}

message GetTemporaryReplicaRequest {
    required uint32 temporary_pid = 1;
    required uint64 temporary_epoch = 2;
}

message GetTemporaryReplicaResponse {
    required TemporaryReplica temporary_replica = 1;
}

message ChunkTemporaryReplicaInfo {
    required uint32 id = 1;
    required uint64 temporary_replica_space  = 2;
    optional uint32 temporary_replica_num = 3;
}

message GetTemporaryReplicaSummaryResponse {
    required uint64 temporary_replica_space = 1 [default = 0];
    repeated ChunkTemporaryReplicaInfo chunk_infos = 2;
}

enum BatchSnapshotVolumeType {
    BATCH_SNAPSHOT_NONE = 0;
    BATCH_SNAPSHOT_VOLUME = 1;
    BATCH_SNAPSHOT_LUN = 2;
    BATCH_SNAPSHOT_FILE = 3;
    BATCH_SNAPSHOT_NAMESPACE = 4;
}

message BatchSnapshotVolume {
    required SnapshotPath snapshot_path = 1;
    optional bytes snapshot_description = 2 [default = "", (zbs.labels).as_str = true];
    required BatchSnapshotVolumeType type = 3;
    extensions 10000 to max;  // for specified volume attribute
}

message BatchSnapshotFile {
    extend BatchSnapshotVolume {
        optional BatchSnapshotFile volume = 10001;
    }
    required bytes inode_id = 1 [(zbs.labels).as_str = true];
}

message BatchSnapshotLUN {
    extend BatchSnapshotVolume {
        optional BatchSnapshotLUN volume = 10002;
    }
    required uint32 lun_id = 1;
}

message BatchSnapshotNamespace {
    extend BatchSnapshotVolume {
        optional BatchSnapshotNamespace volume = 10003;
    }
    required uint32 ns_id = 1;
}

message BatchCreateSnapshotsRequest {
    repeated BatchSnapshotVolume volumes = 1;
}

message BatchCreateSnapshotsResponse {
    repeated Volume snapshots = 1;
}

message ReclaimVolumeTemporaryReplicaRequest {
    required string volume_id = 1;
}

message ReclaimVolumeTemporaryReplicaResponse {
    optional uint64 reclaim_num = 1 [default = 0];
}

message ChunkRepositionExtentsSummary {
    optional uint32 cid = 1;

    optional uint32 cap_src_pids_num = 2;
    optional uint32 cap_replace_pids_num = 3;
    optional uint32 cap_dst_pids_num = 4;

    optional uint32 perf_src_pids_num = 5;
    optional uint32 perf_replace_pids_num = 6;
    optional uint32 perf_dst_pids_num = 7;
    optional uint32 prior_dst_pids_num = 8;

    optional uint64 cap_src_pids_space = 9;
    optional uint64 cap_replace_pids_space = 10;
    optional uint64 cap_dst_pids_space = 11;

    optional uint64 perf_src_pids_space = 12;
    optional uint64 perf_replace_pids_space = 13;
    optional uint64 perf_dst_pids_space = 14;
    optional uint64 prior_dst_pids_space = 15;
}

message RepositionExtentsSummaryResponse {
    repeated ChunkRepositionExtentsSummary chunk_reposition_extents_summary = 1;
}

message RepositionDetailedPids {
    repeated uint32 cap_src_pids = 1;
    repeated uint32 cap_replace_pids = 2;
    repeated uint32 cap_dst_pids = 3;

    repeated uint32 perf_src_pids = 4;
    repeated uint32 perf_replace_pids = 5;
    repeated uint32 perf_dst_pids = 6;
    repeated uint32 prior_dst_pids = 7;
}

message ElevateTaskRecord {
    message ExtentCmd {
        optional uint32 lid = 1;
        optional uint32 vextent_no = 2;
    }
    optional string volume_id = 1;
    repeated ExtentCmd running_cmd = 2;
    repeated ExtentCmd pending_cmd = 3;
}

message ElevateTaskRecords {
    repeated ElevateTaskRecord record = 1;
}

message ShowNodeRequest {
    optional uint32 node_data_ip = 1 [default = 0];
}

message ListMasterKeyIdsResponse {
    repeated string master_key_id = 1;
}

message MigratePExtentRequest {
    optional uint32 pid = 1;
    optional uint32 dst_cid = 2;
    optional uint32 src_cid = 3;
    optional uint32 replace_cid = 4;
    optional bool ignore_topo_safety = 5;
}

message GatherVolumeLocalSegmentsRequest {
    optional string volume_id = 1;
    optional bool ignore_perf = 2;
    optional bool ignore_cap = 3;
}

message GatherVolumeLocalSegmentsResponse {
    optional uint32 need_migrate_num = 1;
}

// metadata service
service MetaService {
    option (rpc_service_id) = 0; // FIXME(keyue) change to 4000 in next major release
    /**************************************************/
    /* pool management                                */
    /**************************************************/

    // create a new pool
    rpc CreatePool (Pool) returns (Pool);

    // list pools
    rpc ListPool (ListPoolRequest) returns (PoolsResponse);

    // delete pool
    rpc DeletePool (PoolPath) returns (Void);

    // show the pool spec
    rpc ShowPool (PoolPath) returns (Pool);

    // update the pool spec
    rpc UpdatePool (UpdatePoolRequest) returns (Pool);

    /**************************************************/
    /* volume management                                */
    /**************************************************/

    // create a new volume
    rpc CreateVolume (CreateVolumeRequest) returns (Volume);

    // list volumes
    rpc ListVolume (ListVolumeRequest) returns (VolumesResponse);

    // show the spec of volume
    rpc ShowVolume (ShowVolumeRequest) returns (ShowVolumeResponse);

    // delete a volume
    rpc DeleteVolume(DeleteVolumeRequest) returns (Void);

    // update a volume
    rpc UpdateVolume (UpdateVolumeRequest) returns (Volume);
    rpc ResizeVolume (ResizeVolumeRequest) returns (Volume);

    // get the volume size: a quicker way to get only the volume size property
    rpc GetVolumeSize (VolumeId) returns (VolumeSizeResponse);

    rpc GetVTable (VolumePath) returns (GetVTableResponse);

    // move a volume
    rpc MoveVolume (MoveVolumeRequest) returns (Volume);

    /**************************************************/
    /* snapshot management                            */
    /**************************************************/

    rpc CreateSnapshot (CreateSnapshotRequest) returns (Volume);

    rpc ShowSnapshot (SnapshotPath) returns (Volume);

    rpc UpdateSnapshot (UpdateSnapshotRequest) returns (Volume);

    // delete a snapshot
    rpc DeleteSnapshot (DeleteSnapshotRequest) returns (Void);

    rpc MoveSnapshot (MoveSnapshotRequest) returns (Volume);

    // list all snapshots
    rpc ListSnapshot (ListSnapshotRequest) returns (SnapshotsResponse);

    // clone a volume from a snapshot
    rpc CloneVolume (CloneVolumeRequest) returns (Volume);

    // rollback a volume to a snapshot
    rpc RollbackVolume (SnapshotPath) returns (Void);

    /**************************************************/
    /* pextent management                             */
    /**************************************************/
    // get extent location
    rpc GetPExtent (GetExtentRequest) returns (PExtent);

    // cow pextent, deprecated
    rpc CowPExtent (CowPExtentRequest) returns (VExtentLease);

    // Get pextent locations, if not exist, alloc replicas.
    rpc GetAllocPExtent (GetAllocPExtentRequest) returns (PExtent);

    // remove a replica of a pextent: the function only return error when 1. the
    // client is not owner, 2. the updated pextent is not persisted into the
    // db. The client should check the PExtent's location to update its cache.
    rpc RemoveReplica (RemoveReplicaRequest) returns (PExtent);

    // add a replica to a pextent
    rpc AddReplica (AddReplicaRequest) returns (PExtent);

    // replace replica
    rpc ReplaceReplica (ReplaceReplicaRequest) returns (PExtent);

    rpc ListPExtent (ListPExtentRequest) returns (PExtentsResponse);

    rpc GetPExtentRef (GetExtentRequest) returns (GetExtentRefResponse);

    rpc FindPExtent (FindPExtentRequest) returns (PExtentsResponse);

    /**************************************************/
    /* db backup                                      */
    /**************************************************/

    rpc ListDb (Void) returns (ListDbResponse);

    // DumpDb V1 has been deprecated, return EMethodNotAllowed
    rpc DumpDb (DumpDbRequest) returns (DumpDbResponse);

    /**************************************************/
    /* migrate management                             */
    /**************************************************/
    // user could trigger a migrate manually
    rpc MigratePExtent (MigratePExtentRequest) returns (Void);

    rpc ListMigrate (Void) returns (RecoverCmds);

    rpc SwitchMigrate (SwitchRequest) returns (Void);

    /**************************************************/
    /* recover management                             */
    /**************************************************/
    rpc RecoverPExtent (RecoverCmd) returns (Void);  // deprecated

    rpc ListRecover (Void) returns (RecoverCmds);

    rpc SwitchRecover (SwitchRequest) returns (Void);

    /**************************************************/
    /* server management                              */
    /**************************************************/
    rpc StopServer (Void) returns (Void);

    /**************************************************/
    /* license management                              */
    /**************************************************/
    rpc UpdateLicense (LicenseRequest) returns (Void);
    rpc ShowLicense (Void) returns (License);
    rpc ParseLicense (LicenseRequest) returns (License);

    /**************************************************/
    /* added later                                    */
    /**************************************************/

    rpc GetVExtentLease (GetVExtentLeaseRequest) returns (VExtentLease);
    rpc GetLease (GetLeaseRequest) returns (Lease);

    // pextent epoch
    rpc UpdateEpoch(UpdateEpochRequest) returns (UpdateEpochResponse); // deprecated

    rpc SetPExtentExistence(PExtent) returns (Void);

    rpc MoveDataAndDeleteVolume(MoveDataAndDeleteVolumeRequest) returns (Volume);

    rpc ListAccessSession (Void) returns (SessionInfos);

    rpc ReleaseLease (ReleaseLeaseRequest) returns (Void);

    rpc ShowVolumes (ShowVolumesRequest) returns (ShowVolumesResponse);

    rpc UpdateClusterInfo (UpdateClusterInfoRequest) returns (Void);

    rpc ShowClusterInfo (Void) returns (ClusterInfo);

    rpc UpdateRerouteStatus (RerouteStatus) returns (Void);

    rpc ShowRerouteStatus (Void) returns (RerouteStatus);

    rpc ReportVolumeAccess(ReportVolumeAccessRequest) returns (Void);

    rpc ReserveVolumeSpace(ReserveVolumeSpaceRequest) returns (Volume);

    rpc RefreshChildExtentLocation(RefreshChildExtentLocationRequest) returns (VExtentLease);

    rpc SetUpgradeMode(SetUpgradeModeRequest) returns (Void);

    rpc GetRecoverModeInfo(Void) returns (RecoverModeInfo);     // deprecated
    rpc SetRecoverModeInfo(RecoverModeInfo) returns (Void);     // deprecated

    rpc EnableScanRecoverImmediate(Void) returns (Void);
    rpc EnableScanMigrateImmediate(Void) returns (Void);

    // Find volumes that fits some attributes
    rpc FindVolume (FindVolumeRequest) returns (VolumesResponse);

    rpc ListISCSIConnection(ListISCSIConnectionRequest) returns (ListISCSIConnectionResponse);

    rpc CheckLease(CheckLeaseRequest) returns (CheckLeaseResponse);

    rpc UpdateRebalanceAccessPointStatus(RebalanceAccessPointStatus) returns (Void);

    rpc ShowRebalanceAccessPointStatus(Void) returns (RebalanceAccessPointStatus);

    rpc CreateConsistencyGroup(CreateConsistencyGroupRequest) returns (ConsistencyGroup);

    rpc ListConsistencyGroup(ListConsistencyGroupRequest) returns (ListConsistencyGroupResponse);

    rpc ShowConsistencyGroup(ShowConsistencyGroupRequest) returns (ConsistencyGroup);

    rpc DeleteConsistencyGroup(DeleteConsistencyGroupRequest) returns (Void);

    rpc UpdateConsistencyGroup(UpdateConsistencyGroupRequest) returns (ConsistencyGroup);

    rpc CreateConsistencyGroupSnapshot(CreateConsistencyGroupSnapshotRequest) returns (ConsistencyGroupSnapshot);

    rpc ListConsistencyGroupSnapshot(ListConsistencyGroupSnapshotRequest)
        returns (ListConsistencyGroupSnapshotResponse);

    rpc ShowConsistencyGroupSnapshot(ShowConsistencyGroupSnapshotRequest) returns (ConsistencyGroupSnapshot);

    rpc UpdateConsistencyGroupSnapshot(UpdateConsistencyGroupSnapshotRequest) returns (ConsistencyGroupSnapshot);

    rpc RollbackConsistencyGroupSnapshot(RollbackConsistencyGroupSnapshotRequest) returns (Void);

    rpc DeleteConsistencyGroupSnapshot(DeleteConsistencyGroupSnapshotRequest) returns (Void);

    rpc ListNVMFConnection(ListNVMFConnectionRequest) returns (ListNVMFConnectionResponse);

    rpc ShowRecoverParameter(Void) returns (RecoverParameter);  // deprecated

    // Vhost IO permission
    rpc AuthorizeVhostIOPermission(VhostIOPermissionRequest) returns (Void);
    rpc RevokeVhostIOPermission(VhostIOPermissionRequest) returns (Void);
    rpc GetVhostIOPermission(VhostIOPermissionRequest) returns (GetVhostIOPermissionResponse);
    rpc ClearVhostIOPermission(ClearVhostIOPermissionRequest) returns (Void);
    rpc SetVhostIOPermissionSingleAccess(VhostIOPermissionRequest) returns (Void);

    rpc ReleaseBadLease (ReleaseBadLeaseRequest) returns (Void);
    rpc SpecialRecover(SpecialRecoverRequest) returns (Void);

    rpc ListTemporaryReplica(ListTemporaryReplicaRequest) returns(ListTemporaryReplicaResponse);
    rpc GetTemporaryReplica(GetTemporaryReplicaRequest) returns(GetTemporaryReplicaResponse);
    rpc GetTemporaryReplicaSummary(Void) returns(GetTemporaryReplicaSummaryResponse);

    rpc BatchCreateSnapshots(BatchCreateSnapshotsRequest) returns(BatchCreateSnapshotsResponse);
    rpc ReclaimVolumeTemporaryReplica(ReclaimVolumeTemporaryReplicaRequest) returns(ReclaimVolumeTemporaryReplicaResponse);

    rpc UpdateRepositionParams(UpdatableRepositionParams) returns(Void);
    rpc UpdateRecoverParams(UpdatableRecoverParams) returns(Void); // deprecated
    rpc UpdateMigrateParams(UpdatableMigrateParams) returns(Void); // deprecated

    rpc ShowRepositionParams(Void) returns(RepositionParamsResponse);
    rpc ShowRecoverParams(Void) returns(RecoverParamsResponse);
    rpc ShowMigrateParams(Void) returns(MigrateParamsResponse);

    rpc DeallocPerfExtents(DeallocPerfExtentsRequest) returns(Void);

    // DumpDb V2
    rpc CreateConsistenceMetaDBSnapshot(CreateConsistenceMetaDBSnapshotRequest) returns (CreateConsistenceMetaDBSnapshotResponse);
    rpc DumpMetaDBSnapshot(DumpMetaDBSnapshotRequest) returns (DumpMetaDBSnapshotResponse);

    rpc SinkVolume(VolumePath) returns (Void);
    rpc GCImmediate(Void) returns (Void);

    rpc UpdateDrainParams(UpdatableDrainParams) returns (Void);
    rpc UpdateSinkParams(UpdatableSinkParams) returns (Void);

    rpc ShowDrainParams(Void) returns (DrainParamsResponse);
    rpc ShowSinkParams(Void) returns (SinkParamsResponse);

    rpc ListDrain(Void) returns (DrainCmds);

    rpc GetLExtent(GetLExtentRequest) returns (LExtent);

    rpc SwitchDrain (SwitchRequest) returns (Void);
    rpc SinkManagerScanImmediate (Void) returns (Void);

    rpc ShowRepositionExtentsSummary(Void) returns (RepositionExtentsSummaryResponse);
    rpc ShowRepositionDetailedPids(ChunkId) returns (RepositionDetailedPids);

    rpc SwitchMetric (SwitchRequest) returns (Void);
    rpc SwitchTrace (SwitchTraceRequest) returns (Void);

    rpc RefreshChildLExtentLocation(RefreshChildLExtentLocationRequest) returns (Lease);

    rpc DeallocCapExtents(DeallocCapExtentsRequest) returns(Void);

    rpc UpdateInternalIOParams(UpdatableInternalIOParams) returns (Void);
    rpc ShowInternalIOParams(Void) returns (InternalIOParamsResponse);
    rpc UpdateVhostIOPermission(UpdateVhostIOPermissionRequest) returns (Void);

    rpc ShowVolumeAccess(ShowVolumeAccessRequest) returns (ShowVolumeAccessResponse);
    rpc ListVolumeAccess(ListVolumeAccessRequest) returns (ListVolumeAccessResponse);

    rpc ShowElevateTask(Void) returns (ElevateTaskRecords);

    rpc GetLExtentRef (GetExtentRequest) returns (GetExtentRefResponse);

    rpc GetMetaLeaderDurationMS(Void) returns (GetMetaLeaderDurationMSResponse);

    /**************************************************/
    /* kmip management                          */
    /**************************************************/

    rpc CreateKmipCluster(CreateKmipClusterRequest) returns (KMIPClusterResponse);
    rpc DeleteKmipCluster(DeleteKmipClusterRequest) returns (Void);
    rpc ListKmipCluster(Void) returns (ListKmipClusterResponse);
    rpc UpdateKmipCluster(UpdateKmipClusterRequest) returns (KMIPClusterResponse);

    rpc AddKmipServer(AddKmipServerRequest) returns (KMIPServer);
    rpc UpdateKmipServer(UpdateKmipServerRequest) returns (KMIPServer);
    rpc DeleteKmipServer(DeleteKmipServerRequest) returns (Void);

    rpc AddKmipAuth(AddKmipAuthRequest) returns (KMIPAuth);
    rpc UpdateKmipAuth(UpdateKmipAuthRequest) returns (KMIPAuth);
    rpc DeleteKmipAuth(DeleteKmipAuthRequest) returns (Void);

    rpc ImportEncryptKeys(ImportEncryptKeyRequest) returns (Void);
    rpc ExportAllEncryptKeys(ExportAllEncryptKeyRequest) returns (ExportAllEncryptKeyResponse);

    rpc RotateKey(RotateKeyRequest) returns (Void);

    rpc ListKmipServersConnectivity(ListKmipServersConnectivityRequest) returns (ListKmipServersConnectivityResponse);
    rpc RefreshKmipCluster(RefreshKmipClusterRequest) returns (KMIPClusterResponse);

    rpc ListMasterKeyIds(Void) returns (ListMasterKeyIdsResponse);

    rpc SweepRecycleBinImmediate(Void) returns (RecycleBinConfig);
    rpc BatchSweepTrashVolumes(BatchSweepTrashVolumesRequest) returns(Void);
    rpc RestoreTrashVolume(RestoreTrashVolumeRequest) returns (RestoreTrashVolumeResponse);
    rpc ShowTrashPool(Void) returns (Pool);
    rpc ShowTrashVolume(ShowTrashVolumeRequest) returns (Volume);
    rpc ListTrashVolume(ListTrashVolumeRequest) returns (ListTrashVolumeResponse);
    rpc ListTrashSnapshot(ListTrashSnapshotRequest) returns (ListTrashSnapshotResponse);

    rpc GatherVolumeLocalSegments(GatherVolumeLocalSegmentsRequest) returns (GatherVolumeLocalSegmentsResponse);
    rpc ClearManualMigrate(Void) returns (Void);
}

// chunk service
service ChunkService {
    option (rpc_service_id) = 1; // FIXME(keyue) change to 4001 in next major release
    // storage pool //
    rpc CreateStoragePool (CreateStoragePoolRequest) returns (StoragePool);
    rpc DeleteStoragePool (StoragePoolId) returns (StoragePool);
    rpc UpdateStoragePool (UpdateStoragePoolRequest) returns (StoragePool);
    rpc ListStoragePool (Void) returns (StoragePools);
    rpc ShowStoragePool (StoragePoolId) returns (StoragePool);
    rpc AddChunkToStoragePool (AddChunkToStoragePoolRequest) returns (StoragePool);
    rpc RemoveChunkFromStoragePool (RemoveChunkFromStoragePoolRequest) returns (StoragePool);

    // topo //
    rpc CreateTopoObj (CreateTopoObjRequest) returns (TopoObj);
    rpc DeleteTopoObj (TopoObjId) returns (TopoObj);
    rpc UpdateTopoObj (UpdateTopoObjRequest) returns (TopoObj);
    rpc ListTopoObj (TopoObjId) returns (TopoObjs);
    rpc ShowTopoObj (TopoObjId) returns (TopoObj);

    // chunk //
    rpc RegisterChunk (Chunk) returns (Chunk);
    rpc ListChunk (Void) returns (Chunks);
    rpc ShowChunk (ChunkId) returns (Chunk);
    rpc RemoveChunk (ChunkId) returns (Void);
    rpc GetChunkTopology (ChunkId) returns (ChunkTopology);

    // chunk notify the meta it is leaving (out of service)
    rpc LeaveService (ChunkId) returns (Void);  // deprecated

    // show chunk pids and space info
    rpc ListPid (ChunkId) returns (ChunkPids);

    // Maintenance
    rpc SetMaintenanceMode(SetMaintenanceModeRequest) returns (Chunk);

    // cancel chunk removing //
    rpc CancelChunkRemoving (CancelChunkRemovingRequest) returns (Void);

    rpc GetMaintenanceInfo(Void) returns (GetMaintenanceInfoResponse);

    // isolate //
    rpc BanChunk (BanChunkRequest) returns (Void);
    rpc UnbanChunk (UnbanChunkRequest) returns (Void);
    rpc SetChunkIsolatePolicy (SetChunkIsolatePolicyRequest) returns (Void);
    rpc GetChunkIsolatePolicy (ChunkId) returns (ChunkIsolateFlag);
    rpc GetChunkIsolateInfo (Void) returns (GetChunkIsolateInfoResponse);

    // prioritized
    rpc GetDefaultPrioSpaceRatio    (Void)                              returns (DefaultPrioSpaceInfo);
    rpc SetDefaultPrioSpaceRatio    (SetDefaultPrioSpaceRatioRequest)   returns (Void);
    rpc SetChunkPrioSpaceRatio      (SetChunkPrioSpaceRatioRequest)     returns (ChunkPrioSpaceInfoList);
    rpc GetChunkPrioSpaceInfo       (ChunkId)                           returns (ChunkPrioSpaceInfo);
    rpc ListChunkPrioSpaceInfo      (Void)                              returns (ChunkPrioSpaceInfoList);

    rpc ListNode(Void) returns (Nodes);
    rpc ShowNode(ShowNodeRequest) returns (NodeObj);
}

message GetChunkConnectivitiesResponse {
    repeated ChunkConnectivity chunk_conns = 1;
}

service StatusService {
    option (rpc_service_id) = 2; // FIXME(keyue) change to 4002 in next major release
    // Get the summary of the cluster
    rpc GetClusterSummary (Void) returns (ClusterSummary);

    // Get ths summary of this meta server
    rpc GetMetaSummary (Void) returns (MetaSummary);

    // get cluster perf
    rpc GetClusterPerf (Void) returns (ClusterPerf);

    rpc ShowClusterStatus (Void) returns (ClusterStatus);

    // get data channel connectivity for chunks
    rpc GetChunkConnectivities (Void) returns (GetChunkConnectivitiesResponse);
    rpc ShowChunkConnectivity (ChunkId) returns (ChunkConnectivity);
    rpc ShowAbilityState (Void) returns (AbilityState);
}

service NFSService {
    option (rpc_service_id) = 3; // FIXME(keyue) change to 4003 in next major release
    rpc CreateExport (Pool) returns (Pool);
    rpc UpdateExport (UpdatePoolRequest) returns (Pool);
    rpc DeleteExport (PoolPath) returns (Void);
    rpc ListExport (Void) returns (PoolsResponse);
    rpc CreateInode (CreateInodeRequest) returns (NFSInode);
    rpc DeleteInode (DeleteInodeRequest) returns (DeleteInodeResponse);
    rpc RenameInode (RenameInodeRequest) returns (RenameInodeResponse);
    // nfs3 set attr request
    rpc SetAttr (SetAttrRequest) returns (NFSInode);
    // nfs3 get attr request: getattr is a frequently accessed interface
    rpc GetAttr (InodeId) returns (NFSAttr);
    rpc ShowInode (InodeId) returns (NFSInode);
    rpc LookupInode (LookupInodeRequest) returns (NFSInode);
    rpc FindInode (FindInodeRequest) returns (NFSInode);
    rpc ListInode (ListInodeRequest) returns (NFSInodes);
    rpc TruncateFile (TruncateRequest) returns (NFSInode);
    rpc GetSizeInfo (InodeId) returns (SizeInfo);
    // mount table
    rpc Mount (MountPoint) returns (Void);
    rpc Umount (MountPoint) returns (Void);
    rpc UmountAll (Void) returns (Void);
    rpc ShowMount (Void) returns (MountTable);
    // snapshot management
    rpc CreateSnapshot(CreateNFSSnapshotRequest) returns (Volume);
    rpc ListSnapshot(NFSSnapshotPath) returns (SnapshotsResponse);
    rpc UpdateSnapshot(UpdateNFSSnapshotRequest) returns (Volume);
    rpc DeleteSnapshot(DeleteNFSSnapshotRequest) returns (Void);
    rpc RollbackSnapshot(RollbackNFSSnapshotRequest) returns (Void);
    rpc ShowSnapshot(NFSSnapshotPath) returns (Volume);
    rpc MoveSnapshot(MoveNFSSnapshotRequest) returns (Volume);
    // hardlink
    rpc CreateHardlink(CreateHardlinkRequest) returns (NFSInode);
    // move a file between two exports
    rpc MoveFile (MoveFileRequest) returns (NFSInode);
    // batched set attr requests for syncing attr cache, doesn't handle guard
    // and size
    rpc BatchedSetAttr (BatchedSetAttrRequest) returns (BatchedSetAttrResponse);
    // storage object convert
    rpc ConvertLunIntoFile (ConvertLunIntoFileRequest) returns (NFSInode);
    rpc ConvertVolumeIntoFile (ConvertVolumeIntoFileRequest) returns (NFSInode);
    rpc UpdateFile(UpdateNFSFileRequest) returns(Void);
}

service ISCSIService {
    option (rpc_service_id) = 4; //FIXME(keyue) change to 4004
    // target management
    rpc CreateTarget (CreateISCSITargetRequest) returns (ISCSITarget);
    rpc UpdateTarget (UpdateISCSITargetRequest) returns (ISCSITarget);
    rpc DeleteTarget (PoolPath) returns (Void);
    rpc ListTarget (Void) returns (ISCSITargetsResponse);
    rpc GetTarget (PoolPath) returns (ISCSITarget);

    // lun management
    rpc CreateLun (CreateISCSILunRequest) returns (ISCSILun);
    rpc UpdateLun (UpdateISCSILunRequest) returns (ISCSILun);
    rpc DeleteLun (DeleteLunRequest) returns (Void);
    rpc ListLun (PoolPath) returns (ISCSILunsResponse);
    rpc GetLun (LunPath) returns (ISCSILun);
    rpc MoveLun (MoveISCSILunRequest) returns (ISCSILun);

    // snapshot management
    rpc CreateSnapshot(CreateISCSISnapshotRequest) returns (Volume);
    rpc ListSnapshot(LunPath) returns (SnapshotsResponse);
    rpc UpdateSnapshot(UpdateISCSISnapshotRequest) returns (Volume);
    rpc DeleteSnapshot(DeleteISCSISnapshotRequest) returns (Void);
    rpc RollbackSnapshot(ISCSISnapshotPath) returns (Void);
    rpc ShowSnapshot(ISCSISnapshotPath) returns (Volume);
    rpc MoveSnapshot(MoveISCSISnapshotRequest) returns (Volume);

    // SPC2 reserve/release
    rpc Spc2Reserve(ISCSISpc2ReserveRequest) returns (Void);
    rpc Spc2Release(ISCSISpc2ReleaseRequest) returns (Void);

    // storage object convert
    rpc ConvertVolumeIntoLun (ConvertVolumeIntoLunRequest) returns (ISCSILun);

    // get target/lun info by lun uuid
    rpc GetTargetAndLunInfo (LunPath) returns(TargetAndLunInfo);

    // List target portals with its iqn name
    rpc ListTargetPortals (ListTargetPortalsRequest) returns (TargetPortals); // deprecated

    rpc ListTargets(ListTargetsRequest) returns (ISCSITargetsResponse);

    rpc SyncTargets(SyncTargetsRequest) returns (ISCSITargetsResponse);

    // for LUN masking
    rpc AddLunAllowedInitiators (AddLunAllowedInitiatorsRequest) returns (ISCSILun);
    rpc RemoveLunAllowedInitiators (RemoveLunAllowedInitiatorsRequest) returns (ISCSILun);

    rpc SyncPRInfoXchg(PRRequest) returns (PRResponse);
    rpc ResetLunPr(ResetLunPrRequest) returns (Void);

    // for iscsi access balance
    rpc GetServicePortal(GetServicePortalRequest) returns (ISCSIServicePortal);

    rpc GetAccessRecord(GetAccessRecordRequest) returns (GetAccessRecordResponse);

    rpc SetAccessRecord(SetAccessRecordRequest) returns (SetAccessRecordResponse);

    rpc BatchDeleteLuns(BatchDeleteLunsRequest) returns (Void);
}

service NVMFService {
    option (rpc_service_id) = 5;

    // DistSubsystem management
    rpc CreateDistSubsystem (CreateNVMFDistSubsystemRequest) returns (NVMFDistSubsystem);
    rpc UpdateDistSubsystem (UpdateNVMFDistSubsystemRequest) returns (NVMFDistSubsystem);
    rpc GetDistSubsystem (PoolPath) returns (NVMFDistSubsystem);
    rpc DeleteDistSubsystem (PoolPath) returns (Void);
    rpc ListDistSubsystems(ListNVMFDistSubsystemsRequest) returns (NVMFDistSubsystemsResponse);

    // DistNamespace management
    rpc CreateDistNamespace (CreateNVMFDistNamespaceRequest) returns (NVMFDistNamespace);
    rpc UpdateDistNamespace (UpdateNVMFDistNamespaceRequest) returns (NVMFDistNamespace);
    rpc DeleteDistNamespace (DeleteDistNamespaceRequest) returns (Void);
    rpc ListDistNamespace (PoolPath) returns (NVMFDistNamespacesResponse);
    rpc ListDistNamespaceByGroupPath (DistNamespaceGroupPath) returns (NVMFDistNamespacesResponse);
    rpc GetDistNamespace (DistNamespacePath) returns (NVMFDistNamespace);

    // DistNamespaceGroup management
    rpc CreateDistNamespaceGroup (DistNamespaceGroupPath) returns (NVMFDistNamespaceGroup);
    rpc UpdateDistNamespaceGroup (UpdateNVMFDistNamespaceGroupRequest) returns (NVMFDistNamespaceGroup);
    rpc DeleteDistNamespaceGroup (DistNamespaceGroupPath) returns (Void);
    rpc ListDistNamespaceGroup (PoolPath) returns (NVMFDistNamespaceGroupsResponse);
    rpc GetDistNamespaceGroup (DistNamespaceGroupPath) returns (NVMFDistNamespaceGroup);

    // snapshot management
    rpc CreateSnapshot(CreateNVMFSnapshotRequest) returns (Volume);
    rpc ListSnapshot(DistNamespacePath) returns (SnapshotsResponse);
    rpc UpdateSnapshot(UpdateNVMFSnapshotRequest) returns (Volume);
    rpc DeleteSnapshot(DeleteNVMFSnapshotRequest) returns (Void);
    rpc RollbackSnapshot(NVMFSnapshotPath) returns (Void);
    rpc ShowSnapshot(NVMFSnapshotPath) returns (Volume);

    rpc GetNVMFOptimizedAccess(GetNVMFOptimizedAccessRequest) returns (NVMFOptimizedPaths);
    rpc SetNVMFOptimizedAccess(SetNVMFOptimizedAccessRequest) returns (NVMFOptimizedPaths);
    rpc GetAccessRecord(GetNVMFAccessRecordRequest) returns (GetNVMFAccessRecordResponse);
    rpc SetAccessRecord(SetNVMFAccessRecordRequest) returns (SetNVMFAccessRecordResponse);

    rpc AcquireNVMFIOPermission(AcquireNVMFIOPermissionRequest) returns (Void);
    rpc ListTargets(Void) returns (NVMFTargetsResponse);

    rpc AddDistNamespaceAllowedHost(NVMFNamespaceAllowedHost) returns (NVMFDistNamespace);
    rpc RemoveDistNamespaceAllowedHost(NVMFNamespaceAllowedHost) returns (NVMFDistNamespace);

    rpc GetNVMFVolumeAccessRecords(GetNVMFVolumeAccessRecordsRequest) returns (NVMFVolumeAccessRecords);

    rpc BatchDeleteDistNamespaces(BatchDeleteDistNamespacesRequest) returns (Void);
}

service CDPService {
    option (rpc_service_id) = 6;

    // CDP job management
    rpc CreateJob(CreateCDPJobRequest) returns (CDPJobInfo);
    rpc GetJob(GetCDPJobRequest) returns (CDPJobInfo);
    rpc GetJobByVolume(GetCDPJobByVolumeRequest) returns (CDPJobInfo);
    rpc FinishJob(FinishCDPJobRequest) returns (Void);
    rpc CancelJob(CancelCDPJobRequest) returns (Void);
    rpc ListJobs(ListCDPJobsRequest) returns (ListCDPJobsResponse);
    rpc CreateJobsByGroup(CreateCDPJobsByGroupRequest) returns (CreateCDPJobsByGroupResponse);
    rpc FinishJobsByGroup(FinishCDPJobsByGroupRequest) returns (Void);
    rpc CancelJobsByGroup(CancelCDPJobsByGroupRequest) returns (Void);
    rpc DeleteJob(DeleteCDPJobRequest) returns (Void);
    rpc DeleteJobsByGroup(DeleteCDPJobsByGroupRequest) returns (Void);
    rpc SyncJobStage(CDPJobStageUpdate) returns (Void);
}
