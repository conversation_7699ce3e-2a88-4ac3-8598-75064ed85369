syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

// we need include this google's proto file which is located in
// /usr/include/google/protobuf/descriptor.proto
// to enable us to use google.protobuf.ServiceOptions
// please make sure import is before package
import "google/protobuf/descriptor.proto";

package zbs;

import "error.proto";
import "options.proto";

extend google.protobuf.ServiceOptions {
    optional uint32 rpc_service_id = 1000;
}

message TimeSpec {
    required int64 seconds = 1;
    required int64 nseconds = 2;
}

message RpcErrorDetail {
    optional  ErrorCode  error_code    =  1;
    optional  string     error_message =  2;
}

message RpcStatus {
    required  bool            ok        =  1  [default =  true];
    repeated  RpcErrorDetail  errors    =  2;
    optional  UserCode        user_code =  3;
}

message Void {
}

message Bool {
   required bool value = 1;
}

message UID {
    required uint64 low_bits = 1;
    required uint64 high_bits = 2;
}

message Label {
    required bytes key = 1 [(zbs.labels).as_str = true];
    required bytes value = 2 [(zbs.labels).as_str = true];
}

message Labels {
    repeated Label labels = 1;
}

message ResetLabelsParams{
    optional Labels labels = 1;
    optional bool force = 2 [default = false];
}

message Address {
    required string ip = 1;
    required int32 port = 2;
}

message Addresses {
    repeated Address addrs = 1;
}

message Pagination {
    // for pagination
    optional uint64 pos = 1 [default = 0];
    optional uint64 num = 2 [default = 512];
    optional string last_key = 3;
}

message CounterInfo {
    required string name        = 1;
    required uint64 duration    = 2;  // second

    required uint64 total_value = 11;
    required uint64 total_time  = 12;
    required uint64 total_cnt   = 13;

    required uint64 last_value  = 21;
    required uint64 last_time   = 22;
    required uint64 last_cnt    = 23;

    required uint64 cur_value   = 31;
    required uint64 cur_time    = 32;
    required uint64 cur_cnt     = 33;
}

message ExtLocation {
    // To support at most 24 ECShards, ExtLocation will be enlarged to store 32(2^5) chunk ids, the first 8 chunk ids
    // will be stored in field1, the next 8 chunk ids will be in field2 and so on.
    optional uint64 field1 = 1 [default = 0];
    optional uint64 field2 = 2 [default = 0];
    optional uint64 field3 = 3 [default = 0];
    optional uint64 field4 = 4 [default = 0];
}

message VExtent {
    required uint32 vextent_id     = 1; // vextent table, max size: 64KB
    required uint32 location       = 2;  // pextent locations;
    required uint32 alive_location = 3;
}

message SessionInfo {
    required bytes uuid = 1 [(zbs.labels).as_str = true];
    required bytes ip = 2 [(zbs.labels).as_str = true];
    optional uint32 num_ip = 3;  // num representation of ip
    required uint32 port = 4;
    optional uint32 cid = 5;  // chunk cid if session is a chunk session
    optional uint32 local_cid = 6;  // closes chunk to this session (e.g. local chunk)
    optional bytes secondary_data_ip = 7 [(zbs.labels).as_str = true];
    optional bytes zone = 8 [(zbs.labels).as_str = true];
    optional bytes scvm_mode_host_data_ip = 9 [(zbs.labels).as_str = true];
    optional uint64 alive_sec             = 10;
    optional string machine_uuid = 11;
    optional bool avoid_nfs_access_point = 12 [default = false];
}

message SessionInfos {
    repeated SessionInfo infos = 1;
}

message RecoverSummary {
    optional uint64      pending_migrates_bytes = 1;
    optional uint64      pending_recovers_bytes = 2;
    optional uint64 recover_speed                    = 3 [deprecated = true];  // bps
    optional uint64 migrate_speed                    = 4 [deprecated = true];  // bps
    optional uint64 recover_migrate_speed            = 5 [deprecated = true];  // bps
    optional uint64 cross_zone_recover_speed         = 6 [deprecated = true];  // bps
    optional uint64 cross_zone_migrate_speed         = 7 [deprecated = true];  // bps
    optional uint64 cross_zone_recover_migrate_speed = 8 [deprecated = true];  // bps
    optional uint64 ongoing_migrates_bytes = 9;
    optional uint64 ongoing_recovers_bytes = 10;
}

message LayerLease {
    optional uint32 pid                                   = 1;
    optional ExtLocation location                         = 2;
    optional uint32 origin_pid                            = 3 [default = 0];
    optional uint64 epoch                                 = 4 [default = 0];
    optional uint64 origin_epoch                          = 5 [default = 0];
    optional bool ever_exist                              = 6 [default = false];
    optional uint64 meta_generation                       = 7 [default = 0];  // meta-recorded gen
    optional uint32 expected_replica_num                  = 8;
    optional bool thin_provision                          = 9 [default = true];
    optional ResiliencyType resiliency_type               = 10 [default = RT_REPLICA];
    optional bool cow_from_snapshot                       = 11 [default = false];

    // for replica
    repeated TemporaryReplica temporary_replicas          = 20;
    repeated TemporaryReplica lossy_temporary_replicas    = 21;

    // for ec
    optional ECAlgorithmParam ec_param                    = 30;
}

enum LeaseVersion {
    LV_UNLAYERED = 0;
    LV_CAP_ONLY = 1;
    LV_LAYERED = 2;
}

// * UNLAYERED lease, pid and epoch is filled with the capacity pid and pepoch for old version (less than 5.6.x) compatibility during upgradation;
// * CAP_ONLY lease, pid and epoch is filled with the logical extent id and epoch, cap_lease is filled with cap
//   pextent info. For these vextent IO, the lease owner can directly read and write the capacity pextent;
// * LAYERED lease, pid and epoch is filled with the logical extent id and epoch, perf_lease and cap_lease are filled
//   with perf and cap pextent info. VExtent write IO is preferred to perform on perf pextent.
message Lease {
    // owner info
    optional SessionInfo owner        = 1;

    optional uint32 pid               = 10;
    optional uint32 location          = 11;
    optional uint32 origin_pid        = 12 [default = 0];
    optional uint64 epoch             = 13 [default = 0];
    optional uint64 origin_epoch      = 14 [default = 0];
    optional bool   ever_exist        = 15 [default = false];
    optional uint64 meta_generation   = 16 [default = 0];  // meta-recorded gen
    optional uint32 expected_replica_num = 17;
    optional bool thin_provision = 18 [default = true];

    repeated Chunk chunks             = 30;
    repeated TemporaryReplica temporary_replicas = 31;
    repeated TemporaryReplica lossy_temporary_replicas = 32;

    optional bool prioritized = 33 [default = false];

    optional LayerLease perf_lease    = 34;
    optional LayerLease cap_lease     = 35;
    optional bool layered = 36;
    // whether perf extent can sink without violate the parent capacity read-only constraint
    optional bool sinkable = 37 [default = false];
    optional LeaseVersion version = 38 [default = LV_UNLAYERED];
    optional bool cow_from_snapshot = 39 [default = false];

    optional uint64 encrypt_metadata_id = 40;
    optional uint32 vextent_no = 41;
}

message VExtentLease {
    required Lease lease = 1;
    optional bool cow = 2;
}

message PExtentResp {
    required uint32 pid      = 1;
    optional uint32 location = 2 [default = 0];

    optional uint32 alive_location = 21;

    optional uint32 expected_replica_num = 12;
    optional bool ever_exist             = 13;

    optional bool garbage      = 14 [default = false];
    optional uint32 origin_pid = 15;

    optional uint64 epoch        = 16 [default = 0];
    optional uint64 origin_epoch = 17 [default = 0];
    optional uint64 generation   = 18 [default = 0];

    optional uint32 preferred_cid = 19 [default = 0];
    optional uint32 owner_cid     = 20 [default = 0];

    optional bool thin_provision = 22 [default = true];
    repeated TemporaryReplica temporary_replicas = 23;
    optional uint64 allocated_space = 24 [default = 0];  // Bytes
    repeated TemporaryReplica lossy_temporary_replicas = 25;

    optional ResiliencyType resiliency_type = 26 [default = RT_REPLICA];
    optional PExtentType type = 28 [default = PT_CAP];
    optional bool sinkable = 29 [default = false];

    optional bool prioritized = 30 [default = false, deprecated = true];
    optional uint64 downgraded_prioritized_space = 31 [default = 0, deprecated = true];  // Bytes

    optional ExtLocation ec_location = 32;
    optional ExtLocation ec_alive_location = 33;

    optional uint32 shared_space = 35 [default = 0]; // Bytes

    optional bool is_cow = 36;
}

message TemporaryReplica {
    required uint32 temporary_pid = 1;
    required uint64 temporary_epoch = 2;
    required uint32 failed_cid = 3;
    required uint64 create_at_generation = 4;
    optional uint32 location = 5 [default = 0];
    optional bool ever_exist = 6 [default = false];
    optional uint64 meta_generation = 7 [default = 0];
    optional uint32 pid = 8;
    optional uint64 epoch = 9;
    optional bool lossy = 10 [default = false];
    optional bool thin_provision = 11 [default = false];
}

message LExtent {
    required uint32 id = 1;
    optional uint64 epoch = 2 [default = 0];

    optional uint64 perf_epoch = 3 [default = 0];
    optional uint32 perf_pid   = 4 [default = 0];

    // For Logical Extent, capacity physical extent should always exist.
    optional uint64 cap_epoch = 5;
    optional uint32 cap_pid  = 6;

    optional bool prioritized = 7 [default = false];

    // encrypt metadata id = 0 means that the logical extent is related to non-encrypted volume.
    optional uint64 encrypt_metadata_id = 8 [default = 0];

    // `vextent_no` is introduced since v5.6.2, The default value of `vextent_no` is UINT32_MAX. By now this field is
    // only used by encrypted volume, which will be created only in the version greater than v5.6.2.
    optional uint32 vextent_no = 9 [default = 4294967295];
}

// for backward compatibility, LExtentResp should contain the same fields of PExtentResp
message LExtentResp {
    // the following fields are same as PExtentResp, while some unused fields are marked as deprecated
    required uint32 id      = 1;
    // when LExtent ever_exist is true, location will be set as uint32_MAX. Some old Extent-granularity incremental
    // backup APPs use it to check if the Extent has data
    optional uint32 location = 2 [default = 0];

    optional uint32 alive_location = 21 [deprecated = true];

    optional uint32 expected_replica_num = 12 [deprecated = true];
    optional bool ever_exist             = 13;

    optional bool garbage      = 14 [deprecated = true];
    optional uint32 origin_pid = 15 [deprecated = true];

    optional uint64 epoch        = 16 [default = 0];
    optional uint64 origin_epoch = 17 [deprecated = true];
    optional uint64 generation   = 18 [deprecated = true];

    optional uint32 preferred_cid = 19 [default = 0];
    optional uint32 owner_cid     = 20 [default = 0];

    optional bool thin_provision = 22 [deprecated = true];
    repeated TemporaryReplica temporary_replicas = 23 [deprecated = true];
    optional uint64 allocated_space = 24 [deprecated = true];
    repeated TemporaryReplica lossy_temporary_replicas = 25 [deprecated = true];

    optional bool prioritized = 30;
    optional uint64 downgraded_prioritized_space = 31 [deprecated = true];

    // the following fields are newly added, which are not in PExtentResp
    optional uint64 perf_epoch = 34 [default = 0];
    optional uint32 perf_pid   = 35 [default = 0];

    // For Logical Extent, capacity physical extent should always exist.
    optional uint64 cap_epoch = 36;
    optional uint32 cap_pid  = 37;

    optional bool is_cow = 38;
}

message PExtent {
    required uint32 pid      = 1;
    optional uint32 location = 2 [default = 0];

    optional uint32 alive_location = 21;

    optional uint32 expected_replica_num = 12;
    optional bool ever_exist = 13;

    optional bool garbage = 14 [default = false];
    optional uint32 origin_pid = 15;

    optional uint64 epoch = 16 [default = 0];
    optional uint64 origin_epoch = 17 [default = 0];
    optional uint64 generation = 18 [default = 0];

    optional uint32 preferred_cid = 19 [default = 0];
    optional bool thin_provision = 20 [default = true];
    optional bool read_only = 22 [default = false];

    repeated TemporaryReplica temporary_replicas = 23;
    repeated TemporaryReplica lossy_temporary_replicas = 24;
    optional uint64 allocated_space = 25 [default = 0];  // Bytes

    optional ResiliencyType resiliency_type = 26 [default = RT_REPLICA];
    optional ECAlgorithmParam ec_param = 27;
    optional PExtentType type = 28 [default = PT_CAP];
    optional bool sinkable = 29 [default = false];

    optional bool prioritized = 30 [default = false, deprecated = true];
    optional uint64 downgraded_prioritized_space = 31 [default = 0, deprecated = true];  // Bytes

    optional ExtLocation ec_location = 32;
    optional ExtLocation ec_alive_location = 33;

    // This flag is only used when this pextent has origin.
    optional bool cow_from_snapshot = 34 [default = false];

    optional uint32 shared_space = 35 [default = 0]; // Bytes
}

// must equal to enum CodingTechnique in algorithm_info.h
enum ECCodingTechnique {
    REED_SOL_VAN = 0;
    CAUCHY_GOOD = 1;
    ZFEC = 2;
}

enum ECAlgorithmType {
    REED_SOLOMON = 0;
    LRC = 1;
    CLAY = 2;
}

message ECReedSolArg {
    optional uint32 w                      = 1 [default = 8];
    optional ECCodingTechnique coding_tech = 2 [default = REED_SOL_VAN];
};

message ECLrcArg {
    optional uint32 l                    = 1;

    optional string rs_algo_name        = 2;
    optional ECReedSolArg rs_algo_arg   = 3;
}

message ECClayArg {
    optional uint32 d                      = 1 [default = 0];

    optional string mds_algo_name         = 2;
    optional ECReedSolArg mds_algo_arg    = 3;

    optional string pft_algo_name         = 4;
    optional ECReedSolArg pft_algo_arg    = 5;

    optional uint32 sub_block_size         = 6 [default = 1];
}

message ECAlgorithmParam {
    optional string name             = 1;
    optional uint32 k                 = 2;
    optional uint32 m                 = 3;

    optional ECReedSolArg rs_arg     = 4;
    optional ECLrcArg lrc_arg        = 5;
    optional ECClayArg clay_arg      = 6;

    optional uint32 block_size       = 7 [default = 4096];
    optional ECAlgorithmType ec_type = 8 [default = REED_SOLOMON];
};

enum ResiliencyType {
    RT_REPLICA = 0;
    RT_EC = 1;
}

enum PExtentType {
    PT_CAP = 0;
    PT_PERF = 1;
}

enum PExtentKind {
    PK_PERF_THICK = 0;
    PK_PERF_THIN = 1;
    PK_CAP = 2;
}

enum SpaceLoad {
    LOAD_LOW = 0;
    LOAD_MEDIUM = 1;
    LOAD_HIGH = 2;
}

enum StatsPerfType {
    CAP_REPLICA_APP = 0;
    PERF_REPLICA_APP = 1;
    CAP_EC_APP = 2;

    // perf write: promote & unmap during sink
    // perf read: sink speed
    // cap read: ec read during block update
    // cap write: sink speed with write amplification
    CAP_REPLICA_SINK = 3;
    PERF_REPLICA_SINK = 4;
    CAP_EC_SINK = 5;

    CAP_REPLICA_REPOSITION = 6;
    PERF_REPLICA_REPOSITION = 7;
    CAP_EC_REPOSITION = 8;
}

message StoragePerf {
    optional float iops             = 1 [default = 0];        // io per second
    optional float bandwidth        = 2 [default = 0];        // Bytes per second
    optional float latency          = 3 [default = 0];        // nano seconds
    optional float avg_request_size = 4 [default = 0];        // Bytes
    optional float cache_hit_rate   = 5 [deprecated = true];  // 0.0~1.0
}

message RecoverPerf {
    optional uint64 recover_migrate_speed            = 1;
    optional uint64 recover_speed                    = 2;
    optional uint64 migrate_speed                    = 3;
    optional uint64 cross_zone_recover_speed         = 4;
    optional uint64 cross_zone_migrate_speed         = 5;
    optional uint64 cross_zone_recover_migrate_speed = 6;
    optional uint64 cross_zone_recover_read_speed    = 7;
    optional uint64 cross_zone_recover_write_speed   = 8;
    optional uint64 cross_zone_migrate_read_speed    = 9;
    optional uint64 cross_zone_migrate_write_speed   = 10;
}

message ReplicaIOPerf {
    required uint32 from_cid        = 1;
    required uint32 to_cid          = 2;
    optional StoragePerf total_perf = 3;
    optional StoragePerf read_perf  = 4;
    optional StoragePerf write_perf = 5;
}

message LocalIOPerf {
    // IO received from remote lease owner
    optional StoragePerf from_remote_total_perf    = 2;
    optional StoragePerf from_remote_read_perf     = 3;
    optional StoragePerf from_remote_write_perf    = 4;
    optional float from_remote_throttle_latency_ns = 5;

    // IO received from local lease owner
    optional StoragePerf from_local_total_perf     = 6;
    optional StoragePerf from_local_read_perf      = 7;
    optional StoragePerf from_local_write_perf     = 8;
    optional float from_local_throttle_latency_ns  = 9;
}

message AccessPerf {
    optional StoragePerf read_perf             = 1;
    optional StoragePerf write_perf            = 2;
    optional StoragePerf total_perf            = 3;
    optional StoragePerf cross_zone_read_perf  = 4;
    optional StoragePerf cross_zone_write_perf = 5;
    optional StoragePerf cross_zone_total_perf = 6;
    optional float hard_rate                   = 7;
    optional float retry_rate                  = 8;
    optional float timeout_rate                = 9;
    repeated ReplicaIOPerf replica_io_perfs    = 10;
    optional LocalIOPerf local_io_perf         = 11;
    optional StoragePerf promote_perf          = 12;
}

message LSMDBPerf {
    optional float get_rate              = 1;
    optional float get_latency_ns        = 2;
    optional float max_get_latency_ns    = 3;
    optional float put_rate              = 4;
    optional float put_latency_ns        = 5;
    optional float max_put_latency_ns    = 6;
    optional float write_rate            = 7;
    optional float write_latency_ns      = 8;
    optional float max_write_latency_ns  = 9;
    optional float delete_rate           = 10;
    optional float delete_latency_ns     = 11;
    optional float max_delete_latency_ns = 12;
}
message JournalPerf {
    optional bytes path           = 1 [(zbs.labels).as_str = true];
    optional StoragePerf perf     = 2;
    optional float max_latency_ns = 3;
}

message BlockDevicePerf {
    optional bytes path                 = 1 [(zbs.labels).as_str = true];
    optional StoragePerf total_perf     = 2;
    optional float max_total_latency_ns = 3;
    optional StoragePerf read_perf      = 4;
    optional float max_read_latency_ns  = 5;
    optional StoragePerf write_perf     = 6;
    optional float max_write_latency_ns = 7;
    optional float sync_rate            = 8;
    optional float sync_latency_ns      = 9;
    optional float max_sync_latency_ns  = 10;
}

message LSMPerf {
    optional StoragePerf read_perf            = 1;
    optional float read_cache_hit_rate        = 2;
    optional StoragePerf write_perf           = 3;
    optional float write_cache_hit_rate       = 4;
    optional StoragePerf total_perf           = 5;
    optional float total_cache_hit_rate       = 6;
    optional float failed_io_rate             = 7;
    optional float hard_io_rate               = 8;
    optional float slow_io_rate               = 9;
    optional float throttle_latency_ns        = 10;
    optional float cache_promotion_rate       = 11;
    optional float cache_promotion_bps        = 12;
    optional float cache_writeback_rate       = 13;
    optional float cache_writeback_bps        = 14;
    optional float journal_reclaim_rate       = 15;
    optional float journal_reclaim_latency_ns = 16;
    optional LSMDBPerf db_perf                = 17;
    repeated JournalPerf journal_perfs        = 18;
    repeated BlockDevicePerf cache_perfs      = 19;
    repeated BlockDevicePerf partition_perfs  = 20;
    optional StoragePerf unmap_perf           = 21;
}

message ChunkPerf {
    optional AccessPerf access_perf   = 1; // app + sink + reposition
    optional RecoverPerf recover_perf = 2;
    optional LSMPerf lsm_perf         = 3;
    optional AccessPerf sink_perf     = 4; // not implemented

    optional AccessPerf access_cap_replica_app_perf         = 11;
    optional AccessPerf access_perf_replica_app_perf        = 12;
    optional AccessPerf access_cap_ec_app_perf              = 13;

    optional AccessPerf access_cap_replica_sink_perf        = 14;
    optional AccessPerf access_perf_replica_sink_perf       = 15;
    optional AccessPerf access_cap_ec_sink_perf             = 16;

    optional AccessPerf access_cap_replica_reposition_perf  = 17;
    optional AccessPerf access_perf_replica_reposition_perf = 18;
    optional AccessPerf access_cap_ec_reposition_perf       = 19;
}

message StorageSpace {
    optional uint64 valid_data_space = 1;
    optional uint64 provisioned_data_space = 2;
    optional uint64 used_data_space = 3;
    optional uint64 total_data_capacity = 13;
    optional uint64 failure_data_space = 14;

    optional uint64 valid_cache_space = 10;
    optional uint64 used_cache_space = 11;
    optional uint64 dirty_cache_space = 12;
    optional uint64 failure_cache_space = 15;
    optional uint64 total_cache_capacity = 16;
    optional uint64 temporary_replica_space = 17;
    optional uint64 allocated_data_space = 18;

    optional uint64 perf_valid_data_space     = 19 [default = 0];
    optional uint64 perf_used_data_space      = 20 [default = 0];
    optional uint64 perf_total_data_capacity  = 21 [default = 0];
    optional uint64 perf_failure_data_space   = 22 [default = 0];
    optional uint64 perf_allocated_data_space = 23 [default = 0];
    optional uint64 perf_planned_space        = 24 [default = 0];

    optional uint64 planned_prioritized_space = 31 [default = 0];
    optional uint64 allocated_prioritized_space = 32 [default = 0];
    optional uint64 downgraded_prioritized_space = 33 [default = 0];

    optional uint64 logical_used_data_space = 41 [default = 0];
    optional uint64 valid_free_cache_space = 42 [default = 0];

    optional uint32 temporary_replica_num = 51 [default = 0];
}

enum ChunkState {
    CHUNK_STATE_UNKNOWN = 0;
    CHUNK_STATE_IDLE = 1;
    CHUNK_STATE_IN_USE = 2;
    CHUNK_STATE_REMOVING = 3;
}

// TODO(wenhao): add more states: CLEANING, CLEANED
enum ChunkStatus {
    CHUNK_STATUS_INITIALIZING = 1;
    CHUNK_STATUS_CONNECTED_HEALTHY = 2;
    CHUNK_STATUS_CONNECTED_ERROR = 3;
    CHUNK_STATUS_CONNECTED_WARNING = 4;
    CHUNK_STATUS_CONNECTING = 5;
    CHUNK_STATUS_SESSION_EXPIRED = 6;
}

enum ChunkHealthyStatus {
    CHUNK_HEALTHY_STATUS_NORMAL = 1;
    CHUNK_HEALTHY_STATUS_ISOLATING = 2;
    CHUNK_HEALTHY_STATUS_NOT_ISOLATE = 3;
    CHUNK_HEALTHY_STATUS_ISOLATED = 4;
}

message ChunkIsolateFlag {
    optional bool migrate_lease  = 1 [default = false];
    optional bool migrate_nvmf_op_node  = 2 [default = false];
    optional bool disable_nvmf_op_path = 3 [default = false];
    optional bool migrate_iscsi  = 4 [default = false];
    optional bool avoid_read_slow_replica  = 5 [default = false];
    optional bool remove_slow_replica  = 6 [default = false];
    optional bool avoid_recover  = 7 [default = false];
    optional bool avoid_alloc_lease = 8 [default = false];
    optional bool replica_io_fast_timeout = 9 [default = false];
    optional bool migrate_nfs = 10 [default = false];
    optional bool migrate_vhost = 11 [default = false];
}

message ChunkIsolateRecord {
    optional uint32 chunk_id = 1;
    optional bool need_clear = 2;
    optional bool is_unbanning = 3;
    optional uint64 event_time = 4;
    optional ChunkIsolateFlag isolate_policy = 5;
    optional ChunkIsolateFlag target_isolate_status = 6;
    optional ChunkHealthyStatus status = 7;
}

message ChunkIsolateInfo {
    optional int32 my_chunk_id = 1;
    repeated ChunkIsolateRecord records = 2;
}

enum DataChannelStatus {
    DC_STATUS_CONNECTED = 1;
    DC_STATUS_DISCONNECTED = 2;
}

message DataChannelConnectivity {
    required uint32 dst_cid = 1;
    optional DataChannelStatus status = 2 [default = DC_STATUS_CONNECTED];
}

message ChunkConnectivity {
    optional uint32 cid = 1;
    repeated DataChannelConnectivity dc_connectivities = 2;
    // whether all data channel clients are using multipath (has more than 1 local send interfaces)
    optional bool use_mpath = 3 [default = false];
}

enum PExtentStatus {
    PEXTENT_STATUS_INIT = 99;  // this status should only appear when object is allocated

    PEXTENT_STATUS_INVALID    = 0;  // processing invalidation
    PEXTENT_STATUS_ALLOCATED  = 1;  // status OK
    PEXTENT_STATUS_RECOVERING = 2;  // data moving in
    PEXTENT_STATUS_OFFLINE    = 3;  // data lost due to device offline
    PEXTENT_STATUS_CORRUPT    = 4;  // checksum error occurred
    PEXTENT_STATUS_IOERROR    = 5;  // io error occurred
    PEXTENT_STATUS_UMOUNTING  = 6;  // umounting and waiting recovery/migration
                                    // /invalidation
}

message PExtentInfo {
    required uint32 pid           = 1;
    required uint64 generation    = 2;
    optional uint32 origin_pid    = 3;
    optional uint64 epoch         = 4;
    optional uint64 origin_epoch  = 5;
    optional PExtentStatus status = 6 [default = PEXTENT_STATUS_ALLOCATED];  // backward compatibility;
    optional bool thin_provision  = 7 [default = true];
    optional uint64 uniq_data_space = 8 [default = 0];
    optional bool prioritized = 9 [default = false, deprecated = true];
    optional uint64 downgraded_prioritized_space = 10 [default = 0];
    optional uint32 shared_data_space = 11 [default = 0];
}

message ChunkPids {
    repeated uint32 pids = 1;
    repeated uint32 rx_pids = 2;
    repeated uint32 tx_pids = 3;
    repeated uint32 recover_src_pids = 4;
    repeated uint32 cap_thick_pids = 5;
    repeated uint32 cap_thin_pids = 6;

    repeated uint32 perf_pids = 7;
    repeated uint32 perf_rx_pids = 8;
    repeated uint32 perf_tx_pids = 9;
    repeated uint32 perf_recover_src_pids = 10;
    repeated uint32 perf_thick_pids = 11;
    repeated uint32 perf_thin_pids = 12;
}

message ChunkSpaceInfo {
    optional uint64 valid_data_space = 1 [default = 0];
    optional uint64 used_data_space = 2 [default = 0];

    // These fields would be used for capacity layer since zbs 5.6.0
    optional uint64 provisioned_data_space = 3 [default = 0];  // chunk sets
    optional uint64 total_data_capacity = 14 [default = 0];    // chunk sets
    optional uint64 failure_data_space = 15 [default = 0];     // chunk sets
    optional uint64 allocated_data_space = 24 [default = 0];   // meta sets
    optional uint64 thin_used_data_space = 25 [default = 0];   // chunk sets
    optional uint64 thin_inherited_space = 46 [default = 0];   // chunk sets

    // id of the chunk, for internal use, chunk should not set this field
    optional uint32 id = 5;

    // Cache still exists and it would be used internally as perf space and capacity-read cache. For forward
    // compatibility, these cache-related fields can be treated as the summary of these two internal usages described
    // above.
    optional uint64 valid_cache_space = 11 [default = 0];
    optional uint64 used_cache_space = 12 [default = 0];
    optional uint64 dirty_cache_space = 13 [default = 0];
    optional uint64 failure_cache_space = 16 [default = 0];
    optional uint64 total_cache_capacity = 17 [default = 0];
    optional uint64 valid_free_cache_space = 42 [default = 0];

    optional uint64 temporary_replica_space = 18 [default = 0];
    optional uint32 temporary_replica_num = 19 [default = 0];

    // These fields would be internally used in meta for capacity layer since zbs 5.6.0
    optional uint32 num_rx_pids = 21 [default = 0];
    optional uint32 num_tx_pids = 22 [default = 0];
    optional uint32 num_reserved_pids = 23 [default = 0];
    optional uint32 num_recover_src_pids = 26 [default = 0];

    // Performance space is logical and adaptive, if any bdev becomes slow or fails, these perf-related field will be
    // updated proportionally.
    optional uint64 perf_valid_data_space = 27 [default = 0];  // chunk sets
    optional uint64 perf_used_data_space = 28 [default = 0];  // chunk sets
    optional uint64 perf_total_data_capacity = 29 [default = 0];  // chunk sets
    optional uint64 perf_failure_data_space = 30 [default = 0];  // chunk sets

    optional uint64 planned_prioritized_space = 31 [default = 0];
    optional uint64 allocated_prioritized_space = 32 [default = 0];
    optional uint64 downgraded_prioritized_space = 33 [default = 0];

    optional uint64 perf_allocated_data_space = 34 [default = 0];  // meta sets
    optional uint64 perf_thin_used_data_space = 35 [default = 0];  // chunk sets
    optional uint64 perf_thick_used_data_space = 44 [default = 0];  // chunk sets
    optional uint64 perf_thin_inherited_space = 45 [default = 0];   // chunk sets

    // These fields would be internally used in meta.
    optional uint32 perf_num_rx_pids = 36 [default = 0];
    optional uint32 perf_num_tx_pids = 37 [default = 0];
    optional uint32 perf_num_reserved_pids = 38 [default = 0];
    optional uint32 perf_num_recover_src_pids = 39 [default = 0];

    optional uint32 prioritized_num_rx_pids = 40 [default = 0];

    // LSM's cache is divided into an adaptive performance space and a read cache for capacity. The real-time reserved
    // performance space will be filled into `perf_planned_space`, so meta will use this field to check whether the
    // cluster can enable tiering or not during upgradation.
    optional uint64 perf_planned_space = 41 [default = 0];
    // LSM set the actual perf_thick_reserved_space(perf_trs) by planned_perf_trs and chunk load.
    optional uint64 perf_thick_reserved_space = 43 [default = 0];
}

enum RecoverState {
    INIT    = 0;
    START   = 1;
    READ    = 2;
    WRITE   = 3;
    END     = 4;
    PAUSE   = 5;
}

message RecoverInfo {
    required uint32       pid          = 1;
    required RecoverState state        = 2;
    required uint32       cur_block    = 3;
    required uint32       src_cid      = 4;
    required uint32       dst_cid      = 5;
    required bool         is_migrate   = 6;
    required uint64       silence_ms   = 7;

    optional uint32       replace_cid  = 8 [default = 0];
    optional uint64       epoch        = 9;

    optional uint32       dst_shard_idx = 10;
    optional PExtentType  pextent_type = 11;
    optional bool thin_provision = 12;

    // time stats
    optional uint64 start_ms = 21 [default = 0];
    optional uint64 pending_ms = 22 [default = 0];
    optional uint64 pausing_ms = 23 [default = 0];
    optional uint64 get_token_ms = 24 [default = 0];
    optional uint64 get_concurrency_ms = 25 [default = 0];
    optional uint64 reposition_read_ms = 26 [default = 0];
    optional uint64 reposition_write_ms = 27 [default = 0];
}

message ListRecoverResponse {
    required uint64      speed      = 1;
    repeated RecoverInfo infos      = 2;
    optional uint64      recover_speed = 3;
    optional uint64      migrate_speed = 4;
    optional uint64      cross_zone_recover_speed = 5;
    optional uint64      cross_zone_migrate_speed = 6;
    optional uint64      cross_zone_speed = 7;
    optional uint64      from_local_speed = 8;
    optional uint64      from_local_recover_speed = 9;
    optional uint64      from_local_migrate_speed = 10;
    optional uint64      from_remote_speed = 11;
    optional uint64      from_remote_recover_speed = 12;
    optional uint64      from_remote_migrate_speed = 13;
    optional uint64      cross_zone_recover_read_speed = 14;
    optional uint64      cross_zone_recover_write_speed = 15;
    optional uint64      cross_zone_migrate_read_speed = 16;
    optional uint64      cross_zone_migrate_write_speed = 17;

    optional uint32 instance_id = 99 [default = 0];
}

message ListRecoverResponseV2 {
    repeated ListRecoverResponse instances_response = 1;
}

message CpuUsage {
    // percentage [0.0, 1.0]
    optional double idle = 1 [default = 0.0];
    optional double busy = 2 [default = 0.0];
    optional double iowait = 3 [default = 0.0];
}

message DiskUsage {
    optional bytes name = 1 [(zbs.labels).as_str = true];
    optional uint64 rd_ios = 2;
    optional uint64 wr_ios = 3;
    // Read Bytes/s
    optional uint64 rd_bps = 4;
    // Write Bytes/s
    optional uint64 wr_bps = 5;
    // Average request size (sector)
    optional double avgrq_sz = 6;
    // Average IO queue size
    optional double avgqu_sz = 7;
    // average io wait time (ms)
    optional double await = 8;
    // average io service time (ms)
    optional double svctm = 9;
    // io util [0, 1.00]
    optional double util = 10;
}

message DiskUsages {
    repeated DiskUsage usages = 1;
}

message NetworkUsage {
    // Bytes/s
    optional bytes name = 1 [(zbs.labels).as_str = true];
    optional uint64 rx = 2;
    optional uint64 tx = 3;
}

message NetworkUsages {
    repeated NetworkUsage usages = 1;
}

message MemUsage {
    // KB
    optional uint64 total = 1;
    optional uint64 free = 2;
    optional uint64 available = 3;
}

message NodePerf {
    optional DiskUsages disks = 1;
    optional NetworkUsages networks = 2;
    optional CpuUsage cpu = 3;
    optional MemUsage mem = 4;
}

message NegotiationAbility {
    optional bool has_config_push_ability = 1 [default = false];
    optional bool deprecated__has_thick_extent_ability = 2 [default = false, deprecated = true];
    optional bool deprecated__synced_thick_extent = 3 [default = false, deprecated = true];
    optional bool has_unmap_ability = 4 [default = false];
    optional bool has_chunk_zk_session_ability = 5 [default = false];
    optional bool has_temporary_replica_ability = 6 [default = false];
    optional bool has_thick_extent_feature_ability = 7 [default = false];
    optional bool synced_thick_extent = 8 [default = false];
    optional bool has_tiering_ability = 9 [default = false];
    optional bool has_offload_unmap_ability = 10 [default = false];
    optional bool has_lextent_ability = 11 [default = false];
    optional bool has_internal_flow_control_ability = 12 [default = false];
    optional bool has_dc_multipath_ability = 13 [default = false];
    optional bool has_auto_special_recover_ability = 14 [default = false];
    optional bool has_encryption_ability = 15 [default = false];
}

message Chunk {
    optional uint32 id = 1;

    // pid is the brick id for chunk, it could be zero
    optional uint32 v1_parent_id = 2 [deprecated = true];

    optional uint32 data_ip = 12;
    optional uint32 data_port = 13;
    optional uint32 rpc_ip = 14;
    optional uint32 rpc_port = 15;
    optional uint32 heartbeat_ip = 16;
    optional uint32 heartbeat_port = 17;

    optional uint64 registered_date = 18;

    optional ChunkStatus status = 21;
    optional ChunkSpaceInfo space_info = 22;
    optional StoragePerf read_perf             = 23 [deprecated = true];
    optional StoragePerf write_perf            = 24 [deprecated = true];
    optional ChunkState use_state = 25 [default = CHUNK_STATE_UNKNOWN];
    optional StoragePerf total_perf            = 26 [deprecated = true];
    optional StoragePerf cross_zone_read_perf  = 27 [deprecated = true];
    optional StoragePerf cross_zone_write_perf = 28 [deprecated = true];
    optional string ipmi_ip = 31;

    // topology info
    optional bytes topo_id = 41 [default = "", (zbs.labels).as_str = true];
    // only internal use, not valid in all scenario
    optional bytes zone_id = 42 [default = "", (zbs.labels).as_str = true];

    // default has no storage pool
    optional bytes storage_pool_id = 45 [default = "", (zbs.labels).as_str = true];

    // heartbeat info
    optional uint64 last_succeed_heartbeat = 51;

    optional NodePerf node_perf = 52;
    optional RecoverSummary recover_info = 53;

    optional bytes host_name = 54 [(zbs.labels).as_str = true];

    optional LSMVersion lsm_version = 55;
    optional bool maintenance_mode = 56 [default = false];
    optional bool has_config_push_ability = 57 [default = false];
    optional uint64 maintenance_mode_expire_time_s = 58 [default = 0];
    optional ChunkPerf chunk_perf                  = 59;

    optional NegotiationAbility ability = 60;

    optional ChunkIsolateRecord isolate_record = 61;
    optional uint32 prio_space_percentage = 62 [default = 0];
    optional bool fail_slow = 63 [default = false];
    // When a chunk enter maintenance mode, recover scan will skip to recover any failed replica on it,
    // after maintenance_mode_enable_recovey_time_s, we recover failed replica on it.
    optional uint64 maintenance_mode_enable_recovey_time_s = 64 [default = 0];

    optional uint32 instance_id = 99 [default = 0];
}

message Chunks {
    repeated Chunk chunks = 2;
}

message NodeObj {
    optional uint32 node_data_ip = 1 [default = 0];
    repeated Chunk chunks = 2;
}

message Nodes {
    repeated NodeObj nodes = 1;
}

message LSMVersion {
    required uint64 major_num    = 1;
    required uint64 minor_num    = 2;
    required uint64 revision_num = 3;
}

enum LSMState {
    LSM_READY       = 0;
    LSM_UPGRADING   = 1;
    LSM_ROLLBACKING = 2;
}

message LSMMeta {
    optional bytes lsm_uuid     = 1 [(zbs.labels).as_str = true];
    optional uint64 ctime       = 2;
    optional bytes deprecated   = 3;
    optional LSMVersion version = 4;
    optional LSMState lsm_state = 5 [default = LSM_READY];
}

message NodeInfo {
    optional bytes host_name = 1 [(zbs.labels).as_str = true];
    optional bytes ip = 2 [(zbs.labels).as_str = true];
    optional ChunkPerf perf = 3;
    optional StorageSpace space = 4;

    optional ChunkStatus status = 5;
    optional ChunkState use_state = 6 [default = CHUNK_STATE_UNKNOWN];
    optional bool maintenance_mode = 7 [default = false];

    // StoragePool doesn't have these fields, so we put them here
    optional uint64 perf_thin_used_data_space = 8 [default = 0];
    optional uint64 perf_thick_used_data_space = 9 [default = 0];
    optional uint64 perf_thick_reserved_space = 10 [default = 0];

    optional uint64 recovers_bytes = 11 [default = 0];
    optional uint64 migrates_bytes = 12 [default = 0];
}

message StoragePool {
    optional bytes id = 1 [(zbs.labels).as_str = true];
    optional bytes name = 2 [(zbs.labels).as_str = true];

    // chunks in the storage pool
    repeated Chunk chunks = 3;
    optional StorageSpace space = 4;
    optional uint32 total_nodes = 5 [default = 0];
    optional uint32 healthy_nodes = 6 [default = 0];
    optional uint32 connecting_nodes = 7 [default = 0];  // nodes are being connecting
    optional uint32 warning_nodes = 8 [default = 0];
    optional uint32 error_nodes = 9 [default = 0];
    optional uint32 removing_nodes = 10 [default = 0];   // nodes are being removed

    optional uint32 total_chunks = 11 [default = 0];
    optional uint32 healthy_chunks = 12 [default = 0];
    optional uint32 connecting_chunks = 13 [default = 0];  // chunks are being connecting
    optional uint32 warning_chunks = 14 [default = 0];
    optional uint32 error_chunks = 15 [default = 0];
    optional uint32 removing_chunks = 16 [default = 0];   // chunks are being removed

    // perf info
    optional StoragePerf total_perf            = 21 [deprecated = true];
    optional StoragePerf read_perf             = 22 [deprecated = true];
    optional StoragePerf write_perf            = 23 [deprecated = true];
    optional StoragePerf cross_zone_read_perf  = 24 [deprecated = true];
    optional StoragePerf cross_zone_write_perf = 25 [deprecated = true];
    // recover and migrate info
    optional uint32 num_ongoing_recovers = 52 [default = 0];
    optional uint32 num_pending_recovers = 53 [default = 0];
    optional uint32 num_ongoing_migrates = 54 [default = 0];
    optional uint32 num_pending_migrates = 55 [default = 0];
    optional uint32 num_pending_recycles = 56 [default = 0];

    optional RecoverSummary recover_info = 57;

    optional ChunkPerf chunk_perf = 58;
    repeated NodeInfo nodes_info = 59;
}

message StoragePools {
    repeated StoragePool storage_pools = 1;
}

message StoragePoolId {
    required bytes id = 1 [(zbs.labels).as_str = true];
}

enum TopoType {
    CHUNK = 0;
    BRICK = 1;
    RACK = 2;
    POD = 3;
    CLUSTER = 4;
    ZONE = 5;
    NODE = 6;
    ALL_TOPO_TYPE = 99;
}

message Position {
    optional int32 row = 1 [default = 1];
    optional int32 column = 2 [default = 1];
}

message Dimension {
    optional int32 row = 1 [default = 1];
    optional int32 column = 2 [default = 1];
}

message Capacity {
    optional int32 row = 1 [default = 1];
    optional int32 column = 2 [default = 1];
}

message TopoObj {
    optional TopoType type = 1 [default = CHUNK];
    optional bytes id = 2 [(zbs.labels).as_str = true];
    // if no parent is set, the default is the root object called "topo"
    optional bytes parent_id = 3 [default = "topo", (zbs.labels).as_str = true];
    optional bytes name = 4 [default = "", (zbs.labels).as_str = true];

    optional uint64 create_time = 5;
    optional bytes desc = 6 [default = "", (zbs.labels).as_str = true];

    optional Position position = 12;
    optional Capacity capacity = 13;
    optional Dimension dimension = 14;
    optional uint64 ring_id = 15 [default = 0];
}

message TopoObjs {
    repeated TopoObj objs = 1;
}

message TopoObjId {
    required bytes id = 1 [(zbs.labels).as_str = true];
    // if show_details is not set, we will return a compatible result for ListTopoObj and ShowTopoObj
    optional bool show_details = 2 [default = false];
}

message ChunkTopology {
    optional bytes pod_id = 1 [default = "", (zbs.labels).as_str = true];
    optional bytes rack_id = 2 [default = "", (zbs.labels).as_str = true];
    optional bytes brick_id = 3 [default = "", (zbs.labels).as_str = true];
    optional bytes zone_id = 4 [default = "", (zbs.labels).as_str = true];
    optional bytes node_id = 5 [default = "", (zbs.labels).as_str = true];
};

// user could specify either id or ip:port to query a chunk.
// Preferred priority: id > data addr > rpc addr
message ChunkId {
    optional uint32 id = 1;  // id identify a chunk
    optional uint32 rpc_ip = 2;  // ip + port
    optional uint32 rpc_port = 3;
    optional uint32 data_ip = 4;
    optional uint32 data_port = 5;
}

message DiskPerf {
    required string path = 1;
    optional StoragePerf perf = 2;
}

message GetPExtentInfoCmd {
    optional uint32 pextent_range_start = 1;  // include this extent
    optional uint32 pextent_range_end = 2;  // not including this extent
}

message GcCmd {
    required uint32 pid = 1;
    optional uint64 epoch = 2;
}

message DrainCmd {
    required uint32 lid = 1;
    optional uint64 lepoch = 2;
    optional uint32 pid = 3; // perf pid
    optional uint64 perf_epoch = 4;
    optional bytes session_uuid = 5 [(zbs.labels).as_str = true];

    optional uint32 cid = 20;
    optional uint64 start_ms = 21;
    optional uint64 timeout_ms = 22;
}

message FailedDrainCmd {
    required uint32 lid = 1;
    optional uint64 lepoch = 2;
}

message RecoverFromTemporaryReplica {
    required TemporaryReplica src_temporary_replica = 1;
    optional bool rollback_failed_replica = 2 [default = false];
    optional bool force_recover_from_temporary_replia = 3 [default = false];
}

message RecoverCmd {
    required uint32 pid = 1;
    optional Lease lease = 2;  // lease for the pextent
    required uint32 dst_chunk = 3;
    optional uint32 replace_chunk = 4;
    optional uint32 src_chunk = 5;
    optional bool is_migrate = 6 [default = false];
    optional uint64 epoch = 7;
    // active_location when meta generation cmd, only for display now
    optional uint32 active_location = 8;
    optional RecoverFromTemporaryReplica recover_from_temporary_replica = 9;
    optional bool agile_recover_only = 10 [default = false];

    optional uint32 dst_shard_idx = 11;
    optional ExtLocation ec_active_location = 12;
    optional PExtentType pextent_type = 13;
    optional bool thin_provision = 14;

    // the following is for internal use by MetaServer
    optional uint64 start_ms = 21;
}

message MaintenanceCmd{
    required ChunkId cid = 1;
}

message RevokeCmd {
    required bytes vtable_id = 1 [(zbs.labels).as_str = true];
    optional uint32 pid = 2;
    optional bool revoke_all = 3 [default = false];

    // need_release_meta_lease can only effect the RevokeCmd with vtable_id only.
    optional bool need_release_meta_lease = 4 [default = true];
}

message CancelRepositionCmd {
    optional uint32 pid = 1;
    optional PExtentType pextent_type = 2;
    optional bool is_migrate = 3;
}

message CleanChunkInfoCmd {
    required Chunk chunk = 1;
    optional uint32 secondary_data_ip = 2;
}

message ElevateCmd {
    optional uint32 lid = 1;
    optional uint64 lepoch = 2;
    optional bytes vtable_id = 3 [(zbs.labels).as_str = true];
    optional uint32 vextent_no = 4;
    optional uint64 start_ms = 5;
}

message RangeU64 {
    required uint64 start = 1;
    required uint64 length = 2;
}

message ThreadCacheInfo {
    required int64 thread_id	= 1;
    required string thread_name = 2;
    required uint64 cached_size = 3;
    required int64  used_size	= 4;
}

message ListThreadCacheResponse {
    repeated ThreadCacheInfo infos = 1;
    required uint64          total_used_memory = 2;
}

message StatusRequest {
    required bool enable = 1;
}

message StatusResponse {
    required bool enabled = 2;
}

message LicenseCapabilities {
    optional bool has_metrox = 1 [default = false];
    optional bool has_remote_backup = 2 [default = false];
}

enum SoftwareEdition {
    STANDARD = 1;
    ENTERPRISE = 2;
    ENTERPRISE_EISOO = 3;
    ESSENTIAL = 4;
    COMMUNITY = 5;
    EXPRESS = 6;
}

enum LicenseType {
    TRIAL = 1;
    PERPETUAL = 2;
    SUBSCRIPTION = 3;
}

enum PricingType {
    PRICING_TYPE_UNKNOWN = 1;
    CPU_SLOT_NUM = 2;
    VM_NUM = 3;
}

message LicenseV1 {
    required uint64 sign_date = 1;  // seconds, UNIX TIME
    required uint64 period = 2;  // seconds
    required string serial = 3;  // instalation serial number
    optional string version = 4;

    optional SoftwareEdition software_edition = 6;

    required uint64 max_chunk_num = 13;
    optional uint64 max_physical_data_capacity = 17;  // bytes. 0 means no limit
    optional uint64 max_physical_data_capacity_per_node = 18;  // bytes. 0 means no limit

    optional LicenseCapabilities license_capabilities = 21;

    optional LicenseType license_type = 22 [default = TRIAL];

    optional uint64 subscription_start_date = 23;  // seconds, UNIX TIME
    optional uint64 subscription_period = 24;  // seconds

    optional uint64 maintenance_start_date = 27;  // seconds, UNIX TIME
    optional uint64 maintenance_period = 28;  // seconds

    // NOTE: The following optionals are deprecated, kept just for compatibility
    required string deprecated__product_name = 5 [deprecated = true];
    optional uint64 max_pool_num = 14 [deprecated = true];
    optional uint64 max_volume_num = 15 [deprecated = true];
    optional uint64 max_snap_num = 11 [deprecated = true]; // per volume
    optional uint64 max_volume_size = 16 [deprecated = true];  // GB
    optional uint64 deprecated__max_physical_space_size = 12 [deprecated = true];  // bytes
}

message LicenseV2 {
    required uint64 period = 2;  // seconds
    required string serial = 3;  // installation serial number
    optional string version = 4;

    optional SoftwareEdition software_edition = 6;

    required uint64 max_chunk_num = 13;
    optional uint64 max_physical_data_capacity = 17;  // bytes. 0 means no limit
    optional uint64 max_physical_data_capacity_per_node = 18;  // bytes. 0 means no limit

    optional LicenseCapabilities license_capabilities = 21;

    optional LicenseType license_type = 22 [default = TRIAL];

    optional uint64 subscription_start_date = 23;  // seconds, UNIX TIME
    optional uint64 subscription_period = 24;  // seconds

    optional string vendor = 26;

    optional uint64 maintenance_start_date = 27;  // seconds, UNIX TIME
    optional uint64 maintenance_period = 28;  // seconds

    // move sign_date from index 1 to index 31 to break compatibility
    required uint64 sign_date = 31;  // seconds, UNIX TIME
    optional uint32 platform = 32;
}

message License {
    required string serial = 3;  // installation serial number
    optional string version = 4;

    optional SoftwareEdition software_edition = 6;

    required uint64 max_chunk_num = 13;
    optional uint64 max_physical_data_capacity = 17;  // bytes. 0 means no limit
    optional uint64 max_physical_data_capacity_per_node = 18;  // bytes. 0 means no limit

    optional LicenseCapabilities license_capabilities = 21;

    optional LicenseType license_type = 22 [default = TRIAL];

    optional uint64 subscription_start_date = 23;  // seconds, UNIX TIME
    optional uint64 subscription_period = 24;  // seconds

    optional string vendor = 26;

    optional uint64 maintenance_start_date = 27;  // seconds, UNIX TIME
    optional uint64 maintenance_period = 28;  // seconds

    // move sign_date from index 1 to index 31 to break compatibility
    required uint64 sign_date = 31;  // seconds, UNIX TIME
    optional uint32 platform = 32;
    optional string mode = 33;       // license mode: ALL/SERVER_SAN/HCI
    required uint64 period = 34;  // seconds. move from index 2 to index 34 to break compatibility
    optional PricingType pricing_type = 35 [default = PRICING_TYPE_UNKNOWN];
}

message LicenseCertificate {
    required License license = 1;
    required string signature_hex = 2;
}

message LicenseCertificateV1 {
    required LicenseV1 license = 1;
    required string signature_hex = 2;
}

message LicenseCertificateV2 {
    required LicenseV2 license = 1;
    required string signature_hex = 2;
}

message TowerLicense {
    required uint32 version = 1;
    required string serial = 2;
    required SoftwareEdition software_edition = 3;
    optional LicenseType license_type = 4 [default = TRIAL];

    required uint64 max_node_num = 5;
    required uint64 max_cluster_num = 6;

    required uint64 sign_date = 7;  //  seconds, UNIX TIME
    required uint64 period = 8;  // seconds

    optional uint64 maintenance_start_date = 9;  // seconds, UNIX TIME
    optional uint64 maintenance_period = 10;  // seconds
    optional uint64 subscription_start_date = 11;  // seconds, UNIX TIME
    optional uint64 subscription_period = 12;  // seconds
}

message TowerLicenseCertificate {
    required TowerLicense license = 1;
    required string signature_hex = 2;
}

message LicenseRequest {
    required string certificate_hex = 1;
}

message ListCounterRequest {
    optional string pattern = 1 [default = "*"];
}

message ListCounterResponse {
    repeated CounterInfo counters = 1;
}

message ProfilerRequest {
    required string file_name = 1;
    optional string thread_filter = 2;
}

message HeapProfilerRequest {
    required string prefix = 1;
}

message SetVLOGRequest {
    optional string module_pattern = 1 [default = ""];
    required int32 log_level = 2;
}

message GFlagsVar {
    required string key   = 1;
    required string value = 2;
}

message GFlagsVarName {
    required string key   = 1;
}

message GFlagsVars {
    repeated GFlagsVar vars = 1;
}

message RepositionConcurrencyInfo {
    optional uint32 cid = 1;
    optional uint64 cur_perf_reposition_concurrency = 2 [default = 0];
    optional uint64 auto_perf_reposition_concurrency_limit = 3;
    optional uint64 cur_cap_reposition_concurrency = 4 [default = 0];
    optional uint64 auto_cap_reposition_concurrency_limit = 5;
}

enum RepositionMode {
    REPOSITION_AUTO = 0;
    REPOSITION_STATIC = 1;
    REPOSITION_INVALID = 2;
}

enum RecoverMode {
    RECOVER_AUTO = 0;
    RECOVER_STATIC = 1;
    RECOVER_INVALID = 2;
}

enum MigrateMode {
    MIGRATE_AUTO = 0;
    MIGRATE_STATIC = 1;
    MIGRATE_INVALID = 2;
}

enum SinkMode {
    SINK_LOW = 0;
    SINK_MID = 1;
    SINK_HIGH = 2;
    SINK_VERY_HIGH = 3;
}

enum InternalIOMode {
    INTERNAL_IO_AUTO = 0;
    INTERNAL_IO_STATIC = 1;
    INTERNAL_IO_INVALID = 2;
}

enum InternalIOPriority {
    INTERNAL_HIGH = 0;
    INTERNAL_MID = 1;
    INTERNAL_LOW = 2;
}

enum CapDirectWritePolicy {
    // all write should send to perf
    CAP_DIO_DISABLED = 0;
    // 256k aligned io should be send to cap
    CAP_DIO_BLOCK_ALIGNED_ONLY = 1;
    // if io cannot get perf-write token, send to cap
    CAP_DIO_ALL_THROTTLED = 2;
    // send all io to cap.
    CAP_DIO_ALL = 3;
}

message SinkablePair {
    optional uint32 lid = 1;
    optional uint64 lepoch = 2;
    optional uint32 pid = 3; // perf pid
    optional uint64 perf_epoch = 4;
}

message SinkCmd {
    optional SinkMode mode = 1 [default = SINK_LOW];
    optional uint64 inactive_lease_interval_s = 2 [default = 3600];
    optional bool is_sink_perf_for_inactive_lease = 3 [default = false];
    optional CapDirectWritePolicy cap_directly_write_policy = 4 [default = CAP_DIO_DISABLED];
    optional uint64 access_reserve_block_num = 5 [default = 18446744073709551615];
}

service SystemManagementService {
    option (rpc_service_id) = 0; // FIXME(keyue) change to 2000 in next major release
    rpc Enable (StatusRequest) returns (RpcStatus);
    rpc Show (Void) returns (StatusResponse);
}

service CommonService {
    option (rpc_service_id) = 2333; // FIXME(keyue) change to 2001 in next major release
    rpc ListCounter (ListCounterRequest) returns (ListCounterResponse);
    rpc StartProfiler (ProfilerRequest) returns (Void);
    rpc StopProfiler (Void) returns (Void);
    rpc StartHeapProfiler (HeapProfilerRequest) returns (Void);
    rpc StopHeapProfiler (Void) returns (Void);
    rpc ListThreadCache (Void) returns (ListThreadCacheResponse);
    rpc SetVLOG (SetVLOGRequest) returns (Void);
    rpc GetGFlagsVar (GFlagsVarName) returns (GFlagsVar);
    rpc SetGFlagsVar (GFlagsVar) returns (Void);
    rpc ListGFlagsVar (Void) returns (GFlagsVars);
    rpc ReleaseFreeMemory (Void) returns (Void);
    rpc GetServiceVersion(Void) returns (GetServiceVersionResponse);
}

message DataReductionInfo {
    optional double data_reduction_ratio = 1 [default = 0];
    optional uint64 data_reduction_saving = 2 [default = 0];
    optional double overall_efficiency = 3 [default = 0];
}

message OverprovisionInfo {
    optional double overprovision_ratio = 1 [default = 0];
    optional uint64 volume_used_space = 2 [default = 0];
    optional uint64 snapshot_used_space = 3 [default = 0];
}

message GetServiceVersionResponse {
    required string major_version = 1;  // Major version number, e.g., "1"
    required string minor_version = 2;  // Minor version number, e.g., "2"
    required string update_version = 3; // Update version number, e.g., "3"
    optional string extra_version = 4;  // Extra version information, e.g., "rc-1"
    required string version_string = 5; // Full version string, e.g., "1.2.3-rc1; build on xxxx..."
}

message MPathInfo {
    optional bool is_all_chunk_use_mpath = 1 [default = false];
}

message RecycleBinSizeInfo {
    optional uint64 logical_size = 1;
    optional uint64 logical_used_size = 2;
    optional uint64 sweep_size_until_today_end = 3;
    optional uint64 sweep_size_in_3d = 4;
    optional uint64 sweep_size_in_7d = 5;
    optional uint64 sweep_size_in_30d = 6;
    optional uint64 sweep_size_after_30d = 7;
}

enum EncryptMethod {
    ENCRYPT_PLAIN_TEXT = 0;
    ENCRYPT_AES256_CTR = 1;
    ENCRYPT_UNKNOWN_ALGO = 99;
}
