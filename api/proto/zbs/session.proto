syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

import "common.proto";
import "encryption.proto";
import "error.proto";
import "options.proto";
package zbs.consensus;

message SessionEpoch {
    optional bytes uuid = 1 [(zbs.labels).as_str = true];
    optional uint64 epoch = 2;  // session epoch used to figure out out-of-order requests
}

message Session {
    required SessionEpoch session_epoch = 1;
    required bytes group = 2 [(zbs.labels).as_str = true];
    optional int64 lease_expire_ns = 3;

    repeated Item items = 10;  // session releated items
}

message Sessions {
    repeated Session sessions = 1;
}

enum ItemType {
    ITEM_TYPE_UNKNOWN = 0;
    ITEM_TYPE_BASIC_INFO = 1;
    ITEM_TYPE_ISCSI= 2;
    ITEM_TYPE_NFS= 3;
}

message Item {
    required bytes key = 1 [(zbs.labels).as_str = true];
    required bytes value = 2 [(zbs.labels).as_str = true];
    optional ItemType type = 3 [default = ITEM_TYPE_UNKNOWN];
}

message CreateSessionRequest {
    required bytes group = 1 [(zbs.labels).as_str = true];

    // set the initial key-value pairs so that the master could get the
    // information when the session is created.
    repeated Item items = 10;
}

// add an item that should be unique among the session group
message AddItemRequest {
    required SessionEpoch session_epoch = 1;
    required bytes group = 2 [(zbs.labels).as_str = true];
    optional bool unique = 3 [default = false];
    required Item item = 10;  // new session related items set
}

// remove an item from the session
message RemoveItemRequest {
    required SessionEpoch session_epoch = 1;
    required Item item = 10;  // new session related items set
}

message ListSessionRequest {
    required bytes group = 1 [(zbs.labels).as_str = true];
    repeated ItemType types = 2; // all types if empty
}

message KeepAliveRequest {
    required SessionEpoch session_epoch = 1;
    repeated Item items = 3;
    optional bytes cluster_uuid = 5 [default = "", (zbs.labels).as_str = true];
    optional bool aware_time_cost = 6 [default = false];

    extensions 10000 to max;  // for customization
}

message KeepAliveResponse {
    optional SessionEpoch session_epoch = 1;
    optional ErrorCode ec = 2 [default = EOK];
    optional int64 lease_interval_ns = 4;  // new lease interval
    optional bytes cluster_uuid = 5 [default = "", (zbs.labels).as_str = true];
    optional bool aware_time_cost = 6 [default = false];

    extensions 10000 to max;
}

message RefreshLocalNFSClientIPsRequest {
    required SessionEpoch session_epoch = 1;
    required bytes client_ips = 2 [(zbs.labels).as_str = true]; // use ',' as split like *********,127.0.0.1
}

message AcquireNFSLoginPermissionRequest {
    required SessionEpoch session_epoch = 1;
    required bytes client_ip = 2 [(zbs.labels).as_str = true];
}

// session master implement session service
service SessionService {
    option (rpc_service_id) = 7001;

    // session
    rpc CreateSession (CreateSessionRequest) returns (KeepAliveResponse);
    rpc LeaveSession (SessionEpoch) returns (Void);  // logout itself
    rpc ListSession (ListSessionRequest) returns (Sessions);
    rpc KeepAlive (KeepAliveRequest) returns (KeepAliveResponse);
    rpc AddItem (AddItemRequest) returns (Session);
    rpc RemoveItem (RemoveItemRequest) returns (Session);
    rpc RefreshLocalNFSClientIPs (RefreshLocalNFSClientIPsRequest) returns (Session);
    rpc ShowSession (SessionEpoch) returns (Session);
    rpc AcquireNFSLoginPermission (AcquireNFSLoginPermissionRequest) returns (Void);
}

message DataReportRequest {
    required SessionEpoch session_epoch = 1;
    optional bytes cluster_uuid = 2 [default = "", (zbs.labels).as_str = true];

    extensions 10000 to max;  // for customization
}

message DataReportResponse {
    optional ErrorCode ec = 1 [default = EOK];

    extensions 10000 to max;
}

service DataReportService {
    option (rpc_service_id) = 7002;

    rpc DataReport(DataReportRequest) returns (DataReportResponse);
}

message GetEncryptInfoRequest {
    required uint64 encrypt_metadata_id = 1;
}

message GetEncryptInfoResponse {
    optional encryption.InternalEncryptMethod method = 1 [default = INTERNAL_ENCRYPT_UNKNOWN_ALGO];
    optional bytes plain_key = 2;
    optional bytes init_vec = 3;
    optional uint64 cipher_metadata_id = 4;
}

service VolumeKeyService {
    option (rpc_service_id) = 7003;

    rpc GetEncryptInfo(GetEncryptInfoRequest) returns (GetEncryptInfoResponse);
}