syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

import "common.proto";

package zbs.block;

message CompareLExtentRequest {
    required uint32 lid1 = 1;
    required uint32 lid2 = 2;
}

message CompareExtentResponse {
    repeated RangeU64 diff_ranges = 1;
}

service CompareExtentService {
    option (rpc_service_id) = 9001;

    rpc CompareLExtent(CompareLExtentRequest) returns (CompareExtentResponse);
}