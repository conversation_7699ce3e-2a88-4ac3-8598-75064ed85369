syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

import "error.proto";
import "options.proto";

package zbs;

// CDP messages
enum CDPJobStage {
    CDP_STAGE_INIT = 0;
    CDP_STAGE_FULL_COPY = 1;
    CDP_STAGE_DELTA_COPY = 2;
    CDP_STAGE_MIRROR = 3;
    CDP_STAGE_CANCELLING = 4;
    CDP_STAGE_FINISHING = 5;
    CDP_STAGE_ERROR = 6;
    CDP_STAGE_CANCELLED = 7;
    CDP_STAGE_DONE = 8;
    CDP_STAGE_ERROR_NEED_NOTIFY = 9;
    CDP_STAGE_NONE=31;
}

message CreateCDPJobRequest {
    required CDPVolumeID local = 1;
    required CDPRemoteInfo remote = 2;
    optional bytes group = 3 [(zbs.labels).as_str = true];
    optional uint32 cid = 4;
    optional bytes id = 5 [(zbs.labels).as_str = true];
    optional bool skip_fc_write_zero = 6;
    optional bool auto_clean = 7;
}

message CDPRemoteInfo {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    required bytes hosts = 2 [(zbs.labels).as_str = true];
}

message ListCDPJobsRequest {
    optional bytes group = 1 [(zbs.labels).as_str = true];
}

message ListCDPJobsResponse {
    repeated CDPJobInfo jobs = 1;
}

message CDPVolumeID {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
}

message CDPJobInfo {
    required bytes id = 1 [(zbs.labels).as_str = true];
    required CDPVolumeID local = 2;
    required CDPRemoteInfo remote = 3;
    optional bytes group = 4 [(zbs.labels).as_str = true];
    optional uint32 cid = 5;
    optional CDPJobStage stage = 6;
    optional bytes session_id = 7 [(zbs.labels).as_str = true];
    optional CDPErrorDetail error = 8;
    optional bool skip_fc_write_zero = 9;
    optional bool auto_clean = 10;
    optional uint64 old_chunk_instances_bits = 11;

    extensions 10000 to max;  // for cdp job metrics
}

message GetCDPJobRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
}

message GetCDPJobByVolumeRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
}

message FinishCDPJobRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    optional bool sync = 2;
}

message CancelCDPJobRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
    optional bool sync = 2;
    optional bool reply_error = 3 [default = false];
}

message DeleteCDPJobRequest {
    required bytes id = 1 [(zbs.labels).as_str = true];
}

message CreateCDPJobsByGroupRequest {
    required bytes group = 1 [(zbs.labels).as_str = true];
    repeated CreateCDPJobRequest create_reqs = 2;
}

message CreateCDPJobsByGroupResponse {
    repeated CDPJobInfo cdp_jobs = 1;
}

message FinishCDPJobsByGroupRequest {
    required bytes group = 1 [(zbs.labels).as_str = true];
    optional bool sync = 2;
}

message CancelCDPJobsByGroupRequest {
    required bytes group = 1 [(zbs.labels).as_str = true];
    optional bool sync = 2;
}

message DeleteCDPJobsByGroupRequest {
    required bytes group = 1 [(zbs.labels).as_str = true];
}

message CDPJobUpdate {
    // notify chunk manage CDP jobs
    repeated CreateCDPJobRequest create_reqs = 1;
    repeated CancelCDPJobRequest cancel_reqs = 2;
    repeated FinishCDPJobRequest finish_reqs = 3;
}

message CDPErrorDetail {
    optional ErrorCode error_code = 1 [default = EOK];
    optional string error_message = 2;
}

message CDPJobStageUpdate {
    required bytes id = 1 [(zbs.labels).as_str = true];
    required CDPJobStage stage = 2;
    optional CDPErrorDetail err = 3;
}

message CDPJobUpdateDone {
    repeated CDPJobStageUpdate job_update = 1;
}

message CDPJobMetrics {
    extend CDPJobInfo {
        optional CDPJobMetrics metrics = 10001;
    }
    optional uint64 total_fc_blocks     =  1;
    optional uint64 remain_fc_blocks    =  2;
    optional uint64 copied_fc_blocks    =  3;

    optional uint64 total_dirty_blocks  =  4;
    optional uint64 remain_dirty_blocks =  5;
    optional uint64 copied_dirty_blocks =  6;

    optional uint64 block_queue_depth   =  7;
    optional uint64 stage_duration      =  8;

    optional uint32 total_inflight      =  9;
    optional float total_iops           = 10;
    optional float total_iop30s         = 11;
    optional float total_speed_bps      = 12;
    optional float total_avg_size_bytes = 13;
    optional float total_avg_latency_ns = 14;

    optional uint32 block_inflight      = 15;
    optional float block_iops           = 16;
    optional float block_iop30s         = 17;
    optional float block_speed_bps      = 18;
    optional float block_avg_size_bytes = 19;
    optional float block_avg_latency_ns = 20;

    optional uint32 user_inflight      = 21;
    optional float user_iops           = 22;
    optional float user_iop30s         = 23;
    optional float user_speed_bps      = 24;
    optional float user_avg_size_bytes = 25;
    optional float user_avg_latency_ns = 26;
}
