syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

import "common.proto";
import "metrics.proto";
import "options.proto";

package zbs;

message VolumePerfRequest {
    required bytes volume_id = 2 [(zbs.labels).as_str = true];
}

message VolumePerf {
    optional bytes volume_id = 1 [(zbs.labels).as_str = true];

    optional float read_iops = 2;  // io per second
    optional float read_bandwidth = 3;  // bytes per second
    optional float read_latency = 4;  // ns

    optional float write_iops = 5;  // io per second
    optional float write_bandwidth = 6;  // bytes per second
    optional float write_latency = 7;  // ns

    optional float total_iops = 8;  // io per second
    optional float total_bandwidth = 9;  // bytes per second
    optional float total_latency = 10;  // ns

    optional float read_avg_request_size = 11; // Bytes
    optional float write_avg_request_size = 12; // Bytes
    optional float total_avg_request_size = 13; // Bytes
    optional float total_iop30s = 14;
    optional float iodepth = 15;

    // volume io perf after split
    optional float ioctx_local_readwrite_iops = 30;
    optional float ioctx_local_readwrite_speed_bps = 31;
    optional float ioctx_local_readwrite_latency   = 32;

    optional float ioctx_local_read_iops = 50;
    optional float ioctx_local_read_speed_bps = 51;
    optional float ioctx_local_read_latency = 52;

    optional float ioctx_local_write_iops = 70;
    optional float ioctx_local_write_speed_bps = 71;
    optional float ioctx_local_write_latency = 72;

    optional float ioctx_readwrite_iops = 90;
    optional float ioctx_readwrite_speed_bps = 91;
    optional float ioctx_readwrite_latency = 92;

    optional float ioctx_read_iops = 110;
    optional float ioctx_read_speed_bps = 111;
    optional float ioctx_read_latency = 112;

    optional float ioctx_write_iops = 130;
    optional float ioctx_write_speed_bps = 131;
    optional float ioctx_write_latency = 132;

    optional zbs.metric.proto.Histogram readwrite_size_range_bucket = 190;
    optional zbs.metric.proto.Histogram read_size_range_bucket = 191;
    optional zbs.metric.proto.Histogram write_size_range_bucket = 192;

    optional zbs.metric.proto.Histogram readwrite_logical_offset_range_bucket = 193;
    optional zbs.metric.proto.Histogram read_logical_offset_range_bucket = 194;
    optional zbs.metric.proto.Histogram write_logical_offset_range_bucket = 195;

    optional zbs.metric.proto.Histogram readwrite_physical_offset_range_bucket = 196;
    optional zbs.metric.proto.Histogram read_physical_offset_range_bucket = 198;
    optional zbs.metric.proto.Histogram write_physical_offset_range_bucket = 199;

    optional zbs.metric.proto.Histogram readwrite_latency_range_bucket = 200;
    optional zbs.metric.proto.Histogram read_latency_range_bucket = 201;
    optional zbs.metric.proto.Histogram write_latency_range_bucket = 202;

    // unmap info at volume level
    optional float unmap_iops = 205;  // count of unmap io per second
    optional float unmap_unaligned_iops = 206;  // count of unaligned unmap io per second
    optional uint64 unmap_total = 207;  // total count of unmap
    optional uint64 unmap_unaligned_total = 208;  // total count of unaligned unmap
}

message VolumesPerfRequest {
    repeated bytes volume_id_list = 1 [(zbs.labels).as_str = true];
}

message VolumesPerf {
    repeated VolumePerf perf_list = 1;
}

message ProbeVolumesRequest {
    repeated bytes volume_id_list   = 1 [(zbs.labels).as_str = true];
    repeated double latency_buckets = 2;
    repeated double size_buckets    = 3;
}

message DisableProbeVolumesRequest {
    repeated bytes volume_id_list = 1 [(zbs.labels).as_str = true];
    optional bool disable_all     = 2 [default = false];
}

service VolumePerfService {
    option (rpc_service_id) = 5002;

    rpc GetVolumePerf (VolumePerfRequest) returns (VolumePerf);
    rpc GetVolumesPerf (VolumesPerfRequest) returns (VolumesPerf);
    rpc GetAllVolumesPerf (Void) returns (VolumesPerf);
    rpc ProbeVolumes(ProbeVolumesRequest) returns (Void);
    rpc DisableProbeVolumes(DisableProbeVolumesRequest) returns (Void);
}

message UIOPerf {
    optional float readwrite_iops = 1;
    optional float read_iops = 2;
    optional float write_iops = 3;
    optional float readwrite_speed_bps = 4;
    optional float read_speed_bps = 5;
    optional float write_speed_bps = 6;
    optional float readwrite_latency = 7;
    optional float read_latency = 8;
    optional float write_latency = 9;

    optional float local_readwrite_iops = 40;
    optional float local_read_iops = 41;
    optional float local_write_iops = 42;
    optional float local_readwrite_speed_bps = 43;
    optional float local_read_speed_bps = 44;
    optional float local_write_speed_bps = 45;
    optional float local_readwrite_latency = 46;
    optional float local_read_latency = 47;
    optional float local_write_latency = 48;

    optional float retry_readwrite_iops = 80;
    optional float retry_read_iops = 81;
    optional float retry_write_iops = 82;

    optional float failed_readwrite_iops = 120;
    optional float failed_write_iops = 121;
    optional float failed_read_iops = 122;
    optional uint64 retry_queue_readwrite_size = 123;
    optional uint64 retry_queue_read_size = 124;
    optional uint64 retry_queue_write_size = 125;

    optional uint64 active_volumes_size = 161;
    optional uint64 active_extents_size = 162;
    optional uint64 waiting_queue_size = 163;
}

message LayeredAccessPerf {
    optional AccessPerf access_perf   = 1; // app + sink + reposition

    optional AccessPerf access_cap_replica_app_perf = 2;
    optional AccessPerf access_perf_replica_app_perf = 3;
    optional AccessPerf access_cap_ec_app_perf = 4;

    optional AccessPerf access_cap_replica_sink_perf = 5;
    optional AccessPerf access_perf_replica_sink_perf = 6;
    optional AccessPerf access_cap_ec_sink_perf = 7;

    optional AccessPerf access_cap_replica_reposition_perf = 8;
    optional AccessPerf access_perf_replica_reposition_perf = 9;
    optional AccessPerf access_cap_ec_reposition_perf = 10;
}

message FlowControllerChunkPerf {
    required uint32 fc_cid                          = 1;
    required uint32 fm_cid                          = 2;
    optional uint32 dst_perf_thin_not_free_ratio    = 3;

    // tokens info
    optional uint32 current_avail_tokens            = 4;
    optional uint32 used_tokens_no_wait_last_sec    = 5;
    optional uint32 used_tokens_after_wait_last_sec = 6;
    optional uint32 over_used_tokens_last_sec       = 7;
    optional uint32 avg_wait_token_latency          = 8;
    optional uint32 avg_wait_token_num              = 9;
}

message FlowControllerPerf {
    repeated FlowControllerChunkPerf fc_chunk_perf  = 1;
}

message FlowManagerChunkPerf {
    required uint32 fc_cid                          = 1;
    required uint32 fm_cid                          = 2;

    // tokens info
    optional uint32 requested_tokens_last_sec       = 3;
    optional uint32 used_tokens_last_sec            = 4;
    optional uint32 over_used_tokens_last_sec       = 5;
}

message FlowManagerPerf {
    optional bool flow_control_enable               = 1;
    optional uint32 avail_tokens                    = 2;
    optional uint32 perf_thin_used_ratio            = 3;
    repeated FlowManagerChunkPerf fm_chunk_perf     = 4;
    optional uint32 perf_thin_free_ratio            = 5;
}

message FlowControlPerf {
    optional FlowManagerPerf flow_mgr_perf      = 1;
    optional FlowControllerPerf flow_ctrl_perf  = 2;
}

message InternalFlowControllerTokenInfo {
    optional uint32 avail_token_num = 1;
    optional uint32 remain_token_num = 2;
    optional uint32 waiting_io_num = 3;

    optional uint32 no_wait_num_last_sec = 4;
    optional uint32 over_wait_num_last_sec = 5;
    optional uint32 after_wait_num_last_sec = 6;
    optional uint32 avg_wait_latency = 7;
}

message InternalFlowControllerChunkPerf {
    required uint32 ifm_cid = 1;
    optional InternalFlowControllerTokenInfo cap_token_info = 2;
    optional InternalFlowControllerTokenInfo perf_token_info = 3;
}

message InternalFlowControllerPerf {
    optional bool ifc_enable = 1;
    optional uint32 ifc_cid = 2;
    optional uint32 request_chunk_num = 3;
    optional uint32 high_waiting_io_num = 4;
    optional uint32 mid_waiting_io_num = 5;
    optional uint32 low_waiting_io_num = 6;
    repeated InternalFlowControllerChunkPerf ifc_chunk_perf = 7;
}

message InternalFlowManagerTokenInfo {
    optional uint32 req_num_last_sec = 1;
    optional uint32 used_num_last_sec = 2;
    optional uint32 over_used_num_last_sec = 3;
}

message InternalFlowManagerChunkPerf {
    required uint32 ifc_cid = 1;
    optional InternalFlowManagerTokenInfo cap_token_info = 2;
    optional InternalFlowManagerTokenInfo perf_token_info = 3;
}

message InternalFlowManagerPerf {
    optional bool ifm_enable = 1;
    optional uint32 ifm_cid = 2;
    optional uint32 cap_avail_token_num = 3;
    optional uint32 perf_avail_token_num = 4;
    optional uint32 cap_remain_token_num = 5;
    optional uint32 perf_remain_token_num = 6;
    repeated InternalFlowManagerChunkPerf ifm_chunk_perf = 7;
}

message InternalFlowControlPerf {
    optional InternalFlowManagerPerf ifm_perf = 1;
    optional InternalFlowControllerPerf ifc_perf = 2;
}

service ChunkPerfService {
    option (rpc_service_id) = 5003;
    rpc GetUIOPerf(Void) returns (UIOPerf);
    rpc GetAccessPerf(Void) returns (AccessPerf);
    rpc GetLSMPerf(Void) returns (LSMPerf);
    rpc GetLayeredAccessPerf(Void) returns (LayeredAccessPerf);
    rpc GetFlowControlPerf(Void) returns (FlowControlPerf);
    rpc GetInternalFlowControlPerf(Void) returns (InternalFlowControlPerf);
}
