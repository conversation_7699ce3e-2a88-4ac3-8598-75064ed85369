syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

package zbs.encryption;

enum InternalEncryptMethod {
    INTERNAL_ENCRYPT_UNKNOWN_ALGO = 0;
    INTERNAL_ENCRYPT_AES256_CTR = 1;
}

message EncryptMetaData {
    required uint64 metadata_id = 1;
    required bytes key = 2;
    required bytes init_vec = 3;
    required bytes cmk_id = 4;
    required string kmip_cluster_id = 5;

    // cipher_metadata_id is encrpted by plain_key and init_vec from metadata_id. It is used to verify if the plain_key
    // is valid or not for meta and access both.
    required uint64 cipher_metadata_id = 6;
    required InternalEncryptMethod method = 7;
}
