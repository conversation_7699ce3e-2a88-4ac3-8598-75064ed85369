syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

import "block.proto";
import "common.proto";
import "cdp.proto";
import "options.proto";

package zbs.chunk;

enum PartitionStatus {
    PARTITION_MOUNTED    = 0;
    PARTITION_STAGING    = 1;
    PARTITION_MIGRATING  = 2;
}

enum DiskErrFlags {
    DISK_ERR_SMART_FAIL                = 1;
    __DEPRECATED_DISK_ERR_HIGH_LAT     = 2;
    DISK_ERR_IO                        = 4;
    DISK_ERR_CHECKSUM                  = 8;
    __DEPRECATED_DISK_ERR_UMOUNTING    = 0x10;
    __DEPRECATED_DISK_ERR_FORCE_UMOUNT = 0X20;
    DISK_ERR_SYNC                      = 0x40;
}

enum DiskWarnFlags {
    DISK_WARN_HIGH_LAT                 = 1;
}

enum LSMCapability {
    // all defined capabilities should be pow of 2
    LSM_CAP_DISK_SAFE_UMOUNT           = 1;
    LSM_CAP_DISK_REJECT_UNHEALTHY      = 2;
    LSM_CAP_PARTITION_ISOLATE          = 4;
    LSM_CAP_COMPARE_EXTENT             = 8;
}

message PartitionInfo {
    required string path                = 1;
    optional string device_id           = 11;
    optional string uuid                = 12;
    optional uint64 total_size          = 3; // Bytes
    optional uint64 used_size           = 4; // Bytes

    optional uint64 num_checksum_errors = 5;
    optional uint64 num_io_errors       = 6;
    optional PartitionStatus status     = 7;
    optional uint32 errflags            = 8;
    optional uint64 num_slow_io         = 9;
    optional string part_uuid           = 14;
    optional uint32 warnflags           = 15;

    optional uint32 checksum_enable = 20;
}

enum CacheStatus {
    CACHE_MOUNTED    = 0;
    CACHE_STAGING    = 1;
    CACHE_MIGRATING  = 2;
}

message CacheInfo {
    required string path          = 1;
    optional string device_id     = 11;
    optional string uuid          = 12;

    optional uint64 num_blocks    = 2;  // deprecated
    optional uint64 num_active    = 3;  // deprecated
    optional uint64 num_inactive  = 4;  // deprecated
    optional uint64 num_free      = 5;  // deprecated
    optional uint64 num_io_errors = 6;
    optional CacheStatus status   = 7;
    optional uint32 errflags      = 8;
    optional uint64 num_slow_io   = 9;
    optional string part_uuid     = 13;
    optional uint64 num_used      = 14;  // deprecated
    optional uint32 warnflags     = 15;

    optional uint64 total_size = 21;
    optional uint64 used_size  = 22;
}

enum JournalStatus {
    JOURNAL_IDLE      = 0;
    JOURNAL_FULL      = 1;
    JOURNAL_BUSY      = 2;
    JOURNAL_UMOUNTING = 3;
    JOURNAL_ERROR     = 4;

    JOURNAL_INIT      = 999;
}

enum JournalGroupStatus {
    JOURNALGROUP_MOUNTED    = 0;
    JOURNALGROUP_STAGING    = 1;
}

message JournalGroupInfo {
    required string path                  = 1;
    optional string device_id             = 11;
    optional string uuid                  = 12;
    optional string part_uuid             = 13;

    optional uint32 errflags              = 21;
    optional uint64 num_slow_io           = 22;
    optional JournalGroupStatus status    = 23;
    optional uint64 num_io_errors         = 24;
    optional uint32 warnflags             = 25;

    optional uint32 id = 30 [default = 0];
    // DISK_ERR_UMOUNTING is enough to represent JournalGroup need umount.
    optional bool   __deprecated_auto_delete = 31 [default = false];
}

message JournalInfo {
    required string        path                = 1;
    optional uint64        reserved_entries    = 3;
    optional uint64        max_entries         = 4;

    optional uint64        seq_no              = 10;
    optional JournalStatus status              = 11;

    optional uint64        num_io_errors       = 13;
}

message MetaAddr {
    required uint32 meta_ip     = 1 [default = 0];
    required uint32 meta_port   = 2 [default = 0];
    required uint32 my_chunk_id = 3 [default = 0];
}

message ClientInfo {
    required string socket             = 1;
    required bool   writable           = 2;
    required uint32 handling_requests  = 3;
    required bool   sender_queue_empty = 4;
    required bool   closed             = 5;
}

message ListClientResponse {
    repeated ClientInfo clients = 1;
    optional uint64  total = 2;
}

message ZbsAddress {
    optional string    rpc_ip         = 2;
    optional int32     rpc_port       = 3;
    optional string    data_ip        = 4;
    optional int32     data_port      = 5;
    optional string    data_unix_path = 6;
    optional string    heartbeat_ip   = 7;
    optional int32     heartbeat_port = 8;
    optional string    meta_ip        = 9;
    optional int32     meta_port      = 10;
    optional uint32    chunk_id       = 11;
    optional string    secondary_data_ip  = 12;
    optional string    scvm_mode_host_data_ip  = 13;

    optional uint32 instance_id = 99 [default = 0];
}

message ZbsAddressV2 {
    repeated ZbsAddress instances_response = 1;
}

message GetZkHostsResponse {
    required string zk_hosts = 1;
}

enum DiskStatus {
    DISK_MOUNTED   = 0;
    DISK_AVAILABLE = 1;
    DISK_STAGING   = 2;
}

enum DiskType {
    DISK_TYPE_PARTITION = 0;
    DISK_TYPE_JOURNAL   = 1;
    DISK_TYPE_CACHE     = 2;
    DISK_TYPE_UNKNOWN   = 3;
}

enum DiskUmountMode {
    DISK_UMOUNT_AUTO    = 0;
    DISK_UMOUNT_MIGRATE = 1;
    DISK_UMOUNT_OFFLINE = 2;
}

enum DiskNameScheme {
    DISK_NAME_BY_UUID  = 0;  // uuid lsm writes on super block
    DISK_NAME_BY_PATH  = 1;
}

// deprecated
message GetDiskInfoRequest {
    required string path = 1;
}

// deprecated
message GetDiskInfoResponse {
    required DiskStatus   status     = 1;
    optional DiskType     disk_type  = 2;

    // Defined by disk_type
    optional string       version    = 3;

    optional string       device_id  = 4;
    optional string       uuid       = 5;
    optional uint64       used_size  = 6;
    optional uint64       total_size = 7;
}

message UpdateSecondaryDataIPRequest {
    required string new_ip = 1;
}

message UpdateScvmModeHostDataIPRequest {
    required string new_ip = 1;
}

message FormatPartitionRequest {
    required string path   = 1;

    // if the partition has already formatted, force re-format.
    optional bool   force  = 3 [default = false];
    optional bool   ignore_data_checksum = 4 [default = false];

    optional uint32 instance_id = 99 [default = 1];
}

message MountPartitionRequest {
    required string path = 1;

    // for mount, if the partition has data, force should be true.
    optional bool   force  = 3 [default = false];

    optional uint32 instance_id = 99 [default = 1];
}

message FormatCacheRequest {
    required string path = 1;
    // for cache, if the cache has data, force should be true.
    optional bool   force  = 3 [default = false];

    optional uint32 instance_id = 99 [default = 1];
}

message MountCacheRequest {
    required string path = 1;

    optional uint32 instance_id = 99 [default = 1];
}

message FormatJournalRequest {
    required string path  = 1;

    // if the partition has already formatted, force re-format.
    optional bool   force = 3 [default = false];

    optional uint32 instance_id = 99 [default = 1];
}

message MountJournalRequest {
    required string path   = 1;

    optional uint32 instance_id = 99 [default = 1];
}

message WorkerInfo {
    required uint32 worker_id = 1;
    required uint64 queue_size = 2;
}

message ListPartitionResponse {
    repeated PartitionInfo partitions = 2;
    repeated WorkerInfo    workers = 3;

    optional uint32 instance_id = 99 [default = 0];
}

message ListPartitionResponseV2 {
    repeated ListPartitionResponse instances_response = 1;
}

message SetUnhealthyRequest {
    required string         disk_name = 1;
    optional DiskNameScheme scheme = 5 [default=DISK_NAME_BY_PATH];
    optional DiskErrFlags   errflag = 2 [default = DISK_ERR_SMART_FAIL];
}

message CacheStat {
    optional double hit_rate = 1;
    optional uint64 promotion_bps = 11;
    optional uint64 writeback_bps = 12;
    optional uint64 used_bytes = 21;
    optional uint64 total_bytes = 22;
    optional uint64 active_bytes = 23;
    optional uint64 inactive_bytes = 24;
    optional uint64 clean_bytes = 25;
}

message ListCacheResponse {
    repeated CacheInfo caches   = 2;
    optional double    hit_rate = 3;  // deprecated
    optional uint64    total_active = 11;  // deprecated
    optional uint64    total_inactive = 12;  // deprecated
    optional uint64    total_clean = 13;  // deprecated
    optional CacheStat cache_stat = 21;

    optional uint32 instance_id = 99 [default = 0];
}

message ListCacheResponseV2 {
    repeated ListCacheResponse instances_response = 1;
}

message ListJournalResponse {
    repeated JournalInfo journals   = 2;
    optional uint32      disabled_group_no = 3 [deprecated=true];
    repeated JournalGroupInfo groups    = 4;

    optional uint32 instance_id = 99 [default = 0];
}

message ListJournalResponseV2 {
    repeated ListJournalResponse instances_response = 1;
}

message DiskInspectorStat {
    required string path = 1;
    required string uuid = 2;
    required DiskType type = 3; // cache, partition

    optional bool patrol_started = 4 [default = false];
    optional uint32 patrol_progress = 5 [default = 0];
    optional uint64 inspect_iops = 6 [default = 0];
    optional uint64 inspect_bw = 7 [default = 0];
}

message GetDiskInspectorStatsResponse {
    repeated DiskInspectorStat stats = 1;

    optional uint32 instance_id = 99 [default = 0];
}

message GetDiskInspectorStatsResponseV2 {
    repeated GetDiskInspectorStatsResponse instances_response = 1;
}

message PathRequest {
    required string path = 1;
}

// must be compatible with PathRequest and MountPartitionRequest
message UmountDiskRequest {
    required string          disk_name = 1;
    optional DiskNameScheme  scheme = 5 [default=DISK_NAME_BY_PATH];

    optional bool            __deprecated_force = 3 [deprecated=true];

    optional DiskUmountMode mode = 4 [default=DISK_UMOUNT_AUTO];
}

// must be compatible with PathRequest
message DiskRequest {
    required string           disk_name = 1;
    optional DiskNameScheme   scheme = 5 [default=DISK_NAME_BY_PATH];
}

message ChunkBlockInfo {
    required uint32 block_no = 1;
    required string hdd_path = 2;
    required uint64 hdd_offset = 3;
    optional string ssd_path = 4;
    optional uint64 ssd_offset = 5;
}

message ChunkExtentInfo {
    required uint32 pid          = 1;
    required uint32 status       = 2;
    required uint32 num_children = 3;
    required uint32 part_id      = 4;
    required uint32 extent_no    = 5;
    required uint32 origin_pid   = 6;
    optional bytes  bitmap       = 7;
    optional uint64 epoch        = 8;
    optional uint64 origin_epoch = 9;
    optional uint64 gen          = 10;
    optional bool thick_provision = 11 [default = false];
    optional bool perf = 12 [default = false];

    repeated ChunkBlockInfo blocks = 21;
}

message ListExtentResponse {
    repeated ChunkExtentInfo infos = 1;
}

message PExtentRequest {
    required uint32 pid = 1;
}

message ListPExtentRequest {
    optional uint32 start = 1 [default = 1];
    optional uint32 length = 2 [default = 1024];

    optional uint32 instance_id = 99 [default = 1];
}

message InvalidateExtentRequest {
    required uint32 pid = 1;
    required bool recursive = 2;
    optional uint64 epoch = 3;

    optional uint32 instance_id = 99 [default = 0];
}

message SetVerifyModeRequest {
    required string mode = 1;
}

message QueryDiskRequest {
    required string path = 1;
}

message QueryDiskResponse {
    optional bool in_use = 1;
    optional bool exist = 2;
    optional string type = 3; // cache, journal, partition
    optional uint32 errflags = 4;
    optional bool formatted = 5;
    optional bool match_host = 6;
    optional string device_id = 7;

    // The query may not find the disk, in this case, the instance_id is set to 0 here.
    optional uint32 instance_id = 99 [default = 0];
}

message RejectedDiskInfo {
    required string   id = 1;
    optional string   device_id = 2;
    optional DiskType last_mount_type = 3;
    optional uint32   errflags = 4;
    optional uint32   warnflags = 5;
}

message ListRejectedDisksResponse {
    repeated RejectedDiskInfo rejected_disks = 1;

    optional uint32 instance_id = 99 [default = 0];
}

message ListRejectedDisksResponseV2 {
    repeated ListRejectedDisksResponse instances_response = 1;
}

message AcceptDiskRequest {
    required string id = 1;
}

message DataChannelServerInfo {
    optional bool use_rdma = 1 [default = false];
}

message ChunkCapability {
    optional bool agile_recover = 1 [default = false];
}

message GatewayServerInfo {
    optional bool vhost_started = 1 [default = false];
    optional bool nvmf_started  = 2 [default = false];
    optional bool iscsi_started = 3 [default = false];
    optional bool nfs_started   = 4 [default = false];
}

message SummaryInfoResponse {
    optional LSMMeta lsm_meta = 1;
    optional fixed64 lsm_capability = 2 [default = 0];

    optional ChunkSpaceInfo space_info = 31;

    optional uint64 merge_iops = 11;
    optional uint64 merge_bw = 12;

    optional uint64 writeback_iops = 13;
    optional uint64 writeback_bw = 14;

    optional uint64 lsm_iops = 15;
    optional uint64 lsm_bw = 16;

    optional uint64 wait_reclaim = 20;
    optional uint64 wait_merge = 21;

    optional uint64 unmap_iops = 22;
    optional uint64 unmap_bw = 23;

    optional DataChannelServerInfo dcs_info = 41;
    optional ChunkCapability chunk_capability = 42;
    optional GatewayServerInfo gateway_info   = 43;

    optional bool metric_on = 50;
    optional bool trace_on = 51;
    optional bool adaptive_trace_on = 52;

    optional uint32 instance_id = 99 [default = 0];
}

message SummaryInfoResponseV2 {
    repeated SummaryInfoResponse instances_response = 1;
    optional uint32 chunk_instances_num = 2 [default = 1];
}

enum AuroraStatus {
    AURORA_STATUS_INITIALIZING = 1;
    AURORA_STATUS_RUNNING      = 2;
    AURORA_STATUS_STOPPING     = 3;
    AURORA_STATUS_STOPPED      = 4;
}

message ChunkServiceStat {
    required bool access_healthy     = 1 [default = false];  // deprecated
    required bool aurora_state       = 2 [default = false];  // deprecated
    optional uint64 access_alive_sec = 3 [default = 0];
    optional AuroraStatus aurora_status = 4;
    optional bool aurora_isolated = 5 [default = false];
}

message DirtyBlockRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    optional bool dry_run = 2 [default = false];
}

message DirtyBlockResponse {
    repeated uint64 blocks = 1;
}

message DiskRefInfo {
    required string path = 1;
    required uint32 num  = 2;
}

message ShowExtentRequest {
    required uint32 pid = 1;
    optional bool show_disk = 2;

    optional uint32 instance_id = 99 [default = 0];
}

message ShowExtentResponse {
    required uint32 pid = 1;
    required uint64 epoch = 2;
    required uint64 generation = 3;
    required uint32 status = 4;
    optional uint32 private_blob_num = 5;
    optional uint32 shared_blob_num = 6;
    repeated DiskRefInfo disk_refs = 7;
    optional bool thick_provision = 8 [default = false];
    optional bool perf = 9 [default = false];
    optional string additional_info = 11;

    repeated uint32 instance_ids = 99;
}

message PromoteExtentRequest {
    required uint32 pid = 1;
    optional uint64 offset = 2;
    optional uint64 length = 3 [default = 268435456];

    optional uint32 instance_id = 99 [default = 0];
}

message PollingThreadInfo {
    required int32 tid = 1;
    required string name = 2;
    required uint64 busy_tsc = 3;
    required uint64 idle_tsc = 4;
}

message GetPollingStatsResponse {
    repeated PollingThreadInfo polling_stats = 1;
}

message VolumeIOLatencyInjection {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    optional uint64 read_latency_ms = 2 [default = 0];
    optional uint64 write_latency_ms = 3 [default = 0];
    optional uint64 readwrite_latency_ms = 4 [default = 0];
}

message SetVolumeIOLatencyInjectionRequest {
    required bytes volume_id = 1 [(zbs.labels).as_str = true];
    optional uint64 read_latency_ms = 2 [default = 0];
    optional uint64 write_latency_ms = 3 [default = 0];
    optional uint64 readwrite_latency_ms = 4 [default = 0];
}

message SetVolumeIOLatencyInjectionResponse {
    required VolumeIOLatencyInjection injection = 1;
}

message ListVolumeIOLatencyInjectionResponse {
    repeated VolumeIOLatencyInjection injections = 1;
}

message ClearVolumeIOLatencyInjectionRequest {
    optional bytes volume_id = 1 [(zbs.labels).as_str = true];
    optional bool clear_all = 2;

}

message TemporaryExtent {
    required uint32 pid = 1;
    required uint64 epoch = 2;
    required uint32 temporary_pid = 3;
    required uint64 temporary_epoch = 4;
    required uint64 base_pextent_generation = 5;
    required uint64 pextent_generation = 6;
    optional uint32 unmap_times = 7;
}

message TemporaryUnmapBitmapSegment {
    required uint32 segment = 1 [default = 0];
    required uint32 temporary_pid = 2 [default = 0];
    required uint32 temporary_epoch = 3 [default = 0];
    required uint64 unmap_bitmap = 4 [default = 0];
}

message BytesBitmap {
    required uint32 sector_no = 1;
    required bytes bitmap = 2;
}

message TemporaryExtentSegmentBitmapV2 {
    required uint32 temporary_pid = 1;
    required uint64 temporary_epoch = 2;
    required bytes block_bitmap = 3;
    optional bytes sector_bitmaps = 4;
    repeated BytesBitmap bytes_bitmaps = 5;
    required uint32 segment = 6;
}

message SectorBitmap {
    required uint32 sector_no = 1;
    required bytes bitmap = 2;
}

message TemporaryExtentBitmapV1 {
    required uint32 temporary_pid = 1;
    required uint64 temporary_epoch = 2;
    required bytes block_bitmap = 3;
    repeated SectorBitmap sector_bitmaps = 4;
}

message TemporaryExtentBitmapV3 {
    required uint32 temporary_pid = 1;
    required uint64 temporary_epoch = 2;
    required bytes block_bitmap = 3;
    optional bytes unmap_bitmap = 4;
    optional bool invalid_bitmap = 5;
}

message GetTemporaryExtentRequest {
    required uint32 temporary_pid = 1;
    required uint64 temporary_epoch = 2;
}

message GetTemporaryExtentResponse {
    required TemporaryExtent temporary_extent = 1;
    optional TemporaryExtentBitmapV3 temporary_extent_bitmap = 2;
    optional uint64 temporary_generation = 3;

    optional uint32 instance_id = 99 [default = 1];
}

message ListTemporaryExtentRequest {
    required uint32 start_temporary_pid = 1 [default = 0];
    optional uint32 max_num = 2 [default = 2048];

    optional uint32 instance_id = 99 [default = 1];
}

message ListTemporaryExtentResponse {
    repeated TemporaryExtent temporary_extents = 1;
}

message GetTemporaryExtentSummaryResponse {
    required uint32 temporary_extent_num = 1;

    optional uint32 instance_id = 99 [default = 0];
}

message GetTemporaryExtentSummaryResponseV2 {
    repeated GetTemporaryExtentSummaryResponse instances_response = 1;
}

message ComparePExtentRequest {
    required uint32 pid1 = 1;
    required uint32 pid2 = 2;

    // caller_version of ComparePExtent regulates the requirement of caller and the cluster behavior.
    // caller_version = 1 means caller don't know tiering and compare proxy;
    // caller_version = 2 means caller knows compare proxy but not tiering (54x >= 544 or 55x >= 5.5.2);
    // caller_version = 3 means caller knows tiering (560 or after);
    optional uint32 caller_version = 3 [default = 1];
}

enum SinkTaskType {
    UNKNOWN_TYPE = 0;
    SINK_EXTENT = 1;
    SINK_BLOCK = 2;
    DRAIN_EXTENT = 3;
}

message SinkTask {
    required uint32 lid = 1;
    optional uint64 lepoch = 2;
    optional uint32 perf_pid = 3;
    optional uint64 perf_epoch = 4;
    optional uint32 perf_location = 5;
    optional uint32 block_no = 6;
    optional SinkTaskType type = 7;
}

message SetSinkParamsRequest {
    optional uint64 sink_task_num = 1;
    optional uint64 sink_io_concurrency = 2;

    optional uint32 instance_id = 99 [default = 0];
}

message ListSinkInfoResponse {
    message BlockLRUInfo {
        optional uint64 active_blocks = 1;
        optional uint64 inactive_blocks = 2;
        optional uint64 clean_blocks = 3;
        optional uint64 reserve_block_num = 4;
    }

    optional SinkCmd cur_sink_cmd = 1;
    repeated SinkTask sink_tasks = 2;
    optional BlockLRUInfo sinkable_lru_info = 3;
    optional BlockLRUInfo potential_sinkable_lru_info = 4;
    optional uint64 pending_inactive_extents = 5;
    repeated uint32 accelerate_cids = 6;
    optional uint32 pending_accelerate_blocks = 7;
    optional uint64 reserve_block_num = 8;
    optional uint64 max_sink_task_num = 9;
    optional uint64 max_sink_io_concurrency = 10;
    optional uint64 pending_child_extents = 11;
    optional uint64 pending_drain_cmds = 12;
    optional uint64 pending_inactive_blocks = 13;

    optional uint32 instance_id = 99 [default = 0];
}

message ListSinkInfoResponseV2 {
    repeated ListSinkInfoResponse instances_response = 1;
}

message SetCapIOThrottleRequest {
    optional uint32 max_cap_io_depth_limit_per_sata_hdd = 1;
    optional bool limit_256k_io = 2;
    optional string reserved_cap_io_depth = 3;
    optional string cap_io_depth_share = 4;
    optional bool limit_app_io = 5;

    optional uint32 instance_id = 99 [default = 0];
}

message CapIOThrottleInfo {
    optional string io_type = 1;
    optional uint32 cur_io_depth = 2;
    optional uint32 io_depth_limit = 3;
    optional uint32 reserved_io_depth = 4;
    optional uint32 io_depth_share = 5;
    optional uint32 pending_rw_io_num = 6;
    optional uint64 iops = 7;
    optional uint64 avg_latency_ns = 8;
}

message GetCapIOThrottleResponse {
    optional bool limit_256k_io = 1;
    optional uint32 total_io_depth_limit = 2;
    optional uint32 pending_rw_io_num = 3;
    optional uint32 pending_io_num = 4;
    optional bool limit_app_io = 5;

    repeated CapIOThrottleInfo infos = 10;

    optional uint32 instance_id = 99 [default = 0];
}

message GetCapIOThrottleResponseV2 {
    repeated GetCapIOThrottleResponse instances_response = 1;
}

message IOPerfStats {
    optional uint64 iops = 1;
    optional uint64 bps = 2;
    optional uint64 bps_limit = 3;
    optional uint64 max_bps_limit = 4;
}

message IOPerfReadWriteDetails {
    optional IOPerfStats from_local_read = 1;
    optional IOPerfStats from_remote_read = 2;
    optional IOPerfStats from_local_write = 3;
    optional IOPerfStats from_remote_write = 4;
}

message ThrottleBucketStats {
    optional uint64 current_level = 1;
    optional uint64 current_size = 2;
    optional uint64 release_speed = 3;
    optional uint64 waiting_io_num = 4;
}

message InternalIOThrottleInfo {
    optional uint64 internal_io_limit_per_sata_hdd = 1;
    optional uint64 app_io_busy_bps_per_sata_hdd = 2;
    optional uint64 app_io_busy_iops_per_sata_hdd = 3;
    optional double internal_flow_mgr_release_level_ratio = 4;

    optional IOPerfStats app_perf_perf = 5;
    optional IOPerfStats app_cap_perf = 6;

    optional IOPerfStats internal_perf_perf = 7;
    optional IOPerfStats internal_cap_perf = 8;

    // internal io throttle bucket details
    optional ThrottleBucketStats internal_perf_bucket_stats = 9;
    optional ThrottleBucketStats internal_cap_bucket_stats = 10;

    // internal io stats details
    optional IOPerfReadWriteDetails reposition_perf_replica_perf_details = 11;
    optional IOPerfReadWriteDetails reposition_cap_replica_perf_details = 12;
    optional IOPerfReadWriteDetails reposition_cap_ec_perf_details = 13;

    optional IOPerfReadWriteDetails sink_perf_replica_perf_details = 14;
    optional IOPerfReadWriteDetails sink_cap_replica_perf_details = 15;
    optional IOPerfReadWriteDetails sink_cap_ec_perf_details = 16;
    optional double max_reposition_waiting_time_ratio = 17;

    optional uint32 instance_id = 99 [default = 0];
}

message InternalIOThrottleInfoV2 {
    repeated InternalIOThrottleInfo instances_response = 1;
}

service ChunkService {
    option (rpc_service_id) = 0; // FIXME(keyue) change to 3000 in next major relase
    rpc GetZbsAddress (Void) returns (ZbsAddress);
    rpc FormatPartition (FormatPartitionRequest) returns (Void);
    rpc MountPartition (MountPartitionRequest) returns (Void);
    rpc UmountPartition (UmountDiskRequest) returns (Void);
    rpc MountCache (MountCacheRequest) returns (Void);
    rpc UmountCache (UmountDiskRequest) returns (Void);
    rpc FormatJournal (FormatJournalRequest) returns (Void);
    rpc MountJournal (MountJournalRequest) returns (Void);
    rpc UmountJournal (UmountDiskRequest) returns (Void);
    rpc FlushAllJournals (Void) returns (Void);
    rpc ListPartition (Void) returns (ListPartitionResponse);
    rpc ListJournal (Void) returns (ListJournalResponse);
    rpc ListCache (Void) returns (ListCacheResponse);
    rpc StopServer (Void) returns (Void);  // not supported
    rpc ListClient (Void) returns (ListClientResponse);  // deprecated
    rpc ListRecover (Void) returns (ListRecoverResponse);
    rpc ListMigrate (Void) returns (ListRecoverResponse);  // deprecated
    rpc ListExtent (ListPExtentRequest) returns (ListExtentResponse);
    rpc CheckExtent (PExtentRequest) returns (Void);  // not implemented
    rpc InvalidateCache (DiskRequest) returns (Void);  // deprecated
    rpc CheckAllExtents (Void) returns (Void);  // not implemented
    rpc ShowExtent (ShowExtentRequest) returns (ShowExtentResponse);
    rpc PromoteExtent (PromoteExtentRequest) returns (Void);
    rpc GetDiskInfo (GetDiskInfoRequest) returns (GetDiskInfoResponse); // deprecated
    rpc InvalidateExtent (InvalidateExtentRequest) returns (Void);
    rpc SetVerifyMode (SetVerifyModeRequest) returns (Void);
    rpc UpdateSecondaryDataIP (UpdateSecondaryDataIPRequest) returns (Void);
    rpc SetUnhealthyPartition(SetUnhealthyRequest) returns (Void);
    rpc SetHealthyPartition(DiskRequest) returns (Void);
    rpc SetUnhealthyCache(SetUnhealthyRequest) returns (Void);
    rpc SetHealthyCache(DiskRequest) returns (Void);
    rpc SetUnhealthyJournal(SetUnhealthyRequest) returns (Void);
    rpc SetHealthyJournal(DiskRequest) returns (Void);
    rpc FormatCache(FormatCacheRequest) returns (Void);
    rpc QueryDisk(QueryDiskRequest) returns (QueryDiskResponse);
    rpc SummaryInfo (Void) returns (SummaryInfoResponse);
    rpc UpdateScvmModeHostDataIP (UpdateScvmModeHostDataIPRequest) returns (Void);
    rpc GetChunkServiceStat(Void) returns (ChunkServiceStat);
    rpc ReloadConfig(Void) returns (Void);
    rpc GetZkHosts(Void) returns (GetZkHostsResponse);
    rpc CancelUmountPartition(DiskRequest) returns (Void);
    rpc CancelUmountCache(DiskRequest) returns (Void);
    rpc InvalidatePartition(DiskRequest) returns (Void);  // deprecated
    rpc ListRejectedDisks(Void) returns (ListRejectedDisksResponse);
    rpc AcceptDisk(AcceptDiskRequest) returns (Void);
    rpc IsolatePartition(DiskRequest) returns (Void);
    rpc StartAurora(Void) returns (Void);
    rpc StopAurora(Void) returns (Void);
    rpc ReloadHostName(Void) returns (Void);
    rpc GetDiskInspectorStats(Void) returns (GetDiskInspectorStatsResponse);
    rpc StartDiskInspectorPatrol(Void) returns (Void);
    rpc StopDiskInspectorPatrol(Void) returns (Void);
    rpc StartDirtyBlockTracking(DirtyBlockRequest) returns (Void);
    rpc StopDirtyBlockTracking(DirtyBlockRequest) returns (Void);
    rpc GetDirtyBlocks(DirtyBlockRequest) returns (DirtyBlockResponse);
    rpc GetCDPJob(GetCDPJobRequest) returns (CDPJobInfo);
    rpc ListCDPJobs(ListCDPJobsRequest) returns (ListCDPJobsResponse);
    rpc GetPollingStats(Void) returns (GetPollingStatsResponse);
    rpc SetVolumeIOLatencyInjection(SetVolumeIOLatencyInjectionRequest) returns (SetVolumeIOLatencyInjectionResponse);
    rpc ClearVolumeIOLatencyInjection(ClearVolumeIOLatencyInjectionRequest) returns(Void);
    rpc ListVolumeIOLatencyInjection(Void) returns(ListVolumeIOLatencyInjectionResponse);
    rpc GetTemporaryExtentSummary(Void) returns(GetTemporaryExtentSummaryResponse);
    rpc GetTemporaryExtent(GetTemporaryExtentRequest) returns(GetTemporaryExtentResponse);
    rpc ListTemporaryExtent(ListTemporaryExtentRequest) returns(ListTemporaryExtentResponse);
    rpc ComparePExtent(ComparePExtentRequest) returns (block.CompareExtentResponse);
    rpc ListSinkInfo(Void) returns (ListSinkInfoResponse);
    rpc SetSinkParams(SetSinkParamsRequest) returns (Void);
    rpc SetCapIOThrottle(SetCapIOThrottleRequest) returns (Void);
    rpc GetCapIOThrottle(Void) returns (GetCapIOThrottleResponse);
    rpc SetInternalIOThrottle(InternalIOThrottleInfo) returns (Void);
    rpc GetInternalIOThrottle(Void) returns (InternalIOThrottleInfo);
    rpc IsolateCache(DiskRequest) returns (Void);
    rpc GetRouteForRDMAUD(Void) returns (RDMAUDRouteInfo);
    rpc GetDCManagerInfo(Void) returns (DCManagerInfos);
    rpc GetDCServerInfo(Void) returns (DCServerInfos);

    // The following V2 version RPCs are introduced for multi chunk instances mode.
    // These RPCs are used to obtain some information about Chunk, and they can return all instances' responses now.
    rpc GetZbsAddressV2 (Void) returns (ZbsAddressV2);
    rpc ListPartitionV2 (Void) returns (ListPartitionResponseV2);
    rpc ListJournalV2 (Void) returns (ListJournalResponseV2);
    rpc ListCacheV2 (Void) returns (ListCacheResponseV2);
    rpc ListRecoverV2 (Void) returns (ListRecoverResponseV2);
    rpc ListMigrateV2 (Void) returns (ListRecoverResponseV2);  // deprecated
    rpc SummaryInfoV2 (Void) returns (SummaryInfoResponseV2);
    rpc ListRejectedDisksV2(Void) returns (ListRejectedDisksResponseV2);
    rpc GetDiskInspectorStatsV2(Void) returns (GetDiskInspectorStatsResponseV2);
    rpc GetTemporaryExtentSummaryV2(Void) returns(GetTemporaryExtentSummaryResponseV2);
    rpc ListSinkInfoV2(Void) returns (ListSinkInfoResponseV2);
    rpc GetCapIOThrottleV2(Void) returns (GetCapIOThrottleResponseV2);
    rpc GetInternalIOThrottleV2(Void) returns (InternalIOThrottleInfoV2);
}

message RDMAUDRoute {
    optional uint32 lid = 1;
    optional uint32 qpn = 2;
    optional string gid = 3;
}

message RDMAUDRouteInfo {
    repeated RDMAUDRoute route = 1;
}

enum TransportType {
    TRANSPORT_TYPE_UNIX = 1;
    TRANSPORT_TYPE_TCP = 2;
    TRANSPORT_TYPE_RDMA = 3;
}

enum DataChannelVersion {
    DATA_CHANNEL_V1 = 1;
    DATA_CHANNEL_V2 = 2;
    DATA_CHANNEL_V3 = 3;
}

message MultipathInfo {
    optional string local_send_if = 1;
    optional string local_recv_if = 2;
    optional string remote_send_if = 3;
    optional string remote_recv_if = 4;
    optional uint64 pid_num = 5;
    optional uint32 flow_label = 6;
}

message TransportInfo {
    optional string local_address = 1;
    optional string remote_address = 2;
    optional TransportType transport_type = 3;
    optional uint64 up_time = 4; // in seconds
    optional MultipathInfo mpath_info = 5;

    extensions 10000 to max;  // for customization
}

message RDMATransportInfo {
    extend TransportInfo {
        optional RDMATransportInfo rdma_info = 10001;
    }

    message IORate {
        optional uint64 total_io_bytes = 1;
        optional uint64 io_rate_packet = 2;
        optional uint64 io_rate_bytes = 3;
    }

    optional uint64 num_handling_send_wr = 1;
    optional uint64 num_handling_recv_wr = 2;
    optional uint64 num_send_ops_in_last_interval = 3;
    optional uint64 num_recv_ops_in_last_interval = 4;
    optional IORate send_rate = 5;
    optional IORate read_rate = 6;
    optional IORate write_rate = 7;
}

message DCServerInfo {
    repeated TransportInfo transports = 1;
    optional DataChannelVersion version = 2;

    optional uint32 instance_id = 99 [default = 0];
}

message DCServerInfos {
    repeated DCServerInfo instances_response = 1;
}

message DCClientInfo {
    // if data channel client use multipath, one client may have multiple transports
    repeated TransportInfo transports = 1;

    optional uint32 aggr_server_response_times = 2;
    optional uint32 aggr_accumulate_error_times = 3;
    optional uint32 aggr_last_second_error_times = 4;
}

message DCManagerInfo {
    repeated DCClientInfo dc_clients = 1;
    optional DataChannelVersion version = 2;

    optional uint32 instance_id = 99 [default = 0];
}

message DCManagerInfos {
    repeated DCManagerInfo instances_response = 1;
}

message DataChannelInfo {
    optional DCServerInfo dcs_info = 1;
    optional DCManagerInfo dcm_info = 2;
}
