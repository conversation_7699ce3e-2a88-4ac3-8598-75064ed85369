syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

package zbs.libmeta;

message CpuInfo {
    required string model = 1;
    required uint32 socket = 2;
    required uint32 cores_per_socket = 3;
}

message MemInfo {
    required uint32 total = 1;  // mb
    required uint32 used = 2;  // mb
}

message DiskInfo {
    optional uint32 disk_id = 1;
    optional string disk_model = 2;
    optional uint64 total = 3;
    optional uint64 used = 4;
    optional string error_reason = 6 [ default = ""];
}

message NicInfo {
}

message ProcessInfo {
}
