syntax = "proto2";

option cc_generic_services = true;
option py_generic_services = true;

import "common.proto";
import "options.proto";

package zbs.task;

enum TaskState {
    NONE = 0;
    CREATED = 1;
    IN_PROGRESS = 2;
    SUCCEED = 3;
    FAILED = 4;
    CANCELED = 5;
    PAUSED = 6;
}

enum CopyVolumeTaskState {
    CV_INIT = 0;
    CV_COPY_EXTENT = 1;
    CV_COPY_EXTENT_DONE = 2;
    CV_CANCELED = 3;
}

message TaskProgress {
    optional float percentage = 1 [default = 0.0];  // progress if valid
    optional bytes message = 2 [default = "", (zbs.labels).as_str = true];  // message of the current status
}

message Task {
    optional bytes id = 1 [(zbs.labels).as_str = true];
    optional TaskState state = 2 [default = NONE];
    optional RpcStatus exit_status = 3;
    optional Address specified_runner_addr = 4;  // specify which runner to use. Use it only you really want to do so.
    optional bytes name = 5 [default = "", (zbs.labels).as_str = true];  // the name of the task
    optional TaskProgress progress = 6;  // the task runner can update progress here
    optional Address runner_addr = 7;  // runner running this task
    optional uint64 created_ms = 8;  // time since epoch
    optional uint64 finished_ms = 9;  // time since epoch
    optional uint64 max_schedule_times = 10 [ default = 10 ];  // max retry times to schedule
    optional uint64 schedule_times = 11 [ default = 0 ];
    optional uint64 last_failed_ms = 12 [ default = 0];

    // for internal use
    optional int32 service_id = 20 [default = 0];  // service id
    optional int32 method_id = 21 [default = 0];  // method id

    optional TaskState expect_state = 30 [default = NONE];

    // for task extensions: don't use duplicate extension id. each task should
    // have different extension id
    extensions 20000 to max;  // for specfied task
}

message VirtualIP {
    required string service_name = 1;
    required string ip = 2;
}

message VirtualIPs {
    repeated VirtualIP vips = 1;
}

message Tasks {
    repeated Task tasks = 1;
}

message TaskId {
    required bytes id = 1 [(zbs.labels).as_str = true];
}

message ListTaskByDateRequest {
    // list tasks created with in the date range specified by the time since epoch.
    optional uint64 start_ms_since_epoch = 1;
    optional uint64 end_ms_since_epoch = 2;
}

message ListTaskByStatusRequest {
    optional bool finished = 1 [default = false];
}


message GatewayEndpoint {
    required string ip = 1;
    required int32 rpc_port = 2;
    required int32 data_port = 3;
}

message RsyncTask {
    extend Task {
        optional RsyncTask task = 20004;
    }

    optional string hosts = 1;
    optional string remote_hosts = 2;
    optional bool use_compression = 3;

    extensions 60000 to max;
}

// copy volume can be use for deep copy
message CopyVolumeTask {
    extend Task {
        optional CopyVolumeTask task = 20005;
    }
    // field for task continue
    required bytes src_volume_id = 1 [(zbs.labels).as_str = true];
    required bytes dst_volume_id = 2 [(zbs.labels).as_str = true];
    required bytes src_hosts = 3 [(zbs.labels).as_str = true];
    required bytes dst_hosts = 4 [(zbs.labels).as_str = true];
    optional uint64 cur_extent = 5;
    optional CopyVolumeTaskState cur_state = 6 [ default = CV_INIT ];
    optional bool skip_zero = 7 [ default = true ];
    optional bool use_compression = 8 [ default = false ];
    optional uint32 preferred_cid = 9 [ default = 0 ];
    // The following are auto generated/updated for metrics
    // src volume size
    optional uint64 volume_size_bytes = 10;

    // currently synced volume bytes
    optional uint64 synced_volume_bytes = 11;

    // actual transfer size, actual_transfer_size / synced_volume_bytes is the
    // dedup ratio
    optional uint64 transferred_volume_bytes = 12;

    // current transferred speed over the network (Bps)
    optional uint64 net_speed_Bps = 13;

    // current sync speed (Bps)
    optional uint64 sync_speed_Bps = 14;

    optional bytes base_volume_id = 15 [(zbs.labels).as_str = true];

    // The following are newly-added fields for task
    optional uint32 io_depth = 20 [ default = 32 ];
    optional uint64 bps_max = 21 [ default = 314572800 ];  // 300 MiB/s
    optional RunTime runtime = 22;
}

message SyncVolumeTask {
    extend RsyncTask {
        optional SyncVolumeTask task = 60001;
    }
    optional bytes volume_id = 1 [(zbs.labels).as_str = true];
    optional bytes base_volume_id = 2 [(zbs.labels).as_str = true];
    optional bytes remote_volume_id = 3 [(zbs.labels).as_str = true];
    // preferred chunk id
    optional uint32 preferred_cid = 9 [ default = 0 ];

    // The following are auto generated/updated for metrics
    // src volume size
    optional uint64 volume_size_bytes = 4;

    // currently synced volume bytes
    optional uint64 synced_volume_bytes = 5;

    // actual transfer size, actual_transfer_size / synced_volume_bytes is the
    // dedup ratio
    optional uint64 transferred_volume_bytes = 6;

    // current transferred speed over the network (Bps)
    optional uint64 net_speed_Bps = 7;

    // current sync speed (Bps)
    optional uint64 sync_speed_Bps = 8;
}

message RunTime {
    required uint32 start_hour = 1 [ default = 0 ];
    required uint32 start_min = 2 [ default = 0 ];
    required uint32 end_hour = 3 [ default = 23 ];
    required uint32 end_min = 4 [ default = 59 ];
}

message SetRunTimeRequest {
    required TaskId task_id = 1;
    required RunTime runtime = 2;
}

message SetBpsMaxRequest {
    required TaskId task_id = 1;
    required uint64 bps_max = 2;
}

service StatusService {
    option (rpc_service_id) = 6001;

    rpc ListRunner(Void) returns (Addresses);
    rpc ListTaskByDate(ListTaskByDateRequest) returns (Tasks);
    rpc ListTaskByStatus(ListTaskByStatusRequest) returns (Tasks);
    rpc ShowTask(TaskId) returns (Task);
    rpc CancelTask(TaskId) returns (Task);
    rpc PauseTask(TaskId) returns (Task);
    rpc ResumeTask(TaskId) returns (Task);
    rpc SetRunTime(SetRunTimeRequest) returns (Task);
    rpc SetBpsMax(SetBpsMaxRequest) returns (Task);
}

service VIPService {
    option (rpc_service_id) = 6003;

    rpc SetVIP(VirtualIP) returns (Void);
    rpc DeleteVIP(VirtualIP) returns (Void);
    rpc ShowVIP(Void) returns (VirtualIPs);
}

service RsyncService {
    option (rpc_service_id) = 6006;

    rpc SyncVolume (Task) returns (Task);
}

service CopyVolumeService {
    option (rpc_service_id) = 6007;

    rpc CopyVolume(Task) returns (Task);
}
