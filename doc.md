<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# zbs

```go
import "github.com/iomesh/zbs-client-go/zbs"
```


## Index

- [Constants](#constants)
- [type CID](#CID)
- [type ChunkPerfService](#ChunkPerfService)
  - [func NewChunkPerfService\(client \*zrpc.Client\) \*ChunkPerfService](#NewChunkPerfService)
  - [func \(s \*ChunkPerfService\) GetAccessPerf\(ctx context.Context\) \(\*zbs.AccessPerf, error\)](#ChunkPerfService.GetAccessPerf)
  - [func \(s \*ChunkPerfService\) GetLSMPerf\(ctx context.Context\) \(\*zbs.LSMPerf, error\)](#ChunkPerfService.GetLSMPerf)
  - [func \(s \*ChunkPerfService\) GetUIOPerf\(ctx context.Context\) \(\*zbs.UIOPerf, error\)](#ChunkPerfService.GetUIOPerf)
- [type ChunkService](#ChunkService)
  - [func NewChunkService\(client \*zrpc.Client\) \*ChunkService](#NewChunkService)
  - [func \(s \*ChunkService\) CheckAllExtents\(ctx context.Context\) error](#ChunkService.CheckAllExtents)
  - [func \(s \*ChunkService\) CheckExtent\(ctx context.Context, pid uint32\) error](#ChunkService.CheckExtent)
  - [func \(s \*ChunkService\) CompareExtent\(ctx context.Context, pid1 uint32, pid2 uint32\) \(\*chunkv1.CompareExtentResponse, error\)](#ChunkService.CompareExtent)
  - [func \(s \*ChunkService\) FlushAllJournals\(ctx context.Context\) error](#ChunkService.FlushAllJournals)
  - [func \(s \*ChunkService\) FormatCache\(ctx context.Context, path string, force bool\) error](#ChunkService.FormatCache)
  - [func \(s \*ChunkService\) FormatJournal\(ctx context.Context, path string, force bool\) error](#ChunkService.FormatJournal)
  - [func \(s \*ChunkService\) FormatPartition\(ctx context.Context, path string, force bool, ignoreDataChecksum bool\) error](#ChunkService.FormatPartition)
  - [func \(s \*ChunkService\) GetZbsAddress\(ctx context.Context\) \(\*chunkv1.ZbsAddress, error\)](#ChunkService.GetZbsAddress)
  - [func \(s \*ChunkService\) InvalidateCache\(ctx context.Context, path string\) error](#ChunkService.InvalidateCache)
  - [func \(s \*ChunkService\) InvalidateExtent\(ctx context.Context, req \*chunkv1.InvalidateExtentRequest\) error](#ChunkService.InvalidateExtent)
  - [func \(s \*ChunkService\) ListCache\(ctx context.Context\) \(\*chunkv1.ListCacheResponse, error\)](#ChunkService.ListCache)
  - [func \(s \*ChunkService\) ListClient\(ctx context.Context\) \(\*chunkv1.ListClientResponse, error\)](#ChunkService.ListClient)
  - [func \(s \*ChunkService\) ListExtent\(ctx context.Context\) \(\*chunkv1.ListExtentResponse, error\)](#ChunkService.ListExtent)
  - [func \(s \*ChunkService\) ListJournal\(ctx context.Context\) \(\*chunkv1.ListJournalResponse, error\)](#ChunkService.ListJournal)
  - [func \(s \*ChunkService\) ListPartition\(ctx context.Context\) \(\*chunkv1.ListPartitionResponse, error\)](#ChunkService.ListPartition)
  - [func \(s \*ChunkService\) ListRecover\(ctx context.Context\) \(\*zbsv1.ListRecoverResponse, error\)](#ChunkService.ListRecover)
  - [func \(s \*ChunkService\) MergeExtent\(ctx context.Context, pid uint32\) error](#ChunkService.MergeExtent)
  - [func \(s \*ChunkService\) MountCache\(ctx context.Context, path string\) error](#ChunkService.MountCache)
  - [func \(s \*ChunkService\) MountJournal\(ctx context.Context, path string\) error](#ChunkService.MountJournal)
  - [func \(s \*ChunkService\) MountPartition\(ctx context.Context, path string, force bool\) error](#ChunkService.MountPartition)
  - [func \(s \*ChunkService\) QueryDisk\(ctx context.Context, path string\) \(\*chunkv1.QueryDiskResponse, error\)](#ChunkService.QueryDisk)
  - [func \(s \*ChunkService\) SetHealthyCache\(ctx context.Context, req \*chunkv1.SetUnhealthyRequest\) error](#ChunkService.SetHealthyCache)
  - [func \(s \*ChunkService\) SetHealthyJournal\(ctx context.Context, req \*chunkv1.SetUnhealthyRequest\) error](#ChunkService.SetHealthyJournal)
  - [func \(s \*ChunkService\) SetHealthyPartition\(ctx context.Context, req \*chunkv1.SetUnhealthyRequest\) error](#ChunkService.SetHealthyPartition)
  - [func \(s \*ChunkService\) SetUnhealthyCache\(ctx context.Context, req \*chunkv1.SetUnhealthyRequest\) error](#ChunkService.SetUnhealthyCache)
  - [func \(s \*ChunkService\) SetUnhealthyJournal\(ctx context.Context, req \*chunkv1.SetUnhealthyRequest\) error](#ChunkService.SetUnhealthyJournal)
  - [func \(s \*ChunkService\) SetUnhealthyPartition\(ctx context.Context, req \*chunkv1.SetUnhealthyRequest\) error](#ChunkService.SetUnhealthyPartition)
  - [func \(s \*ChunkService\) SetVerifyMode\(ctx context.Context, req \*chunkv1.SetVerifyModeRequest\) error](#ChunkService.SetVerifyMode)
  - [func \(s \*ChunkService\) ShowExtent\(ctx context.Context, pid uint32\) \(\*chunkv1.ShowExtentResponse, error\)](#ChunkService.ShowExtent)
  - [func \(s \*ChunkService\) StopServer\(ctx context.Context\) error](#ChunkService.StopServer)
  - [func \(s \*ChunkService\) SummaryInfo\(ctx context.Context\) \(\*chunkv1.SummaryInfoResponse, error\)](#ChunkService.SummaryInfo)
  - [func \(s \*ChunkService\) UmountCache\(ctx context.Context, path string\) error](#ChunkService.UmountCache)
  - [func \(s \*ChunkService\) UmountJournal\(ctx context.Context, path string\) error](#ChunkService.UmountJournal)
  - [func \(s \*ChunkService\) UmountPartition\(ctx context.Context, path string, force bool\) error](#ChunkService.UmountPartition)
- [type Client](#Client)
  - [func NewClient\(watcher \*zrpc.LeaderWatcher\) \(\*Client, error\)](#NewClient)
  - [func NewClientWithConfig\(config \*zrpc.Config\) \(\*Client, error\)](#NewClientWithConfig)
  - [func NewStaticClient\(addr string\) \(\*Client, error\)](#NewStaticClient)
  - [func \(c \*Client\) Close\(\) error](#Client.Close)
  - [func \(c \*Client\) GetRpcClient\(\) \*zrpc.Client](#Client.GetRpcClient)
- [type CreateTopoObjOption](#CreateTopoObjOption)
- [type DataChannelService](#DataChannelService)
  - [func NewDataChannelService\(endpoint string, config \*dc.Config\) \(\*DataChannelService, error\)](#NewDataChannelService)
  - [func \(dcs \*DataChannelService\) Close\(\) error](#DataChannelService.Close)
  - [func \(dcs \*DataChannelService\) Ping\(ctx context.Context\) error](#DataChannelService.Ping)
  - [func \(dcs \*DataChannelService\) VolumeRead\(ctx context.Context, volumeID string, buf \[\]byte, length uint32, offset uint64, flags dc.Flags\) error](#DataChannelService.VolumeRead)
  - [func \(dcs \*DataChannelService\) VolumeWrite\(ctx context.Context, volumeID string, buf \[\]byte, length uint32, offset uint64, flags dc.Flags, preferredCid dc.Cid\) error](#DataChannelService.VolumeWrite)
- [type DiffIterator](#DiffIterator)
  - [func \(it \*DiffIterator\) Error\(\) error](#DiffIterator.Error)
  - [func \(it \*DiffIterator\) HasNext\(\) bool](#DiffIterator.HasNext)
  - [func \(it \*DiffIterator\) Next\(\) RangeUint64](#DiffIterator.Next)
- [type ISCSILun](#ISCSILun)
- [type ISCSIService](#ISCSIService)
  - [func NewISCSIService\(client \*zrpc.Client\) \*ISCSIService](#NewISCSIService)
  - [func \(s \*ISCSIService\) AddLunAllowedInitiators\(ctx context.Context, lunUuId string, initiators \[\]string\) \(\*meta.ISCSILun, error\)](#ISCSIService.AddLunAllowedInitiators)
  - [func \(s \*ISCSIService\) CreateLun\(ctx context.Context, targetName string, lunName string, lunId uint32, size uint64, opts ...LunOptionSetter\) \(\*ISCSILun, error\)](#ISCSIService.CreateLun)
  - [func \(s \*ISCSIService\) CreateLunBySecondaryId\(ctx context.Context, targetId string, lunName string, secondaryId string, size uint64, opts ...LunOptionSetter\) \(\*ISCSILun, error\)](#ISCSIService.CreateLunBySecondaryId)
  - [func \(s \*ISCSIService\) CreateLunByTargetUUID\(ctx context.Context, targetId string, lunName string, lunId uint32, size uint64, opts ...LunOptionSetter\) \(\*ISCSILun, error\)](#ISCSIService.CreateLunByTargetUUID)
  - [func \(s \*ISCSIService\) CreateSnapshot\(ctx context.Context, lunUuid string, name string, desc string\) \(\*ISCSISnapshot, error\)](#ISCSIService.CreateSnapshot)
  - [func \(s \*ISCSIService\) CreateSnapshotBySecondaryId\(ctx context.Context, lunUuid string, secondaryId string, name string, desc string\) \(\*ISCSISnapshot, error\)](#ISCSIService.CreateSnapshotBySecondaryId)
  - [func \(s \*ISCSIService\) CreateTarget\(ctx context.Context, name string, sp \*StoragePolicy, config \*ISCSITargetConfig\) \(\*ISCSITarget, error\)](#ISCSIService.CreateTarget)
  - [func \(s \*ISCSIService\) DeleteLun\(ctx context.Context, lunUuid string\) error](#ISCSIService.DeleteLun)
  - [func \(s \*ISCSIService\) DeleteLunBySecondaryId\(ctx context.Context, secondaryId string\) error](#ISCSIService.DeleteLunBySecondaryId)
  - [func \(s \*ISCSIService\) DeleteSnapshot\(ctx context.Context, snapshotId string\) error](#ISCSIService.DeleteSnapshot)
  - [func \(s \*ISCSIService\) DeleteSnapshotBySecondaryId\(ctx context.Context, secondaryId string\) error](#ISCSIService.DeleteSnapshotBySecondaryId)
  - [func \(s \*ISCSIService\) DeleteTarget\(ctx context.Context, id string\) error](#ISCSIService.DeleteTarget)
  - [func \(s \*ISCSIService\) DescribeLun\(ctx context.Context, lunUuid string\) \(\*ISCSILun, error\)](#ISCSIService.DescribeLun)
  - [func \(s \*ISCSIService\) DescribeLunBySecondaryId\(ctx context.Context, secondaryId string\) \(\*ISCSILun, error\)](#ISCSIService.DescribeLunBySecondaryId)
  - [func \(s \*ISCSIService\) DescribeSnapshot\(ctx context.Context, snapshotId string\) \(\*ISCSISnapshot, error\)](#ISCSIService.DescribeSnapshot)
  - [func \(s \*ISCSIService\) DescribeSnapshotBySecondaryId\(ctx context.Context, secondaryId string\) \(\*ISCSISnapshot, error\)](#ISCSIService.DescribeSnapshotBySecondaryId)
  - [func \(s \*ISCSIService\) DescribeTarget\(ctx context.Context, id string\) \(\*ISCSITarget, error\)](#ISCSIService.DescribeTarget)
  - [func \(s \*ISCSIService\) GetLuns\(ctx context.Context, targetId string\) \(\[\]\*ISCSILun, error\)](#ISCSIService.GetLuns)
  - [func \(s \*ISCSIService\) GetTargets\(ctx context.Context\) \(\[\]\*ISCSITarget, error\)](#ISCSIService.GetTargets)
  - [func \(s \*ISCSIService\) ListLunSnapshot\(ctx context.Context, lunUuid string\) \(\[\]\*ISCSISnapshot, error\)](#ISCSIService.ListLunSnapshot)
  - [func \(s \*ISCSIService\) ListTargetSnapshot\(ctx context.Context, targetId string\) \(\[\]\*ISCSISnapshot, error\)](#ISCSIService.ListTargetSnapshot)
  - [func \(s \*ISCSIService\) RemoveLunAllowedInitiators\(ctx context.Context, lunUuId string, initiators \[\]string\) \(\*meta.ISCSILun, error\)](#ISCSIService.RemoveLunAllowedInitiators)
  - [func \(s \*ISCSIService\) UpdateLun\(ctx context.Context, lunUuid string, opts ...LunOptionSetter\) \(\*ISCSILun, error\)](#ISCSIService.UpdateLun)
  - [func \(s \*ISCSIService\) UpdateTarget\(ctx context.Context, id string, newName \*string, config \*ISCSITargetConfig\) \(\*ISCSITarget, error\)](#ISCSIService.UpdateTarget)
- [type ISCSISnapshot](#ISCSISnapshot)
- [type ISCSITarget](#ISCSITarget)
- [type ISCSITargetConfig](#ISCSITargetConfig)
- [type InitiatorChapInfo](#InitiatorChapInfo)
- [type LunCloneOptions](#LunCloneOptions)
- [type LunOptionSetter](#LunOptionSetter)
  - [func WithLunAllowedInitiators\(allowedInitiators \[\]string\) LunOptionSetter](#WithLunAllowedInitiators)
  - [func WithLunCloneOpts\(co \*LunCloneOptions\) LunOptionSetter](#WithLunCloneOpts)
  - [func WithLunDescription\(description string\) LunOptionSetter](#WithLunDescription)
  - [func WithLunNewAllowedInitiators\(newAllowedInitiators \[\]string\) LunOptionSetter](#WithLunNewAllowedInitiators)
  - [func WithLunNewName\(newName string\) LunOptionSetter](#WithLunNewName)
  - [func WithLunNewSingleAccess\(singleAccess bool\) LunOptionSetter](#WithLunNewSingleAccess)
  - [func WithLunNewSize\(newSize uint64\) LunOptionSetter](#WithLunNewSize)
  - [func WithLunPath\(lp \*meta.LunPath\) LunOptionSetter](#WithLunPath)
  - [func WithLunReplicaNum\(rn uint32\) LunOptionSetter](#WithLunReplicaNum)
  - [func WithLunSingleAccess\(singleAccess bool\) LunOptionSetter](#WithLunSingleAccess)
  - [func WithLunSize\(size uint64\) LunOptionSetter](#WithLunSize)
  - [func WithLunStoragePolicy\(sp \*StoragePolicy\) LunOptionSetter](#WithLunStoragePolicy)
  - [func WithLunThinProvision\(tp bool\) LunOptionSetter](#WithLunThinProvision)
  - [func WithLunThrottling\(tc \*meta.IOThrottleConfig\) LunOptionSetter](#WithLunThrottling)
  - [func WithTargetRequirement\(tr \*meta.TargetRequirement\) LunOptionSetter](#WithTargetRequirement)
- [type LunOptions](#LunOptions)
- [type MetaChunkService](#MetaChunkService)
  - [func NewMetaChunkService\(client \*zrpc.Client\) \*MetaChunkService](#NewMetaChunkService)
  - [func \(s \*MetaChunkService\) AddChunkToStoragePool\(ctx context.Context, storagePoolID string, chunkID CID\) \(\*zbsv1.StoragePool, error\)](#MetaChunkService.AddChunkToStoragePool)
  - [func \(s \*MetaChunkService\) BanChunk\(ctx context.Context, id \*zbsv1.ChunkId\) error](#MetaChunkService.BanChunk)
  - [func \(s \*MetaChunkService\) CreateStoragePool\(ctx context.Context, name string, chunks ...CID\) \(\*zbsv1.StoragePool, error\)](#MetaChunkService.CreateStoragePool)
  - [func \(s \*MetaChunkService\) CreateTopoObj\(ctx context.Context, topoType \*zbsv1.TopoType, options ...CreateTopoObjOption\) \(\*zbsv1.TopoObj, error\)](#MetaChunkService.CreateTopoObj)
  - [func \(s \*MetaChunkService\) DeleteStoragePool\(ctx context.Context, id string\) \(\*zbsv1.StoragePool, error\)](#MetaChunkService.DeleteStoragePool)
  - [func \(s \*MetaChunkService\) DeleteTopoObj\(ctx context.Context, id \*zbsv1.TopoObjId\) \(\*zbsv1.TopoObj, error\)](#MetaChunkService.DeleteTopoObj)
  - [func \(s \*MetaChunkService\) GetChunkTopology\(ctx context.Context, id \*zbsv1.ChunkId\) \(\*zbsv1.ChunkTopology, error\)](#MetaChunkService.GetChunkTopology)
  - [func \(s \*MetaChunkService\) LeaveService\(ctx context.Context, id \*zbsv1.ChunkId\) error](#MetaChunkService.LeaveService)
  - [func \(s \*MetaChunkService\) ListChunk\(ctx context.Context\) \(\*zbsv1.Chunks, error\)](#MetaChunkService.ListChunk)
  - [func \(s \*MetaChunkService\) ListPid\(ctx context.Context, id \*zbsv1.ChunkId\) \(\*zbsv1.ChunkPids, error\)](#MetaChunkService.ListPid)
  - [func \(s \*MetaChunkService\) ListStoragePool\(ctx context.Context\) \(\*zbsv1.StoragePools, error\)](#MetaChunkService.ListStoragePool)
  - [func \(s \*MetaChunkService\) ListTopoObj\(ctx context.Context, id \*zbsv1.TopoObjId\) \(\*zbsv1.TopoObjs, error\)](#MetaChunkService.ListTopoObj)
  - [func \(s \*MetaChunkService\) RegisterChunk\(ctx context.Context, chunk \*zbsv1.Chunk\) \(\*zbsv1.Chunk, error\)](#MetaChunkService.RegisterChunk)
  - [func \(s \*MetaChunkService\) RemoveChunk\(ctx context.Context, id \*zbsv1.ChunkId\) error](#MetaChunkService.RemoveChunk)
  - [func \(s \*MetaChunkService\) RemoveChunkFromStoragePool\(ctx context.Context, storagePoolID string, chunkID CID, options ...RemoveChunkFromStoragePoolOption\) \(\*zbsv1.StoragePool, error\)](#MetaChunkService.RemoveChunkFromStoragePool)
  - [func \(s \*MetaChunkService\) SetMaintenanceMode\(ctx context.Context, id \*zbsv1.ChunkId, mode bool\) \(\*zbsv1.Chunk, error\)](#MetaChunkService.SetMaintenanceMode)
  - [func \(s \*MetaChunkService\) ShowChunk\(ctx context.Context, id \*zbsv1.ChunkId\) \(\*zbsv1.Chunk, error\)](#MetaChunkService.ShowChunk)
  - [func \(s \*MetaChunkService\) ShowStoragePool\(ctx context.Context, id string\) \(\*zbsv1.StoragePool, error\)](#MetaChunkService.ShowStoragePool)
  - [func \(s \*MetaChunkService\) ShowTopoObj\(ctx context.Context, id \*zbsv1.TopoObjId\) \(\*zbsv1.TopoObj, error\)](#MetaChunkService.ShowTopoObj)
  - [func \(s \*MetaChunkService\) UnbanChunk\(ctx context.Context, id \*zbsv1.ChunkId\) error](#MetaChunkService.UnbanChunk)
  - [func \(s \*MetaChunkService\) UpdateStoragePool\(ctx context.Context, req \*metav1.UpdateStoragePoolRequest\) \(\*zbsv1.StoragePool, error\)](#MetaChunkService.UpdateStoragePool)
  - [func \(s \*MetaChunkService\) UpdateTopoObj\(ctx context.Context, id \*zbsv1.TopoObjId, options ...UpdateTopoObjOption\) \(\*zbsv1.TopoObj, error\)](#MetaChunkService.UpdateTopoObj)
- [type MetaService](#MetaService)
  - [func NewMetaService\(client \*zrpc.Client\) \*MetaService](#NewMetaService)
  - [func \(s \*MetaService\) CloneVolume\(ctx context.Context, req \*metav1.CloneVolumeRequest\) \(\*metav1.Volume, error\)](#MetaService.CloneVolume)
  - [func \(s \*MetaService\) CompareVolume\(baseVoumeId string, volumeIdToCompare string\) \(DiffIterator, error\)](#MetaService.CompareVolume)
  - [func \(s \*MetaService\) CreatePool\(ctx context.Context, name string, options \*metav1.Pool\) \(\*metav1.Pool, error\)](#MetaService.CreatePool)
  - [func \(s \*MetaService\) CreateSnapshot\(ctx context.Context, path \*metav1.SnapshotPath, options \*metav1.CreateSnapshotRequest\) \(\*metav1.Volume, error\)](#MetaService.CreateSnapshot)
  - [func \(s \*MetaService\) CreateVolume\(ctx context.Context, poolPath \*metav1.PoolPath, name string, size uint64, options \*metav1.CreateVolumeRequest\) \(\*metav1.Volume, error\)](#MetaService.CreateVolume)
  - [func \(s \*MetaService\) DeletePool\(ctx context.Context, req \*metav1.PoolPath\) error](#MetaService.DeletePool)
  - [func \(s \*MetaService\) DeletePoolById\(ctx context.Context, id string\) error](#MetaService.DeletePoolById)
  - [func \(s \*MetaService\) DeletePoolByName\(ctx context.Context, name string\) error](#MetaService.DeletePoolByName)
  - [func \(s \*MetaService\) DeleteSnapshot\(ctx context.Context, path \*metav1.SnapshotPath\) error](#MetaService.DeleteSnapshot)
  - [func \(s \*MetaService\) DeleteVolume\(ctx context.Context, volumePath \*metav1.VolumePath\) error](#MetaService.DeleteVolume)
  - [func \(s \*MetaService\) FindPExtent\(ctx context.Context, status metav1.PExtentStatus\) \(\*metav1.PExtentsResponse, error\)](#MetaService.FindPExtent)
  - [func \(s \*MetaService\) GetClient\(\) metav1.MetaServiceClient](#MetaService.GetClient)
  - [func \(s \*MetaService\) GetPExtent\(ctx context.Context, pid uint32\) \(\*zbsv1.PExtent, error\)](#MetaService.GetPExtent)
  - [func \(s \*MetaService\) GetVExtentLease\(ctx context.Context, vtableId string, vExtentNo uint32\) \(\*zbsv1.VExtentLease, error\)](#MetaService.GetVExtentLease)
  - [func \(s \*MetaService\) GetVTable\(ctx context.Context, volumePath \*metav1.VolumePath\) \(\*metav1.GetVTableResponse, error\)](#MetaService.GetVTable)
  - [func \(s \*MetaService\) GetVolumeSize\(ctx context.Context, id string\) \(\*metav1.VolumeSizeResponse, error\)](#MetaService.GetVolumeSize)
  - [func \(s \*MetaService\) ListPool\(ctx context.Context, req \*metav1.ListPoolRequest\) \(\*metav1.PoolsResponse, error\)](#MetaService.ListPool)
  - [func \(s \*MetaService\) ListSnapshot\(ctx context.Context, path \*metav1.VolumePath, pagination \*zbsv1.Pagination\) \(\*metav1.SnapshotsResponse, error\)](#MetaService.ListSnapshot)
  - [func \(s \*MetaService\) ListVolume\(ctx context.Context, poolPath \*metav1.PoolPath\) \(\*metav1.VolumesResponse, error\)](#MetaService.ListVolume)
  - [func \(s \*MetaService\) MoveSnapshot\(ctx context.Context, snapshot \*metav1.SnapshotPath, dest \*metav1.PoolPath\) \(\*metav1.Volume, error\)](#MetaService.MoveSnapshot)
  - [func \(s \*MetaService\) MoveVolume\(ctx context.Context, volumePath \*metav1.VolumePath, dest \*metav1.PoolPath\) \(\*metav1.Volume, error\)](#MetaService.MoveVolume)
  - [func \(s \*MetaService\) ParseLicense\(ctx context.Context, in \*zbs.LicenseRequest\) \(\*zbs.License, error\)](#MetaService.ParseLicense)
  - [func \(s \*MetaService\) ResizeVolume\(ctx context.Context, volumePath \*metav1.VolumePath, size uint64\) \(\*metav1.Volume, error\)](#MetaService.ResizeVolume)
  - [func \(s \*MetaService\) RollbackVolume\(ctx context.Context, snapshot \*metav1.SnapshotPath\) error](#MetaService.RollbackVolume)
  - [func \(s \*MetaService\) SetUpgradeMode\(ctx context.Context, duration time.Duration\) error](#MetaService.SetUpgradeMode)
  - [func \(s \*MetaService\) ShowClusterInfo\(ctx context.Context\) \(\*metav1.ClusterInfo, error\)](#MetaService.ShowClusterInfo)
  - [func \(s \*MetaService\) ShowLicense\(ctx context.Context\) \(\*zbs.License, error\)](#MetaService.ShowLicense)
  - [func \(s \*MetaService\) ShowPool\(ctx context.Context, req \*metav1.PoolPath\) \(\*metav1.Pool, error\)](#MetaService.ShowPool)
  - [func \(s \*MetaService\) ShowPoolById\(ctx context.Context, id string\) \(\*metav1.Pool, error\)](#MetaService.ShowPoolById)
  - [func \(s \*MetaService\) ShowPoolByName\(ctx context.Context, name string\) \(\*metav1.Pool, error\)](#MetaService.ShowPoolByName)
  - [func \(s \*MetaService\) ShowSnapshot\(ctx context.Context, path \*metav1.SnapshotPath\) \(\*metav1.Volume, error\)](#MetaService.ShowSnapshot)
  - [func \(s \*MetaService\) ShowVolume\(ctx context.Context, volumePath \*metav1.VolumePath\) \(\*metav1.ShowVolumeResponse, error\)](#MetaService.ShowVolume)
  - [func \(s \*MetaService\) UpdateLicense\(ctx context.Context, in \*zbs.LicenseRequest\) error](#MetaService.UpdateLicense)
  - [func \(s \*MetaService\) UpdatePool\(ctx context.Context, req \*metav1.UpdatePoolRequest\) \(\*metav1.Pool, error\)](#MetaService.UpdatePool)
  - [func \(s \*MetaService\) UpdateSnapshot\(ctx context.Context, path \*metav1.SnapshotPath, name string, desc \[\]byte, allocEven \*bool\) \(\*metav1.Volume, error\)](#MetaService.UpdateSnapshot)
  - [func \(s \*MetaService\) UpdateVolume\(ctx context.Context, req \*metav1.UpdateVolumeRequest\) \(\*metav1.Volume, error\)](#MetaService.UpdateVolume)
- [type NVMFNamespace](#NVMFNamespace)
- [type NVMFService](#NVMFService)
  - [func NewNVMFService\(client \*zrpc.Client\) \*NVMFService](#NewNVMFService)
  - [func \(n \*NVMFService\) AddNamespaceAllowedHost\(ctx context.Context, nsVolumeId string, allowedHost \[\]string\) \(\*NVMFNamespace, error\)](#NVMFService.AddNamespaceAllowedHost)
  - [func \(n \*NVMFService\) CreateNamespaceBySecondaryId\(ctx context.Context, nsName string, secondaryId string, size uint64, setters ...NsOptionSetter\) \(\*NVMFNamespace, error\)](#NVMFService.CreateNamespaceBySecondaryId)
  - [func \(n \*NVMFService\) DeleteNamespace\(ctx context.Context, nsVolumeId string\) error](#NVMFService.DeleteNamespace)
  - [func \(n \*NVMFService\) DescribeNamespace\(ctx context.Context, nsVolumeId string\) \(\*NVMFNamespace, error\)](#NVMFService.DescribeNamespace)
  - [func \(n \*NVMFService\) DescribeNamespaceBySecondaryId\(ctx context.Context, secondaryId string\) \(\*NVMFNamespace, error\)](#NVMFService.DescribeNamespaceBySecondaryId)
  - [func \(n \*NVMFService\) DescribeSubsystem\(ctx context.Context, id string\) \(\*NVMFSubsystem, error\)](#NVMFService.DescribeSubsystem)
  - [func \(n \*NVMFService\) ListSubsystems\(ctx context.Context\) \(\*meta.NVMFDistSubsystemsResponse, error\)](#NVMFService.ListSubsystems)
  - [func \(n \*NVMFService\) ListTargets\(ctx context.Context\) \(\*meta.NVMFTargetsResponse, error\)](#NVMFService.ListTargets)
  - [func \(n \*NVMFService\) RemoveNsNqnWhiteList\(ctx context.Context, nsVolumeId string, allowedHost \[\]string\) \(\*NVMFNamespace, error\)](#NVMFService.RemoveNsNqnWhiteList)
  - [func \(n \*NVMFService\) UpdateNamespace\(ctx context.Context, nsVolumeId string, setters ...NsOptionSetter\) \(\*NVMFNamespace, error\)](#NVMFService.UpdateNamespace)
  - [func \(n \*NVMFService\) UpdateSubsystem\(ctx context.Context, id string, cfg NVMFSubsystemConfig\) \(\*NVMFSubsystem, error\)](#NVMFService.UpdateSubsystem)
- [type NVMFSubsystem](#NVMFSubsystem)
- [type NVMFSubsystemConfig](#NVMFSubsystemConfig)
- [type NsCloneOptions](#NsCloneOptions)
- [type NsOptionSetter](#NsOptionSetter)
  - [func WithNsCloneOpts\(co \*NsCloneOptions\) NsOptionSetter](#WithNsCloneOpts)
  - [func WithNsDescription\(description string\) NsOptionSetter](#WithNsDescription)
  - [func WithNsNqnWhitelist\(nqnWhitelist \[\]string\) NsOptionSetter](#WithNsNqnWhitelist)
  - [func WithNsPath\(lp \*meta.DistNamespacePath\) NsOptionSetter](#WithNsPath)
  - [func WithNsReplicaNum\(rn uint32\) NsOptionSetter](#WithNsReplicaNum)
  - [func WithNsShare\(isShare bool\) NsOptionSetter](#WithNsShare)
  - [func WithNsSingleAccess\(singleAccess bool\) NsOptionSetter](#WithNsSingleAccess)
  - [func WithNsSize\(size uint64\) NsOptionSetter](#WithNsSize)
  - [func WithNsStoragePolicy\(sp \*StoragePolicy\) NsOptionSetter](#WithNsStoragePolicy)
  - [func WithNsThinProvision\(tp bool\) NsOptionSetter](#WithNsThinProvision)
  - [func WithNsThrottling\(tc \*meta.IOThrottleConfig\) NsOptionSetter](#WithNsThrottling)
  - [func WithSubsystemRequirement\(sr \*meta.SubsystemRequirement\) NsOptionSetter](#WithSubsystemRequirement)
- [type NsOptions](#NsOptions)
- [type PExtentInfo](#PExtentInfo)
- [type RangeUint64](#RangeUint64)
- [type RangeUint64SortByOffset](#RangeUint64SortByOffset)
  - [func \(r RangeUint64SortByOffset\) Len\(\) int](#RangeUint64SortByOffset.Len)
  - [func \(r RangeUint64SortByOffset\) Less\(i, j int\) bool](#RangeUint64SortByOffset.Less)
  - [func \(r RangeUint64SortByOffset\) Swap\(i, j int\)](#RangeUint64SortByOffset.Swap)
- [type RemoveChunkFromStoragePoolOption](#RemoveChunkFromStoragePoolOption)
- [type StatusService](#StatusService)
  - [func NewStatusService\(client \*zrpc.Client\) \*StatusService](#NewStatusService)
  - [func \(s \*StatusService\) GetClusterPerf\(ctx context.Context\) \(\*metav1.ClusterPerf, error\)](#StatusService.GetClusterPerf)
  - [func \(s \*StatusService\) GetClusterSummary\(ctx context.Context\) \(\*metav1.ClusterSummary, error\)](#StatusService.GetClusterSummary)
  - [func \(s \*StatusService\) GetMetaSummary\(ctx context.Context\) \(\*metav1.MetaSummary, error\)](#StatusService.GetMetaSummary)
  - [func \(s \*StatusService\) ShowClusterStatus\(ctx context.Context\) \(\*metav1.ClusterStatus, error\)](#StatusService.ShowClusterStatus)
- [type StoragePolicy](#StoragePolicy)
  - [func NewDefaultStoragePolicy\(\) \*StoragePolicy](#NewDefaultStoragePolicy)
- [type UpdateTopoObjOption](#UpdateTopoObjOption)
- [type VIPService](#VIPService)
  - [func NewVIPService\(client \*zrpc.Client\) \*VIPService](#NewVIPService)
  - [func \(s \*VIPService\) DeleteVirtualIP\(ctx context.Context, name, ip string\) error](#VIPService.DeleteVirtualIP)
  - [func \(s \*VIPService\) ListVirtualIPs\(ctx context.Context\) \(\*VirtualIPs, error\)](#VIPService.ListVirtualIPs)
  - [func \(s \*VIPService\) UpsertVirtualIP\(ctx context.Context, name, ip string\) error](#VIPService.UpsertVirtualIP)
- [type VirtualIPs](#VirtualIPs)
- [type VolumePerfService](#VolumePerfService)
  - [func NewVolumePerfService\(client \*zrpc.Client\) \*VolumePerfService](#NewVolumePerfService)
  - [func \(s \*VolumePerfService\) DisableProbeVolumes\(ctx context.Context, volumeIds \[\]string, disableAll bool\) error](#VolumePerfService.DisableProbeVolumes)
  - [func \(s \*VolumePerfService\) GetAllVolumesPerf\(ctx context.Context\) \(\*zbs.VolumesPerf, error\)](#VolumePerfService.GetAllVolumesPerf)
  - [func \(s \*VolumePerfService\) GetVolumePerf\(ctx context.Context, volumeId string\) \(\*zbs.VolumePerf, error\)](#VolumePerfService.GetVolumePerf)
  - [func \(s \*VolumePerfService\) GetVolumesPerf\(ctx context.Context, volumeIds \[\]string\) \(\*zbs.VolumesPerf, error\)](#VolumePerfService.GetVolumesPerf)
  - [func \(s \*VolumePerfService\) ProbeVolumes\(ctx context.Context, volumeIds \[\]string\) error](#VolumePerfService.ProbeVolumes)



## Constants

<a name="SystemStoragePoolId"></a>

```go
const SystemStoragePoolId = "system"
```

<a name="CID"></a>

## type [CID](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L318)



```go
type CID = uint32
```

<a name="ChunkPerfService"></a>

## type [ChunkPerfService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L76-L78)



```go
type ChunkPerfService struct {
    // contains filtered or unexported fields
}
```

<a name="NewChunkPerfService"></a>


### func [NewChunkPerfService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L80)

```go
func NewChunkPerfService(client *zrpc.Client) *ChunkPerfService
```



<a name="ChunkPerfService.GetAccessPerf"></a>


### func \(\*ChunkPerfService\) [GetAccessPerf](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L90)

```go
func (s *ChunkPerfService) GetAccessPerf(ctx context.Context) (*zbs.AccessPerf, error)
```



<a name="ChunkPerfService.GetLSMPerf"></a>


### func \(\*ChunkPerfService\) [GetLSMPerf](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L94)

```go
func (s *ChunkPerfService) GetLSMPerf(ctx context.Context) (*zbs.LSMPerf, error)
```



<a name="ChunkPerfService.GetUIOPerf"></a>


### func \(\*ChunkPerfService\) [GetUIOPerf](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L86)

```go
func (s *ChunkPerfService) GetUIOPerf(ctx context.Context) (*zbs.UIOPerf, error)
```



<a name="ChunkService"></a>

## type [ChunkService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L11-L13)



```go
type ChunkService struct {
    // contains filtered or unexported fields
}
```

<a name="NewChunkService"></a>


### func [NewChunkService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L15)

```go
func NewChunkService(client *zrpc.Client) *ChunkService
```



<a name="ChunkService.CheckAllExtents"></a>


### func \(\*ChunkService\) [CheckAllExtents](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L164)

```go
func (s *ChunkService) CheckAllExtents(ctx context.Context) error
```



<a name="ChunkService.CheckExtent"></a>


### func \(\*ChunkService\) [CheckExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L155)

```go
func (s *ChunkService) CheckExtent(ctx context.Context, pid uint32) error
```



<a name="ChunkService.CompareExtent"></a>


### func \(\*ChunkService\) [CompareExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L171)

```go
func (s *ChunkService) CompareExtent(ctx context.Context, pid1 uint32, pid2 uint32) (*chunkv1.CompareExtentResponse, error)
```



<a name="ChunkService.FlushAllJournals"></a>


### func \(\*ChunkService\) [FlushAllJournals](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L128)

```go
func (s *ChunkService) FlushAllJournals(ctx context.Context) error
```



<a name="ChunkService.FormatCache"></a>


### func \(\*ChunkService\) [FormatCache](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L78)

```go
func (s *ChunkService) FormatCache(ctx context.Context, path string, force bool) error
```



<a name="ChunkService.FormatJournal"></a>


### func \(\*ChunkService\) [FormatJournal](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L100)

```go
func (s *ChunkService) FormatJournal(ctx context.Context, path string, force bool) error
```



<a name="ChunkService.FormatPartition"></a>


### func \(\*ChunkService\) [FormatPartition](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L25)

```go
func (s *ChunkService) FormatPartition(ctx context.Context, path string, force bool, ignoreDataChecksum bool) error
```



<a name="ChunkService.GetZbsAddress"></a>


### func \(\*ChunkService\) [GetZbsAddress](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L21)

```go
func (s *ChunkService) GetZbsAddress(ctx context.Context) (*chunkv1.ZbsAddress, error)
```



<a name="ChunkService.InvalidateCache"></a>


### func \(\*ChunkService\) [InvalidateCache](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L93)

```go
func (s *ChunkService) InvalidateCache(ctx context.Context, path string) error
```



<a name="ChunkService.InvalidateExtent"></a>


### func \(\*ChunkService\) [InvalidateExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L192)

```go
func (s *ChunkService) InvalidateExtent(ctx context.Context, req *chunkv1.InvalidateExtentRequest) error
```



<a name="ChunkService.ListCache"></a>


### func \(\*ChunkService\) [ListCache](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L88)

```go
func (s *ChunkService) ListCache(ctx context.Context) (*chunkv1.ListCacheResponse, error)
```



<a name="ChunkService.ListClient"></a>


### func \(\*ChunkService\) [ListClient](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L140)

```go
func (s *ChunkService) ListClient(ctx context.Context) (*chunkv1.ListClientResponse, error)
```



<a name="ChunkService.ListExtent"></a>


### func \(\*ChunkService\) [ListExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L150)

```go
func (s *ChunkService) ListExtent(ctx context.Context) (*chunkv1.ListExtentResponse, error)
```



<a name="ChunkService.ListJournal"></a>


### func \(\*ChunkService\) [ListJournal](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L135)

```go
func (s *ChunkService) ListJournal(ctx context.Context) (*chunkv1.ListJournalResponse, error)
```



<a name="ChunkService.ListPartition"></a>


### func \(\*ChunkService\) [ListPartition](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L59)

```go
func (s *ChunkService) ListPartition(ctx context.Context) (*chunkv1.ListPartitionResponse, error)
```



<a name="ChunkService.ListRecover"></a>


### func \(\*ChunkService\) [ListRecover](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L145)

```go
func (s *ChunkService) ListRecover(ctx context.Context) (*zbsv1.ListRecoverResponse, error)
```



<a name="ChunkService.MergeExtent"></a>


### func \(\*ChunkService\) [MergeExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L185)

```go
func (s *ChunkService) MergeExtent(ctx context.Context, pid uint32) error
```



<a name="ChunkService.MountCache"></a>


### func \(\*ChunkService\) [MountCache](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L64)

```go
func (s *ChunkService) MountCache(ctx context.Context, path string) error
```



<a name="ChunkService.MountJournal"></a>


### func \(\*ChunkService\) [MountJournal](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L110)

```go
func (s *ChunkService) MountJournal(ctx context.Context, path string) error
```



<a name="ChunkService.MountPartition"></a>


### func \(\*ChunkService\) [MountPartition](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L37)

```go
func (s *ChunkService) MountPartition(ctx context.Context, path string, force bool) error
```



<a name="ChunkService.QueryDisk"></a>


### func \(\*ChunkService\) [QueryDisk](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L256)

```go
func (s *ChunkService) QueryDisk(ctx context.Context, path string) (*chunkv1.QueryDiskResponse, error)
```



<a name="ChunkService.SetHealthyCache"></a>


### func \(\*ChunkService\) [SetHealthyCache](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L225)

```go
func (s *ChunkService) SetHealthyCache(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error
```



<a name="ChunkService.SetHealthyJournal"></a>


### func \(\*ChunkService\) [SetHealthyJournal](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L243)

```go
func (s *ChunkService) SetHealthyJournal(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error
```



<a name="ChunkService.SetHealthyPartition"></a>


### func \(\*ChunkService\) [SetHealthyPartition](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L207)

```go
func (s *ChunkService) SetHealthyPartition(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error
```



<a name="ChunkService.SetUnhealthyCache"></a>


### func \(\*ChunkService\) [SetUnhealthyCache](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L220)

```go
func (s *ChunkService) SetUnhealthyCache(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error
```



<a name="ChunkService.SetUnhealthyJournal"></a>


### func \(\*ChunkService\) [SetUnhealthyJournal](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L238)

```go
func (s *ChunkService) SetUnhealthyJournal(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error
```



<a name="ChunkService.SetUnhealthyPartition"></a>


### func \(\*ChunkService\) [SetUnhealthyPartition](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L202)

```go
func (s *ChunkService) SetUnhealthyPartition(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error
```



<a name="ChunkService.SetVerifyMode"></a>


### func \(\*ChunkService\) [SetVerifyMode](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L197)

```go
func (s *ChunkService) SetVerifyMode(ctx context.Context, req *chunkv1.SetVerifyModeRequest) error
```



<a name="ChunkService.ShowExtent"></a>


### func \(\*ChunkService\) [ShowExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L180)

```go
func (s *ChunkService) ShowExtent(ctx context.Context, pid uint32) (*chunkv1.ShowExtentResponse, error)
```



<a name="ChunkService.StopServer"></a>


### func \(\*ChunkService\) [StopServer](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L266)

```go
func (s *ChunkService) StopServer(ctx context.Context) error
```



<a name="ChunkService.SummaryInfo"></a>


### func \(\*ChunkService\) [SummaryInfo](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L261)

```go
func (s *ChunkService) SummaryInfo(ctx context.Context) (*chunkv1.SummaryInfoResponse, error)
```



<a name="ChunkService.UmountCache"></a>


### func \(\*ChunkService\) [UmountCache](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L71)

```go
func (s *ChunkService) UmountCache(ctx context.Context, path string) error
```



<a name="ChunkService.UmountJournal"></a>


### func \(\*ChunkService\) [UmountJournal](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L119)

```go
func (s *ChunkService) UmountJournal(ctx context.Context, path string) error
```



<a name="ChunkService.UmountPartition"></a>


### func \(\*ChunkService\) [UmountPartition](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/chunk.go#L48)

```go
func (s *ChunkService) UmountPartition(ctx context.Context, path string, force bool) error
```



<a name="Client"></a>

## type [Client](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/zbs.go#L7-L16)



```go
type Client struct {
    ISCSI  *ISCSIService
    NVMF   *NVMFService
    Meta   *MetaService
    Status *StatusService
    Chunk  *ChunkService
    VIP    *VIPService
    // contains filtered or unexported fields
}
```

<a name="NewClient"></a>


### func [NewClient](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/zbs.go#L47)

```go
func NewClient(watcher *zrpc.LeaderWatcher) (*Client, error)
```



<a name="NewClientWithConfig"></a>


### func [NewClientWithConfig](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/zbs.go#L26)

```go
func NewClientWithConfig(config *zrpc.Config) (*Client, error)
```



<a name="NewStaticClient"></a>


### func [NewStaticClient](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/zbs.go#L54)

```go
func NewStaticClient(addr string) (*Client, error)
```



<a name="Client.Close"></a>


### func \(\*Client\) [Close](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/zbs.go#L22)

```go
func (c *Client) Close() error
```



<a name="Client.GetRpcClient"></a>


### func \(\*Client\) [GetRpcClient](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/zbs.go#L18)

```go
func (c *Client) GetRpcClient() *zrpc.Client
```



<a name="CreateTopoObjOption"></a>

## type [CreateTopoObjOption](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L373)



```go
type CreateTopoObjOption = func(*metav1.CreateTopoObjRequest) *metav1.CreateTopoObjRequest
```

<a name="DataChannelService"></a>

## type [DataChannelService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/datachannel.go#L9-L11)



```go
type DataChannelService struct {
    // contains filtered or unexported fields
}
```

<a name="NewDataChannelService"></a>


### func [NewDataChannelService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/datachannel.go#L13)

```go
func NewDataChannelService(endpoint string, config *dc.Config) (*DataChannelService, error)
```



<a name="DataChannelService.Close"></a>


### func \(\*DataChannelService\) [Close](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/datachannel.go#L95)

```go
func (dcs *DataChannelService) Close() error
```



<a name="DataChannelService.Ping"></a>


### func \(\*DataChannelService\) [Ping](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/datachannel.go#L79)

```go
func (dcs *DataChannelService) Ping(ctx context.Context) error
```



<a name="DataChannelService.VolumeRead"></a>


### func \(\*DataChannelService\) [VolumeRead](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/datachannel.go#L24)

```go
func (dcs *DataChannelService) VolumeRead(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags) error
```



<a name="DataChannelService.VolumeWrite"></a>


### func \(\*DataChannelService\) [VolumeWrite](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/datachannel.go#L51)

```go
func (dcs *DataChannelService) VolumeWrite(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags, preferredCid dc.Cid) error
```



<a name="DiffIterator"></a>

## type [DiffIterator](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L24-L39)

DiffIterator

```go
type DiffIterator struct {
    VolumeToComparePids []uint32
    // contains filtered or unexported fields
}
```

<a name="DiffIterator.Error"></a>


### func \(\*DiffIterator\) [Error](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L41)

```go
func (it *DiffIterator) Error() error
```



<a name="DiffIterator.HasNext"></a>


### func \(\*DiffIterator\) [HasNext](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L45)

```go
func (it *DiffIterator) HasNext() bool
```



<a name="DiffIterator.Next"></a>


### func \(\*DiffIterator\) [Next](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L83)

```go
func (it *DiffIterator) Next() RangeUint64
```



<a name="ISCSILun"></a>

## type [ISCSILun](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L12)



```go
type ISCSILun = meta.ISCSILun
```

<a name="ISCSIService"></a>

## type [ISCSIService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L35-L37)



```go
type ISCSIService struct {
    // contains filtered or unexported fields
}
```

<a name="NewISCSIService"></a>


### func [NewISCSIService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L39)

```go
func NewISCSIService(client *zrpc.Client) *ISCSIService
```



<a name="ISCSIService.AddLunAllowedInitiators"></a>


### func \(\*ISCSIService\) [AddLunAllowedInitiators](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L314)

```go
func (s *ISCSIService) AddLunAllowedInitiators(ctx context.Context, lunUuId string, initiators []string) (*meta.ISCSILun, error)
```



<a name="ISCSIService.CreateLun"></a>


### func \(\*ISCSIService\) [CreateLun](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L213-L214)

```go
func (s *ISCSIService) CreateLun(ctx context.Context, targetName string, lunName string, lunId uint32, size uint64, opts ...LunOptionSetter) (*ISCSILun, error)
```



<a name="ISCSIService.CreateLunBySecondaryId"></a>


### func \(\*ISCSIService\) [CreateLunBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L156-L157)

```go
func (s *ISCSIService) CreateLunBySecondaryId(ctx context.Context, targetId string, lunName string, secondaryId string, size uint64, opts ...LunOptionSetter) (*ISCSILun, error)
```



<a name="ISCSIService.CreateLunByTargetUUID"></a>


### func \(\*ISCSIService\) [CreateLunByTargetUUID](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L143-L144)

```go
func (s *ISCSIService) CreateLunByTargetUUID(ctx context.Context, targetId string, lunName string, lunId uint32, size uint64, opts ...LunOptionSetter) (*ISCSILun, error)
```



<a name="ISCSIService.CreateSnapshot"></a>


### func \(\*ISCSIService\) [CreateSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L338)

```go
func (s *ISCSIService) CreateSnapshot(ctx context.Context, lunUuid string, name string, desc string) (*ISCSISnapshot, error)
```



<a name="ISCSIService.CreateSnapshotBySecondaryId"></a>


### func \(\*ISCSIService\) [CreateSnapshotBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L351-L352)

```go
func (s *ISCSIService) CreateSnapshotBySecondaryId(ctx context.Context, lunUuid string, secondaryId string, name string, desc string) (*ISCSISnapshot, error)
```



<a name="ISCSIService.CreateTarget"></a>


### func \(\*ISCSIService\) [CreateTarget](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L45)

```go
func (s *ISCSIService) CreateTarget(ctx context.Context, name string, sp *StoragePolicy, config *ISCSITargetConfig) (*ISCSITarget, error)
```



<a name="ISCSIService.DeleteLun"></a>


### func \(\*ISCSIService\) [DeleteLun](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L278)

```go
func (s *ISCSIService) DeleteLun(ctx context.Context, lunUuid string) error
```



<a name="ISCSIService.DeleteLunBySecondaryId"></a>


### func \(\*ISCSIService\) [DeleteLunBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L290)

```go
func (s *ISCSIService) DeleteLunBySecondaryId(ctx context.Context, secondaryId string) error
```



<a name="ISCSIService.DeleteSnapshot"></a>


### func \(\*ISCSIService\) [DeleteSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L382)

```go
func (s *ISCSIService) DeleteSnapshot(ctx context.Context, snapshotId string) error
```



<a name="ISCSIService.DeleteSnapshotBySecondaryId"></a>


### func \(\*ISCSIService\) [DeleteSnapshotBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L392)

```go
func (s *ISCSIService) DeleteSnapshotBySecondaryId(ctx context.Context, secondaryId string) error
```



<a name="ISCSIService.DeleteTarget"></a>


### func \(\*ISCSIService\) [DeleteTarget](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L124)

```go
func (s *ISCSIService) DeleteTarget(ctx context.Context, id string) error
```



<a name="ISCSIService.DescribeLun"></a>


### func \(\*ISCSIService\) [DescribeLun](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L258)

```go
func (s *ISCSIService) DescribeLun(ctx context.Context, lunUuid string) (*ISCSILun, error)
```



<a name="ISCSIService.DescribeLunBySecondaryId"></a>


### func \(\*ISCSIService\) [DescribeLunBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L268)

```go
func (s *ISCSIService) DescribeLunBySecondaryId(ctx context.Context, secondaryId string) (*ISCSILun, error)
```



<a name="ISCSIService.DescribeSnapshot"></a>


### func \(\*ISCSIService\) [DescribeSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L366)

```go
func (s *ISCSIService) DescribeSnapshot(ctx context.Context, snapshotId string) (*ISCSISnapshot, error)
```



<a name="ISCSIService.DescribeSnapshotBySecondaryId"></a>


### func \(\*ISCSIService\) [DescribeSnapshotBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L374)

```go
func (s *ISCSIService) DescribeSnapshotBySecondaryId(ctx context.Context, secondaryId string) (*ISCSISnapshot, error)
```



<a name="ISCSIService.DescribeTarget"></a>


### func \(\*ISCSIService\) [DescribeTarget](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L117)

```go
func (s *ISCSIService) DescribeTarget(ctx context.Context, id string) (*ISCSITarget, error)
```



<a name="ISCSIService.GetLuns"></a>


### func \(\*ISCSIService\) [GetLuns](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L302)

```go
func (s *ISCSIService) GetLuns(ctx context.Context, targetId string) ([]*ISCSILun, error)
```



<a name="ISCSIService.GetTargets"></a>


### func \(\*ISCSIService\) [GetTargets](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L133)

```go
func (s *ISCSIService) GetTargets(ctx context.Context) ([]*ISCSITarget, error)
```



<a name="ISCSIService.ListLunSnapshot"></a>


### func \(\*ISCSIService\) [ListLunSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L417)

```go
func (s *ISCSIService) ListLunSnapshot(ctx context.Context, lunUuid string) ([]*ISCSISnapshot, error)
```



<a name="ISCSIService.ListTargetSnapshot"></a>


### func \(\*ISCSIService\) [ListTargetSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L402)

```go
func (s *ISCSIService) ListTargetSnapshot(ctx context.Context, targetId string) ([]*ISCSISnapshot, error)
```



<a name="ISCSIService.RemoveLunAllowedInitiators"></a>


### func \(\*ISCSIService\) [RemoveLunAllowedInitiators](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L326)

```go
func (s *ISCSIService) RemoveLunAllowedInitiators(ctx context.Context, lunUuId string, initiators []string) (*meta.ISCSILun, error)
```



<a name="ISCSIService.UpdateLun"></a>


### func \(\*ISCSIService\) [UpdateLun](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L248-L249)

```go
func (s *ISCSIService) UpdateLun(ctx context.Context, lunUuid string, opts ...LunOptionSetter) (*ISCSILun, error)
```



<a name="ISCSIService.UpdateTarget"></a>


### func \(\*ISCSIService\) [UpdateTarget](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L71-L72)

```go
func (s *ISCSIService) UpdateTarget(ctx context.Context, id string, newName *string, config *ISCSITargetConfig) (*ISCSITarget, error)
```



<a name="ISCSISnapshot"></a>

## type [ISCSISnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L13)



```go
type ISCSISnapshot = meta.Volume
```

<a name="ISCSITarget"></a>

## type [ISCSITarget](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L11)



```go
type ISCSITarget = meta.ISCSITarget
```

<a name="ISCSITargetConfig"></a>

## type [ISCSITargetConfig](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L15-L21)



```go
type ISCSITargetConfig struct {
    IqnWhiteList        []string
    WhiteList           []string
    CreateInitiatorChap []*InitiatorChapInfo
    UpdateInitiatorChap []*InitiatorChapInfo
    RemoveInitiatorChap []string
}
```

<a name="InitiatorChapInfo"></a>

## type [InitiatorChapInfo](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L23-L28)



```go
type InitiatorChapInfo struct {
    Iqn      string
    ChapName string
    Secret   string
    Enable   *bool
}
```

<a name="LunCloneOptions"></a>

## type [LunCloneOptions](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/iscsi.go#L30-L33)



```go
type LunCloneOptions struct {
    SrcLunPath    *meta.LunPath
    SrcSnapshotId *string
}
```

<a name="LunOptionSetter"></a>

## type [LunOptionSetter](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L9)



```go
type LunOptionSetter func(*LunOptions)
```

<a name="WithLunAllowedInitiators"></a>


### func [WithLunAllowedInitiators](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L68)

```go
func WithLunAllowedInitiators(allowedInitiators []string) LunOptionSetter
```



<a name="WithLunCloneOpts"></a>


### func [WithLunCloneOpts](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L44)

```go
func WithLunCloneOpts(co *LunCloneOptions) LunOptionSetter
```



<a name="WithLunDescription"></a>


### func [WithLunDescription](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L89)

```go
func WithLunDescription(description string) LunOptionSetter
```



<a name="WithLunNewAllowedInitiators"></a>


### func [WithLunNewAllowedInitiators](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L113)

```go
func WithLunNewAllowedInitiators(newAllowedInitiators []string) LunOptionSetter
```



<a name="WithLunNewName"></a>


### func [WithLunNewName](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L77)

```go
func WithLunNewName(newName string) LunOptionSetter
```



<a name="WithLunNewSingleAccess"></a>


### func [WithLunNewSingleAccess](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L122)

```go
func WithLunNewSingleAccess(singleAccess bool) LunOptionSetter
```



<a name="WithLunNewSize"></a>


### func [WithLunNewSize](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L83)

```go
func WithLunNewSize(newSize uint64) LunOptionSetter
```



<a name="WithLunPath"></a>


### func [WithLunPath](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L32)

```go
func WithLunPath(lp *meta.LunPath) LunOptionSetter
```



<a name="WithLunReplicaNum"></a>


### func [WithLunReplicaNum](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L95)

```go
func WithLunReplicaNum(rn uint32) LunOptionSetter
```



<a name="WithLunSingleAccess"></a>


### func [WithLunSingleAccess](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L62)

```go
func WithLunSingleAccess(singleAccess bool) LunOptionSetter
```



<a name="WithLunSize"></a>


### func [WithLunSize](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L50)

```go
func WithLunSize(size uint64) LunOptionSetter
```



<a name="WithLunStoragePolicy"></a>


### func [WithLunStoragePolicy](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L38)

```go
func WithLunStoragePolicy(sp *StoragePolicy) LunOptionSetter
```



<a name="WithLunThinProvision"></a>


### func [WithLunThinProvision](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L101)

```go
func WithLunThinProvision(tp bool) LunOptionSetter
```



<a name="WithLunThrottling"></a>


### func [WithLunThrottling](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L107)

```go
func WithLunThrottling(tc *meta.IOThrottleConfig) LunOptionSetter
```



<a name="WithTargetRequirement"></a>


### func [WithTargetRequirement](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L56)

```go
func WithTargetRequirement(tr *meta.TargetRequirement) LunOptionSetter
```



<a name="LunOptions"></a>

## type [LunOptions](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L11-L30)



```go
type LunOptions struct {
    // For create
    Size              uint64
    LunPath           *meta.LunPath
    StoragePolicy     *StoragePolicy
    CloneOpts         *LunCloneOptions
    TargetRequirement *meta.TargetRequirement
    SingleAccess      bool
    AllowedInitiators []byte

    // For update
    NewName              []byte
    NewSize              *uint64
    Description          []byte
    ReplicaNum           *uint32
    ThinProvision        *bool
    Throttling           *meta.IOThrottleConfig
    NewAllowedInitiators []byte
    NewSingleAccess      *bool
}
```

<a name="MetaChunkService"></a>

## type [MetaChunkService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L320-L322)



```go
type MetaChunkService struct {
    // contains filtered or unexported fields
}
```

<a name="NewMetaChunkService"></a>


### func [NewMetaChunkService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L324)

```go
func NewMetaChunkService(client *zrpc.Client) *MetaChunkService
```



<a name="MetaChunkService.AddChunkToStoragePool"></a>


### func \(\*MetaChunkService\) [AddChunkToStoragePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L350)

```go
func (s *MetaChunkService) AddChunkToStoragePool(ctx context.Context, storagePoolID string, chunkID CID) (*zbsv1.StoragePool, error)
```



<a name="MetaChunkService.BanChunk"></a>


### func \(\*MetaChunkService\) [BanChunk](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L424)

```go
func (s *MetaChunkService) BanChunk(ctx context.Context, id *zbsv1.ChunkId) error
```



<a name="MetaChunkService.CreateStoragePool"></a>


### func \(\*MetaChunkService\) [CreateStoragePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L330)

```go
func (s *MetaChunkService) CreateStoragePool(ctx context.Context, name string, chunks ...CID) (*zbsv1.StoragePool, error)
```



<a name="MetaChunkService.CreateTopoObj"></a>


### func \(\*MetaChunkService\) [CreateTopoObj](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L375)

```go
func (s *MetaChunkService) CreateTopoObj(ctx context.Context, topoType *zbsv1.TopoType, options ...CreateTopoObjOption) (*zbsv1.TopoObj, error)
```



<a name="MetaChunkService.DeleteStoragePool"></a>


### func \(\*MetaChunkService\) [DeleteStoragePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L334)

```go
func (s *MetaChunkService) DeleteStoragePool(ctx context.Context, id string) (*zbsv1.StoragePool, error)
```



<a name="MetaChunkService.DeleteTopoObj"></a>


### func \(\*MetaChunkService\) [DeleteTopoObj](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L384)

```go
func (s *MetaChunkService) DeleteTopoObj(ctx context.Context, id *zbsv1.TopoObjId) (*zbsv1.TopoObj, error)
```



<a name="MetaChunkService.GetChunkTopology"></a>


### func \(\*MetaChunkService\) [GetChunkTopology](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L434)

```go
func (s *MetaChunkService) GetChunkTopology(ctx context.Context, id *zbsv1.ChunkId) (*zbsv1.ChunkTopology, error)
```



<a name="MetaChunkService.LeaveService"></a>


### func \(\*MetaChunkService\) [LeaveService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L438)

```go
func (s *MetaChunkService) LeaveService(ctx context.Context, id *zbsv1.ChunkId) error
```



<a name="MetaChunkService.ListChunk"></a>


### func \(\*MetaChunkService\) [ListChunk](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L411)

```go
func (s *MetaChunkService) ListChunk(ctx context.Context) (*zbsv1.Chunks, error)
```



<a name="MetaChunkService.ListPid"></a>


### func \(\*MetaChunkService\) [ListPid](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L443)

```go
func (s *MetaChunkService) ListPid(ctx context.Context, id *zbsv1.ChunkId) (*zbsv1.ChunkPids, error)
```



<a name="MetaChunkService.ListStoragePool"></a>


### func \(\*MetaChunkService\) [ListStoragePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L342)

```go
func (s *MetaChunkService) ListStoragePool(ctx context.Context) (*zbsv1.StoragePools, error)
```



<a name="MetaChunkService.ListTopoObj"></a>


### func \(\*MetaChunkService\) [ListTopoObj](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L399)

```go
func (s *MetaChunkService) ListTopoObj(ctx context.Context, id *zbsv1.TopoObjId) (*zbsv1.TopoObjs, error)
```



<a name="MetaChunkService.RegisterChunk"></a>


### func \(\*MetaChunkService\) [RegisterChunk](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L407)

```go
func (s *MetaChunkService) RegisterChunk(ctx context.Context, chunk *zbsv1.Chunk) (*zbsv1.Chunk, error)
```



<a name="MetaChunkService.RemoveChunk"></a>


### func \(\*MetaChunkService\) [RemoveChunk](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L419)

```go
func (s *MetaChunkService) RemoveChunk(ctx context.Context, id *zbsv1.ChunkId) error
```



<a name="MetaChunkService.RemoveChunkFromStoragePool"></a>


### func \(\*MetaChunkService\) [RemoveChunkFromStoragePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L359-L360)

```go
func (s *MetaChunkService) RemoveChunkFromStoragePool(ctx context.Context, storagePoolID string, chunkID CID, options ...RemoveChunkFromStoragePoolOption) (*zbsv1.StoragePool, error)
```



<a name="MetaChunkService.SetMaintenanceMode"></a>


### func \(\*MetaChunkService\) [SetMaintenanceMode](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L447)

```go
func (s *MetaChunkService) SetMaintenanceMode(ctx context.Context, id *zbsv1.ChunkId, mode bool) (*zbsv1.Chunk, error)
```



<a name="MetaChunkService.ShowChunk"></a>


### func \(\*MetaChunkService\) [ShowChunk](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L415)

```go
func (s *MetaChunkService) ShowChunk(ctx context.Context, id *zbsv1.ChunkId) (*zbsv1.Chunk, error)
```



<a name="MetaChunkService.ShowStoragePool"></a>


### func \(\*MetaChunkService\) [ShowStoragePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L346)

```go
func (s *MetaChunkService) ShowStoragePool(ctx context.Context, id string) (*zbsv1.StoragePool, error)
```



<a name="MetaChunkService.ShowTopoObj"></a>


### func \(\*MetaChunkService\) [ShowTopoObj](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L403)

```go
func (s *MetaChunkService) ShowTopoObj(ctx context.Context, id *zbsv1.TopoObjId) (*zbsv1.TopoObj, error)
```



<a name="MetaChunkService.UnbanChunk"></a>


### func \(\*MetaChunkService\) [UnbanChunk](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L429)

```go
func (s *MetaChunkService) UnbanChunk(ctx context.Context, id *zbsv1.ChunkId) error
```



<a name="MetaChunkService.UpdateStoragePool"></a>


### func \(\*MetaChunkService\) [UpdateStoragePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L338)

```go
func (s *MetaChunkService) UpdateStoragePool(ctx context.Context, req *metav1.UpdateStoragePoolRequest) (*zbsv1.StoragePool, error)
```



<a name="MetaChunkService.UpdateTopoObj"></a>


### func \(\*MetaChunkService\) [UpdateTopoObj](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L390)

```go
func (s *MetaChunkService) UpdateTopoObj(ctx context.Context, id *zbsv1.TopoObjId, options ...UpdateTopoObjOption) (*zbsv1.TopoObj, error)
```



<a name="MetaService"></a>

## type [MetaService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L17-L21)



```go
type MetaService struct {
    Chunk *MetaChunkService
    // contains filtered or unexported fields
}
```

<a name="NewMetaService"></a>


### func [NewMetaService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L23)

```go
func NewMetaService(client *zrpc.Client) *MetaService
```



<a name="MetaService.CloneVolume"></a>


### func \(\*MetaService\) [CloneVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L258)

```go
func (s *MetaService) CloneVolume(ctx context.Context, req *metav1.CloneVolumeRequest) (*metav1.Volume, error)
```



<a name="MetaService.CompareVolume"></a>


### func \(\*MetaService\) [CompareVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L414-L417)

```go
func (s *MetaService) CompareVolume(baseVoumeId string, volumeIdToCompare string) (DiffIterator, error)
```



<a name="MetaService.CreatePool"></a>


### func \(\*MetaService\) [CreatePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L34)

```go
func (s *MetaService) CreatePool(ctx context.Context, name string, options *metav1.Pool) (*metav1.Pool, error)
```



<a name="MetaService.CreateSnapshot"></a>


### func \(\*MetaService\) [CreateSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L206)

```go
func (s *MetaService) CreateSnapshot(ctx context.Context, path *metav1.SnapshotPath, options *metav1.CreateSnapshotRequest) (*metav1.Volume, error)
```



<a name="MetaService.CreateVolume"></a>


### func \(\*MetaService\) [CreateVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L117-L121)

```go
func (s *MetaService) CreateVolume(ctx context.Context, poolPath *metav1.PoolPath, name string, size uint64, options *metav1.CreateVolumeRequest) (*metav1.Volume, error)
```



<a name="MetaService.DeletePool"></a>


### func \(\*MetaService\) [DeletePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L79)

```go
func (s *MetaService) DeletePool(ctx context.Context, req *metav1.PoolPath) error
```



<a name="MetaService.DeletePoolById"></a>


### func \(\*MetaService\) [DeletePoolById](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L88)

```go
func (s *MetaService) DeletePoolById(ctx context.Context, id string) error
```



<a name="MetaService.DeletePoolByName"></a>


### func \(\*MetaService\) [DeletePoolByName](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L92)

```go
func (s *MetaService) DeletePoolByName(ctx context.Context, name string) error
```



<a name="MetaService.DeleteSnapshot"></a>


### func \(\*MetaService\) [DeleteSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L236)

```go
func (s *MetaService) DeleteSnapshot(ctx context.Context, path *metav1.SnapshotPath) error
```



<a name="MetaService.DeleteVolume"></a>


### func \(\*MetaService\) [DeleteVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L170)

```go
func (s *MetaService) DeleteVolume(ctx context.Context, volumePath *metav1.VolumePath) error
```



<a name="MetaService.FindPExtent"></a>


### func \(\*MetaService\) [FindPExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L284)

```go
func (s *MetaService) FindPExtent(ctx context.Context, status metav1.PExtentStatus) (*metav1.PExtentsResponse, error)
```



<a name="MetaService.GetClient"></a>


### func \(\*MetaService\) [GetClient](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L30)

```go
func (s *MetaService) GetClient() metav1.MetaServiceClient
```



<a name="MetaService.GetPExtent"></a>


### func \(\*MetaService\) [GetPExtent](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L295)

```go
func (s *MetaService) GetPExtent(ctx context.Context, pid uint32) (*zbsv1.PExtent, error)
```



<a name="MetaService.GetVExtentLease"></a>


### func \(\*MetaService\) [GetVExtentLease](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L306)

```go
func (s *MetaService) GetVExtentLease(ctx context.Context, vtableId string, vExtentNo uint32) (*zbsv1.VExtentLease, error)
```



<a name="MetaService.GetVTable"></a>


### func \(\*MetaService\) [GetVTable](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L195)

```go
func (s *MetaService) GetVTable(ctx context.Context, volumePath *metav1.VolumePath) (*metav1.GetVTableResponse, error)
```



<a name="MetaService.GetVolumeSize"></a>


### func \(\*MetaService\) [GetVolumeSize](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L189)

```go
func (s *MetaService) GetVolumeSize(ctx context.Context, id string) (*metav1.VolumeSizeResponse, error)
```



<a name="MetaService.ListPool"></a>


### func \(\*MetaService\) [ListPool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L70)

```go
func (s *MetaService) ListPool(ctx context.Context, req *metav1.ListPoolRequest) (*metav1.PoolsResponse, error)
```



<a name="MetaService.ListSnapshot"></a>


### func \(\*MetaService\) [ListSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L251)

```go
func (s *MetaService) ListSnapshot(ctx context.Context, path *metav1.VolumePath, pagination *zbsv1.Pagination) (*metav1.SnapshotsResponse, error)
```



<a name="MetaService.ListVolume"></a>


### func \(\*MetaService\) [ListVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L148)

```go
func (s *MetaService) ListVolume(ctx context.Context, poolPath *metav1.PoolPath) (*metav1.VolumesResponse, error)
```



<a name="MetaService.MoveSnapshot"></a>


### func \(\*MetaService\) [MoveSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L244)

```go
func (s *MetaService) MoveSnapshot(ctx context.Context, snapshot *metav1.SnapshotPath, dest *metav1.PoolPath) (*metav1.Volume, error)
```



<a name="MetaService.MoveVolume"></a>


### func \(\*MetaService\) [MoveVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L199)

```go
func (s *MetaService) MoveVolume(ctx context.Context, volumePath *metav1.VolumePath, dest *metav1.PoolPath) (*metav1.Volume, error)
```



<a name="MetaService.ParseLicense"></a>


### func \(\*MetaService\) [ParseLicense](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L460)

```go
func (s *MetaService) ParseLicense(ctx context.Context, in *zbs.LicenseRequest) (*zbs.License, error)
```



<a name="MetaService.ResizeVolume"></a>


### func \(\*MetaService\) [ResizeVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L182)

```go
func (s *MetaService) ResizeVolume(ctx context.Context, volumePath *metav1.VolumePath, size uint64) (*metav1.Volume, error)
```



<a name="MetaService.RollbackVolume"></a>


### func \(\*MetaService\) [RollbackVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L262)

```go
func (s *MetaService) RollbackVolume(ctx context.Context, snapshot *metav1.SnapshotPath) error
```



<a name="MetaService.SetUpgradeMode"></a>


### func \(\*MetaService\) [SetUpgradeMode](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L274)

```go
func (s *MetaService) SetUpgradeMode(ctx context.Context, duration time.Duration) error
```



<a name="MetaService.ShowClusterInfo"></a>


### func \(\*MetaService\) [ShowClusterInfo](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L270)

```go
func (s *MetaService) ShowClusterInfo(ctx context.Context) (*metav1.ClusterInfo, error)
```



<a name="MetaService.ShowLicense"></a>


### func \(\*MetaService\) [ShowLicense](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L451)

```go
func (s *MetaService) ShowLicense(ctx context.Context) (*zbs.License, error)
```



<a name="MetaService.ShowPool"></a>


### func \(\*MetaService\) [ShowPool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L96)

```go
func (s *MetaService) ShowPool(ctx context.Context, req *metav1.PoolPath) (*metav1.Pool, error)
```



<a name="MetaService.ShowPoolById"></a>


### func \(\*MetaService\) [ShowPoolById](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L105)

```go
func (s *MetaService) ShowPoolById(ctx context.Context, id string) (*metav1.Pool, error)
```



<a name="MetaService.ShowPoolByName"></a>


### func \(\*MetaService\) [ShowPoolByName](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L109)

```go
func (s *MetaService) ShowPoolByName(ctx context.Context, name string) (*metav1.Pool, error)
```



<a name="MetaService.ShowSnapshot"></a>


### func \(\*MetaService\) [ShowSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L221)

```go
func (s *MetaService) ShowSnapshot(ctx context.Context, path *metav1.SnapshotPath) (*metav1.Volume, error)
```



<a name="MetaService.ShowVolume"></a>


### func \(\*MetaService\) [ShowVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L154)

```go
func (s *MetaService) ShowVolume(ctx context.Context, volumePath *metav1.VolumePath) (*metav1.ShowVolumeResponse, error)
```



<a name="MetaService.UpdateLicense"></a>


### func \(\*MetaService\) [UpdateLicense](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L455)

```go
func (s *MetaService) UpdateLicense(ctx context.Context, in *zbs.LicenseRequest) error
```



<a name="MetaService.UpdatePool"></a>


### func \(\*MetaService\) [UpdatePool](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L113)

```go
func (s *MetaService) UpdatePool(ctx context.Context, req *metav1.UpdatePoolRequest) (*metav1.Pool, error)
```



<a name="MetaService.UpdateSnapshot"></a>


### func \(\*MetaService\) [UpdateSnapshot](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L225)

```go
func (s *MetaService) UpdateSnapshot(ctx context.Context, path *metav1.SnapshotPath, name string, desc []byte, allocEven *bool) (*metav1.Volume, error)
```



<a name="MetaService.UpdateVolume"></a>


### func \(\*MetaService\) [UpdateVolume](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L178)

```go
func (s *MetaService) UpdateVolume(ctx context.Context, req *metav1.UpdateVolumeRequest) (*metav1.Volume, error)
```



<a name="NVMFNamespace"></a>

## type [NVMFNamespace](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L12)



```go
type NVMFNamespace = meta.NVMFDistNamespace
```

<a name="NVMFService"></a>

## type [NVMFService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L15-L17)



```go
type NVMFService struct {
    // contains filtered or unexported fields
}
```

<a name="NewNVMFService"></a>


### func [NewNVMFService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L19)

```go
func NewNVMFService(client *zrpc.Client) *NVMFService
```



<a name="NVMFService.AddNamespaceAllowedHost"></a>


### func \(\*NVMFService\) [AddNamespaceAllowedHost](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L140)

```go
func (n *NVMFService) AddNamespaceAllowedHost(ctx context.Context, nsVolumeId string, allowedHost []string) (*NVMFNamespace, error)
```



<a name="NVMFService.CreateNamespaceBySecondaryId"></a>


### func \(\*NVMFService\) [CreateNamespaceBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L37-L38)

```go
func (n *NVMFService) CreateNamespaceBySecondaryId(ctx context.Context, nsName string, secondaryId string, size uint64, setters ...NsOptionSetter) (*NVMFNamespace, error)
```



<a name="NVMFService.DeleteNamespace"></a>


### func \(\*NVMFService\) [DeleteNamespace](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L129)

```go
func (n *NVMFService) DeleteNamespace(ctx context.Context, nsVolumeId string) error
```



<a name="NVMFService.DescribeNamespace"></a>


### func \(\*NVMFService\) [DescribeNamespace](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L84)

```go
func (n *NVMFService) DescribeNamespace(ctx context.Context, nsVolumeId string) (*NVMFNamespace, error)
```



<a name="NVMFService.DescribeNamespaceBySecondaryId"></a>


### func \(\*NVMFService\) [DescribeNamespaceBySecondaryId](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L93)

```go
func (n *NVMFService) DescribeNamespaceBySecondaryId(ctx context.Context, secondaryId string) (*NVMFNamespace, error)
```



<a name="NVMFService.DescribeSubsystem"></a>


### func \(\*NVMFService\) [DescribeSubsystem](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L164)

```go
func (n *NVMFService) DescribeSubsystem(ctx context.Context, id string) (*NVMFSubsystem, error)
```



<a name="NVMFService.ListSubsystems"></a>


### func \(\*NVMFService\) [ListSubsystems](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L198)

```go
func (n *NVMFService) ListSubsystems(ctx context.Context) (*meta.NVMFDistSubsystemsResponse, error)
```



<a name="NVMFService.ListTargets"></a>


### func \(\*NVMFService\) [ListTargets](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L202)

```go
func (n *NVMFService) ListTargets(ctx context.Context) (*meta.NVMFTargetsResponse, error)
```



<a name="NVMFService.RemoveNsNqnWhiteList"></a>


### func \(\*NVMFService\) [RemoveNsNqnWhiteList](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L152)

```go
func (n *NVMFService) RemoveNsNqnWhiteList(ctx context.Context, nsVolumeId string, allowedHost []string) (*NVMFNamespace, error)
```



<a name="NVMFService.UpdateNamespace"></a>


### func \(\*NVMFService\) [UpdateNamespace](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L103)

```go
func (n *NVMFService) UpdateNamespace(ctx context.Context, nsVolumeId string, setters ...NsOptionSetter) (*NVMFNamespace, error)
```



<a name="NVMFService.UpdateSubsystem"></a>


### func \(\*NVMFService\) [UpdateSubsystem](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L172)

```go
func (n *NVMFService) UpdateSubsystem(ctx context.Context, id string, cfg NVMFSubsystemConfig) (*NVMFSubsystem, error)
```



<a name="NVMFSubsystem"></a>

## type [NVMFSubsystem](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L13)



```go
type NVMFSubsystem = meta.NVMFDistSubsystem
```

<a name="NVMFSubsystemConfig"></a>

## type [NVMFSubsystemConfig](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L30-L35)



```go
type NVMFSubsystemConfig struct {
    NqnWhiteList  []string
    Ipv4Whitelist []string
    ReplicaNum    *uint32
    Description   string
}
```

<a name="NsCloneOptions"></a>

## type [NsCloneOptions](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/nvmf.go#L25-L28)



```go
type NsCloneOptions struct {
    SrcNsPath     *meta.DistNamespacePath
    SrcSnapshotId *string
}
```

<a name="NsOptionSetter"></a>

## type [NsOptionSetter](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L128)



```go
type NsOptionSetter func(*NsOptions)
```

<a name="WithNsCloneOpts"></a>


### func [WithNsCloneOpts](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L158)

```go
func WithNsCloneOpts(co *NsCloneOptions) NsOptionSetter
```



<a name="WithNsDescription"></a>


### func [WithNsDescription](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L197)

```go
func WithNsDescription(description string) NsOptionSetter
```



<a name="WithNsNqnWhitelist"></a>


### func [WithNsNqnWhitelist](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L188)

```go
func WithNsNqnWhitelist(nqnWhitelist []string) NsOptionSetter
```



<a name="WithNsPath"></a>


### func [WithNsPath](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L146)

```go
func WithNsPath(lp *meta.DistNamespacePath) NsOptionSetter
```



<a name="WithNsReplicaNum"></a>


### func [WithNsReplicaNum](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L203)

```go
func WithNsReplicaNum(rn uint32) NsOptionSetter
```



<a name="WithNsShare"></a>


### func [WithNsShare](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L182)

```go
func WithNsShare(isShare bool) NsOptionSetter
```



<a name="WithNsSingleAccess"></a>


### func [WithNsSingleAccess](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L176)

```go
func WithNsSingleAccess(singleAccess bool) NsOptionSetter
```



<a name="WithNsSize"></a>


### func [WithNsSize](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L164)

```go
func WithNsSize(size uint64) NsOptionSetter
```



<a name="WithNsStoragePolicy"></a>


### func [WithNsStoragePolicy](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L152)

```go
func WithNsStoragePolicy(sp *StoragePolicy) NsOptionSetter
```



<a name="WithNsThinProvision"></a>


### func [WithNsThinProvision](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L209)

```go
func WithNsThinProvision(tp bool) NsOptionSetter
```



<a name="WithNsThrottling"></a>


### func [WithNsThrottling](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L215)

```go
func WithNsThrottling(tc *meta.IOThrottleConfig) NsOptionSetter
```



<a name="WithSubsystemRequirement"></a>


### func [WithSubsystemRequirement](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L170)

```go
func WithSubsystemRequirement(sr *meta.SubsystemRequirement) NsOptionSetter
```



<a name="NsOptions"></a>

## type [NsOptions](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/functional_options.go#L130-L144)



```go
type NsOptions struct {
    NsPath               *meta.DistNamespacePath
    Size                 uint64
    StoragePolicy        *StoragePolicy
    CloneOpts            *NsCloneOptions
    SubsystemRequirement *meta.SubsystemRequirement
    SingleAccess         bool
    NqnWhitelist         []byte
    IsShared             bool
    Name                 []byte
    Description          []byte
    ReplicaNum           *uint32
    ThinProvision        *bool
    Throttling           *meta.IOThrottleConfig
}
```

<a name="PExtentInfo"></a>

## type [PExtentInfo](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L341-L345)



```go
type PExtentInfo struct {
    Pid       uint32
    IsBlank   bool
    AliveLocs []uint8
}
```

<a name="RangeUint64"></a>

## type [RangeUint64](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L13-L16)



```go
type RangeUint64 struct {
    Start  uint64
    Length uint64
}
```

<a name="RangeUint64SortByOffset"></a>

## type [RangeUint64SortByOffset](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L17)



```go
type RangeUint64SortByOffset []RangeUint64
```

<a name="RangeUint64SortByOffset.Len"></a>


### func \(RangeUint64SortByOffset\) [Len](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L19)

```go
func (r RangeUint64SortByOffset) Len() int
```



<a name="RangeUint64SortByOffset.Less"></a>


### func \(RangeUint64SortByOffset\) [Less](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L21)

```go
func (r RangeUint64SortByOffset) Less(i, j int) bool
```



<a name="RangeUint64SortByOffset.Swap"></a>


### func \(RangeUint64SortByOffset\) [Swap](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/compare_volume.go#L20)

```go
func (r RangeUint64SortByOffset) Swap(i, j int)
```



<a name="RemoveChunkFromStoragePoolOption"></a>

## type [RemoveChunkFromStoragePoolOption](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L357)



```go
type RemoveChunkFromStoragePoolOption = func(request *metav1.RemoveChunkFromStoragePoolRequest) *metav1.RemoveChunkFromStoragePoolRequest
```

<a name="StatusService"></a>

## type [StatusService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/status.go#L11-L13)



```go
type StatusService struct {
    // contains filtered or unexported fields
}
```

<a name="NewStatusService"></a>


### func [NewStatusService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/status.go#L15)

```go
func NewStatusService(client *zrpc.Client) *StatusService
```



<a name="StatusService.GetClusterPerf"></a>


### func \(\*StatusService\) [GetClusterPerf](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/status.go#L29)

```go
func (s *StatusService) GetClusterPerf(ctx context.Context) (*metav1.ClusterPerf, error)
```



<a name="StatusService.GetClusterSummary"></a>


### func \(\*StatusService\) [GetClusterSummary](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/status.go#L21)

```go
func (s *StatusService) GetClusterSummary(ctx context.Context) (*metav1.ClusterSummary, error)
```



<a name="StatusService.GetMetaSummary"></a>


### func \(\*StatusService\) [GetMetaSummary](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/status.go#L25)

```go
func (s *StatusService) GetMetaSummary(ctx context.Context) (*metav1.MetaSummary, error)
```



<a name="StatusService.ShowClusterStatus"></a>


### func \(\*StatusService\) [ShowClusterStatus](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/status.go#L33)

```go
func (s *StatusService) ShowClusterStatus(ctx context.Context) (*metav1.ClusterStatus, error)
```



<a name="StoragePolicy"></a>

## type [StoragePolicy](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/storage_policy.go#L4-L9)

StoragePolicy represents the attribute of a storage object

```go
type StoragePolicy struct {
    ReplicaFactor uint32
    ThinProvision bool
    StripeNum     uint32
    StripeSize    uint32
}
```

<a name="NewDefaultStoragePolicy"></a>


### func [NewDefaultStoragePolicy](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/storage_policy.go#L11)

```go
func NewDefaultStoragePolicy() *StoragePolicy
```



<a name="UpdateTopoObjOption"></a>

## type [UpdateTopoObjOption](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/meta.go#L388)



```go
type UpdateTopoObjOption = func(*metav1.UpdateTopoObjRequest) *metav1.UpdateTopoObjRequest
```

<a name="VIPService"></a>

## type [VIPService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/vip.go#L13-L15)



```go
type VIPService struct {
    // contains filtered or unexported fields
}
```

<a name="NewVIPService"></a>


### func [NewVIPService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/vip.go#L17)

```go
func NewVIPService(client *zrpc.Client) *VIPService
```



<a name="VIPService.DeleteVirtualIP"></a>


### func \(\*VIPService\) [DeleteVirtualIP](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/vip.go#L36)

```go
func (s *VIPService) DeleteVirtualIP(ctx context.Context, name, ip string) error
```



<a name="VIPService.ListVirtualIPs"></a>


### func \(\*VIPService\) [ListVirtualIPs](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/vip.go#L32)

```go
func (s *VIPService) ListVirtualIPs(ctx context.Context) (*VirtualIPs, error)
```



<a name="VIPService.UpsertVirtualIP"></a>


### func \(\*VIPService\) [UpsertVirtualIP](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/vip.go#L23)

```go
func (s *VIPService) UpsertVirtualIP(ctx context.Context, name, ip string) error
```



<a name="VirtualIPs"></a>

## type [VirtualIPs](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/vip.go#L11)



```go
type VirtualIPs = task.VirtualIPs
```

<a name="VolumePerfService"></a>

## type [VolumePerfService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L10-L12)



```go
type VolumePerfService struct {
    // contains filtered or unexported fields
}
```

<a name="NewVolumePerfService"></a>


### func [NewVolumePerfService](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L14)

```go
func NewVolumePerfService(client *zrpc.Client) *VolumePerfService
```



<a name="VolumePerfService.DisableProbeVolumes"></a>


### func \(\*VolumePerfService\) [DisableProbeVolumes](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L60)

```go
func (s *VolumePerfService) DisableProbeVolumes(ctx context.Context, volumeIds []string, disableAll bool) error
```



<a name="VolumePerfService.GetAllVolumesPerf"></a>


### func \(\*VolumePerfService\) [GetAllVolumesPerf](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L20)

```go
func (s *VolumePerfService) GetAllVolumesPerf(ctx context.Context) (*zbs.VolumesPerf, error)
```



<a name="VolumePerfService.GetVolumePerf"></a>


### func \(\*VolumePerfService\) [GetVolumePerf](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L24)

```go
func (s *VolumePerfService) GetVolumePerf(ctx context.Context, volumeId string) (*zbs.VolumePerf, error)
```



<a name="VolumePerfService.GetVolumesPerf"></a>


### func \(\*VolumePerfService\) [GetVolumesPerf](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L32)

```go
func (s *VolumePerfService) GetVolumesPerf(ctx context.Context, volumeIds []string) (*zbs.VolumesPerf, error)
```



<a name="VolumePerfService.ProbeVolumes"></a>


### func \(\*VolumePerfService\) [ProbeVolumes](https://newgh.smartx.com/zbs-client-go/zbs-client-go/blob/master/zbs/perf.go#L45)

```go
func (s *VolumePerfService) ProbeVolumes(ctx context.Context, volumeIds []string) error
```



Generated by [gomarkdoc](https://github.com/princjef/gomarkdoc)
