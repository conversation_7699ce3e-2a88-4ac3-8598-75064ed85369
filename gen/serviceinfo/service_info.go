// This file is generated by service_info_generator.py, DO NOT MODIFY!

package serviceinfo

import (
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/block"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/chunk"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/consensus"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/encryption"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/libmeta"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/metric/proto"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/task"
)

var PackageServiceIds = map[string]map[string]int32{
	"zbs.chunk": {
		"ChunkService": 0,
	},
	"zbs.block": {
		"CompareExtentService": 9001,
	},
	"zbs.consensus": {
		"SessionService":    7001,
		"DataReportService": 7002,
		"VolumeKeyService":  7003,
	},
	"zbs.meta": {
		"MetaService":   0,
		"ChunkService":  1,
		"StatusService": 2,
		"NFSService":    3,
		"ISCSIService":  4,
		"NVMFService":   5,
		"CDPService":    6,
		"HostService":   7,
	},
	"zbs.task": {
		"StatusService":     6001,
		"VIPService":        6003,
		"RsyncService":      6006,
		"CopyVolumeService": 6007,
	},
	"zbs": {
		"VolumePerfService":       5002,
		"ChunkPerfService":        5003,
		"SystemManagementService": 0,
		"CommonService":           2333,
	},
}

var ServicePortOffsetTable = map[string]int{
	"zbs.meta.MetaService":           0,
	"zbs.meta.ISCSIService":          0,
	"zbs.meta.NFSService":            0,
	"zbs.meta.NVMFService":           0,
	"zbs.meta.StatusService":         1,
	"zbs.meta.ChunkService":          0,
	"zbs.meta.CDPService":            0,
	"zbs.chunk.ChunkService":         0,
	"zbs.VolumePerfService":          3,
	"zbs.ChunkPerfService":           3,
	"zbs.task.VIPService":            0,
	"zbs.block.CompareExtentService": 0,
}

var ProtoFileDescriptorMap = map[string][]protoreflect.FileDescriptor{
	"zbs.chunk":        {chunk.File_chunk_proto},
	"zbs.block":        {block.File_block_proto},
	"zbs":              {zbs.File_cdp_proto, zbs.File_common_proto, zbs.File_error_proto, zbs.File_event_proto, zbs.File_options_proto, zbs.File_perf_rpc_proto},
	"zbs.metric.proto": {proto.File_zbs_metrics_proto},
	"zbs.consensus":    {consensus.File_session_proto},
	"zbs.meta":         {meta.File_meta_proto},
	"zbs.libmeta":      {libmeta.File_libmeta_proto},
	"zbs.task":         {task.File_task_proto},
	"zbs.encryption":   {encryption.File_encryption_proto},
}
