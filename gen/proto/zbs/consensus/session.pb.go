// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: session.proto

package consensus

import (
	zbs "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	encryption "github.com/iomesh/zbs-client-go/gen/proto/zbs/encryption"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ItemType int32

const (
	ItemType_ITEM_TYPE_UNKNOWN    ItemType = 0
	ItemType_ITEM_TYPE_BASIC_INFO ItemType = 1
	ItemType_ITEM_TYPE_ISCSI      ItemType = 2
	ItemType_ITEM_TYPE_NFS        ItemType = 3
)

// Enum value maps for ItemType.
var (
	ItemType_name = map[int32]string{
		0: "ITEM_TYPE_UNKNOWN",
		1: "ITEM_TYPE_BASIC_INFO",
		2: "ITEM_TYPE_ISCSI",
		3: "ITEM_TYPE_NFS",
	}
	ItemType_value = map[string]int32{
		"ITEM_TYPE_UNKNOWN":    0,
		"ITEM_TYPE_BASIC_INFO": 1,
		"ITEM_TYPE_ISCSI":      2,
		"ITEM_TYPE_NFS":        3,
	}
)

func (x ItemType) Enum() *ItemType {
	p := new(ItemType)
	*p = x
	return p
}

func (x ItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_session_proto_enumTypes[0].Descriptor()
}

func (ItemType) Type() protoreflect.EnumType {
	return &file_session_proto_enumTypes[0]
}

func (x ItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ItemType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ItemType(num)
	return nil
}

// Deprecated: Use ItemType.Descriptor instead.
func (ItemType) EnumDescriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{0}
}

type SessionEpoch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid  []byte  `protobuf:"bytes,1,opt,name=uuid" json:"uuid,omitempty"`
	Epoch *uint64 `protobuf:"varint,2,opt,name=epoch" json:"epoch,omitempty"` // session epoch used to figure out out-of-order requests
}

func (x *SessionEpoch) Reset() {
	*x = SessionEpoch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionEpoch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionEpoch) ProtoMessage() {}

func (x *SessionEpoch) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionEpoch.ProtoReflect.Descriptor instead.
func (*SessionEpoch) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{0}
}

func (x *SessionEpoch) GetUuid() []byte {
	if x != nil {
		return x.Uuid
	}
	return nil
}

func (x *SessionEpoch) GetEpoch() uint64 {
	if x != nil && x.Epoch != nil {
		return *x.Epoch
	}
	return 0
}

type Session struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionEpoch  *SessionEpoch `protobuf:"bytes,1,req,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	Group         []byte        `protobuf:"bytes,2,req,name=group" json:"group,omitempty"`
	LeaseExpireNs *int64        `protobuf:"varint,3,opt,name=lease_expire_ns,json=leaseExpireNs" json:"lease_expire_ns,omitempty"`
	Items         []*Item       `protobuf:"bytes,10,rep,name=items" json:"items,omitempty"` // session releated items
}

func (x *Session) Reset() {
	*x = Session{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Session) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Session) ProtoMessage() {}

func (x *Session) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Session.ProtoReflect.Descriptor instead.
func (*Session) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{1}
}

func (x *Session) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *Session) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *Session) GetLeaseExpireNs() int64 {
	if x != nil && x.LeaseExpireNs != nil {
		return *x.LeaseExpireNs
	}
	return 0
}

func (x *Session) GetItems() []*Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type Sessions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sessions []*Session `protobuf:"bytes,1,rep,name=sessions" json:"sessions,omitempty"`
}

func (x *Sessions) Reset() {
	*x = Sessions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Sessions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sessions) ProtoMessage() {}

func (x *Sessions) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sessions.ProtoReflect.Descriptor instead.
func (*Sessions) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{2}
}

func (x *Sessions) GetSessions() []*Session {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   []byte    `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value []byte    `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
	Type  *ItemType `protobuf:"varint,3,opt,name=type,enum=zbs.consensus.ItemType,def=0" json:"type,omitempty"`
}

// Default values for Item fields.
const (
	Default_Item_Type = ItemType_ITEM_TYPE_UNKNOWN
)

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{3}
}

func (x *Item) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Item) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Item) GetType() ItemType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return Default_Item_Type
}

type CreateSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group []byte `protobuf:"bytes,1,req,name=group" json:"group,omitempty"`
	// set the initial key-value pairs so that the master could get the
	// information when the session is created.
	Items []*Item `protobuf:"bytes,10,rep,name=items" json:"items,omitempty"`
}

func (x *CreateSessionRequest) Reset() {
	*x = CreateSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionRequest) ProtoMessage() {}

func (x *CreateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateSessionRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{4}
}

func (x *CreateSessionRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *CreateSessionRequest) GetItems() []*Item {
	if x != nil {
		return x.Items
	}
	return nil
}

// add an item that should be unique among the session group
type AddItemRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionEpoch *SessionEpoch `protobuf:"bytes,1,req,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	Group        []byte        `protobuf:"bytes,2,req,name=group" json:"group,omitempty"`
	Unique       *bool         `protobuf:"varint,3,opt,name=unique,def=0" json:"unique,omitempty"`
	Item         *Item         `protobuf:"bytes,10,req,name=item" json:"item,omitempty"` // new session related items set
}

// Default values for AddItemRequest fields.
const (
	Default_AddItemRequest_Unique = bool(false)
)

func (x *AddItemRequest) Reset() {
	*x = AddItemRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddItemRequest) ProtoMessage() {}

func (x *AddItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddItemRequest.ProtoReflect.Descriptor instead.
func (*AddItemRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{5}
}

func (x *AddItemRequest) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *AddItemRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *AddItemRequest) GetUnique() bool {
	if x != nil && x.Unique != nil {
		return *x.Unique
	}
	return Default_AddItemRequest_Unique
}

func (x *AddItemRequest) GetItem() *Item {
	if x != nil {
		return x.Item
	}
	return nil
}

// remove an item from the session
type RemoveItemRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionEpoch *SessionEpoch `protobuf:"bytes,1,req,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	Item         *Item         `protobuf:"bytes,10,req,name=item" json:"item,omitempty"` // new session related items set
}

func (x *RemoveItemRequest) Reset() {
	*x = RemoveItemRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveItemRequest) ProtoMessage() {}

func (x *RemoveItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveItemRequest.ProtoReflect.Descriptor instead.
func (*RemoveItemRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{6}
}

func (x *RemoveItemRequest) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *RemoveItemRequest) GetItem() *Item {
	if x != nil {
		return x.Item
	}
	return nil
}

type ListSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group []byte     `protobuf:"bytes,1,req,name=group" json:"group,omitempty"`
	Types []ItemType `protobuf:"varint,2,rep,name=types,enum=zbs.consensus.ItemType" json:"types,omitempty"` // all types if empty
}

func (x *ListSessionRequest) Reset() {
	*x = ListSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSessionRequest) ProtoMessage() {}

func (x *ListSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSessionRequest.ProtoReflect.Descriptor instead.
func (*ListSessionRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{7}
}

func (x *ListSessionRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *ListSessionRequest) GetTypes() []ItemType {
	if x != nil {
		return x.Types
	}
	return nil
}

type KeepAliveRequest struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	SessionEpoch  *SessionEpoch `protobuf:"bytes,1,req,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	Items         []*Item       `protobuf:"bytes,3,rep,name=items" json:"items,omitempty"`
	ClusterUuid   []byte        `protobuf:"bytes,5,opt,name=cluster_uuid,json=clusterUuid,def=" json:"cluster_uuid,omitempty"`
	AwareTimeCost *bool         `protobuf:"varint,6,opt,name=aware_time_cost,json=awareTimeCost,def=0" json:"aware_time_cost,omitempty"`
}

// Default values for KeepAliveRequest fields.
const (
	Default_KeepAliveRequest_AwareTimeCost = bool(false)
)

// Default values for KeepAliveRequest fields.
var (
	Default_KeepAliveRequest_ClusterUuid = []byte("")
)

func (x *KeepAliveRequest) Reset() {
	*x = KeepAliveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepAliveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepAliveRequest) ProtoMessage() {}

func (x *KeepAliveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepAliveRequest.ProtoReflect.Descriptor instead.
func (*KeepAliveRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{8}
}

func (x *KeepAliveRequest) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *KeepAliveRequest) GetItems() []*Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *KeepAliveRequest) GetClusterUuid() []byte {
	if x != nil && x.ClusterUuid != nil {
		return x.ClusterUuid
	}
	return append([]byte(nil), Default_KeepAliveRequest_ClusterUuid...)
}

func (x *KeepAliveRequest) GetAwareTimeCost() bool {
	if x != nil && x.AwareTimeCost != nil {
		return *x.AwareTimeCost
	}
	return Default_KeepAliveRequest_AwareTimeCost
}

type KeepAliveResponse struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	SessionEpoch    *SessionEpoch  `protobuf:"bytes,1,opt,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	Ec              *zbs.ErrorCode `protobuf:"varint,2,opt,name=ec,enum=zbs.ErrorCode,def=0" json:"ec,omitempty"`
	LeaseIntervalNs *int64         `protobuf:"varint,4,opt,name=lease_interval_ns,json=leaseIntervalNs" json:"lease_interval_ns,omitempty"` // new lease interval
	ClusterUuid     []byte         `protobuf:"bytes,5,opt,name=cluster_uuid,json=clusterUuid,def=" json:"cluster_uuid,omitempty"`
	AwareTimeCost   *bool          `protobuf:"varint,6,opt,name=aware_time_cost,json=awareTimeCost,def=0" json:"aware_time_cost,omitempty"`
}

// Default values for KeepAliveResponse fields.
const (
	Default_KeepAliveResponse_Ec            = zbs.ErrorCode(0) // zbs.ErrorCode_EOK
	Default_KeepAliveResponse_AwareTimeCost = bool(false)
)

// Default values for KeepAliveResponse fields.
var (
	Default_KeepAliveResponse_ClusterUuid = []byte("")
)

func (x *KeepAliveResponse) Reset() {
	*x = KeepAliveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepAliveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepAliveResponse) ProtoMessage() {}

func (x *KeepAliveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepAliveResponse.ProtoReflect.Descriptor instead.
func (*KeepAliveResponse) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{9}
}

func (x *KeepAliveResponse) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *KeepAliveResponse) GetEc() zbs.ErrorCode {
	if x != nil && x.Ec != nil {
		return *x.Ec
	}
	return Default_KeepAliveResponse_Ec
}

func (x *KeepAliveResponse) GetLeaseIntervalNs() int64 {
	if x != nil && x.LeaseIntervalNs != nil {
		return *x.LeaseIntervalNs
	}
	return 0
}

func (x *KeepAliveResponse) GetClusterUuid() []byte {
	if x != nil && x.ClusterUuid != nil {
		return x.ClusterUuid
	}
	return append([]byte(nil), Default_KeepAliveResponse_ClusterUuid...)
}

func (x *KeepAliveResponse) GetAwareTimeCost() bool {
	if x != nil && x.AwareTimeCost != nil {
		return *x.AwareTimeCost
	}
	return Default_KeepAliveResponse_AwareTimeCost
}

type RefreshLocalNFSClientIPsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionEpoch *SessionEpoch `protobuf:"bytes,1,req,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	ClientIps    []byte        `protobuf:"bytes,2,req,name=client_ips,json=clientIps" json:"client_ips,omitempty"` // use ',' as split like *********,127.0.0.1
}

func (x *RefreshLocalNFSClientIPsRequest) Reset() {
	*x = RefreshLocalNFSClientIPsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshLocalNFSClientIPsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshLocalNFSClientIPsRequest) ProtoMessage() {}

func (x *RefreshLocalNFSClientIPsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshLocalNFSClientIPsRequest.ProtoReflect.Descriptor instead.
func (*RefreshLocalNFSClientIPsRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{10}
}

func (x *RefreshLocalNFSClientIPsRequest) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *RefreshLocalNFSClientIPsRequest) GetClientIps() []byte {
	if x != nil {
		return x.ClientIps
	}
	return nil
}

type AcquireNFSLoginPermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionEpoch *SessionEpoch `protobuf:"bytes,1,req,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	ClientIp     []byte        `protobuf:"bytes,2,req,name=client_ip,json=clientIp" json:"client_ip,omitempty"`
}

func (x *AcquireNFSLoginPermissionRequest) Reset() {
	*x = AcquireNFSLoginPermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquireNFSLoginPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquireNFSLoginPermissionRequest) ProtoMessage() {}

func (x *AcquireNFSLoginPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquireNFSLoginPermissionRequest.ProtoReflect.Descriptor instead.
func (*AcquireNFSLoginPermissionRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{11}
}

func (x *AcquireNFSLoginPermissionRequest) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *AcquireNFSLoginPermissionRequest) GetClientIp() []byte {
	if x != nil {
		return x.ClientIp
	}
	return nil
}

type DataReportRequest struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	SessionEpoch *SessionEpoch `protobuf:"bytes,1,req,name=session_epoch,json=sessionEpoch" json:"session_epoch,omitempty"`
	ClusterUuid  []byte        `protobuf:"bytes,2,opt,name=cluster_uuid,json=clusterUuid,def=" json:"cluster_uuid,omitempty"`
}

// Default values for DataReportRequest fields.
var (
	Default_DataReportRequest_ClusterUuid = []byte("")
)

func (x *DataReportRequest) Reset() {
	*x = DataReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataReportRequest) ProtoMessage() {}

func (x *DataReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataReportRequest.ProtoReflect.Descriptor instead.
func (*DataReportRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{12}
}

func (x *DataReportRequest) GetSessionEpoch() *SessionEpoch {
	if x != nil {
		return x.SessionEpoch
	}
	return nil
}

func (x *DataReportRequest) GetClusterUuid() []byte {
	if x != nil && x.ClusterUuid != nil {
		return x.ClusterUuid
	}
	return append([]byte(nil), Default_DataReportRequest_ClusterUuid...)
}

type DataReportResponse struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Ec *zbs.ErrorCode `protobuf:"varint,1,opt,name=ec,enum=zbs.ErrorCode,def=0" json:"ec,omitempty"`
}

// Default values for DataReportResponse fields.
const (
	Default_DataReportResponse_Ec = zbs.ErrorCode(0) // zbs.ErrorCode_EOK
)

func (x *DataReportResponse) Reset() {
	*x = DataReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataReportResponse) ProtoMessage() {}

func (x *DataReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataReportResponse.ProtoReflect.Descriptor instead.
func (*DataReportResponse) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{13}
}

func (x *DataReportResponse) GetEc() zbs.ErrorCode {
	if x != nil && x.Ec != nil {
		return *x.Ec
	}
	return Default_DataReportResponse_Ec
}

type GetEncryptInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EncryptMetadataId *uint64 `protobuf:"varint,1,req,name=encrypt_metadata_id,json=encryptMetadataId" json:"encrypt_metadata_id,omitempty"`
}

func (x *GetEncryptInfoRequest) Reset() {
	*x = GetEncryptInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEncryptInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEncryptInfoRequest) ProtoMessage() {}

func (x *GetEncryptInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEncryptInfoRequest.ProtoReflect.Descriptor instead.
func (*GetEncryptInfoRequest) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{14}
}

func (x *GetEncryptInfoRequest) GetEncryptMetadataId() uint64 {
	if x != nil && x.EncryptMetadataId != nil {
		return *x.EncryptMetadataId
	}
	return 0
}

type GetEncryptInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method           *encryption.InternalEncryptMethod `protobuf:"varint,1,opt,name=method,enum=zbs.encryption.InternalEncryptMethod,def=0" json:"method,omitempty"`
	PlainKey         []byte                            `protobuf:"bytes,2,opt,name=plain_key,json=plainKey" json:"plain_key,omitempty"`
	InitVec          []byte                            `protobuf:"bytes,3,opt,name=init_vec,json=initVec" json:"init_vec,omitempty"`
	CipherMetadataId *uint64                           `protobuf:"varint,4,opt,name=cipher_metadata_id,json=cipherMetadataId" json:"cipher_metadata_id,omitempty"`
}

// Default values for GetEncryptInfoResponse fields.
const (
	Default_GetEncryptInfoResponse_Method = encryption.InternalEncryptMethod(0) // encryption.InternalEncryptMethod_INTERNAL_ENCRYPT_UNKNOWN_ALGO
)

func (x *GetEncryptInfoResponse) Reset() {
	*x = GetEncryptInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_session_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEncryptInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEncryptInfoResponse) ProtoMessage() {}

func (x *GetEncryptInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_session_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEncryptInfoResponse.ProtoReflect.Descriptor instead.
func (*GetEncryptInfoResponse) Descriptor() ([]byte, []int) {
	return file_session_proto_rawDescGZIP(), []int{15}
}

func (x *GetEncryptInfoResponse) GetMethod() encryption.InternalEncryptMethod {
	if x != nil && x.Method != nil {
		return *x.Method
	}
	return Default_GetEncryptInfoResponse_Method
}

func (x *GetEncryptInfoResponse) GetPlainKey() []byte {
	if x != nil {
		return x.PlainKey
	}
	return nil
}

func (x *GetEncryptInfoResponse) GetInitVec() []byte {
	if x != nil {
		return x.InitVec
	}
	return nil
}

func (x *GetEncryptInfoResponse) GetCipherMetadataId() uint64 {
	if x != nil && x.CipherMetadataId != nil {
		return *x.CipherMetadataId
	}
	return 0
}

var File_session_proto protoreflect.FileDescriptor

var file_session_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0d, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x1a, 0x0c,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x65, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0b,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x0c, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x19, 0x0a, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x22, 0xbb, 0x01, 0x0a, 0x07,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x52, 0x0c, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52,
	0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x4e, 0x73, 0x12, 0x29,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x3e, 0x0a, 0x08, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x7c, 0x0a, 0x04, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x17, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05,
	0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x3a, 0x11,
	0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x5e, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05,
	0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x29, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75,
	0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x52, 0x0c,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x1b, 0x0a, 0x05,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02,
	0x08, 0x01, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x06, 0x75, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65,
	0x52, 0x06, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d,
	0x18, 0x0a, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x69, 0x74, 0x65,
	0x6d, 0x22, 0x7e, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x27, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d,
	0x18, 0x0a, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x69, 0x74, 0x65,
	0x6d, 0x22, 0x60, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x2d, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x73, 0x75, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x22, 0xe5, 0x01, 0x0a, 0x10, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x52, 0x0c, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x29, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x00, 0x42, 0x05, 0xca,
	0x49, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x2d, 0x0a, 0x0f, 0x61, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x63, 0x6f, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73,
	0x65, 0x52, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x73, 0x74,
	0x2a, 0x09, 0x08, 0x90, 0x4e, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0x8c, 0x02, 0x0a, 0x11,
	0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f,
	0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x45, 0x70, 0x6f, 0x63, 0x68, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70,
	0x6f, 0x63, 0x68, 0x12, 0x23, 0x0a, 0x02, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x3a,
	0x03, 0x45, 0x4f, 0x4b, 0x52, 0x02, 0x65, 0x63, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x6e, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x4e, 0x73, 0x12, 0x2a, 0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x00, 0x42, 0x05, 0xca, 0x49,
	0x02, 0x08, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x2d, 0x0a, 0x0f, 0x61, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65,
	0x52, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x73, 0x74, 0x2a,
	0x09, 0x08, 0x90, 0x4e, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0x89, 0x01, 0x0a, 0x1f, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x4e, 0x46, 0x53, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40,
	0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f,
	0x63, 0x68, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68,
	0x12, 0x24, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x70, 0x73, 0x22, 0x88, 0x01, 0x0a, 0x20, 0x41, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x4e, 0x46, 0x53, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73,
	0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x52,
	0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x22, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c,
	0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x70, 0x22, 0x8c, 0x01, 0x0a, 0x11, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x52, 0x0c, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x2a, 0x0a, 0x0c, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x3a,
	0x00, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x55, 0x75, 0x69, 0x64, 0x2a, 0x09, 0x08, 0x90, 0x4e, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02,
	0x22, 0x44, 0x0a, 0x12, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x02, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x3a, 0x03, 0x45, 0x4f, 0x4b, 0x52, 0x02, 0x65, 0x63, 0x2a, 0x09, 0x08, 0x90, 0x4e,
	0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0x47, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2e, 0x0a, 0x13, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x11, 0x65, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x22,
	0xdc, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x3a, 0x1d, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x4e, 0x43, 0x52,
	0x59, 0x50, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x41, 0x4c, 0x47, 0x4f,
	0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x76, 0x65,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x69, 0x6e, 0x69, 0x74, 0x56, 0x65, 0x63,
	0x12, 0x2c, 0x0a, 0x12, 0x63, 0x69, 0x70, 0x68, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x63, 0x69,
	0x70, 0x68, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x2a, 0x63,
	0x0a, 0x08, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42,
	0x41, 0x53, 0x49, 0x43, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x49,
	0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x43, 0x53, 0x49, 0x10, 0x02,
	0x12, 0x11, 0x0a, 0x0d, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x46,
	0x53, 0x10, 0x03, 0x32, 0xcc, 0x05, 0x0a, 0x0e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x4b, 0x65, 0x65,
	0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36,
	0x0a, 0x0c, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4e, 0x0a, 0x09, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x1f,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x4b,
	0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e,
	0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x41, 0x64, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x0a, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75,
	0x73, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x18, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x4e, 0x46, 0x53, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x73, 0x12, 0x2e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x4c,
	0x6f, 0x63, 0x61, 0x6c, 0x4e, 0x46, 0x53, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x42, 0x0a, 0x0b, 0x53, 0x68, 0x6f, 0x77, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x1a, 0x16, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x19, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4e, 0x46,
	0x53, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x2f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73,
	0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4e, 0x46, 0x53, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x04, 0xc0, 0x3e,
	0xd9, 0x36, 0x32, 0x6c, 0x0a, 0x11, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x51, 0x0a, 0x0a, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x04, 0xc0, 0x3e, 0xda, 0x36,
	0x32, 0x77, 0x0a, 0x10, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4b, 0x65, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x5d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x1a, 0x04, 0xc0, 0x3e, 0xdb, 0x36, 0x42, 0x3f, 0x5a, 0x37, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68, 0x2f, 0x7a,
	0x62, 0x73, 0x2d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x7a, 0x62, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x73, 0x75, 0x73, 0x80, 0x01, 0x01, 0x90, 0x01, 0x01,
}

var (
	file_session_proto_rawDescOnce sync.Once
	file_session_proto_rawDescData = file_session_proto_rawDesc
)

func file_session_proto_rawDescGZIP() []byte {
	file_session_proto_rawDescOnce.Do(func() {
		file_session_proto_rawDescData = protoimpl.X.CompressGZIP(file_session_proto_rawDescData)
	})
	return file_session_proto_rawDescData
}

var file_session_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_session_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_session_proto_goTypes = []interface{}{
	(ItemType)(0),                            // 0: zbs.consensus.ItemType
	(*SessionEpoch)(nil),                     // 1: zbs.consensus.SessionEpoch
	(*Session)(nil),                          // 2: zbs.consensus.Session
	(*Sessions)(nil),                         // 3: zbs.consensus.Sessions
	(*Item)(nil),                             // 4: zbs.consensus.Item
	(*CreateSessionRequest)(nil),             // 5: zbs.consensus.CreateSessionRequest
	(*AddItemRequest)(nil),                   // 6: zbs.consensus.AddItemRequest
	(*RemoveItemRequest)(nil),                // 7: zbs.consensus.RemoveItemRequest
	(*ListSessionRequest)(nil),               // 8: zbs.consensus.ListSessionRequest
	(*KeepAliveRequest)(nil),                 // 9: zbs.consensus.KeepAliveRequest
	(*KeepAliveResponse)(nil),                // 10: zbs.consensus.KeepAliveResponse
	(*RefreshLocalNFSClientIPsRequest)(nil),  // 11: zbs.consensus.RefreshLocalNFSClientIPsRequest
	(*AcquireNFSLoginPermissionRequest)(nil), // 12: zbs.consensus.AcquireNFSLoginPermissionRequest
	(*DataReportRequest)(nil),                // 13: zbs.consensus.DataReportRequest
	(*DataReportResponse)(nil),               // 14: zbs.consensus.DataReportResponse
	(*GetEncryptInfoRequest)(nil),            // 15: zbs.consensus.GetEncryptInfoRequest
	(*GetEncryptInfoResponse)(nil),           // 16: zbs.consensus.GetEncryptInfoResponse
	(zbs.ErrorCode)(0),                       // 17: zbs.ErrorCode
	(encryption.InternalEncryptMethod)(0),    // 18: zbs.encryption.InternalEncryptMethod
	(*zbs.Void)(nil),                         // 19: zbs.Void
}
var file_session_proto_depIdxs = []int32{
	1,  // 0: zbs.consensus.Session.session_epoch:type_name -> zbs.consensus.SessionEpoch
	4,  // 1: zbs.consensus.Session.items:type_name -> zbs.consensus.Item
	2,  // 2: zbs.consensus.Sessions.sessions:type_name -> zbs.consensus.Session
	0,  // 3: zbs.consensus.Item.type:type_name -> zbs.consensus.ItemType
	4,  // 4: zbs.consensus.CreateSessionRequest.items:type_name -> zbs.consensus.Item
	1,  // 5: zbs.consensus.AddItemRequest.session_epoch:type_name -> zbs.consensus.SessionEpoch
	4,  // 6: zbs.consensus.AddItemRequest.item:type_name -> zbs.consensus.Item
	1,  // 7: zbs.consensus.RemoveItemRequest.session_epoch:type_name -> zbs.consensus.SessionEpoch
	4,  // 8: zbs.consensus.RemoveItemRequest.item:type_name -> zbs.consensus.Item
	0,  // 9: zbs.consensus.ListSessionRequest.types:type_name -> zbs.consensus.ItemType
	1,  // 10: zbs.consensus.KeepAliveRequest.session_epoch:type_name -> zbs.consensus.SessionEpoch
	4,  // 11: zbs.consensus.KeepAliveRequest.items:type_name -> zbs.consensus.Item
	1,  // 12: zbs.consensus.KeepAliveResponse.session_epoch:type_name -> zbs.consensus.SessionEpoch
	17, // 13: zbs.consensus.KeepAliveResponse.ec:type_name -> zbs.ErrorCode
	1,  // 14: zbs.consensus.RefreshLocalNFSClientIPsRequest.session_epoch:type_name -> zbs.consensus.SessionEpoch
	1,  // 15: zbs.consensus.AcquireNFSLoginPermissionRequest.session_epoch:type_name -> zbs.consensus.SessionEpoch
	1,  // 16: zbs.consensus.DataReportRequest.session_epoch:type_name -> zbs.consensus.SessionEpoch
	17, // 17: zbs.consensus.DataReportResponse.ec:type_name -> zbs.ErrorCode
	18, // 18: zbs.consensus.GetEncryptInfoResponse.method:type_name -> zbs.encryption.InternalEncryptMethod
	5,  // 19: zbs.consensus.SessionService.CreateSession:input_type -> zbs.consensus.CreateSessionRequest
	1,  // 20: zbs.consensus.SessionService.LeaveSession:input_type -> zbs.consensus.SessionEpoch
	8,  // 21: zbs.consensus.SessionService.ListSession:input_type -> zbs.consensus.ListSessionRequest
	9,  // 22: zbs.consensus.SessionService.KeepAlive:input_type -> zbs.consensus.KeepAliveRequest
	6,  // 23: zbs.consensus.SessionService.AddItem:input_type -> zbs.consensus.AddItemRequest
	7,  // 24: zbs.consensus.SessionService.RemoveItem:input_type -> zbs.consensus.RemoveItemRequest
	11, // 25: zbs.consensus.SessionService.RefreshLocalNFSClientIPs:input_type -> zbs.consensus.RefreshLocalNFSClientIPsRequest
	1,  // 26: zbs.consensus.SessionService.ShowSession:input_type -> zbs.consensus.SessionEpoch
	12, // 27: zbs.consensus.SessionService.AcquireNFSLoginPermission:input_type -> zbs.consensus.AcquireNFSLoginPermissionRequest
	13, // 28: zbs.consensus.DataReportService.DataReport:input_type -> zbs.consensus.DataReportRequest
	15, // 29: zbs.consensus.VolumeKeyService.GetEncryptInfo:input_type -> zbs.consensus.GetEncryptInfoRequest
	10, // 30: zbs.consensus.SessionService.CreateSession:output_type -> zbs.consensus.KeepAliveResponse
	19, // 31: zbs.consensus.SessionService.LeaveSession:output_type -> zbs.Void
	3,  // 32: zbs.consensus.SessionService.ListSession:output_type -> zbs.consensus.Sessions
	10, // 33: zbs.consensus.SessionService.KeepAlive:output_type -> zbs.consensus.KeepAliveResponse
	2,  // 34: zbs.consensus.SessionService.AddItem:output_type -> zbs.consensus.Session
	2,  // 35: zbs.consensus.SessionService.RemoveItem:output_type -> zbs.consensus.Session
	2,  // 36: zbs.consensus.SessionService.RefreshLocalNFSClientIPs:output_type -> zbs.consensus.Session
	2,  // 37: zbs.consensus.SessionService.ShowSession:output_type -> zbs.consensus.Session
	19, // 38: zbs.consensus.SessionService.AcquireNFSLoginPermission:output_type -> zbs.Void
	14, // 39: zbs.consensus.DataReportService.DataReport:output_type -> zbs.consensus.DataReportResponse
	16, // 40: zbs.consensus.VolumeKeyService.GetEncryptInfo:output_type -> zbs.consensus.GetEncryptInfoResponse
	30, // [30:41] is the sub-list for method output_type
	19, // [19:30] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_session_proto_init() }
func file_session_proto_init() {
	if File_session_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_session_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionEpoch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Session); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Sessions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddItemRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveItemRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepAliveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepAliveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshLocalNFSClientIPsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquireNFSLoginPermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEncryptInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_session_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEncryptInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_session_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_session_proto_goTypes,
		DependencyIndexes: file_session_proto_depIdxs,
		EnumInfos:         file_session_proto_enumTypes,
		MessageInfos:      file_session_proto_msgTypes,
	}.Build()
	File_session_proto = out.File
	file_session_proto_rawDesc = nil
	file_session_proto_goTypes = nil
	file_session_proto_depIdxs = nil
}
