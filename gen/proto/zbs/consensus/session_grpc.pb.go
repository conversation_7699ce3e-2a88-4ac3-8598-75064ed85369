// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.12
// source: session.proto

package consensus

import (
	context "context"
	zbs "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SessionService_CreateSession_FullMethodName             = "/zbs.consensus.SessionService/CreateSession"
	SessionService_LeaveSession_FullMethodName              = "/zbs.consensus.SessionService/LeaveSession"
	SessionService_ListSession_FullMethodName               = "/zbs.consensus.SessionService/ListSession"
	SessionService_KeepAlive_FullMethodName                 = "/zbs.consensus.SessionService/KeepAlive"
	SessionService_AddItem_FullMethodName                   = "/zbs.consensus.SessionService/AddItem"
	SessionService_RemoveItem_FullMethodName                = "/zbs.consensus.SessionService/RemoveItem"
	SessionService_RefreshLocalNFSClientIPs_FullMethodName  = "/zbs.consensus.SessionService/RefreshLocalNFSClientIPs"
	SessionService_ShowSession_FullMethodName               = "/zbs.consensus.SessionService/ShowSession"
	SessionService_AcquireNFSLoginPermission_FullMethodName = "/zbs.consensus.SessionService/AcquireNFSLoginPermission"
)

// SessionServiceClient is the client API for SessionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SessionServiceClient interface {
	// session
	CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*KeepAliveResponse, error)
	LeaveSession(ctx context.Context, in *SessionEpoch, opts ...grpc.CallOption) (*zbs.Void, error)
	ListSession(ctx context.Context, in *ListSessionRequest, opts ...grpc.CallOption) (*Sessions, error)
	KeepAlive(ctx context.Context, in *KeepAliveRequest, opts ...grpc.CallOption) (*KeepAliveResponse, error)
	AddItem(ctx context.Context, in *AddItemRequest, opts ...grpc.CallOption) (*Session, error)
	RemoveItem(ctx context.Context, in *RemoveItemRequest, opts ...grpc.CallOption) (*Session, error)
	RefreshLocalNFSClientIPs(ctx context.Context, in *RefreshLocalNFSClientIPsRequest, opts ...grpc.CallOption) (*Session, error)
	ShowSession(ctx context.Context, in *SessionEpoch, opts ...grpc.CallOption) (*Session, error)
	AcquireNFSLoginPermission(ctx context.Context, in *AcquireNFSLoginPermissionRequest, opts ...grpc.CallOption) (*zbs.Void, error)
}

type sessionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSessionServiceClient(cc grpc.ClientConnInterface) SessionServiceClient {
	return &sessionServiceClient{cc}
}

func (c *sessionServiceClient) CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*KeepAliveResponse, error) {
	out := new(KeepAliveResponse)
	err := c.cc.Invoke(ctx, SessionService_CreateSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) LeaveSession(ctx context.Context, in *SessionEpoch, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, SessionService_LeaveSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) ListSession(ctx context.Context, in *ListSessionRequest, opts ...grpc.CallOption) (*Sessions, error) {
	out := new(Sessions)
	err := c.cc.Invoke(ctx, SessionService_ListSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) KeepAlive(ctx context.Context, in *KeepAliveRequest, opts ...grpc.CallOption) (*KeepAliveResponse, error) {
	out := new(KeepAliveResponse)
	err := c.cc.Invoke(ctx, SessionService_KeepAlive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) AddItem(ctx context.Context, in *AddItemRequest, opts ...grpc.CallOption) (*Session, error) {
	out := new(Session)
	err := c.cc.Invoke(ctx, SessionService_AddItem_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) RemoveItem(ctx context.Context, in *RemoveItemRequest, opts ...grpc.CallOption) (*Session, error) {
	out := new(Session)
	err := c.cc.Invoke(ctx, SessionService_RemoveItem_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) RefreshLocalNFSClientIPs(ctx context.Context, in *RefreshLocalNFSClientIPsRequest, opts ...grpc.CallOption) (*Session, error) {
	out := new(Session)
	err := c.cc.Invoke(ctx, SessionService_RefreshLocalNFSClientIPs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) ShowSession(ctx context.Context, in *SessionEpoch, opts ...grpc.CallOption) (*Session, error) {
	out := new(Session)
	err := c.cc.Invoke(ctx, SessionService_ShowSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) AcquireNFSLoginPermission(ctx context.Context, in *AcquireNFSLoginPermissionRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, SessionService_AcquireNFSLoginPermission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SessionServiceServer is the server API for SessionService service.
// All implementations must embed UnimplementedSessionServiceServer
// for forward compatibility
type SessionServiceServer interface {
	// session
	CreateSession(context.Context, *CreateSessionRequest) (*KeepAliveResponse, error)
	LeaveSession(context.Context, *SessionEpoch) (*zbs.Void, error)
	ListSession(context.Context, *ListSessionRequest) (*Sessions, error)
	KeepAlive(context.Context, *KeepAliveRequest) (*KeepAliveResponse, error)
	AddItem(context.Context, *AddItemRequest) (*Session, error)
	RemoveItem(context.Context, *RemoveItemRequest) (*Session, error)
	RefreshLocalNFSClientIPs(context.Context, *RefreshLocalNFSClientIPsRequest) (*Session, error)
	ShowSession(context.Context, *SessionEpoch) (*Session, error)
	AcquireNFSLoginPermission(context.Context, *AcquireNFSLoginPermissionRequest) (*zbs.Void, error)
	mustEmbedUnimplementedSessionServiceServer()
}

// UnimplementedSessionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSessionServiceServer struct {
}

func (UnimplementedSessionServiceServer) CreateSession(context.Context, *CreateSessionRequest) (*KeepAliveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSession not implemented")
}
func (UnimplementedSessionServiceServer) LeaveSession(context.Context, *SessionEpoch) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveSession not implemented")
}
func (UnimplementedSessionServiceServer) ListSession(context.Context, *ListSessionRequest) (*Sessions, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSession not implemented")
}
func (UnimplementedSessionServiceServer) KeepAlive(context.Context, *KeepAliveRequest) (*KeepAliveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KeepAlive not implemented")
}
func (UnimplementedSessionServiceServer) AddItem(context.Context, *AddItemRequest) (*Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddItem not implemented")
}
func (UnimplementedSessionServiceServer) RemoveItem(context.Context, *RemoveItemRequest) (*Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveItem not implemented")
}
func (UnimplementedSessionServiceServer) RefreshLocalNFSClientIPs(context.Context, *RefreshLocalNFSClientIPsRequest) (*Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshLocalNFSClientIPs not implemented")
}
func (UnimplementedSessionServiceServer) ShowSession(context.Context, *SessionEpoch) (*Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShowSession not implemented")
}
func (UnimplementedSessionServiceServer) AcquireNFSLoginPermission(context.Context, *AcquireNFSLoginPermissionRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcquireNFSLoginPermission not implemented")
}
func (UnimplementedSessionServiceServer) mustEmbedUnimplementedSessionServiceServer() {}

// UnsafeSessionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SessionServiceServer will
// result in compilation errors.
type UnsafeSessionServiceServer interface {
	mustEmbedUnimplementedSessionServiceServer()
}

func RegisterSessionServiceServer(s grpc.ServiceRegistrar, srv SessionServiceServer) {
	s.RegisterService(&SessionService_ServiceDesc, srv)
}

func _SessionService_CreateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).CreateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_CreateSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).CreateSession(ctx, req.(*CreateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_LeaveSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SessionEpoch)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).LeaveSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_LeaveSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).LeaveSession(ctx, req.(*SessionEpoch))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_ListSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).ListSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_ListSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).ListSession(ctx, req.(*ListSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_KeepAlive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeepAliveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).KeepAlive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_KeepAlive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).KeepAlive(ctx, req.(*KeepAliveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_AddItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddItemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).AddItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_AddItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).AddItem(ctx, req.(*AddItemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_RemoveItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveItemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).RemoveItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_RemoveItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).RemoveItem(ctx, req.(*RemoveItemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_RefreshLocalNFSClientIPs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshLocalNFSClientIPsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).RefreshLocalNFSClientIPs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_RefreshLocalNFSClientIPs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).RefreshLocalNFSClientIPs(ctx, req.(*RefreshLocalNFSClientIPsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_ShowSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SessionEpoch)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).ShowSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_ShowSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).ShowSession(ctx, req.(*SessionEpoch))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_AcquireNFSLoginPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcquireNFSLoginPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).AcquireNFSLoginPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionService_AcquireNFSLoginPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).AcquireNFSLoginPermission(ctx, req.(*AcquireNFSLoginPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SessionService_ServiceDesc is the grpc.ServiceDesc for SessionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SessionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.consensus.SessionService",
	HandlerType: (*SessionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSession",
			Handler:    _SessionService_CreateSession_Handler,
		},
		{
			MethodName: "LeaveSession",
			Handler:    _SessionService_LeaveSession_Handler,
		},
		{
			MethodName: "ListSession",
			Handler:    _SessionService_ListSession_Handler,
		},
		{
			MethodName: "KeepAlive",
			Handler:    _SessionService_KeepAlive_Handler,
		},
		{
			MethodName: "AddItem",
			Handler:    _SessionService_AddItem_Handler,
		},
		{
			MethodName: "RemoveItem",
			Handler:    _SessionService_RemoveItem_Handler,
		},
		{
			MethodName: "RefreshLocalNFSClientIPs",
			Handler:    _SessionService_RefreshLocalNFSClientIPs_Handler,
		},
		{
			MethodName: "ShowSession",
			Handler:    _SessionService_ShowSession_Handler,
		},
		{
			MethodName: "AcquireNFSLoginPermission",
			Handler:    _SessionService_AcquireNFSLoginPermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "session.proto",
}

const (
	DataReportService_DataReport_FullMethodName = "/zbs.consensus.DataReportService/DataReport"
)

// DataReportServiceClient is the client API for DataReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataReportServiceClient interface {
	DataReport(ctx context.Context, in *DataReportRequest, opts ...grpc.CallOption) (*DataReportResponse, error)
}

type dataReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataReportServiceClient(cc grpc.ClientConnInterface) DataReportServiceClient {
	return &dataReportServiceClient{cc}
}

func (c *dataReportServiceClient) DataReport(ctx context.Context, in *DataReportRequest, opts ...grpc.CallOption) (*DataReportResponse, error) {
	out := new(DataReportResponse)
	err := c.cc.Invoke(ctx, DataReportService_DataReport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataReportServiceServer is the server API for DataReportService service.
// All implementations must embed UnimplementedDataReportServiceServer
// for forward compatibility
type DataReportServiceServer interface {
	DataReport(context.Context, *DataReportRequest) (*DataReportResponse, error)
	mustEmbedUnimplementedDataReportServiceServer()
}

// UnimplementedDataReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDataReportServiceServer struct {
}

func (UnimplementedDataReportServiceServer) DataReport(context.Context, *DataReportRequest) (*DataReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataReport not implemented")
}
func (UnimplementedDataReportServiceServer) mustEmbedUnimplementedDataReportServiceServer() {}

// UnsafeDataReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataReportServiceServer will
// result in compilation errors.
type UnsafeDataReportServiceServer interface {
	mustEmbedUnimplementedDataReportServiceServer()
}

func RegisterDataReportServiceServer(s grpc.ServiceRegistrar, srv DataReportServiceServer) {
	s.RegisterService(&DataReportService_ServiceDesc, srv)
}

func _DataReportService_DataReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataReportServiceServer).DataReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataReportService_DataReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataReportServiceServer).DataReport(ctx, req.(*DataReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DataReportService_ServiceDesc is the grpc.ServiceDesc for DataReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.consensus.DataReportService",
	HandlerType: (*DataReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DataReport",
			Handler:    _DataReportService_DataReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "session.proto",
}

const (
	VolumeKeyService_GetEncryptInfo_FullMethodName = "/zbs.consensus.VolumeKeyService/GetEncryptInfo"
)

// VolumeKeyServiceClient is the client API for VolumeKeyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VolumeKeyServiceClient interface {
	GetEncryptInfo(ctx context.Context, in *GetEncryptInfoRequest, opts ...grpc.CallOption) (*GetEncryptInfoResponse, error)
}

type volumeKeyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVolumeKeyServiceClient(cc grpc.ClientConnInterface) VolumeKeyServiceClient {
	return &volumeKeyServiceClient{cc}
}

func (c *volumeKeyServiceClient) GetEncryptInfo(ctx context.Context, in *GetEncryptInfoRequest, opts ...grpc.CallOption) (*GetEncryptInfoResponse, error) {
	out := new(GetEncryptInfoResponse)
	err := c.cc.Invoke(ctx, VolumeKeyService_GetEncryptInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VolumeKeyServiceServer is the server API for VolumeKeyService service.
// All implementations must embed UnimplementedVolumeKeyServiceServer
// for forward compatibility
type VolumeKeyServiceServer interface {
	GetEncryptInfo(context.Context, *GetEncryptInfoRequest) (*GetEncryptInfoResponse, error)
	mustEmbedUnimplementedVolumeKeyServiceServer()
}

// UnimplementedVolumeKeyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedVolumeKeyServiceServer struct {
}

func (UnimplementedVolumeKeyServiceServer) GetEncryptInfo(context.Context, *GetEncryptInfoRequest) (*GetEncryptInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEncryptInfo not implemented")
}
func (UnimplementedVolumeKeyServiceServer) mustEmbedUnimplementedVolumeKeyServiceServer() {}

// UnsafeVolumeKeyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VolumeKeyServiceServer will
// result in compilation errors.
type UnsafeVolumeKeyServiceServer interface {
	mustEmbedUnimplementedVolumeKeyServiceServer()
}

func RegisterVolumeKeyServiceServer(s grpc.ServiceRegistrar, srv VolumeKeyServiceServer) {
	s.RegisterService(&VolumeKeyService_ServiceDesc, srv)
}

func _VolumeKeyService_GetEncryptInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEncryptInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VolumeKeyServiceServer).GetEncryptInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VolumeKeyService_GetEncryptInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VolumeKeyServiceServer).GetEncryptInfo(ctx, req.(*GetEncryptInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VolumeKeyService_ServiceDesc is the grpc.ServiceDesc for VolumeKeyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VolumeKeyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.consensus.VolumeKeyService",
	HandlerType: (*VolumeKeyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEncryptInfo",
			Handler:    _VolumeKeyService_GetEncryptInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "session.proto",
}
