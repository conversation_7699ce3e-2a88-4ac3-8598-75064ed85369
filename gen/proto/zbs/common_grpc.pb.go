// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.12
// source: common.proto

package zbs

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SystemManagementService_Enable_FullMethodName = "/zbs.SystemManagementService/Enable"
	SystemManagementService_Show_FullMethodName   = "/zbs.SystemManagementService/Show"
)

// SystemManagementServiceClient is the client API for SystemManagementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SystemManagementServiceClient interface {
	Enable(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*RpcStatus, error)
	Show(ctx context.Context, in *Void, opts ...grpc.CallOption) (*StatusResponse, error)
}

type systemManagementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSystemManagementServiceClient(cc grpc.ClientConnInterface) SystemManagementServiceClient {
	return &systemManagementServiceClient{cc}
}

func (c *systemManagementServiceClient) Enable(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*RpcStatus, error) {
	out := new(RpcStatus)
	err := c.cc.Invoke(ctx, SystemManagementService_Enable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *systemManagementServiceClient) Show(ctx context.Context, in *Void, opts ...grpc.CallOption) (*StatusResponse, error) {
	out := new(StatusResponse)
	err := c.cc.Invoke(ctx, SystemManagementService_Show_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SystemManagementServiceServer is the server API for SystemManagementService service.
// All implementations must embed UnimplementedSystemManagementServiceServer
// for forward compatibility
type SystemManagementServiceServer interface {
	Enable(context.Context, *StatusRequest) (*RpcStatus, error)
	Show(context.Context, *Void) (*StatusResponse, error)
	mustEmbedUnimplementedSystemManagementServiceServer()
}

// UnimplementedSystemManagementServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSystemManagementServiceServer struct {
}

func (UnimplementedSystemManagementServiceServer) Enable(context.Context, *StatusRequest) (*RpcStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Enable not implemented")
}
func (UnimplementedSystemManagementServiceServer) Show(context.Context, *Void) (*StatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Show not implemented")
}
func (UnimplementedSystemManagementServiceServer) mustEmbedUnimplementedSystemManagementServiceServer() {
}

// UnsafeSystemManagementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SystemManagementServiceServer will
// result in compilation errors.
type UnsafeSystemManagementServiceServer interface {
	mustEmbedUnimplementedSystemManagementServiceServer()
}

func RegisterSystemManagementServiceServer(s grpc.ServiceRegistrar, srv SystemManagementServiceServer) {
	s.RegisterService(&SystemManagementService_ServiceDesc, srv)
}

func _SystemManagementService_Enable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SystemManagementServiceServer).Enable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SystemManagementService_Enable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SystemManagementServiceServer).Enable(ctx, req.(*StatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SystemManagementService_Show_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SystemManagementServiceServer).Show(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SystemManagementService_Show_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SystemManagementServiceServer).Show(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

// SystemManagementService_ServiceDesc is the grpc.ServiceDesc for SystemManagementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SystemManagementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.SystemManagementService",
	HandlerType: (*SystemManagementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Enable",
			Handler:    _SystemManagementService_Enable_Handler,
		},
		{
			MethodName: "Show",
			Handler:    _SystemManagementService_Show_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "common.proto",
}

const (
	CommonService_ListCounter_FullMethodName       = "/zbs.CommonService/ListCounter"
	CommonService_StartProfiler_FullMethodName     = "/zbs.CommonService/StartProfiler"
	CommonService_StopProfiler_FullMethodName      = "/zbs.CommonService/StopProfiler"
	CommonService_StartHeapProfiler_FullMethodName = "/zbs.CommonService/StartHeapProfiler"
	CommonService_StopHeapProfiler_FullMethodName  = "/zbs.CommonService/StopHeapProfiler"
	CommonService_ListThreadCache_FullMethodName   = "/zbs.CommonService/ListThreadCache"
	CommonService_SetVLOG_FullMethodName           = "/zbs.CommonService/SetVLOG"
	CommonService_GetGFlagsVar_FullMethodName      = "/zbs.CommonService/GetGFlagsVar"
	CommonService_SetGFlagsVar_FullMethodName      = "/zbs.CommonService/SetGFlagsVar"
	CommonService_ListGFlagsVar_FullMethodName     = "/zbs.CommonService/ListGFlagsVar"
	CommonService_ReleaseFreeMemory_FullMethodName = "/zbs.CommonService/ReleaseFreeMemory"
	CommonService_GetServiceVersion_FullMethodName = "/zbs.CommonService/GetServiceVersion"
)

// CommonServiceClient is the client API for CommonService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CommonServiceClient interface {
	ListCounter(ctx context.Context, in *ListCounterRequest, opts ...grpc.CallOption) (*ListCounterResponse, error)
	StartProfiler(ctx context.Context, in *ProfilerRequest, opts ...grpc.CallOption) (*Void, error)
	StopProfiler(ctx context.Context, in *Void, opts ...grpc.CallOption) (*Void, error)
	StartHeapProfiler(ctx context.Context, in *HeapProfilerRequest, opts ...grpc.CallOption) (*Void, error)
	StopHeapProfiler(ctx context.Context, in *Void, opts ...grpc.CallOption) (*Void, error)
	ListThreadCache(ctx context.Context, in *Void, opts ...grpc.CallOption) (*ListThreadCacheResponse, error)
	SetVLOG(ctx context.Context, in *SetVLOGRequest, opts ...grpc.CallOption) (*Void, error)
	GetGFlagsVar(ctx context.Context, in *GFlagsVarName, opts ...grpc.CallOption) (*GFlagsVar, error)
	SetGFlagsVar(ctx context.Context, in *GFlagsVar, opts ...grpc.CallOption) (*Void, error)
	ListGFlagsVar(ctx context.Context, in *Void, opts ...grpc.CallOption) (*GFlagsVars, error)
	ReleaseFreeMemory(ctx context.Context, in *Void, opts ...grpc.CallOption) (*Void, error)
	GetServiceVersion(ctx context.Context, in *Void, opts ...grpc.CallOption) (*GetServiceVersionResponse, error)
}

type commonServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCommonServiceClient(cc grpc.ClientConnInterface) CommonServiceClient {
	return &commonServiceClient{cc}
}

func (c *commonServiceClient) ListCounter(ctx context.Context, in *ListCounterRequest, opts ...grpc.CallOption) (*ListCounterResponse, error) {
	out := new(ListCounterResponse)
	err := c.cc.Invoke(ctx, CommonService_ListCounter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) StartProfiler(ctx context.Context, in *ProfilerRequest, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, CommonService_StartProfiler_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) StopProfiler(ctx context.Context, in *Void, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, CommonService_StopProfiler_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) StartHeapProfiler(ctx context.Context, in *HeapProfilerRequest, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, CommonService_StartHeapProfiler_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) StopHeapProfiler(ctx context.Context, in *Void, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, CommonService_StopHeapProfiler_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) ListThreadCache(ctx context.Context, in *Void, opts ...grpc.CallOption) (*ListThreadCacheResponse, error) {
	out := new(ListThreadCacheResponse)
	err := c.cc.Invoke(ctx, CommonService_ListThreadCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) SetVLOG(ctx context.Context, in *SetVLOGRequest, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, CommonService_SetVLOG_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) GetGFlagsVar(ctx context.Context, in *GFlagsVarName, opts ...grpc.CallOption) (*GFlagsVar, error) {
	out := new(GFlagsVar)
	err := c.cc.Invoke(ctx, CommonService_GetGFlagsVar_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) SetGFlagsVar(ctx context.Context, in *GFlagsVar, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, CommonService_SetGFlagsVar_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) ListGFlagsVar(ctx context.Context, in *Void, opts ...grpc.CallOption) (*GFlagsVars, error) {
	out := new(GFlagsVars)
	err := c.cc.Invoke(ctx, CommonService_ListGFlagsVar_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) ReleaseFreeMemory(ctx context.Context, in *Void, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, CommonService_ReleaseFreeMemory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonServiceClient) GetServiceVersion(ctx context.Context, in *Void, opts ...grpc.CallOption) (*GetServiceVersionResponse, error) {
	out := new(GetServiceVersionResponse)
	err := c.cc.Invoke(ctx, CommonService_GetServiceVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommonServiceServer is the server API for CommonService service.
// All implementations must embed UnimplementedCommonServiceServer
// for forward compatibility
type CommonServiceServer interface {
	ListCounter(context.Context, *ListCounterRequest) (*ListCounterResponse, error)
	StartProfiler(context.Context, *ProfilerRequest) (*Void, error)
	StopProfiler(context.Context, *Void) (*Void, error)
	StartHeapProfiler(context.Context, *HeapProfilerRequest) (*Void, error)
	StopHeapProfiler(context.Context, *Void) (*Void, error)
	ListThreadCache(context.Context, *Void) (*ListThreadCacheResponse, error)
	SetVLOG(context.Context, *SetVLOGRequest) (*Void, error)
	GetGFlagsVar(context.Context, *GFlagsVarName) (*GFlagsVar, error)
	SetGFlagsVar(context.Context, *GFlagsVar) (*Void, error)
	ListGFlagsVar(context.Context, *Void) (*GFlagsVars, error)
	ReleaseFreeMemory(context.Context, *Void) (*Void, error)
	GetServiceVersion(context.Context, *Void) (*GetServiceVersionResponse, error)
	mustEmbedUnimplementedCommonServiceServer()
}

// UnimplementedCommonServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCommonServiceServer struct {
}

func (UnimplementedCommonServiceServer) ListCounter(context.Context, *ListCounterRequest) (*ListCounterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCounter not implemented")
}
func (UnimplementedCommonServiceServer) StartProfiler(context.Context, *ProfilerRequest) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartProfiler not implemented")
}
func (UnimplementedCommonServiceServer) StopProfiler(context.Context, *Void) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopProfiler not implemented")
}
func (UnimplementedCommonServiceServer) StartHeapProfiler(context.Context, *HeapProfilerRequest) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartHeapProfiler not implemented")
}
func (UnimplementedCommonServiceServer) StopHeapProfiler(context.Context, *Void) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopHeapProfiler not implemented")
}
func (UnimplementedCommonServiceServer) ListThreadCache(context.Context, *Void) (*ListThreadCacheResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListThreadCache not implemented")
}
func (UnimplementedCommonServiceServer) SetVLOG(context.Context, *SetVLOGRequest) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetVLOG not implemented")
}
func (UnimplementedCommonServiceServer) GetGFlagsVar(context.Context, *GFlagsVarName) (*GFlagsVar, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGFlagsVar not implemented")
}
func (UnimplementedCommonServiceServer) SetGFlagsVar(context.Context, *GFlagsVar) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGFlagsVar not implemented")
}
func (UnimplementedCommonServiceServer) ListGFlagsVar(context.Context, *Void) (*GFlagsVars, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGFlagsVar not implemented")
}
func (UnimplementedCommonServiceServer) ReleaseFreeMemory(context.Context, *Void) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseFreeMemory not implemented")
}
func (UnimplementedCommonServiceServer) GetServiceVersion(context.Context, *Void) (*GetServiceVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceVersion not implemented")
}
func (UnimplementedCommonServiceServer) mustEmbedUnimplementedCommonServiceServer() {}

// UnsafeCommonServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CommonServiceServer will
// result in compilation errors.
type UnsafeCommonServiceServer interface {
	mustEmbedUnimplementedCommonServiceServer()
}

func RegisterCommonServiceServer(s grpc.ServiceRegistrar, srv CommonServiceServer) {
	s.RegisterService(&CommonService_ServiceDesc, srv)
}

func _CommonService_ListCounter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCounterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).ListCounter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_ListCounter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).ListCounter(ctx, req.(*ListCounterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_StartProfiler_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProfilerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).StartProfiler(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_StartProfiler_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).StartProfiler(ctx, req.(*ProfilerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_StopProfiler_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).StopProfiler(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_StopProfiler_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).StopProfiler(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_StartHeapProfiler_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeapProfilerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).StartHeapProfiler(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_StartHeapProfiler_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).StartHeapProfiler(ctx, req.(*HeapProfilerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_StopHeapProfiler_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).StopHeapProfiler(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_StopHeapProfiler_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).StopHeapProfiler(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_ListThreadCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).ListThreadCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_ListThreadCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).ListThreadCache(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_SetVLOG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVLOGRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).SetVLOG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_SetVLOG_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).SetVLOG(ctx, req.(*SetVLOGRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_GetGFlagsVar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GFlagsVarName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).GetGFlagsVar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_GetGFlagsVar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).GetGFlagsVar(ctx, req.(*GFlagsVarName))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_SetGFlagsVar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GFlagsVar)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).SetGFlagsVar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_SetGFlagsVar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).SetGFlagsVar(ctx, req.(*GFlagsVar))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_ListGFlagsVar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).ListGFlagsVar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_ListGFlagsVar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).ListGFlagsVar(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_ReleaseFreeMemory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).ReleaseFreeMemory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_ReleaseFreeMemory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).ReleaseFreeMemory(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommonService_GetServiceVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).GetServiceVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CommonService_GetServiceVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).GetServiceVersion(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

// CommonService_ServiceDesc is the grpc.ServiceDesc for CommonService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CommonService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.CommonService",
	HandlerType: (*CommonServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListCounter",
			Handler:    _CommonService_ListCounter_Handler,
		},
		{
			MethodName: "StartProfiler",
			Handler:    _CommonService_StartProfiler_Handler,
		},
		{
			MethodName: "StopProfiler",
			Handler:    _CommonService_StopProfiler_Handler,
		},
		{
			MethodName: "StartHeapProfiler",
			Handler:    _CommonService_StartHeapProfiler_Handler,
		},
		{
			MethodName: "StopHeapProfiler",
			Handler:    _CommonService_StopHeapProfiler_Handler,
		},
		{
			MethodName: "ListThreadCache",
			Handler:    _CommonService_ListThreadCache_Handler,
		},
		{
			MethodName: "SetVLOG",
			Handler:    _CommonService_SetVLOG_Handler,
		},
		{
			MethodName: "GetGFlagsVar",
			Handler:    _CommonService_GetGFlagsVar_Handler,
		},
		{
			MethodName: "SetGFlagsVar",
			Handler:    _CommonService_SetGFlagsVar_Handler,
		},
		{
			MethodName: "ListGFlagsVar",
			Handler:    _CommonService_ListGFlagsVar_Handler,
		},
		{
			MethodName: "ReleaseFreeMemory",
			Handler:    _CommonService_ReleaseFreeMemory_Handler,
		},
		{
			MethodName: "GetServiceVersion",
			Handler:    _CommonService_GetServiceVersion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "common.proto",
}
