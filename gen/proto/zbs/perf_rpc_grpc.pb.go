// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.12
// source: perf_rpc.proto

package zbs

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	VolumePerfService_GetVolumePerf_FullMethodName       = "/zbs.VolumePerfService/GetVolumePerf"
	VolumePerfService_GetVolumesPerf_FullMethodName      = "/zbs.VolumePerfService/GetVolumesPerf"
	VolumePerfService_GetAllVolumesPerf_FullMethodName   = "/zbs.VolumePerfService/GetAllVolumesPerf"
	VolumePerfService_ProbeVolumes_FullMethodName        = "/zbs.VolumePerfService/ProbeVolumes"
	VolumePerfService_DisableProbeVolumes_FullMethodName = "/zbs.VolumePerfService/DisableProbeVolumes"
)

// VolumePerfServiceClient is the client API for VolumePerfService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VolumePerfServiceClient interface {
	GetVolumePerf(ctx context.Context, in *VolumePerfRequest, opts ...grpc.CallOption) (*VolumePerf, error)
	GetVolumesPerf(ctx context.Context, in *VolumesPerfRequest, opts ...grpc.CallOption) (*VolumesPerf, error)
	GetAllVolumesPerf(ctx context.Context, in *Void, opts ...grpc.CallOption) (*VolumesPerf, error)
	ProbeVolumes(ctx context.Context, in *ProbeVolumesRequest, opts ...grpc.CallOption) (*Void, error)
	DisableProbeVolumes(ctx context.Context, in *DisableProbeVolumesRequest, opts ...grpc.CallOption) (*Void, error)
}

type volumePerfServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVolumePerfServiceClient(cc grpc.ClientConnInterface) VolumePerfServiceClient {
	return &volumePerfServiceClient{cc}
}

func (c *volumePerfServiceClient) GetVolumePerf(ctx context.Context, in *VolumePerfRequest, opts ...grpc.CallOption) (*VolumePerf, error) {
	out := new(VolumePerf)
	err := c.cc.Invoke(ctx, VolumePerfService_GetVolumePerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *volumePerfServiceClient) GetVolumesPerf(ctx context.Context, in *VolumesPerfRequest, opts ...grpc.CallOption) (*VolumesPerf, error) {
	out := new(VolumesPerf)
	err := c.cc.Invoke(ctx, VolumePerfService_GetVolumesPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *volumePerfServiceClient) GetAllVolumesPerf(ctx context.Context, in *Void, opts ...grpc.CallOption) (*VolumesPerf, error) {
	out := new(VolumesPerf)
	err := c.cc.Invoke(ctx, VolumePerfService_GetAllVolumesPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *volumePerfServiceClient) ProbeVolumes(ctx context.Context, in *ProbeVolumesRequest, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, VolumePerfService_ProbeVolumes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *volumePerfServiceClient) DisableProbeVolumes(ctx context.Context, in *DisableProbeVolumesRequest, opts ...grpc.CallOption) (*Void, error) {
	out := new(Void)
	err := c.cc.Invoke(ctx, VolumePerfService_DisableProbeVolumes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VolumePerfServiceServer is the server API for VolumePerfService service.
// All implementations must embed UnimplementedVolumePerfServiceServer
// for forward compatibility
type VolumePerfServiceServer interface {
	GetVolumePerf(context.Context, *VolumePerfRequest) (*VolumePerf, error)
	GetVolumesPerf(context.Context, *VolumesPerfRequest) (*VolumesPerf, error)
	GetAllVolumesPerf(context.Context, *Void) (*VolumesPerf, error)
	ProbeVolumes(context.Context, *ProbeVolumesRequest) (*Void, error)
	DisableProbeVolumes(context.Context, *DisableProbeVolumesRequest) (*Void, error)
	mustEmbedUnimplementedVolumePerfServiceServer()
}

// UnimplementedVolumePerfServiceServer must be embedded to have forward compatible implementations.
type UnimplementedVolumePerfServiceServer struct {
}

func (UnimplementedVolumePerfServiceServer) GetVolumePerf(context.Context, *VolumePerfRequest) (*VolumePerf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVolumePerf not implemented")
}
func (UnimplementedVolumePerfServiceServer) GetVolumesPerf(context.Context, *VolumesPerfRequest) (*VolumesPerf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVolumesPerf not implemented")
}
func (UnimplementedVolumePerfServiceServer) GetAllVolumesPerf(context.Context, *Void) (*VolumesPerf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllVolumesPerf not implemented")
}
func (UnimplementedVolumePerfServiceServer) ProbeVolumes(context.Context, *ProbeVolumesRequest) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProbeVolumes not implemented")
}
func (UnimplementedVolumePerfServiceServer) DisableProbeVolumes(context.Context, *DisableProbeVolumesRequest) (*Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableProbeVolumes not implemented")
}
func (UnimplementedVolumePerfServiceServer) mustEmbedUnimplementedVolumePerfServiceServer() {}

// UnsafeVolumePerfServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VolumePerfServiceServer will
// result in compilation errors.
type UnsafeVolumePerfServiceServer interface {
	mustEmbedUnimplementedVolumePerfServiceServer()
}

func RegisterVolumePerfServiceServer(s grpc.ServiceRegistrar, srv VolumePerfServiceServer) {
	s.RegisterService(&VolumePerfService_ServiceDesc, srv)
}

func _VolumePerfService_GetVolumePerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VolumePerfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VolumePerfServiceServer).GetVolumePerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VolumePerfService_GetVolumePerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VolumePerfServiceServer).GetVolumePerf(ctx, req.(*VolumePerfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VolumePerfService_GetVolumesPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VolumesPerfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VolumePerfServiceServer).GetVolumesPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VolumePerfService_GetVolumesPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VolumePerfServiceServer).GetVolumesPerf(ctx, req.(*VolumesPerfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VolumePerfService_GetAllVolumesPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VolumePerfServiceServer).GetAllVolumesPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VolumePerfService_GetAllVolumesPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VolumePerfServiceServer).GetAllVolumesPerf(ctx, req.(*Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _VolumePerfService_ProbeVolumes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProbeVolumesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VolumePerfServiceServer).ProbeVolumes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VolumePerfService_ProbeVolumes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VolumePerfServiceServer).ProbeVolumes(ctx, req.(*ProbeVolumesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VolumePerfService_DisableProbeVolumes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableProbeVolumesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VolumePerfServiceServer).DisableProbeVolumes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VolumePerfService_DisableProbeVolumes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VolumePerfServiceServer).DisableProbeVolumes(ctx, req.(*DisableProbeVolumesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VolumePerfService_ServiceDesc is the grpc.ServiceDesc for VolumePerfService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VolumePerfService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.VolumePerfService",
	HandlerType: (*VolumePerfServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVolumePerf",
			Handler:    _VolumePerfService_GetVolumePerf_Handler,
		},
		{
			MethodName: "GetVolumesPerf",
			Handler:    _VolumePerfService_GetVolumesPerf_Handler,
		},
		{
			MethodName: "GetAllVolumesPerf",
			Handler:    _VolumePerfService_GetAllVolumesPerf_Handler,
		},
		{
			MethodName: "ProbeVolumes",
			Handler:    _VolumePerfService_ProbeVolumes_Handler,
		},
		{
			MethodName: "DisableProbeVolumes",
			Handler:    _VolumePerfService_DisableProbeVolumes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "perf_rpc.proto",
}

const (
	ChunkPerfService_GetUIOPerf_FullMethodName                 = "/zbs.ChunkPerfService/GetUIOPerf"
	ChunkPerfService_GetAccessPerf_FullMethodName              = "/zbs.ChunkPerfService/GetAccessPerf"
	ChunkPerfService_GetLSMPerf_FullMethodName                 = "/zbs.ChunkPerfService/GetLSMPerf"
	ChunkPerfService_GetLayeredAccessPerf_FullMethodName       = "/zbs.ChunkPerfService/GetLayeredAccessPerf"
	ChunkPerfService_GetFlowControlPerf_FullMethodName         = "/zbs.ChunkPerfService/GetFlowControlPerf"
	ChunkPerfService_GetInternalFlowControlPerf_FullMethodName = "/zbs.ChunkPerfService/GetInternalFlowControlPerf"
)

// ChunkPerfServiceClient is the client API for ChunkPerfService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChunkPerfServiceClient interface {
	GetUIOPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*UIOPerf, error)
	GetAccessPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*AccessPerf, error)
	GetLSMPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*LSMPerf, error)
	GetLayeredAccessPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*LayeredAccessPerf, error)
	GetFlowControlPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*FlowControlPerfs, error)
	GetInternalFlowControlPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*InternalFlowControlPerfs, error)
}

type chunkPerfServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewChunkPerfServiceClient(cc grpc.ClientConnInterface) ChunkPerfServiceClient {
	return &chunkPerfServiceClient{cc}
}

func (c *chunkPerfServiceClient) GetUIOPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*UIOPerf, error) {
	out := new(UIOPerf)
	err := c.cc.Invoke(ctx, ChunkPerfService_GetUIOPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkPerfServiceClient) GetAccessPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*AccessPerf, error) {
	out := new(AccessPerf)
	err := c.cc.Invoke(ctx, ChunkPerfService_GetAccessPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkPerfServiceClient) GetLSMPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*LSMPerf, error) {
	out := new(LSMPerf)
	err := c.cc.Invoke(ctx, ChunkPerfService_GetLSMPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkPerfServiceClient) GetLayeredAccessPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*LayeredAccessPerf, error) {
	out := new(LayeredAccessPerf)
	err := c.cc.Invoke(ctx, ChunkPerfService_GetLayeredAccessPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkPerfServiceClient) GetFlowControlPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*FlowControlPerfs, error) {
	out := new(FlowControlPerfs)
	err := c.cc.Invoke(ctx, ChunkPerfService_GetFlowControlPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkPerfServiceClient) GetInternalFlowControlPerf(ctx context.Context, in *ChunkInstance, opts ...grpc.CallOption) (*InternalFlowControlPerfs, error) {
	out := new(InternalFlowControlPerfs)
	err := c.cc.Invoke(ctx, ChunkPerfService_GetInternalFlowControlPerf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChunkPerfServiceServer is the server API for ChunkPerfService service.
// All implementations must embed UnimplementedChunkPerfServiceServer
// for forward compatibility
type ChunkPerfServiceServer interface {
	GetUIOPerf(context.Context, *ChunkInstance) (*UIOPerf, error)
	GetAccessPerf(context.Context, *ChunkInstance) (*AccessPerf, error)
	GetLSMPerf(context.Context, *ChunkInstance) (*LSMPerf, error)
	GetLayeredAccessPerf(context.Context, *ChunkInstance) (*LayeredAccessPerf, error)
	GetFlowControlPerf(context.Context, *ChunkInstance) (*FlowControlPerfs, error)
	GetInternalFlowControlPerf(context.Context, *ChunkInstance) (*InternalFlowControlPerfs, error)
	mustEmbedUnimplementedChunkPerfServiceServer()
}

// UnimplementedChunkPerfServiceServer must be embedded to have forward compatible implementations.
type UnimplementedChunkPerfServiceServer struct {
}

func (UnimplementedChunkPerfServiceServer) GetUIOPerf(context.Context, *ChunkInstance) (*UIOPerf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUIOPerf not implemented")
}
func (UnimplementedChunkPerfServiceServer) GetAccessPerf(context.Context, *ChunkInstance) (*AccessPerf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccessPerf not implemented")
}
func (UnimplementedChunkPerfServiceServer) GetLSMPerf(context.Context, *ChunkInstance) (*LSMPerf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLSMPerf not implemented")
}
func (UnimplementedChunkPerfServiceServer) GetLayeredAccessPerf(context.Context, *ChunkInstance) (*LayeredAccessPerf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLayeredAccessPerf not implemented")
}
func (UnimplementedChunkPerfServiceServer) GetFlowControlPerf(context.Context, *ChunkInstance) (*FlowControlPerfs, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFlowControlPerf not implemented")
}
func (UnimplementedChunkPerfServiceServer) GetInternalFlowControlPerf(context.Context, *ChunkInstance) (*InternalFlowControlPerfs, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternalFlowControlPerf not implemented")
}
func (UnimplementedChunkPerfServiceServer) mustEmbedUnimplementedChunkPerfServiceServer() {}

// UnsafeChunkPerfServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChunkPerfServiceServer will
// result in compilation errors.
type UnsafeChunkPerfServiceServer interface {
	mustEmbedUnimplementedChunkPerfServiceServer()
}

func RegisterChunkPerfServiceServer(s grpc.ServiceRegistrar, srv ChunkPerfServiceServer) {
	s.RegisterService(&ChunkPerfService_ServiceDesc, srv)
}

func _ChunkPerfService_GetUIOPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkPerfServiceServer).GetUIOPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkPerfService_GetUIOPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkPerfServiceServer).GetUIOPerf(ctx, req.(*ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkPerfService_GetAccessPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkPerfServiceServer).GetAccessPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkPerfService_GetAccessPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkPerfServiceServer).GetAccessPerf(ctx, req.(*ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkPerfService_GetLSMPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkPerfServiceServer).GetLSMPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkPerfService_GetLSMPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkPerfServiceServer).GetLSMPerf(ctx, req.(*ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkPerfService_GetLayeredAccessPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkPerfServiceServer).GetLayeredAccessPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkPerfService_GetLayeredAccessPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkPerfServiceServer).GetLayeredAccessPerf(ctx, req.(*ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkPerfService_GetFlowControlPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkPerfServiceServer).GetFlowControlPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkPerfService_GetFlowControlPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkPerfServiceServer).GetFlowControlPerf(ctx, req.(*ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkPerfService_GetInternalFlowControlPerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkPerfServiceServer).GetInternalFlowControlPerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkPerfService_GetInternalFlowControlPerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkPerfServiceServer).GetInternalFlowControlPerf(ctx, req.(*ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

// ChunkPerfService_ServiceDesc is the grpc.ServiceDesc for ChunkPerfService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChunkPerfService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.ChunkPerfService",
	HandlerType: (*ChunkPerfServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUIOPerf",
			Handler:    _ChunkPerfService_GetUIOPerf_Handler,
		},
		{
			MethodName: "GetAccessPerf",
			Handler:    _ChunkPerfService_GetAccessPerf_Handler,
		},
		{
			MethodName: "GetLSMPerf",
			Handler:    _ChunkPerfService_GetLSMPerf_Handler,
		},
		{
			MethodName: "GetLayeredAccessPerf",
			Handler:    _ChunkPerfService_GetLayeredAccessPerf_Handler,
		},
		{
			MethodName: "GetFlowControlPerf",
			Handler:    _ChunkPerfService_GetFlowControlPerf_Handler,
		},
		{
			MethodName: "GetInternalFlowControlPerf",
			Handler:    _ChunkPerfService_GetInternalFlowControlPerf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "perf_rpc.proto",
}
