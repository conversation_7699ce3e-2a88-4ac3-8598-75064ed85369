// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.12
// source: block.proto

package block

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CompareExtentService_CompareLExtent_FullMethodName = "/zbs.block.CompareExtentService/CompareLExtent"
)

// CompareExtentServiceClient is the client API for CompareExtentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CompareExtentServiceClient interface {
	CompareLExtent(ctx context.Context, in *CompareLExtentRequest, opts ...grpc.CallOption) (*CompareExtentResponse, error)
}

type compareExtentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCompareExtentServiceClient(cc grpc.ClientConnInterface) CompareExtentServiceClient {
	return &compareExtentServiceClient{cc}
}

func (c *compareExtentServiceClient) CompareLExtent(ctx context.Context, in *CompareLExtentRequest, opts ...grpc.CallOption) (*CompareExtentResponse, error) {
	out := new(CompareExtentResponse)
	err := c.cc.Invoke(ctx, CompareExtentService_CompareLExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CompareExtentServiceServer is the server API for CompareExtentService service.
// All implementations must embed UnimplementedCompareExtentServiceServer
// for forward compatibility
type CompareExtentServiceServer interface {
	CompareLExtent(context.Context, *CompareLExtentRequest) (*CompareExtentResponse, error)
	mustEmbedUnimplementedCompareExtentServiceServer()
}

// UnimplementedCompareExtentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCompareExtentServiceServer struct {
}

func (UnimplementedCompareExtentServiceServer) CompareLExtent(context.Context, *CompareLExtentRequest) (*CompareExtentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompareLExtent not implemented")
}
func (UnimplementedCompareExtentServiceServer) mustEmbedUnimplementedCompareExtentServiceServer() {}

// UnsafeCompareExtentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CompareExtentServiceServer will
// result in compilation errors.
type UnsafeCompareExtentServiceServer interface {
	mustEmbedUnimplementedCompareExtentServiceServer()
}

func RegisterCompareExtentServiceServer(s grpc.ServiceRegistrar, srv CompareExtentServiceServer) {
	s.RegisterService(&CompareExtentService_ServiceDesc, srv)
}

func _CompareExtentService_CompareLExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompareLExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompareExtentServiceServer).CompareLExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CompareExtentService_CompareLExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompareExtentServiceServer).CompareLExtent(ctx, req.(*CompareLExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CompareExtentService_ServiceDesc is the grpc.ServiceDesc for CompareExtentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CompareExtentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.block.CompareExtentService",
	HandlerType: (*CompareExtentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CompareLExtent",
			Handler:    _CompareExtentService_CompareLExtent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "block.proto",
}
