// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: block.proto

package block

import (
	zbs "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CompareLExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lid1 *uint32 `protobuf:"varint,1,req,name=lid1" json:"lid1,omitempty"`
	Lid2 *uint32 `protobuf:"varint,2,req,name=lid2" json:"lid2,omitempty"`
}

func (x *CompareLExtentRequest) Reset() {
	*x = CompareLExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_block_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompareLExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompareLExtentRequest) ProtoMessage() {}

func (x *CompareLExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_block_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompareLExtentRequest.ProtoReflect.Descriptor instead.
func (*CompareLExtentRequest) Descriptor() ([]byte, []int) {
	return file_block_proto_rawDescGZIP(), []int{0}
}

func (x *CompareLExtentRequest) GetLid1() uint32 {
	if x != nil && x.Lid1 != nil {
		return *x.Lid1
	}
	return 0
}

func (x *CompareLExtentRequest) GetLid2() uint32 {
	if x != nil && x.Lid2 != nil {
		return *x.Lid2
	}
	return 0
}

type CompareExtentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DiffRanges []*zbs.RangeU64 `protobuf:"bytes,1,rep,name=diff_ranges,json=diffRanges" json:"diff_ranges,omitempty"`
}

func (x *CompareExtentResponse) Reset() {
	*x = CompareExtentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_block_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompareExtentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompareExtentResponse) ProtoMessage() {}

func (x *CompareExtentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_block_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompareExtentResponse.ProtoReflect.Descriptor instead.
func (*CompareExtentResponse) Descriptor() ([]byte, []int) {
	return file_block_proto_rawDescGZIP(), []int{1}
}

func (x *CompareExtentResponse) GetDiffRanges() []*zbs.RangeU64 {
	if x != nil {
		return x.DiffRanges
	}
	return nil
}

var File_block_proto protoreflect.FileDescriptor

var file_block_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x7a,
	0x62, 0x73, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72,
	0x65, 0x4c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x69, 0x64, 0x31, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x04, 0x6c,
	0x69, 0x64, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x64, 0x32, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x0d, 0x52, 0x04, 0x6c, 0x69, 0x64, 0x32, 0x22, 0x47, 0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x72, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2e, 0x0a, 0x0b, 0x64, 0x69, 0x66, 0x66, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x55, 0x36, 0x34, 0x52, 0x0a, 0x64, 0x69, 0x66, 0x66, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x32, 0x72, 0x0a, 0x14, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x72, 0x65, 0x4c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x4c, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x04,
	0xc0, 0x3e, 0xa9, 0x46, 0x42, 0x3b, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68, 0x2f, 0x7a, 0x62, 0x73, 0x2d, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x7a, 0x62, 0x73, 0x2f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x80, 0x01, 0x01, 0x90, 0x01,
	0x01,
}

var (
	file_block_proto_rawDescOnce sync.Once
	file_block_proto_rawDescData = file_block_proto_rawDesc
)

func file_block_proto_rawDescGZIP() []byte {
	file_block_proto_rawDescOnce.Do(func() {
		file_block_proto_rawDescData = protoimpl.X.CompressGZIP(file_block_proto_rawDescData)
	})
	return file_block_proto_rawDescData
}

var file_block_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_block_proto_goTypes = []interface{}{
	(*CompareLExtentRequest)(nil), // 0: zbs.block.CompareLExtentRequest
	(*CompareExtentResponse)(nil), // 1: zbs.block.CompareExtentResponse
	(*zbs.RangeU64)(nil),          // 2: zbs.RangeU64
}
var file_block_proto_depIdxs = []int32{
	2, // 0: zbs.block.CompareExtentResponse.diff_ranges:type_name -> zbs.RangeU64
	0, // 1: zbs.block.CompareExtentService.CompareLExtent:input_type -> zbs.block.CompareLExtentRequest
	1, // 2: zbs.block.CompareExtentService.CompareLExtent:output_type -> zbs.block.CompareExtentResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_block_proto_init() }
func file_block_proto_init() {
	if File_block_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_block_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompareLExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_block_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompareExtentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_block_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_block_proto_goTypes,
		DependencyIndexes: file_block_proto_depIdxs,
		MessageInfos:      file_block_proto_msgTypes,
	}.Build()
	File_block_proto = out.File
	file_block_proto_rawDesc = nil
	file_block_proto_goTypes = nil
	file_block_proto_depIdxs = nil
}
