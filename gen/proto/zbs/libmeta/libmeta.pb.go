// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: libmeta.proto

package libmeta

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CpuInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model          *string `protobuf:"bytes,1,req,name=model" json:"model,omitempty"`
	Socket         *uint32 `protobuf:"varint,2,req,name=socket" json:"socket,omitempty"`
	CoresPerSocket *uint32 `protobuf:"varint,3,req,name=cores_per_socket,json=coresPerSocket" json:"cores_per_socket,omitempty"`
}

func (x *CpuInfo) Reset() {
	*x = CpuInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_libmeta_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CpuInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CpuInfo) ProtoMessage() {}

func (x *CpuInfo) ProtoReflect() protoreflect.Message {
	mi := &file_libmeta_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CpuInfo.ProtoReflect.Descriptor instead.
func (*CpuInfo) Descriptor() ([]byte, []int) {
	return file_libmeta_proto_rawDescGZIP(), []int{0}
}

func (x *CpuInfo) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *CpuInfo) GetSocket() uint32 {
	if x != nil && x.Socket != nil {
		return *x.Socket
	}
	return 0
}

func (x *CpuInfo) GetCoresPerSocket() uint32 {
	if x != nil && x.CoresPerSocket != nil {
		return *x.CoresPerSocket
	}
	return 0
}

type MemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total *uint32 `protobuf:"varint,1,req,name=total" json:"total,omitempty"` // mb
	Used  *uint32 `protobuf:"varint,2,req,name=used" json:"used,omitempty"`   // mb
}

func (x *MemInfo) Reset() {
	*x = MemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_libmeta_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemInfo) ProtoMessage() {}

func (x *MemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_libmeta_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemInfo.ProtoReflect.Descriptor instead.
func (*MemInfo) Descriptor() ([]byte, []int) {
	return file_libmeta_proto_rawDescGZIP(), []int{1}
}

func (x *MemInfo) GetTotal() uint32 {
	if x != nil && x.Total != nil {
		return *x.Total
	}
	return 0
}

func (x *MemInfo) GetUsed() uint32 {
	if x != nil && x.Used != nil {
		return *x.Used
	}
	return 0
}

type DiskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DiskId      *uint32 `protobuf:"varint,1,opt,name=disk_id,json=diskId" json:"disk_id,omitempty"`
	DiskModel   *string `protobuf:"bytes,2,opt,name=disk_model,json=diskModel" json:"disk_model,omitempty"`
	Total       *uint64 `protobuf:"varint,3,opt,name=total" json:"total,omitempty"`
	Used        *uint64 `protobuf:"varint,4,opt,name=used" json:"used,omitempty"`
	ErrorReason *string `protobuf:"bytes,6,opt,name=error_reason,json=errorReason,def=" json:"error_reason,omitempty"`
}

// Default values for DiskInfo fields.
const (
	Default_DiskInfo_ErrorReason = string("")
)

func (x *DiskInfo) Reset() {
	*x = DiskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_libmeta_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiskInfo) ProtoMessage() {}

func (x *DiskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_libmeta_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiskInfo.ProtoReflect.Descriptor instead.
func (*DiskInfo) Descriptor() ([]byte, []int) {
	return file_libmeta_proto_rawDescGZIP(), []int{2}
}

func (x *DiskInfo) GetDiskId() uint32 {
	if x != nil && x.DiskId != nil {
		return *x.DiskId
	}
	return 0
}

func (x *DiskInfo) GetDiskModel() string {
	if x != nil && x.DiskModel != nil {
		return *x.DiskModel
	}
	return ""
}

func (x *DiskInfo) GetTotal() uint64 {
	if x != nil && x.Total != nil {
		return *x.Total
	}
	return 0
}

func (x *DiskInfo) GetUsed() uint64 {
	if x != nil && x.Used != nil {
		return *x.Used
	}
	return 0
}

func (x *DiskInfo) GetErrorReason() string {
	if x != nil && x.ErrorReason != nil {
		return *x.ErrorReason
	}
	return Default_DiskInfo_ErrorReason
}

type NicInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NicInfo) Reset() {
	*x = NicInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_libmeta_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NicInfo) ProtoMessage() {}

func (x *NicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_libmeta_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NicInfo.ProtoReflect.Descriptor instead.
func (*NicInfo) Descriptor() ([]byte, []int) {
	return file_libmeta_proto_rawDescGZIP(), []int{3}
}

type ProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProcessInfo) Reset() {
	*x = ProcessInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_libmeta_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInfo) ProtoMessage() {}

func (x *ProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_libmeta_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInfo.ProtoReflect.Descriptor instead.
func (*ProcessInfo) Descriptor() ([]byte, []int) {
	return file_libmeta_proto_rawDescGZIP(), []int{4}
}

var File_libmeta_proto protoreflect.FileDescriptor

var file_libmeta_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6c, 0x69, 0x62, 0x6d, 0x65, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0b, 0x7a, 0x62, 0x73, 0x2e, 0x6c, 0x69, 0x62, 0x6d, 0x65, 0x74, 0x61, 0x22, 0x61, 0x0a, 0x07,
	0x43, 0x70, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06, 0x73,
	0x6f, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x5f, 0x70,
	0x65, 0x72, 0x5f, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0d, 0x52,
	0x0e, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x50, 0x65, 0x72, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x22,
	0x33, 0x0a, 0x07, 0x4d, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x64, 0x22, 0x91, 0x01, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x64, 0x69, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69,
	0x73, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x69, 0x73, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x0b, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x09, 0x0a, 0x07, 0x4e, 0x69, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x0d, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x3d, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68, 0x2f, 0x7a, 0x62, 0x73, 0x2d, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x7a, 0x62, 0x73, 0x2f, 0x6c, 0x69, 0x62, 0x6d, 0x65, 0x74, 0x61, 0x80, 0x01, 0x01, 0x90, 0x01,
	0x01,
}

var (
	file_libmeta_proto_rawDescOnce sync.Once
	file_libmeta_proto_rawDescData = file_libmeta_proto_rawDesc
)

func file_libmeta_proto_rawDescGZIP() []byte {
	file_libmeta_proto_rawDescOnce.Do(func() {
		file_libmeta_proto_rawDescData = protoimpl.X.CompressGZIP(file_libmeta_proto_rawDescData)
	})
	return file_libmeta_proto_rawDescData
}

var file_libmeta_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_libmeta_proto_goTypes = []interface{}{
	(*CpuInfo)(nil),     // 0: zbs.libmeta.CpuInfo
	(*MemInfo)(nil),     // 1: zbs.libmeta.MemInfo
	(*DiskInfo)(nil),    // 2: zbs.libmeta.DiskInfo
	(*NicInfo)(nil),     // 3: zbs.libmeta.NicInfo
	(*ProcessInfo)(nil), // 4: zbs.libmeta.ProcessInfo
}
var file_libmeta_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_libmeta_proto_init() }
func file_libmeta_proto_init() {
	if File_libmeta_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_libmeta_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CpuInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_libmeta_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_libmeta_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_libmeta_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NicInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_libmeta_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_libmeta_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_libmeta_proto_goTypes,
		DependencyIndexes: file_libmeta_proto_depIdxs,
		MessageInfos:      file_libmeta_proto_msgTypes,
	}.Build()
	File_libmeta_proto = out.File
	file_libmeta_proto_rawDesc = nil
	file_libmeta_proto_goTypes = nil
	file_libmeta_proto_depIdxs = nil
}
