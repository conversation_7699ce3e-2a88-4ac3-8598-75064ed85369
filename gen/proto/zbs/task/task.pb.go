// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: task.proto

package task

import (
	zbs "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskState int32

const (
	TaskState_NONE        TaskState = 0
	TaskState_CREATED     TaskState = 1
	TaskState_IN_PROGRESS TaskState = 2
	TaskState_SUCCEED     TaskState = 3
	TaskState_FAILED      TaskState = 4
	TaskState_CANCELED    TaskState = 5
	TaskState_PAUSED      TaskState = 6
)

// Enum value maps for TaskState.
var (
	TaskState_name = map[int32]string{
		0: "NONE",
		1: "CREATED",
		2: "IN_PROGRESS",
		3: "SUCCEED",
		4: "FAILED",
		5: "CANCELED",
		6: "PAUSED",
	}
	TaskState_value = map[string]int32{
		"NONE":        0,
		"CREATED":     1,
		"IN_PROGRESS": 2,
		"SUCCEED":     3,
		"FAILED":      4,
		"CANCELED":    5,
		"PAUSED":      6,
	}
)

func (x TaskState) Enum() *TaskState {
	p := new(TaskState)
	*p = x
	return p
}

func (x TaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_task_proto_enumTypes[0].Descriptor()
}

func (TaskState) Type() protoreflect.EnumType {
	return &file_task_proto_enumTypes[0]
}

func (x TaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TaskState) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TaskState(num)
	return nil
}

// Deprecated: Use TaskState.Descriptor instead.
func (TaskState) EnumDescriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{0}
}

type CopyVolumeTaskState int32

const (
	CopyVolumeTaskState_CV_INIT             CopyVolumeTaskState = 0
	CopyVolumeTaskState_CV_COPY_EXTENT      CopyVolumeTaskState = 1
	CopyVolumeTaskState_CV_COPY_EXTENT_DONE CopyVolumeTaskState = 2
	CopyVolumeTaskState_CV_CANCELED         CopyVolumeTaskState = 3
)

// Enum value maps for CopyVolumeTaskState.
var (
	CopyVolumeTaskState_name = map[int32]string{
		0: "CV_INIT",
		1: "CV_COPY_EXTENT",
		2: "CV_COPY_EXTENT_DONE",
		3: "CV_CANCELED",
	}
	CopyVolumeTaskState_value = map[string]int32{
		"CV_INIT":             0,
		"CV_COPY_EXTENT":      1,
		"CV_COPY_EXTENT_DONE": 2,
		"CV_CANCELED":         3,
	}
)

func (x CopyVolumeTaskState) Enum() *CopyVolumeTaskState {
	p := new(CopyVolumeTaskState)
	*p = x
	return p
}

func (x CopyVolumeTaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CopyVolumeTaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_task_proto_enumTypes[1].Descriptor()
}

func (CopyVolumeTaskState) Type() protoreflect.EnumType {
	return &file_task_proto_enumTypes[1]
}

func (x CopyVolumeTaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CopyVolumeTaskState) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CopyVolumeTaskState(num)
	return nil
}

// Deprecated: Use CopyVolumeTaskState.Descriptor instead.
func (CopyVolumeTaskState) EnumDescriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{1}
}

type TaskProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Percentage *float32 `protobuf:"fixed32,1,opt,name=percentage,def=0" json:"percentage,omitempty"` // progress if valid
	Message    []byte   `protobuf:"bytes,2,opt,name=message,def=" json:"message,omitempty"`          // message of the current status
}

// Default values for TaskProgress fields.
const (
	Default_TaskProgress_Percentage = float32(0)
)

// Default values for TaskProgress fields.
var (
	Default_TaskProgress_Message = []byte("")
)

func (x *TaskProgress) Reset() {
	*x = TaskProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgress) ProtoMessage() {}

func (x *TaskProgress) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgress.ProtoReflect.Descriptor instead.
func (*TaskProgress) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{0}
}

func (x *TaskProgress) GetPercentage() float32 {
	if x != nil && x.Percentage != nil {
		return *x.Percentage
	}
	return Default_TaskProgress_Percentage
}

func (x *TaskProgress) GetMessage() []byte {
	if x != nil && x.Message != nil {
		return x.Message
	}
	return append([]byte(nil), Default_TaskProgress_Message...)
}

type Task struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id                  []byte         `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	State               *TaskState     `protobuf:"varint,2,opt,name=state,enum=zbs.task.TaskState,def=0" json:"state,omitempty"`
	ExitStatus          *zbs.RpcStatus `protobuf:"bytes,3,opt,name=exit_status,json=exitStatus" json:"exit_status,omitempty"`
	SpecifiedRunnerAddr *zbs.Address   `protobuf:"bytes,4,opt,name=specified_runner_addr,json=specifiedRunnerAddr" json:"specified_runner_addr,omitempty"` // specify which runner to use. Use it only you really want to do so.
	Name                []byte         `protobuf:"bytes,5,opt,name=name,def=" json:"name,omitempty"`                                                       // the name of the task
	Progress            *TaskProgress  `protobuf:"bytes,6,opt,name=progress" json:"progress,omitempty"`                                                    // the task runner can update progress here
	RunnerAddr          *zbs.Address   `protobuf:"bytes,7,opt,name=runner_addr,json=runnerAddr" json:"runner_addr,omitempty"`                              // runner running this task
	CreatedMs           *uint64        `protobuf:"varint,8,opt,name=created_ms,json=createdMs" json:"created_ms,omitempty"`                                // time since epoch
	FinishedMs          *uint64        `protobuf:"varint,9,opt,name=finished_ms,json=finishedMs" json:"finished_ms,omitempty"`                             // time since epoch
	MaxScheduleTimes    *uint64        `protobuf:"varint,10,opt,name=max_schedule_times,json=maxScheduleTimes,def=10" json:"max_schedule_times,omitempty"` // max retry times to schedule
	ScheduleTimes       *uint64        `protobuf:"varint,11,opt,name=schedule_times,json=scheduleTimes,def=0" json:"schedule_times,omitempty"`
	LastFailedMs        *uint64        `protobuf:"varint,12,opt,name=last_failed_ms,json=lastFailedMs,def=0" json:"last_failed_ms,omitempty"`
	// for internal use
	ServiceId   *int32     `protobuf:"varint,20,opt,name=service_id,json=serviceId,def=0" json:"service_id,omitempty"` // service id
	MethodId    *int32     `protobuf:"varint,21,opt,name=method_id,json=methodId,def=0" json:"method_id,omitempty"`    // method id
	ExpectState *TaskState `protobuf:"varint,30,opt,name=expect_state,json=expectState,enum=zbs.task.TaskState,def=0" json:"expect_state,omitempty"`
}

// Default values for Task fields.
const (
	Default_Task_State            = TaskState_NONE
	Default_Task_MaxScheduleTimes = uint64(10)
	Default_Task_ScheduleTimes    = uint64(0)
	Default_Task_LastFailedMs     = uint64(0)
	Default_Task_ServiceId        = int32(0)
	Default_Task_MethodId         = int32(0)
	Default_Task_ExpectState      = TaskState_NONE
)

// Default values for Task fields.
var (
	Default_Task_Name = []byte("")
)

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{1}
}

func (x *Task) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Task) GetState() TaskState {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Default_Task_State
}

func (x *Task) GetExitStatus() *zbs.RpcStatus {
	if x != nil {
		return x.ExitStatus
	}
	return nil
}

func (x *Task) GetSpecifiedRunnerAddr() *zbs.Address {
	if x != nil {
		return x.SpecifiedRunnerAddr
	}
	return nil
}

func (x *Task) GetName() []byte {
	if x != nil && x.Name != nil {
		return x.Name
	}
	return append([]byte(nil), Default_Task_Name...)
}

func (x *Task) GetProgress() *TaskProgress {
	if x != nil {
		return x.Progress
	}
	return nil
}

func (x *Task) GetRunnerAddr() *zbs.Address {
	if x != nil {
		return x.RunnerAddr
	}
	return nil
}

func (x *Task) GetCreatedMs() uint64 {
	if x != nil && x.CreatedMs != nil {
		return *x.CreatedMs
	}
	return 0
}

func (x *Task) GetFinishedMs() uint64 {
	if x != nil && x.FinishedMs != nil {
		return *x.FinishedMs
	}
	return 0
}

func (x *Task) GetMaxScheduleTimes() uint64 {
	if x != nil && x.MaxScheduleTimes != nil {
		return *x.MaxScheduleTimes
	}
	return Default_Task_MaxScheduleTimes
}

func (x *Task) GetScheduleTimes() uint64 {
	if x != nil && x.ScheduleTimes != nil {
		return *x.ScheduleTimes
	}
	return Default_Task_ScheduleTimes
}

func (x *Task) GetLastFailedMs() uint64 {
	if x != nil && x.LastFailedMs != nil {
		return *x.LastFailedMs
	}
	return Default_Task_LastFailedMs
}

func (x *Task) GetServiceId() int32 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return Default_Task_ServiceId
}

func (x *Task) GetMethodId() int32 {
	if x != nil && x.MethodId != nil {
		return *x.MethodId
	}
	return Default_Task_MethodId
}

func (x *Task) GetExpectState() TaskState {
	if x != nil && x.ExpectState != nil {
		return *x.ExpectState
	}
	return Default_Task_ExpectState
}

type VirtualIP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName *string `protobuf:"bytes,1,req,name=service_name,json=serviceName" json:"service_name,omitempty"`
	Ip          *string `protobuf:"bytes,2,req,name=ip" json:"ip,omitempty"`
}

func (x *VirtualIP) Reset() {
	*x = VirtualIP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualIP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualIP) ProtoMessage() {}

func (x *VirtualIP) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualIP.ProtoReflect.Descriptor instead.
func (*VirtualIP) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{2}
}

func (x *VirtualIP) GetServiceName() string {
	if x != nil && x.ServiceName != nil {
		return *x.ServiceName
	}
	return ""
}

func (x *VirtualIP) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

type VirtualIPs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vips []*VirtualIP `protobuf:"bytes,1,rep,name=vips" json:"vips,omitempty"`
}

func (x *VirtualIPs) Reset() {
	*x = VirtualIPs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualIPs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualIPs) ProtoMessage() {}

func (x *VirtualIPs) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualIPs.ProtoReflect.Descriptor instead.
func (*VirtualIPs) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{3}
}

func (x *VirtualIPs) GetVips() []*VirtualIP {
	if x != nil {
		return x.Vips
	}
	return nil
}

type Tasks struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*Task `protobuf:"bytes,1,rep,name=tasks" json:"tasks,omitempty"`
}

func (x *Tasks) Reset() {
	*x = Tasks{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tasks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tasks) ProtoMessage() {}

func (x *Tasks) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tasks.ProtoReflect.Descriptor instead.
func (*Tasks) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{4}
}

func (x *Tasks) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type TaskId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []byte `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
}

func (x *TaskId) Reset() {
	*x = TaskId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskId) ProtoMessage() {}

func (x *TaskId) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskId.ProtoReflect.Descriptor instead.
func (*TaskId) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{5}
}

func (x *TaskId) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

type ListTaskByDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list tasks created with in the date range specified by the time since epoch.
	StartMsSinceEpoch *uint64 `protobuf:"varint,1,opt,name=start_ms_since_epoch,json=startMsSinceEpoch" json:"start_ms_since_epoch,omitempty"`
	EndMsSinceEpoch   *uint64 `protobuf:"varint,2,opt,name=end_ms_since_epoch,json=endMsSinceEpoch" json:"end_ms_since_epoch,omitempty"`
}

func (x *ListTaskByDateRequest) Reset() {
	*x = ListTaskByDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTaskByDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTaskByDateRequest) ProtoMessage() {}

func (x *ListTaskByDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTaskByDateRequest.ProtoReflect.Descriptor instead.
func (*ListTaskByDateRequest) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{6}
}

func (x *ListTaskByDateRequest) GetStartMsSinceEpoch() uint64 {
	if x != nil && x.StartMsSinceEpoch != nil {
		return *x.StartMsSinceEpoch
	}
	return 0
}

func (x *ListTaskByDateRequest) GetEndMsSinceEpoch() uint64 {
	if x != nil && x.EndMsSinceEpoch != nil {
		return *x.EndMsSinceEpoch
	}
	return 0
}

type ListTaskByStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Finished *bool `protobuf:"varint,1,opt,name=finished,def=0" json:"finished,omitempty"`
}

// Default values for ListTaskByStatusRequest fields.
const (
	Default_ListTaskByStatusRequest_Finished = bool(false)
)

func (x *ListTaskByStatusRequest) Reset() {
	*x = ListTaskByStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTaskByStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTaskByStatusRequest) ProtoMessage() {}

func (x *ListTaskByStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTaskByStatusRequest.ProtoReflect.Descriptor instead.
func (*ListTaskByStatusRequest) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{7}
}

func (x *ListTaskByStatusRequest) GetFinished() bool {
	if x != nil && x.Finished != nil {
		return *x.Finished
	}
	return Default_ListTaskByStatusRequest_Finished
}

type GatewayEndpoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip       *string `protobuf:"bytes,1,req,name=ip" json:"ip,omitempty"`
	RpcPort  *int32  `protobuf:"varint,2,req,name=rpc_port,json=rpcPort" json:"rpc_port,omitempty"`
	DataPort *int32  `protobuf:"varint,3,req,name=data_port,json=dataPort" json:"data_port,omitempty"`
}

func (x *GatewayEndpoint) Reset() {
	*x = GatewayEndpoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayEndpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayEndpoint) ProtoMessage() {}

func (x *GatewayEndpoint) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayEndpoint.ProtoReflect.Descriptor instead.
func (*GatewayEndpoint) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{8}
}

func (x *GatewayEndpoint) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *GatewayEndpoint) GetRpcPort() int32 {
	if x != nil && x.RpcPort != nil {
		return *x.RpcPort
	}
	return 0
}

func (x *GatewayEndpoint) GetDataPort() int32 {
	if x != nil && x.DataPort != nil {
		return *x.DataPort
	}
	return 0
}

type RsyncTask struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Hosts          *string `protobuf:"bytes,1,opt,name=hosts" json:"hosts,omitempty"`
	RemoteHosts    *string `protobuf:"bytes,2,opt,name=remote_hosts,json=remoteHosts" json:"remote_hosts,omitempty"`
	UseCompression *bool   `protobuf:"varint,3,opt,name=use_compression,json=useCompression" json:"use_compression,omitempty"`
}

func (x *RsyncTask) Reset() {
	*x = RsyncTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RsyncTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RsyncTask) ProtoMessage() {}

func (x *RsyncTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RsyncTask.ProtoReflect.Descriptor instead.
func (*RsyncTask) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{9}
}

func (x *RsyncTask) GetHosts() string {
	if x != nil && x.Hosts != nil {
		return *x.Hosts
	}
	return ""
}

func (x *RsyncTask) GetRemoteHosts() string {
	if x != nil && x.RemoteHosts != nil {
		return *x.RemoteHosts
	}
	return ""
}

func (x *RsyncTask) GetUseCompression() bool {
	if x != nil && x.UseCompression != nil {
		return *x.UseCompression
	}
	return false
}

// copy volume can be use for deep copy
type CopyVolumeTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field for task continue
	SrcVolumeId    []byte               `protobuf:"bytes,1,req,name=src_volume_id,json=srcVolumeId" json:"src_volume_id,omitempty"`
	DstVolumeId    []byte               `protobuf:"bytes,2,req,name=dst_volume_id,json=dstVolumeId" json:"dst_volume_id,omitempty"`
	SrcHosts       []byte               `protobuf:"bytes,3,req,name=src_hosts,json=srcHosts" json:"src_hosts,omitempty"`
	DstHosts       []byte               `protobuf:"bytes,4,req,name=dst_hosts,json=dstHosts" json:"dst_hosts,omitempty"`
	CurExtent      *uint64              `protobuf:"varint,5,opt,name=cur_extent,json=curExtent" json:"cur_extent,omitempty"`
	CurState       *CopyVolumeTaskState `protobuf:"varint,6,opt,name=cur_state,json=curState,enum=zbs.task.CopyVolumeTaskState,def=0" json:"cur_state,omitempty"`
	SkipZero       *bool                `protobuf:"varint,7,opt,name=skip_zero,json=skipZero,def=1" json:"skip_zero,omitempty"`
	UseCompression *bool                `protobuf:"varint,8,opt,name=use_compression,json=useCompression,def=0" json:"use_compression,omitempty"`
	PreferredCid   *uint32              `protobuf:"varint,9,opt,name=preferred_cid,json=preferredCid,def=0" json:"preferred_cid,omitempty"`
	// The following are auto generated/updated for metrics
	// src volume size
	VolumeSizeBytes *uint64 `protobuf:"varint,10,opt,name=volume_size_bytes,json=volumeSizeBytes" json:"volume_size_bytes,omitempty"`
	// currently synced volume bytes
	SyncedVolumeBytes *uint64 `protobuf:"varint,11,opt,name=synced_volume_bytes,json=syncedVolumeBytes" json:"synced_volume_bytes,omitempty"`
	// actual transfer size, actual_transfer_size / synced_volume_bytes is the
	// dedup ratio
	TransferredVolumeBytes *uint64 `protobuf:"varint,12,opt,name=transferred_volume_bytes,json=transferredVolumeBytes" json:"transferred_volume_bytes,omitempty"`
	// current transferred speed over the network (Bps)
	NetSpeed_Bps *uint64 `protobuf:"varint,13,opt,name=net_speed_Bps,json=netSpeedBps" json:"net_speed_Bps,omitempty"`
	// current sync speed (Bps)
	SyncSpeed_Bps *uint64 `protobuf:"varint,14,opt,name=sync_speed_Bps,json=syncSpeedBps" json:"sync_speed_Bps,omitempty"`
	BaseVolumeId  []byte  `protobuf:"bytes,15,opt,name=base_volume_id,json=baseVolumeId" json:"base_volume_id,omitempty"`
	// The following are newly-added fields for task
	IoDepth *uint32  `protobuf:"varint,20,opt,name=io_depth,json=ioDepth,def=32" json:"io_depth,omitempty"`
	BpsMax  *uint64  `protobuf:"varint,21,opt,name=bps_max,json=bpsMax,def=314572800" json:"bps_max,omitempty"` // 300 MiB/s
	Runtime *RunTime `protobuf:"bytes,22,opt,name=runtime" json:"runtime,omitempty"`
}

// Default values for CopyVolumeTask fields.
const (
	Default_CopyVolumeTask_CurState       = CopyVolumeTaskState_CV_INIT
	Default_CopyVolumeTask_SkipZero       = bool(true)
	Default_CopyVolumeTask_UseCompression = bool(false)
	Default_CopyVolumeTask_PreferredCid   = uint32(0)
	Default_CopyVolumeTask_IoDepth        = uint32(32)
	Default_CopyVolumeTask_BpsMax         = uint64(314572800)
)

func (x *CopyVolumeTask) Reset() {
	*x = CopyVolumeTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyVolumeTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyVolumeTask) ProtoMessage() {}

func (x *CopyVolumeTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyVolumeTask.ProtoReflect.Descriptor instead.
func (*CopyVolumeTask) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{10}
}

func (x *CopyVolumeTask) GetSrcVolumeId() []byte {
	if x != nil {
		return x.SrcVolumeId
	}
	return nil
}

func (x *CopyVolumeTask) GetDstVolumeId() []byte {
	if x != nil {
		return x.DstVolumeId
	}
	return nil
}

func (x *CopyVolumeTask) GetSrcHosts() []byte {
	if x != nil {
		return x.SrcHosts
	}
	return nil
}

func (x *CopyVolumeTask) GetDstHosts() []byte {
	if x != nil {
		return x.DstHosts
	}
	return nil
}

func (x *CopyVolumeTask) GetCurExtent() uint64 {
	if x != nil && x.CurExtent != nil {
		return *x.CurExtent
	}
	return 0
}

func (x *CopyVolumeTask) GetCurState() CopyVolumeTaskState {
	if x != nil && x.CurState != nil {
		return *x.CurState
	}
	return Default_CopyVolumeTask_CurState
}

func (x *CopyVolumeTask) GetSkipZero() bool {
	if x != nil && x.SkipZero != nil {
		return *x.SkipZero
	}
	return Default_CopyVolumeTask_SkipZero
}

func (x *CopyVolumeTask) GetUseCompression() bool {
	if x != nil && x.UseCompression != nil {
		return *x.UseCompression
	}
	return Default_CopyVolumeTask_UseCompression
}

func (x *CopyVolumeTask) GetPreferredCid() uint32 {
	if x != nil && x.PreferredCid != nil {
		return *x.PreferredCid
	}
	return Default_CopyVolumeTask_PreferredCid
}

func (x *CopyVolumeTask) GetVolumeSizeBytes() uint64 {
	if x != nil && x.VolumeSizeBytes != nil {
		return *x.VolumeSizeBytes
	}
	return 0
}

func (x *CopyVolumeTask) GetSyncedVolumeBytes() uint64 {
	if x != nil && x.SyncedVolumeBytes != nil {
		return *x.SyncedVolumeBytes
	}
	return 0
}

func (x *CopyVolumeTask) GetTransferredVolumeBytes() uint64 {
	if x != nil && x.TransferredVolumeBytes != nil {
		return *x.TransferredVolumeBytes
	}
	return 0
}

func (x *CopyVolumeTask) GetNetSpeed_Bps() uint64 {
	if x != nil && x.NetSpeed_Bps != nil {
		return *x.NetSpeed_Bps
	}
	return 0
}

func (x *CopyVolumeTask) GetSyncSpeed_Bps() uint64 {
	if x != nil && x.SyncSpeed_Bps != nil {
		return *x.SyncSpeed_Bps
	}
	return 0
}

func (x *CopyVolumeTask) GetBaseVolumeId() []byte {
	if x != nil {
		return x.BaseVolumeId
	}
	return nil
}

func (x *CopyVolumeTask) GetIoDepth() uint32 {
	if x != nil && x.IoDepth != nil {
		return *x.IoDepth
	}
	return Default_CopyVolumeTask_IoDepth
}

func (x *CopyVolumeTask) GetBpsMax() uint64 {
	if x != nil && x.BpsMax != nil {
		return *x.BpsMax
	}
	return Default_CopyVolumeTask_BpsMax
}

func (x *CopyVolumeTask) GetRuntime() *RunTime {
	if x != nil {
		return x.Runtime
	}
	return nil
}

type SyncVolumeTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId       []byte `protobuf:"bytes,1,opt,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
	BaseVolumeId   []byte `protobuf:"bytes,2,opt,name=base_volume_id,json=baseVolumeId" json:"base_volume_id,omitempty"`
	RemoteVolumeId []byte `protobuf:"bytes,3,opt,name=remote_volume_id,json=remoteVolumeId" json:"remote_volume_id,omitempty"`
	// preferred chunk id
	PreferredCid *uint32 `protobuf:"varint,9,opt,name=preferred_cid,json=preferredCid,def=0" json:"preferred_cid,omitempty"`
	// The following are auto generated/updated for metrics
	// src volume size
	VolumeSizeBytes *uint64 `protobuf:"varint,4,opt,name=volume_size_bytes,json=volumeSizeBytes" json:"volume_size_bytes,omitempty"`
	// currently synced volume bytes
	SyncedVolumeBytes *uint64 `protobuf:"varint,5,opt,name=synced_volume_bytes,json=syncedVolumeBytes" json:"synced_volume_bytes,omitempty"`
	// actual transfer size, actual_transfer_size / synced_volume_bytes is the
	// dedup ratio
	TransferredVolumeBytes *uint64 `protobuf:"varint,6,opt,name=transferred_volume_bytes,json=transferredVolumeBytes" json:"transferred_volume_bytes,omitempty"`
	// current transferred speed over the network (Bps)
	NetSpeed_Bps *uint64 `protobuf:"varint,7,opt,name=net_speed_Bps,json=netSpeedBps" json:"net_speed_Bps,omitempty"`
	// current sync speed (Bps)
	SyncSpeed_Bps *uint64 `protobuf:"varint,8,opt,name=sync_speed_Bps,json=syncSpeedBps" json:"sync_speed_Bps,omitempty"`
}

// Default values for SyncVolumeTask fields.
const (
	Default_SyncVolumeTask_PreferredCid = uint32(0)
)

func (x *SyncVolumeTask) Reset() {
	*x = SyncVolumeTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncVolumeTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncVolumeTask) ProtoMessage() {}

func (x *SyncVolumeTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncVolumeTask.ProtoReflect.Descriptor instead.
func (*SyncVolumeTask) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{11}
}

func (x *SyncVolumeTask) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

func (x *SyncVolumeTask) GetBaseVolumeId() []byte {
	if x != nil {
		return x.BaseVolumeId
	}
	return nil
}

func (x *SyncVolumeTask) GetRemoteVolumeId() []byte {
	if x != nil {
		return x.RemoteVolumeId
	}
	return nil
}

func (x *SyncVolumeTask) GetPreferredCid() uint32 {
	if x != nil && x.PreferredCid != nil {
		return *x.PreferredCid
	}
	return Default_SyncVolumeTask_PreferredCid
}

func (x *SyncVolumeTask) GetVolumeSizeBytes() uint64 {
	if x != nil && x.VolumeSizeBytes != nil {
		return *x.VolumeSizeBytes
	}
	return 0
}

func (x *SyncVolumeTask) GetSyncedVolumeBytes() uint64 {
	if x != nil && x.SyncedVolumeBytes != nil {
		return *x.SyncedVolumeBytes
	}
	return 0
}

func (x *SyncVolumeTask) GetTransferredVolumeBytes() uint64 {
	if x != nil && x.TransferredVolumeBytes != nil {
		return *x.TransferredVolumeBytes
	}
	return 0
}

func (x *SyncVolumeTask) GetNetSpeed_Bps() uint64 {
	if x != nil && x.NetSpeed_Bps != nil {
		return *x.NetSpeed_Bps
	}
	return 0
}

func (x *SyncVolumeTask) GetSyncSpeed_Bps() uint64 {
	if x != nil && x.SyncSpeed_Bps != nil {
		return *x.SyncSpeed_Bps
	}
	return 0
}

type RunTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartHour *uint32 `protobuf:"varint,1,req,name=start_hour,json=startHour,def=0" json:"start_hour,omitempty"`
	StartMin  *uint32 `protobuf:"varint,2,req,name=start_min,json=startMin,def=0" json:"start_min,omitempty"`
	EndHour   *uint32 `protobuf:"varint,3,req,name=end_hour,json=endHour,def=23" json:"end_hour,omitempty"`
	EndMin    *uint32 `protobuf:"varint,4,req,name=end_min,json=endMin,def=59" json:"end_min,omitempty"`
}

// Default values for RunTime fields.
const (
	Default_RunTime_StartHour = uint32(0)
	Default_RunTime_StartMin  = uint32(0)
	Default_RunTime_EndHour   = uint32(23)
	Default_RunTime_EndMin    = uint32(59)
)

func (x *RunTime) Reset() {
	*x = RunTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunTime) ProtoMessage() {}

func (x *RunTime) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunTime.ProtoReflect.Descriptor instead.
func (*RunTime) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{12}
}

func (x *RunTime) GetStartHour() uint32 {
	if x != nil && x.StartHour != nil {
		return *x.StartHour
	}
	return Default_RunTime_StartHour
}

func (x *RunTime) GetStartMin() uint32 {
	if x != nil && x.StartMin != nil {
		return *x.StartMin
	}
	return Default_RunTime_StartMin
}

func (x *RunTime) GetEndHour() uint32 {
	if x != nil && x.EndHour != nil {
		return *x.EndHour
	}
	return Default_RunTime_EndHour
}

func (x *RunTime) GetEndMin() uint32 {
	if x != nil && x.EndMin != nil {
		return *x.EndMin
	}
	return Default_RunTime_EndMin
}

type SetRunTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId  *TaskId  `protobuf:"bytes,1,req,name=task_id,json=taskId" json:"task_id,omitempty"`
	Runtime *RunTime `protobuf:"bytes,2,req,name=runtime" json:"runtime,omitempty"`
}

func (x *SetRunTimeRequest) Reset() {
	*x = SetRunTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRunTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRunTimeRequest) ProtoMessage() {}

func (x *SetRunTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRunTimeRequest.ProtoReflect.Descriptor instead.
func (*SetRunTimeRequest) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{13}
}

func (x *SetRunTimeRequest) GetTaskId() *TaskId {
	if x != nil {
		return x.TaskId
	}
	return nil
}

func (x *SetRunTimeRequest) GetRuntime() *RunTime {
	if x != nil {
		return x.Runtime
	}
	return nil
}

type SetBpsMaxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId *TaskId `protobuf:"bytes,1,req,name=task_id,json=taskId" json:"task_id,omitempty"`
	BpsMax *uint64 `protobuf:"varint,2,req,name=bps_max,json=bpsMax" json:"bps_max,omitempty"`
}

func (x *SetBpsMaxRequest) Reset() {
	*x = SetBpsMaxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBpsMaxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBpsMaxRequest) ProtoMessage() {}

func (x *SetBpsMaxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBpsMaxRequest.ProtoReflect.Descriptor instead.
func (*SetBpsMaxRequest) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{14}
}

func (x *SetBpsMaxRequest) GetTaskId() *TaskId {
	if x != nil {
		return x.TaskId
	}
	return nil
}

func (x *SetBpsMaxRequest) GetBpsMax() uint64 {
	if x != nil && x.BpsMax != nil {
		return *x.BpsMax
	}
	return 0
}

var file_task_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*Task)(nil),
		ExtensionType: (*RsyncTask)(nil),
		Field:         20004,
		Name:          "zbs.task.RsyncTask.task",
		Tag:           "bytes,20004,opt,name=task",
		Filename:      "task.proto",
	},
	{
		ExtendedType:  (*Task)(nil),
		ExtensionType: (*CopyVolumeTask)(nil),
		Field:         20005,
		Name:          "zbs.task.CopyVolumeTask.task",
		Tag:           "bytes,20005,opt,name=task",
		Filename:      "task.proto",
	},
	{
		ExtendedType:  (*RsyncTask)(nil),
		ExtensionType: (*SyncVolumeTask)(nil),
		Field:         60001,
		Name:          "zbs.task.SyncVolumeTask.task",
		Tag:           "bytes,60001,opt,name=task",
		Filename:      "task.proto",
	},
}

// Extension fields to Task.
var (
	// optional zbs.task.RsyncTask task = 20004;
	E_RsyncTask_Task = &file_task_proto_extTypes[0]
	// optional zbs.task.CopyVolumeTask task = 20005;
	E_CopyVolumeTask_Task = &file_task_proto_extTypes[1]
)

// Extension fields to RsyncTask.
var (
	// optional zbs.task.SyncVolumeTask task = 60001;
	E_SyncVolumeTask_Task = &file_task_proto_extTypes[2]
)

var File_task_proto protoreflect.FileDescriptor

var file_task_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x7a, 0x62,
	0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x54, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x00, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x92, 0x05, 0x0a, 0x04, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x15, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05,
	0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x3a, 0x04, 0x4e,
	0x4f, 0x4e, 0x45, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x65, 0x78,
	0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x52, 0x70, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0a, 0x65, 0x78, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x15, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x13, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x52, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1b, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x00, 0x42, 0x05, 0xca,
	0x49, 0x02, 0x08, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2d,
	0x0a, 0x0b, 0x72, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x0a, 0x72, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4d, 0x73, 0x12, 0x30, 0x0a,
	0x12, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x02, 0x31, 0x30, 0x52, 0x10, 0x6d,
	0x61, 0x78, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12,
	0x28, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0e, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x04, 0x3a, 0x01, 0x30, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x4d, 0x73, 0x12, 0x20, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x3a,
	0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x2a, 0x0a, 0x08, 0xa0, 0x9c, 0x01, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0x3e,
	0x0a, 0x09, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x35,
	0x0a, 0x0a, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x73, 0x12, 0x27, 0x0a, 0x04,
	0x76, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x52,
	0x04, 0x76, 0x69, 0x70, 0x73, 0x22, 0x2d, 0x0a, 0x05, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x24,
	0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74,
	0x61, 0x73, 0x6b, 0x73, 0x22, 0x1f, 0x0a, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08,
	0x01, 0x52, 0x02, 0x69, 0x64, 0x22, 0x75, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f,
	0x0a, 0x14, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6d, 0x73, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65,
	0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x4d, 0x73, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12,
	0x2b, 0x0a, 0x12, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x73, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f,
	0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x65, 0x6e, 0x64,
	0x4d, 0x73, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x22, 0x3c, 0x0a, 0x17,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65,
	0x52, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22, 0x59, 0x0a, 0x0f, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x19, 0x0a,
	0x08, 0x72, 0x70, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52,
	0x07, 0x72, 0x70, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x50, 0x6f, 0x72, 0x74, 0x22, 0xb4, 0x01, 0x0a, 0x09, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x75, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2a, 0x0a, 0x08, 0xe0, 0xd4, 0x03, 0x10, 0x80, 0x80, 0x80, 0x80,
	0x02, 0x32, 0x39, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x18, 0xa4, 0x9c, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x52, 0x73, 0x79,
	0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0xca, 0x06, 0x0a,
	0x0e, 0x43, 0x6f, 0x70, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x29, 0x0a, 0x0d, 0x73, 0x72, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x73,
	0x72, 0x63, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0d, 0x64, 0x73,
	0x74, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x64, 0x73, 0x74, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x09, 0x73, 0x72, 0x63, 0x5f, 0x68, 0x6f, 0x73,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52,
	0x08, 0x73, 0x72, 0x63, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x09, 0x64, 0x73, 0x74,
	0x5f, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49,
	0x02, 0x08, 0x01, 0x52, 0x08, 0x64, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x75, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x63, 0x75, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x09,
	0x63, 0x75, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x3a, 0x07,
	0x43, 0x56, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x52, 0x08, 0x63, 0x75, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x7a, 0x65, 0x72, 0x6f, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x08, 0x73, 0x6b, 0x69, 0x70,
	0x5a, 0x65, 0x72, 0x6f, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x5f, 0x63, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0c,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x43, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x79, 0x6e, 0x63,
	0x65, 0x64, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f,
	0x42, 0x70, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x42, 0x70, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c,
	0x73, 0x79, 0x6e, 0x63, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x2b, 0x0a, 0x0e,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x62, 0x61, 0x73,
	0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x08, 0x69, 0x6f, 0x5f,
	0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x02, 0x33, 0x32, 0x52,
	0x07, 0x69, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x07, 0x62, 0x70, 0x73, 0x5f,
	0x6d, 0x61, 0x78, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x09, 0x33, 0x31, 0x34, 0x35, 0x37,
	0x32, 0x38, 0x30, 0x30, 0x52, 0x06, 0x62, 0x70, 0x73, 0x4d, 0x61, 0x78, 0x12, 0x2b, 0x0a, 0x07,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x52, 0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x32, 0x3e, 0x0a, 0x04, 0x74, 0x61, 0x73,
	0x6b, 0x12, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x18, 0xa5, 0x9c, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0xdf, 0x03, 0x0a, 0x0e, 0x53, 0x79,
	0x6e, 0x63, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x22, 0x0a, 0x09,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42,
	0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64,
	0x12, 0x2b, 0x0a, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52,
	0x0c, 0x62, 0x61, 0x73, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a,
	0x10, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x0e,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x43, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x11, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0d,
	0x6e, 0x65, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x42, 0x70, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73,
	0x12, 0x24, 0x0a, 0x0e, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x42,
	0x70, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x73, 0x79, 0x6e, 0x63, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x32, 0x43, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x13,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x54,
	0x61, 0x73, 0x6b, 0x18, 0xe1, 0xd4, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0x87, 0x01, 0x0a, 0x07,
	0x52, 0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x1e, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52,
	0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x69, 0x6e, 0x12, 0x1d, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0d, 0x3a, 0x02, 0x32, 0x33, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x1b, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x5f,
	0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0d, 0x3a, 0x02, 0x35, 0x39, 0x52, 0x06, 0x65,
	0x6e, 0x64, 0x4d, 0x69, 0x6e, 0x22, 0x6b, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x52, 0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x22, 0x56, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x42, 0x70, 0x73, 0x4d, 0x61, 0x78, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x70, 0x73, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x04, 0x52, 0x06, 0x62, 0x70, 0x73, 0x4d, 0x61, 0x78, 0x2a, 0x66, 0x0a, 0x09, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0f,
	0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43,
	0x45, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x41, 0x55, 0x53, 0x45, 0x44,
	0x10, 0x06, 0x2a, 0x60, 0x0a, 0x13, 0x43, 0x6f, 0x70, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x56, 0x5f,
	0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x56, 0x5f, 0x43, 0x4f, 0x50,
	0x59, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x56,
	0x5f, 0x43, 0x4f, 0x50, 0x59, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x4f, 0x4e,
	0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x56, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c,
	0x45, 0x44, 0x10, 0x03, 0x32, 0xfb, 0x03, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75,
	0x6e, 0x6e, 0x65, 0x72, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a,
	0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12,
	0x42, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x79, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x53,
	0x68, 0x6f, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x1a, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a, 0x0a, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x1a, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2d, 0x0a, 0x09, 0x50, 0x61, 0x75,
	0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x1a, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a, 0x0a, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x1a, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x39, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x52,
	0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x37, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x42, 0x70, 0x73, 0x4d, 0x61, 0x78,
	0x12, 0x1a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x42,
	0x70, 0x73, 0x4d, 0x61, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x1a, 0x04, 0xc0, 0x3e,
	0xf1, 0x2e, 0x32, 0x95, 0x01, 0x0a, 0x0a, 0x56, 0x49, 0x50, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x28, 0x0a, 0x06, 0x53, 0x65, 0x74, 0x56, 0x49, 0x50, 0x12, 0x13, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50,
	0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x09, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x49, 0x50, 0x12, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x1a, 0x09, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x53, 0x68, 0x6f, 0x77,
	0x56, 0x49, 0x50, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x14,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x49, 0x50, 0x73, 0x1a, 0x04, 0xc0, 0x3e, 0xf3, 0x2e, 0x32, 0x42, 0x0a, 0x0c, 0x52, 0x73,
	0x79, 0x6e, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x0a, 0x53, 0x79,
	0x6e, 0x63, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x1a, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x1a, 0x04, 0xc0, 0x3e, 0xf6, 0x2e, 0x32, 0x47,
	0x0a, 0x11, 0x43, 0x6f, 0x70, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x0a, 0x43, 0x6f, 0x70, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x12, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x1a, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x1a, 0x04, 0xc0, 0x3e, 0xf7, 0x2e, 0x42, 0x3a, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68, 0x2f, 0x7a, 0x62, 0x73,
	0x2d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x7a, 0x62, 0x73, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x80, 0x01, 0x01,
	0x90, 0x01, 0x01,
}

var (
	file_task_proto_rawDescOnce sync.Once
	file_task_proto_rawDescData = file_task_proto_rawDesc
)

func file_task_proto_rawDescGZIP() []byte {
	file_task_proto_rawDescOnce.Do(func() {
		file_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_task_proto_rawDescData)
	})
	return file_task_proto_rawDescData
}

var file_task_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_task_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_task_proto_goTypes = []interface{}{
	(TaskState)(0),                  // 0: zbs.task.TaskState
	(CopyVolumeTaskState)(0),        // 1: zbs.task.CopyVolumeTaskState
	(*TaskProgress)(nil),            // 2: zbs.task.TaskProgress
	(*Task)(nil),                    // 3: zbs.task.Task
	(*VirtualIP)(nil),               // 4: zbs.task.VirtualIP
	(*VirtualIPs)(nil),              // 5: zbs.task.VirtualIPs
	(*Tasks)(nil),                   // 6: zbs.task.Tasks
	(*TaskId)(nil),                  // 7: zbs.task.TaskId
	(*ListTaskByDateRequest)(nil),   // 8: zbs.task.ListTaskByDateRequest
	(*ListTaskByStatusRequest)(nil), // 9: zbs.task.ListTaskByStatusRequest
	(*GatewayEndpoint)(nil),         // 10: zbs.task.GatewayEndpoint
	(*RsyncTask)(nil),               // 11: zbs.task.RsyncTask
	(*CopyVolumeTask)(nil),          // 12: zbs.task.CopyVolumeTask
	(*SyncVolumeTask)(nil),          // 13: zbs.task.SyncVolumeTask
	(*RunTime)(nil),                 // 14: zbs.task.RunTime
	(*SetRunTimeRequest)(nil),       // 15: zbs.task.SetRunTimeRequest
	(*SetBpsMaxRequest)(nil),        // 16: zbs.task.SetBpsMaxRequest
	(*zbs.RpcStatus)(nil),           // 17: zbs.RpcStatus
	(*zbs.Address)(nil),             // 18: zbs.Address
	(*zbs.Void)(nil),                // 19: zbs.Void
	(*zbs.Addresses)(nil),           // 20: zbs.Addresses
}
var file_task_proto_depIdxs = []int32{
	0,  // 0: zbs.task.Task.state:type_name -> zbs.task.TaskState
	17, // 1: zbs.task.Task.exit_status:type_name -> zbs.RpcStatus
	18, // 2: zbs.task.Task.specified_runner_addr:type_name -> zbs.Address
	2,  // 3: zbs.task.Task.progress:type_name -> zbs.task.TaskProgress
	18, // 4: zbs.task.Task.runner_addr:type_name -> zbs.Address
	0,  // 5: zbs.task.Task.expect_state:type_name -> zbs.task.TaskState
	4,  // 6: zbs.task.VirtualIPs.vips:type_name -> zbs.task.VirtualIP
	3,  // 7: zbs.task.Tasks.tasks:type_name -> zbs.task.Task
	1,  // 8: zbs.task.CopyVolumeTask.cur_state:type_name -> zbs.task.CopyVolumeTaskState
	14, // 9: zbs.task.CopyVolumeTask.runtime:type_name -> zbs.task.RunTime
	7,  // 10: zbs.task.SetRunTimeRequest.task_id:type_name -> zbs.task.TaskId
	14, // 11: zbs.task.SetRunTimeRequest.runtime:type_name -> zbs.task.RunTime
	7,  // 12: zbs.task.SetBpsMaxRequest.task_id:type_name -> zbs.task.TaskId
	3,  // 13: zbs.task.RsyncTask.task:extendee -> zbs.task.Task
	3,  // 14: zbs.task.CopyVolumeTask.task:extendee -> zbs.task.Task
	11, // 15: zbs.task.SyncVolumeTask.task:extendee -> zbs.task.RsyncTask
	11, // 16: zbs.task.RsyncTask.task:type_name -> zbs.task.RsyncTask
	12, // 17: zbs.task.CopyVolumeTask.task:type_name -> zbs.task.CopyVolumeTask
	13, // 18: zbs.task.SyncVolumeTask.task:type_name -> zbs.task.SyncVolumeTask
	19, // 19: zbs.task.StatusService.ListRunner:input_type -> zbs.Void
	8,  // 20: zbs.task.StatusService.ListTaskByDate:input_type -> zbs.task.ListTaskByDateRequest
	9,  // 21: zbs.task.StatusService.ListTaskByStatus:input_type -> zbs.task.ListTaskByStatusRequest
	7,  // 22: zbs.task.StatusService.ShowTask:input_type -> zbs.task.TaskId
	7,  // 23: zbs.task.StatusService.CancelTask:input_type -> zbs.task.TaskId
	7,  // 24: zbs.task.StatusService.PauseTask:input_type -> zbs.task.TaskId
	7,  // 25: zbs.task.StatusService.ResumeTask:input_type -> zbs.task.TaskId
	15, // 26: zbs.task.StatusService.SetRunTime:input_type -> zbs.task.SetRunTimeRequest
	16, // 27: zbs.task.StatusService.SetBpsMax:input_type -> zbs.task.SetBpsMaxRequest
	4,  // 28: zbs.task.VIPService.SetVIP:input_type -> zbs.task.VirtualIP
	4,  // 29: zbs.task.VIPService.DeleteVIP:input_type -> zbs.task.VirtualIP
	19, // 30: zbs.task.VIPService.ShowVIP:input_type -> zbs.Void
	3,  // 31: zbs.task.RsyncService.SyncVolume:input_type -> zbs.task.Task
	3,  // 32: zbs.task.CopyVolumeService.CopyVolume:input_type -> zbs.task.Task
	20, // 33: zbs.task.StatusService.ListRunner:output_type -> zbs.Addresses
	6,  // 34: zbs.task.StatusService.ListTaskByDate:output_type -> zbs.task.Tasks
	6,  // 35: zbs.task.StatusService.ListTaskByStatus:output_type -> zbs.task.Tasks
	3,  // 36: zbs.task.StatusService.ShowTask:output_type -> zbs.task.Task
	3,  // 37: zbs.task.StatusService.CancelTask:output_type -> zbs.task.Task
	3,  // 38: zbs.task.StatusService.PauseTask:output_type -> zbs.task.Task
	3,  // 39: zbs.task.StatusService.ResumeTask:output_type -> zbs.task.Task
	3,  // 40: zbs.task.StatusService.SetRunTime:output_type -> zbs.task.Task
	3,  // 41: zbs.task.StatusService.SetBpsMax:output_type -> zbs.task.Task
	19, // 42: zbs.task.VIPService.SetVIP:output_type -> zbs.Void
	19, // 43: zbs.task.VIPService.DeleteVIP:output_type -> zbs.Void
	5,  // 44: zbs.task.VIPService.ShowVIP:output_type -> zbs.task.VirtualIPs
	3,  // 45: zbs.task.RsyncService.SyncVolume:output_type -> zbs.task.Task
	3,  // 46: zbs.task.CopyVolumeService.CopyVolume:output_type -> zbs.task.Task
	33, // [33:47] is the sub-list for method output_type
	19, // [19:33] is the sub-list for method input_type
	16, // [16:19] is the sub-list for extension type_name
	13, // [13:16] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_task_proto_init() }
func file_task_proto_init() {
	if File_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualIP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualIPs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tasks); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTaskByDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTaskByStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayEndpoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RsyncTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyVolumeTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncVolumeTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRunTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBpsMaxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_task_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   15,
			NumExtensions: 3,
			NumServices:   4,
		},
		GoTypes:           file_task_proto_goTypes,
		DependencyIndexes: file_task_proto_depIdxs,
		EnumInfos:         file_task_proto_enumTypes,
		MessageInfos:      file_task_proto_msgTypes,
		ExtensionInfos:    file_task_proto_extTypes,
	}.Build()
	File_task_proto = out.File
	file_task_proto_rawDesc = nil
	file_task_proto_goTypes = nil
	file_task_proto_depIdxs = nil
}
