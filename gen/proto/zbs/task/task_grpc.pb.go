// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.12
// source: task.proto

package task

import (
	context "context"
	zbs "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	StatusService_ListRunner_FullMethodName       = "/zbs.task.StatusService/ListRunner"
	StatusService_ListTaskByDate_FullMethodName   = "/zbs.task.StatusService/ListTaskByDate"
	StatusService_ListTaskByStatus_FullMethodName = "/zbs.task.StatusService/ListTaskByStatus"
	StatusService_ShowTask_FullMethodName         = "/zbs.task.StatusService/ShowTask"
	StatusService_CancelTask_FullMethodName       = "/zbs.task.StatusService/CancelTask"
	StatusService_PauseTask_FullMethodName        = "/zbs.task.StatusService/PauseTask"
	StatusService_ResumeTask_FullMethodName       = "/zbs.task.StatusService/ResumeTask"
	StatusService_SetRunTime_FullMethodName       = "/zbs.task.StatusService/SetRunTime"
	StatusService_SetBpsMax_FullMethodName        = "/zbs.task.StatusService/SetBpsMax"
)

// StatusServiceClient is the client API for StatusService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StatusServiceClient interface {
	ListRunner(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Addresses, error)
	ListTaskByDate(ctx context.Context, in *ListTaskByDateRequest, opts ...grpc.CallOption) (*Tasks, error)
	ListTaskByStatus(ctx context.Context, in *ListTaskByStatusRequest, opts ...grpc.CallOption) (*Tasks, error)
	ShowTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error)
	CancelTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error)
	PauseTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error)
	ResumeTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error)
	SetRunTime(ctx context.Context, in *SetRunTimeRequest, opts ...grpc.CallOption) (*Task, error)
	SetBpsMax(ctx context.Context, in *SetBpsMaxRequest, opts ...grpc.CallOption) (*Task, error)
}

type statusServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStatusServiceClient(cc grpc.ClientConnInterface) StatusServiceClient {
	return &statusServiceClient{cc}
}

func (c *statusServiceClient) ListRunner(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Addresses, error) {
	out := new(zbs.Addresses)
	err := c.cc.Invoke(ctx, StatusService_ListRunner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) ListTaskByDate(ctx context.Context, in *ListTaskByDateRequest, opts ...grpc.CallOption) (*Tasks, error) {
	out := new(Tasks)
	err := c.cc.Invoke(ctx, StatusService_ListTaskByDate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) ListTaskByStatus(ctx context.Context, in *ListTaskByStatusRequest, opts ...grpc.CallOption) (*Tasks, error) {
	out := new(Tasks)
	err := c.cc.Invoke(ctx, StatusService_ListTaskByStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) ShowTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, StatusService_ShowTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) CancelTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, StatusService_CancelTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) PauseTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, StatusService_PauseTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) ResumeTask(ctx context.Context, in *TaskId, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, StatusService_ResumeTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) SetRunTime(ctx context.Context, in *SetRunTimeRequest, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, StatusService_SetRunTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusServiceClient) SetBpsMax(ctx context.Context, in *SetBpsMaxRequest, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, StatusService_SetBpsMax_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatusServiceServer is the server API for StatusService service.
// All implementations must embed UnimplementedStatusServiceServer
// for forward compatibility
type StatusServiceServer interface {
	ListRunner(context.Context, *zbs.Void) (*zbs.Addresses, error)
	ListTaskByDate(context.Context, *ListTaskByDateRequest) (*Tasks, error)
	ListTaskByStatus(context.Context, *ListTaskByStatusRequest) (*Tasks, error)
	ShowTask(context.Context, *TaskId) (*Task, error)
	CancelTask(context.Context, *TaskId) (*Task, error)
	PauseTask(context.Context, *TaskId) (*Task, error)
	ResumeTask(context.Context, *TaskId) (*Task, error)
	SetRunTime(context.Context, *SetRunTimeRequest) (*Task, error)
	SetBpsMax(context.Context, *SetBpsMaxRequest) (*Task, error)
	mustEmbedUnimplementedStatusServiceServer()
}

// UnimplementedStatusServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStatusServiceServer struct {
}

func (UnimplementedStatusServiceServer) ListRunner(context.Context, *zbs.Void) (*zbs.Addresses, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRunner not implemented")
}
func (UnimplementedStatusServiceServer) ListTaskByDate(context.Context, *ListTaskByDateRequest) (*Tasks, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTaskByDate not implemented")
}
func (UnimplementedStatusServiceServer) ListTaskByStatus(context.Context, *ListTaskByStatusRequest) (*Tasks, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTaskByStatus not implemented")
}
func (UnimplementedStatusServiceServer) ShowTask(context.Context, *TaskId) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShowTask not implemented")
}
func (UnimplementedStatusServiceServer) CancelTask(context.Context, *TaskId) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelTask not implemented")
}
func (UnimplementedStatusServiceServer) PauseTask(context.Context, *TaskId) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseTask not implemented")
}
func (UnimplementedStatusServiceServer) ResumeTask(context.Context, *TaskId) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeTask not implemented")
}
func (UnimplementedStatusServiceServer) SetRunTime(context.Context, *SetRunTimeRequest) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRunTime not implemented")
}
func (UnimplementedStatusServiceServer) SetBpsMax(context.Context, *SetBpsMaxRequest) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetBpsMax not implemented")
}
func (UnimplementedStatusServiceServer) mustEmbedUnimplementedStatusServiceServer() {}

// UnsafeStatusServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StatusServiceServer will
// result in compilation errors.
type UnsafeStatusServiceServer interface {
	mustEmbedUnimplementedStatusServiceServer()
}

func RegisterStatusServiceServer(s grpc.ServiceRegistrar, srv StatusServiceServer) {
	s.RegisterService(&StatusService_ServiceDesc, srv)
}

func _StatusService_ListRunner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).ListRunner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_ListRunner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).ListRunner(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_ListTaskByDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTaskByDateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).ListTaskByDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_ListTaskByDate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).ListTaskByDate(ctx, req.(*ListTaskByDateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_ListTaskByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTaskByStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).ListTaskByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_ListTaskByStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).ListTaskByStatus(ctx, req.(*ListTaskByStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_ShowTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).ShowTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_ShowTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).ShowTask(ctx, req.(*TaskId))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_CancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).CancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_CancelTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).CancelTask(ctx, req.(*TaskId))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_PauseTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).PauseTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_PauseTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).PauseTask(ctx, req.(*TaskId))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_ResumeTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).ResumeTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_ResumeTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).ResumeTask(ctx, req.(*TaskId))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_SetRunTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRunTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).SetRunTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_SetRunTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).SetRunTime(ctx, req.(*SetRunTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusService_SetBpsMax_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBpsMaxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusServiceServer).SetBpsMax(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusService_SetBpsMax_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusServiceServer).SetBpsMax(ctx, req.(*SetBpsMaxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StatusService_ServiceDesc is the grpc.ServiceDesc for StatusService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StatusService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.task.StatusService",
	HandlerType: (*StatusServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListRunner",
			Handler:    _StatusService_ListRunner_Handler,
		},
		{
			MethodName: "ListTaskByDate",
			Handler:    _StatusService_ListTaskByDate_Handler,
		},
		{
			MethodName: "ListTaskByStatus",
			Handler:    _StatusService_ListTaskByStatus_Handler,
		},
		{
			MethodName: "ShowTask",
			Handler:    _StatusService_ShowTask_Handler,
		},
		{
			MethodName: "CancelTask",
			Handler:    _StatusService_CancelTask_Handler,
		},
		{
			MethodName: "PauseTask",
			Handler:    _StatusService_PauseTask_Handler,
		},
		{
			MethodName: "ResumeTask",
			Handler:    _StatusService_ResumeTask_Handler,
		},
		{
			MethodName: "SetRunTime",
			Handler:    _StatusService_SetRunTime_Handler,
		},
		{
			MethodName: "SetBpsMax",
			Handler:    _StatusService_SetBpsMax_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "task.proto",
}

const (
	VIPService_SetVIP_FullMethodName    = "/zbs.task.VIPService/SetVIP"
	VIPService_DeleteVIP_FullMethodName = "/zbs.task.VIPService/DeleteVIP"
	VIPService_ShowVIP_FullMethodName   = "/zbs.task.VIPService/ShowVIP"
)

// VIPServiceClient is the client API for VIPService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VIPServiceClient interface {
	SetVIP(ctx context.Context, in *VirtualIP, opts ...grpc.CallOption) (*zbs.Void, error)
	DeleteVIP(ctx context.Context, in *VirtualIP, opts ...grpc.CallOption) (*zbs.Void, error)
	ShowVIP(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*VirtualIPs, error)
}

type vIPServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVIPServiceClient(cc grpc.ClientConnInterface) VIPServiceClient {
	return &vIPServiceClient{cc}
}

func (c *vIPServiceClient) SetVIP(ctx context.Context, in *VirtualIP, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, VIPService_SetVIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vIPServiceClient) DeleteVIP(ctx context.Context, in *VirtualIP, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, VIPService_DeleteVIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vIPServiceClient) ShowVIP(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*VirtualIPs, error) {
	out := new(VirtualIPs)
	err := c.cc.Invoke(ctx, VIPService_ShowVIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VIPServiceServer is the server API for VIPService service.
// All implementations must embed UnimplementedVIPServiceServer
// for forward compatibility
type VIPServiceServer interface {
	SetVIP(context.Context, *VirtualIP) (*zbs.Void, error)
	DeleteVIP(context.Context, *VirtualIP) (*zbs.Void, error)
	ShowVIP(context.Context, *zbs.Void) (*VirtualIPs, error)
	mustEmbedUnimplementedVIPServiceServer()
}

// UnimplementedVIPServiceServer must be embedded to have forward compatible implementations.
type UnimplementedVIPServiceServer struct {
}

func (UnimplementedVIPServiceServer) SetVIP(context.Context, *VirtualIP) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetVIP not implemented")
}
func (UnimplementedVIPServiceServer) DeleteVIP(context.Context, *VirtualIP) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteVIP not implemented")
}
func (UnimplementedVIPServiceServer) ShowVIP(context.Context, *zbs.Void) (*VirtualIPs, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShowVIP not implemented")
}
func (UnimplementedVIPServiceServer) mustEmbedUnimplementedVIPServiceServer() {}

// UnsafeVIPServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VIPServiceServer will
// result in compilation errors.
type UnsafeVIPServiceServer interface {
	mustEmbedUnimplementedVIPServiceServer()
}

func RegisterVIPServiceServer(s grpc.ServiceRegistrar, srv VIPServiceServer) {
	s.RegisterService(&VIPService_ServiceDesc, srv)
}

func _VIPService_SetVIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VirtualIP)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VIPServiceServer).SetVIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VIPService_SetVIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VIPServiceServer).SetVIP(ctx, req.(*VirtualIP))
	}
	return interceptor(ctx, in, info, handler)
}

func _VIPService_DeleteVIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VirtualIP)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VIPServiceServer).DeleteVIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VIPService_DeleteVIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VIPServiceServer).DeleteVIP(ctx, req.(*VirtualIP))
	}
	return interceptor(ctx, in, info, handler)
}

func _VIPService_ShowVIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VIPServiceServer).ShowVIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VIPService_ShowVIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VIPServiceServer).ShowVIP(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

// VIPService_ServiceDesc is the grpc.ServiceDesc for VIPService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VIPService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.task.VIPService",
	HandlerType: (*VIPServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetVIP",
			Handler:    _VIPService_SetVIP_Handler,
		},
		{
			MethodName: "DeleteVIP",
			Handler:    _VIPService_DeleteVIP_Handler,
		},
		{
			MethodName: "ShowVIP",
			Handler:    _VIPService_ShowVIP_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "task.proto",
}

const (
	RsyncService_SyncVolume_FullMethodName = "/zbs.task.RsyncService/SyncVolume"
)

// RsyncServiceClient is the client API for RsyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RsyncServiceClient interface {
	SyncVolume(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error)
}

type rsyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRsyncServiceClient(cc grpc.ClientConnInterface) RsyncServiceClient {
	return &rsyncServiceClient{cc}
}

func (c *rsyncServiceClient) SyncVolume(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, RsyncService_SyncVolume_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RsyncServiceServer is the server API for RsyncService service.
// All implementations must embed UnimplementedRsyncServiceServer
// for forward compatibility
type RsyncServiceServer interface {
	SyncVolume(context.Context, *Task) (*Task, error)
	mustEmbedUnimplementedRsyncServiceServer()
}

// UnimplementedRsyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRsyncServiceServer struct {
}

func (UnimplementedRsyncServiceServer) SyncVolume(context.Context, *Task) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncVolume not implemented")
}
func (UnimplementedRsyncServiceServer) mustEmbedUnimplementedRsyncServiceServer() {}

// UnsafeRsyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RsyncServiceServer will
// result in compilation errors.
type UnsafeRsyncServiceServer interface {
	mustEmbedUnimplementedRsyncServiceServer()
}

func RegisterRsyncServiceServer(s grpc.ServiceRegistrar, srv RsyncServiceServer) {
	s.RegisterService(&RsyncService_ServiceDesc, srv)
}

func _RsyncService_SyncVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RsyncServiceServer).SyncVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RsyncService_SyncVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RsyncServiceServer).SyncVolume(ctx, req.(*Task))
	}
	return interceptor(ctx, in, info, handler)
}

// RsyncService_ServiceDesc is the grpc.ServiceDesc for RsyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RsyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.task.RsyncService",
	HandlerType: (*RsyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncVolume",
			Handler:    _RsyncService_SyncVolume_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "task.proto",
}

const (
	CopyVolumeService_CopyVolume_FullMethodName = "/zbs.task.CopyVolumeService/CopyVolume"
)

// CopyVolumeServiceClient is the client API for CopyVolumeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CopyVolumeServiceClient interface {
	CopyVolume(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error)
}

type copyVolumeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCopyVolumeServiceClient(cc grpc.ClientConnInterface) CopyVolumeServiceClient {
	return &copyVolumeServiceClient{cc}
}

func (c *copyVolumeServiceClient) CopyVolume(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, CopyVolumeService_CopyVolume_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CopyVolumeServiceServer is the server API for CopyVolumeService service.
// All implementations must embed UnimplementedCopyVolumeServiceServer
// for forward compatibility
type CopyVolumeServiceServer interface {
	CopyVolume(context.Context, *Task) (*Task, error)
	mustEmbedUnimplementedCopyVolumeServiceServer()
}

// UnimplementedCopyVolumeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCopyVolumeServiceServer struct {
}

func (UnimplementedCopyVolumeServiceServer) CopyVolume(context.Context, *Task) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyVolume not implemented")
}
func (UnimplementedCopyVolumeServiceServer) mustEmbedUnimplementedCopyVolumeServiceServer() {}

// UnsafeCopyVolumeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CopyVolumeServiceServer will
// result in compilation errors.
type UnsafeCopyVolumeServiceServer interface {
	mustEmbedUnimplementedCopyVolumeServiceServer()
}

func RegisterCopyVolumeServiceServer(s grpc.ServiceRegistrar, srv CopyVolumeServiceServer) {
	s.RegisterService(&CopyVolumeService_ServiceDesc, srv)
}

func _CopyVolumeService_CopyVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CopyVolumeServiceServer).CopyVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CopyVolumeService_CopyVolume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CopyVolumeServiceServer).CopyVolume(ctx, req.(*Task))
	}
	return interceptor(ctx, in, info, handler)
}

// CopyVolumeService_ServiceDesc is the grpc.ServiceDesc for CopyVolumeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CopyVolumeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.task.CopyVolumeService",
	HandlerType: (*CopyVolumeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CopyVolume",
			Handler:    _CopyVolumeService_CopyVolume_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "task.proto",
}
