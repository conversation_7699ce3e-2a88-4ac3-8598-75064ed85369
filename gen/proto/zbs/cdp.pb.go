// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: cdp.proto

package zbs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CDP messages
type CDPJobStage int32

const (
	CDPJobStage_CDP_STAGE_INIT              CDPJobStage = 0
	CDPJobStage_CDP_STAGE_FULL_COPY         CDPJobStage = 1
	CDPJobStage_CDP_STAGE_DELTA_COPY        CDPJobStage = 2
	CDPJobStage_CDP_STAGE_MIRROR            CDPJobStage = 3
	CDPJobStage_CDP_STAGE_CANCELLING        CDPJobStage = 4
	CDPJobStage_CDP_STAGE_FINISHING         CDPJobStage = 5
	CDPJobStage_CDP_STAGE_ERROR             CDPJobStage = 6
	CDPJobStage_CDP_STAGE_CANCELLED         CDPJobStage = 7
	CDPJobStage_CDP_STAGE_DONE              CDPJobStage = 8
	CDPJobStage_CDP_STAGE_ERROR_NEED_NOTIFY CDPJobStage = 9
	CDPJobStage_CDP_STAGE_NONE              CDPJobStage = 31
)

// Enum value maps for CDPJobStage.
var (
	CDPJobStage_name = map[int32]string{
		0:  "CDP_STAGE_INIT",
		1:  "CDP_STAGE_FULL_COPY",
		2:  "CDP_STAGE_DELTA_COPY",
		3:  "CDP_STAGE_MIRROR",
		4:  "CDP_STAGE_CANCELLING",
		5:  "CDP_STAGE_FINISHING",
		6:  "CDP_STAGE_ERROR",
		7:  "CDP_STAGE_CANCELLED",
		8:  "CDP_STAGE_DONE",
		9:  "CDP_STAGE_ERROR_NEED_NOTIFY",
		31: "CDP_STAGE_NONE",
	}
	CDPJobStage_value = map[string]int32{
		"CDP_STAGE_INIT":              0,
		"CDP_STAGE_FULL_COPY":         1,
		"CDP_STAGE_DELTA_COPY":        2,
		"CDP_STAGE_MIRROR":            3,
		"CDP_STAGE_CANCELLING":        4,
		"CDP_STAGE_FINISHING":         5,
		"CDP_STAGE_ERROR":             6,
		"CDP_STAGE_CANCELLED":         7,
		"CDP_STAGE_DONE":              8,
		"CDP_STAGE_ERROR_NEED_NOTIFY": 9,
		"CDP_STAGE_NONE":              31,
	}
)

func (x CDPJobStage) Enum() *CDPJobStage {
	p := new(CDPJobStage)
	*p = x
	return p
}

func (x CDPJobStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CDPJobStage) Descriptor() protoreflect.EnumDescriptor {
	return file_cdp_proto_enumTypes[0].Descriptor()
}

func (CDPJobStage) Type() protoreflect.EnumType {
	return &file_cdp_proto_enumTypes[0]
}

func (x CDPJobStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CDPJobStage) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CDPJobStage(num)
	return nil
}

// Deprecated: Use CDPJobStage.Descriptor instead.
func (CDPJobStage) EnumDescriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{0}
}

type CreateCDPJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Local           *CDPVolumeID   `protobuf:"bytes,1,req,name=local" json:"local,omitempty"`
	Remote          *CDPRemoteInfo `protobuf:"bytes,2,req,name=remote" json:"remote,omitempty"`
	Group           []byte         `protobuf:"bytes,3,opt,name=group" json:"group,omitempty"`
	Cid             *uint32        `protobuf:"varint,4,opt,name=cid" json:"cid,omitempty"`
	Id              []byte         `protobuf:"bytes,5,opt,name=id" json:"id,omitempty"`
	SkipFcWriteZero *bool          `protobuf:"varint,6,opt,name=skip_fc_write_zero,json=skipFcWriteZero" json:"skip_fc_write_zero,omitempty"`
	AutoClean       *bool          `protobuf:"varint,7,opt,name=auto_clean,json=autoClean" json:"auto_clean,omitempty"`
}

func (x *CreateCDPJobRequest) Reset() {
	*x = CreateCDPJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCDPJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCDPJobRequest) ProtoMessage() {}

func (x *CreateCDPJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCDPJobRequest.ProtoReflect.Descriptor instead.
func (*CreateCDPJobRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCDPJobRequest) GetLocal() *CDPVolumeID {
	if x != nil {
		return x.Local
	}
	return nil
}

func (x *CreateCDPJobRequest) GetRemote() *CDPRemoteInfo {
	if x != nil {
		return x.Remote
	}
	return nil
}

func (x *CreateCDPJobRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *CreateCDPJobRequest) GetCid() uint32 {
	if x != nil && x.Cid != nil {
		return *x.Cid
	}
	return 0
}

func (x *CreateCDPJobRequest) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *CreateCDPJobRequest) GetSkipFcWriteZero() bool {
	if x != nil && x.SkipFcWriteZero != nil {
		return *x.SkipFcWriteZero
	}
	return false
}

func (x *CreateCDPJobRequest) GetAutoClean() bool {
	if x != nil && x.AutoClean != nil {
		return *x.AutoClean
	}
	return false
}

type CDPRemoteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId []byte `protobuf:"bytes,1,req,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
	Hosts    []byte `protobuf:"bytes,2,req,name=hosts" json:"hosts,omitempty"`
}

func (x *CDPRemoteInfo) Reset() {
	*x = CDPRemoteInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPRemoteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPRemoteInfo) ProtoMessage() {}

func (x *CDPRemoteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPRemoteInfo.ProtoReflect.Descriptor instead.
func (*CDPRemoteInfo) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{1}
}

func (x *CDPRemoteInfo) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

func (x *CDPRemoteInfo) GetHosts() []byte {
	if x != nil {
		return x.Hosts
	}
	return nil
}

type ListCDPJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group []byte `protobuf:"bytes,1,opt,name=group" json:"group,omitempty"`
}

func (x *ListCDPJobsRequest) Reset() {
	*x = ListCDPJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCDPJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCDPJobsRequest) ProtoMessage() {}

func (x *ListCDPJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCDPJobsRequest.ProtoReflect.Descriptor instead.
func (*ListCDPJobsRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{2}
}

func (x *ListCDPJobsRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

type ListCDPJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jobs []*CDPJobInfo `protobuf:"bytes,1,rep,name=jobs" json:"jobs,omitempty"`
}

func (x *ListCDPJobsResponse) Reset() {
	*x = ListCDPJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCDPJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCDPJobsResponse) ProtoMessage() {}

func (x *ListCDPJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCDPJobsResponse.ProtoReflect.Descriptor instead.
func (*ListCDPJobsResponse) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{3}
}

func (x *ListCDPJobsResponse) GetJobs() []*CDPJobInfo {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type CDPVolumeID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId []byte `protobuf:"bytes,1,req,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
}

func (x *CDPVolumeID) Reset() {
	*x = CDPVolumeID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPVolumeID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPVolumeID) ProtoMessage() {}

func (x *CDPVolumeID) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPVolumeID.ProtoReflect.Descriptor instead.
func (*CDPVolumeID) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{4}
}

func (x *CDPVolumeID) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

type CDPJobInfo struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id                    []byte          `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	Local                 *CDPVolumeID    `protobuf:"bytes,2,req,name=local" json:"local,omitempty"`
	Remote                *CDPRemoteInfo  `protobuf:"bytes,3,req,name=remote" json:"remote,omitempty"`
	Group                 []byte          `protobuf:"bytes,4,opt,name=group" json:"group,omitempty"`
	Cid                   *uint32         `protobuf:"varint,5,opt,name=cid" json:"cid,omitempty"`
	Stage                 *CDPJobStage    `protobuf:"varint,6,opt,name=stage,enum=zbs.CDPJobStage" json:"stage,omitempty"`
	SessionId             []byte          `protobuf:"bytes,7,opt,name=session_id,json=sessionId" json:"session_id,omitempty"`
	Error                 *CDPErrorDetail `protobuf:"bytes,8,opt,name=error" json:"error,omitempty"`
	SkipFcWriteZero       *bool           `protobuf:"varint,9,opt,name=skip_fc_write_zero,json=skipFcWriteZero" json:"skip_fc_write_zero,omitempty"`
	AutoClean             *bool           `protobuf:"varint,10,opt,name=auto_clean,json=autoClean" json:"auto_clean,omitempty"`
	OldChunkInstancesBits *uint64         `protobuf:"varint,11,opt,name=old_chunk_instances_bits,json=oldChunkInstancesBits" json:"old_chunk_instances_bits,omitempty"`
}

func (x *CDPJobInfo) Reset() {
	*x = CDPJobInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPJobInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPJobInfo) ProtoMessage() {}

func (x *CDPJobInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPJobInfo.ProtoReflect.Descriptor instead.
func (*CDPJobInfo) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{5}
}

func (x *CDPJobInfo) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *CDPJobInfo) GetLocal() *CDPVolumeID {
	if x != nil {
		return x.Local
	}
	return nil
}

func (x *CDPJobInfo) GetRemote() *CDPRemoteInfo {
	if x != nil {
		return x.Remote
	}
	return nil
}

func (x *CDPJobInfo) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *CDPJobInfo) GetCid() uint32 {
	if x != nil && x.Cid != nil {
		return *x.Cid
	}
	return 0
}

func (x *CDPJobInfo) GetStage() CDPJobStage {
	if x != nil && x.Stage != nil {
		return *x.Stage
	}
	return CDPJobStage_CDP_STAGE_INIT
}

func (x *CDPJobInfo) GetSessionId() []byte {
	if x != nil {
		return x.SessionId
	}
	return nil
}

func (x *CDPJobInfo) GetError() *CDPErrorDetail {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CDPJobInfo) GetSkipFcWriteZero() bool {
	if x != nil && x.SkipFcWriteZero != nil {
		return *x.SkipFcWriteZero
	}
	return false
}

func (x *CDPJobInfo) GetAutoClean() bool {
	if x != nil && x.AutoClean != nil {
		return *x.AutoClean
	}
	return false
}

func (x *CDPJobInfo) GetOldChunkInstancesBits() uint64 {
	if x != nil && x.OldChunkInstancesBits != nil {
		return *x.OldChunkInstancesBits
	}
	return 0
}

type GetCDPJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []byte `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
}

func (x *GetCDPJobRequest) Reset() {
	*x = GetCDPJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCDPJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCDPJobRequest) ProtoMessage() {}

func (x *GetCDPJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCDPJobRequest.ProtoReflect.Descriptor instead.
func (*GetCDPJobRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{6}
}

func (x *GetCDPJobRequest) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

type GetCDPJobByVolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId []byte `protobuf:"bytes,1,req,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
}

func (x *GetCDPJobByVolumeRequest) Reset() {
	*x = GetCDPJobByVolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCDPJobByVolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCDPJobByVolumeRequest) ProtoMessage() {}

func (x *GetCDPJobByVolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCDPJobByVolumeRequest.ProtoReflect.Descriptor instead.
func (*GetCDPJobByVolumeRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{7}
}

func (x *GetCDPJobByVolumeRequest) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

type FinishCDPJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   []byte `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	Sync *bool  `protobuf:"varint,2,opt,name=sync" json:"sync,omitempty"`
}

func (x *FinishCDPJobRequest) Reset() {
	*x = FinishCDPJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishCDPJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishCDPJobRequest) ProtoMessage() {}

func (x *FinishCDPJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishCDPJobRequest.ProtoReflect.Descriptor instead.
func (*FinishCDPJobRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{8}
}

func (x *FinishCDPJobRequest) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *FinishCDPJobRequest) GetSync() bool {
	if x != nil && x.Sync != nil {
		return *x.Sync
	}
	return false
}

type CancelCDPJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         []byte `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	Sync       *bool  `protobuf:"varint,2,opt,name=sync" json:"sync,omitempty"`
	ReplyError *bool  `protobuf:"varint,3,opt,name=reply_error,json=replyError,def=0" json:"reply_error,omitempty"`
}

// Default values for CancelCDPJobRequest fields.
const (
	Default_CancelCDPJobRequest_ReplyError = bool(false)
)

func (x *CancelCDPJobRequest) Reset() {
	*x = CancelCDPJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelCDPJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelCDPJobRequest) ProtoMessage() {}

func (x *CancelCDPJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelCDPJobRequest.ProtoReflect.Descriptor instead.
func (*CancelCDPJobRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{9}
}

func (x *CancelCDPJobRequest) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *CancelCDPJobRequest) GetSync() bool {
	if x != nil && x.Sync != nil {
		return *x.Sync
	}
	return false
}

func (x *CancelCDPJobRequest) GetReplyError() bool {
	if x != nil && x.ReplyError != nil {
		return *x.ReplyError
	}
	return Default_CancelCDPJobRequest_ReplyError
}

type DeleteCDPJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []byte `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
}

func (x *DeleteCDPJobRequest) Reset() {
	*x = DeleteCDPJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCDPJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCDPJobRequest) ProtoMessage() {}

func (x *DeleteCDPJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCDPJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteCDPJobRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteCDPJobRequest) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

type CreateCDPJobsByGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group      []byte                 `protobuf:"bytes,1,req,name=group" json:"group,omitempty"`
	CreateReqs []*CreateCDPJobRequest `protobuf:"bytes,2,rep,name=create_reqs,json=createReqs" json:"create_reqs,omitempty"`
}

func (x *CreateCDPJobsByGroupRequest) Reset() {
	*x = CreateCDPJobsByGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCDPJobsByGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCDPJobsByGroupRequest) ProtoMessage() {}

func (x *CreateCDPJobsByGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCDPJobsByGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateCDPJobsByGroupRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{11}
}

func (x *CreateCDPJobsByGroupRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *CreateCDPJobsByGroupRequest) GetCreateReqs() []*CreateCDPJobRequest {
	if x != nil {
		return x.CreateReqs
	}
	return nil
}

type CreateCDPJobsByGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CdpJobs []*CDPJobInfo `protobuf:"bytes,1,rep,name=cdp_jobs,json=cdpJobs" json:"cdp_jobs,omitempty"`
}

func (x *CreateCDPJobsByGroupResponse) Reset() {
	*x = CreateCDPJobsByGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCDPJobsByGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCDPJobsByGroupResponse) ProtoMessage() {}

func (x *CreateCDPJobsByGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCDPJobsByGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateCDPJobsByGroupResponse) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{12}
}

func (x *CreateCDPJobsByGroupResponse) GetCdpJobs() []*CDPJobInfo {
	if x != nil {
		return x.CdpJobs
	}
	return nil
}

type FinishCDPJobsByGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group []byte `protobuf:"bytes,1,req,name=group" json:"group,omitempty"`
	Sync  *bool  `protobuf:"varint,2,opt,name=sync" json:"sync,omitempty"`
}

func (x *FinishCDPJobsByGroupRequest) Reset() {
	*x = FinishCDPJobsByGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishCDPJobsByGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishCDPJobsByGroupRequest) ProtoMessage() {}

func (x *FinishCDPJobsByGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishCDPJobsByGroupRequest.ProtoReflect.Descriptor instead.
func (*FinishCDPJobsByGroupRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{13}
}

func (x *FinishCDPJobsByGroupRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *FinishCDPJobsByGroupRequest) GetSync() bool {
	if x != nil && x.Sync != nil {
		return *x.Sync
	}
	return false
}

type CancelCDPJobsByGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group []byte `protobuf:"bytes,1,req,name=group" json:"group,omitempty"`
	Sync  *bool  `protobuf:"varint,2,opt,name=sync" json:"sync,omitempty"`
}

func (x *CancelCDPJobsByGroupRequest) Reset() {
	*x = CancelCDPJobsByGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelCDPJobsByGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelCDPJobsByGroupRequest) ProtoMessage() {}

func (x *CancelCDPJobsByGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelCDPJobsByGroupRequest.ProtoReflect.Descriptor instead.
func (*CancelCDPJobsByGroupRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{14}
}

func (x *CancelCDPJobsByGroupRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *CancelCDPJobsByGroupRequest) GetSync() bool {
	if x != nil && x.Sync != nil {
		return *x.Sync
	}
	return false
}

type DeleteCDPJobsByGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group []byte `protobuf:"bytes,1,req,name=group" json:"group,omitempty"`
}

func (x *DeleteCDPJobsByGroupRequest) Reset() {
	*x = DeleteCDPJobsByGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCDPJobsByGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCDPJobsByGroupRequest) ProtoMessage() {}

func (x *DeleteCDPJobsByGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCDPJobsByGroupRequest.ProtoReflect.Descriptor instead.
func (*DeleteCDPJobsByGroupRequest) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteCDPJobsByGroupRequest) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

type CDPJobUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notify chunk manage CDP jobs
	CreateReqs []*CreateCDPJobRequest `protobuf:"bytes,1,rep,name=create_reqs,json=createReqs" json:"create_reqs,omitempty"`
	CancelReqs []*CancelCDPJobRequest `protobuf:"bytes,2,rep,name=cancel_reqs,json=cancelReqs" json:"cancel_reqs,omitempty"`
	FinishReqs []*FinishCDPJobRequest `protobuf:"bytes,3,rep,name=finish_reqs,json=finishReqs" json:"finish_reqs,omitempty"`
}

func (x *CDPJobUpdate) Reset() {
	*x = CDPJobUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPJobUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPJobUpdate) ProtoMessage() {}

func (x *CDPJobUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPJobUpdate.ProtoReflect.Descriptor instead.
func (*CDPJobUpdate) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{16}
}

func (x *CDPJobUpdate) GetCreateReqs() []*CreateCDPJobRequest {
	if x != nil {
		return x.CreateReqs
	}
	return nil
}

func (x *CDPJobUpdate) GetCancelReqs() []*CancelCDPJobRequest {
	if x != nil {
		return x.CancelReqs
	}
	return nil
}

func (x *CDPJobUpdate) GetFinishReqs() []*FinishCDPJobRequest {
	if x != nil {
		return x.FinishReqs
	}
	return nil
}

type CDPErrorDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode    *ErrorCode `protobuf:"varint,1,opt,name=error_code,json=errorCode,enum=zbs.ErrorCode,def=0" json:"error_code,omitempty"`
	ErrorMessage *string    `protobuf:"bytes,2,opt,name=error_message,json=errorMessage" json:"error_message,omitempty"`
}

// Default values for CDPErrorDetail fields.
const (
	Default_CDPErrorDetail_ErrorCode = ErrorCode_EOK
)

func (x *CDPErrorDetail) Reset() {
	*x = CDPErrorDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPErrorDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPErrorDetail) ProtoMessage() {}

func (x *CDPErrorDetail) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPErrorDetail.ProtoReflect.Descriptor instead.
func (*CDPErrorDetail) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{17}
}

func (x *CDPErrorDetail) GetErrorCode() ErrorCode {
	if x != nil && x.ErrorCode != nil {
		return *x.ErrorCode
	}
	return Default_CDPErrorDetail_ErrorCode
}

func (x *CDPErrorDetail) GetErrorMessage() string {
	if x != nil && x.ErrorMessage != nil {
		return *x.ErrorMessage
	}
	return ""
}

type CDPJobStageUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    []byte          `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	Stage *CDPJobStage    `protobuf:"varint,2,req,name=stage,enum=zbs.CDPJobStage" json:"stage,omitempty"`
	Err   *CDPErrorDetail `protobuf:"bytes,3,opt,name=err" json:"err,omitempty"`
}

func (x *CDPJobStageUpdate) Reset() {
	*x = CDPJobStageUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPJobStageUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPJobStageUpdate) ProtoMessage() {}

func (x *CDPJobStageUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPJobStageUpdate.ProtoReflect.Descriptor instead.
func (*CDPJobStageUpdate) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{18}
}

func (x *CDPJobStageUpdate) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *CDPJobStageUpdate) GetStage() CDPJobStage {
	if x != nil && x.Stage != nil {
		return *x.Stage
	}
	return CDPJobStage_CDP_STAGE_INIT
}

func (x *CDPJobStageUpdate) GetErr() *CDPErrorDetail {
	if x != nil {
		return x.Err
	}
	return nil
}

type CDPJobUpdateDone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobUpdate []*CDPJobStageUpdate `protobuf:"bytes,1,rep,name=job_update,json=jobUpdate" json:"job_update,omitempty"`
}

func (x *CDPJobUpdateDone) Reset() {
	*x = CDPJobUpdateDone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPJobUpdateDone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPJobUpdateDone) ProtoMessage() {}

func (x *CDPJobUpdateDone) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPJobUpdateDone.ProtoReflect.Descriptor instead.
func (*CDPJobUpdateDone) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{19}
}

func (x *CDPJobUpdateDone) GetJobUpdate() []*CDPJobStageUpdate {
	if x != nil {
		return x.JobUpdate
	}
	return nil
}

type CDPJobMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalFcBlocks     *uint64  `protobuf:"varint,1,opt,name=total_fc_blocks,json=totalFcBlocks" json:"total_fc_blocks,omitempty"`
	RemainFcBlocks    *uint64  `protobuf:"varint,2,opt,name=remain_fc_blocks,json=remainFcBlocks" json:"remain_fc_blocks,omitempty"`
	CopiedFcBlocks    *uint64  `protobuf:"varint,3,opt,name=copied_fc_blocks,json=copiedFcBlocks" json:"copied_fc_blocks,omitempty"`
	TotalDirtyBlocks  *uint64  `protobuf:"varint,4,opt,name=total_dirty_blocks,json=totalDirtyBlocks" json:"total_dirty_blocks,omitempty"`
	RemainDirtyBlocks *uint64  `protobuf:"varint,5,opt,name=remain_dirty_blocks,json=remainDirtyBlocks" json:"remain_dirty_blocks,omitempty"`
	CopiedDirtyBlocks *uint64  `protobuf:"varint,6,opt,name=copied_dirty_blocks,json=copiedDirtyBlocks" json:"copied_dirty_blocks,omitempty"`
	BlockQueueDepth   *uint64  `protobuf:"varint,7,opt,name=block_queue_depth,json=blockQueueDepth" json:"block_queue_depth,omitempty"`
	StageDuration     *uint64  `protobuf:"varint,8,opt,name=stage_duration,json=stageDuration" json:"stage_duration,omitempty"`
	TotalInflight     *uint32  `protobuf:"varint,9,opt,name=total_inflight,json=totalInflight" json:"total_inflight,omitempty"`
	TotalIops         *float32 `protobuf:"fixed32,10,opt,name=total_iops,json=totalIops" json:"total_iops,omitempty"`
	TotalIop30S       *float32 `protobuf:"fixed32,11,opt,name=total_iop30s,json=totalIop30s" json:"total_iop30s,omitempty"`
	TotalSpeedBps     *float32 `protobuf:"fixed32,12,opt,name=total_speed_bps,json=totalSpeedBps" json:"total_speed_bps,omitempty"`
	TotalAvgSizeBytes *float32 `protobuf:"fixed32,13,opt,name=total_avg_size_bytes,json=totalAvgSizeBytes" json:"total_avg_size_bytes,omitempty"`
	TotalAvgLatencyNs *float32 `protobuf:"fixed32,14,opt,name=total_avg_latency_ns,json=totalAvgLatencyNs" json:"total_avg_latency_ns,omitempty"`
	BlockInflight     *uint32  `protobuf:"varint,15,opt,name=block_inflight,json=blockInflight" json:"block_inflight,omitempty"`
	BlockIops         *float32 `protobuf:"fixed32,16,opt,name=block_iops,json=blockIops" json:"block_iops,omitempty"`
	BlockIop30S       *float32 `protobuf:"fixed32,17,opt,name=block_iop30s,json=blockIop30s" json:"block_iop30s,omitempty"`
	BlockSpeedBps     *float32 `protobuf:"fixed32,18,opt,name=block_speed_bps,json=blockSpeedBps" json:"block_speed_bps,omitempty"`
	BlockAvgSizeBytes *float32 `protobuf:"fixed32,19,opt,name=block_avg_size_bytes,json=blockAvgSizeBytes" json:"block_avg_size_bytes,omitempty"`
	BlockAvgLatencyNs *float32 `protobuf:"fixed32,20,opt,name=block_avg_latency_ns,json=blockAvgLatencyNs" json:"block_avg_latency_ns,omitempty"`
	UserInflight      *uint32  `protobuf:"varint,21,opt,name=user_inflight,json=userInflight" json:"user_inflight,omitempty"`
	UserIops          *float32 `protobuf:"fixed32,22,opt,name=user_iops,json=userIops" json:"user_iops,omitempty"`
	UserIop30S        *float32 `protobuf:"fixed32,23,opt,name=user_iop30s,json=userIop30s" json:"user_iop30s,omitempty"`
	UserSpeedBps      *float32 `protobuf:"fixed32,24,opt,name=user_speed_bps,json=userSpeedBps" json:"user_speed_bps,omitempty"`
	UserAvgSizeBytes  *float32 `protobuf:"fixed32,25,opt,name=user_avg_size_bytes,json=userAvgSizeBytes" json:"user_avg_size_bytes,omitempty"`
	UserAvgLatencyNs  *float32 `protobuf:"fixed32,26,opt,name=user_avg_latency_ns,json=userAvgLatencyNs" json:"user_avg_latency_ns,omitempty"`
}

func (x *CDPJobMetrics) Reset() {
	*x = CDPJobMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cdp_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDPJobMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDPJobMetrics) ProtoMessage() {}

func (x *CDPJobMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_cdp_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDPJobMetrics.ProtoReflect.Descriptor instead.
func (*CDPJobMetrics) Descriptor() ([]byte, []int) {
	return file_cdp_proto_rawDescGZIP(), []int{20}
}

func (x *CDPJobMetrics) GetTotalFcBlocks() uint64 {
	if x != nil && x.TotalFcBlocks != nil {
		return *x.TotalFcBlocks
	}
	return 0
}

func (x *CDPJobMetrics) GetRemainFcBlocks() uint64 {
	if x != nil && x.RemainFcBlocks != nil {
		return *x.RemainFcBlocks
	}
	return 0
}

func (x *CDPJobMetrics) GetCopiedFcBlocks() uint64 {
	if x != nil && x.CopiedFcBlocks != nil {
		return *x.CopiedFcBlocks
	}
	return 0
}

func (x *CDPJobMetrics) GetTotalDirtyBlocks() uint64 {
	if x != nil && x.TotalDirtyBlocks != nil {
		return *x.TotalDirtyBlocks
	}
	return 0
}

func (x *CDPJobMetrics) GetRemainDirtyBlocks() uint64 {
	if x != nil && x.RemainDirtyBlocks != nil {
		return *x.RemainDirtyBlocks
	}
	return 0
}

func (x *CDPJobMetrics) GetCopiedDirtyBlocks() uint64 {
	if x != nil && x.CopiedDirtyBlocks != nil {
		return *x.CopiedDirtyBlocks
	}
	return 0
}

func (x *CDPJobMetrics) GetBlockQueueDepth() uint64 {
	if x != nil && x.BlockQueueDepth != nil {
		return *x.BlockQueueDepth
	}
	return 0
}

func (x *CDPJobMetrics) GetStageDuration() uint64 {
	if x != nil && x.StageDuration != nil {
		return *x.StageDuration
	}
	return 0
}

func (x *CDPJobMetrics) GetTotalInflight() uint32 {
	if x != nil && x.TotalInflight != nil {
		return *x.TotalInflight
	}
	return 0
}

func (x *CDPJobMetrics) GetTotalIops() float32 {
	if x != nil && x.TotalIops != nil {
		return *x.TotalIops
	}
	return 0
}

func (x *CDPJobMetrics) GetTotalIop30S() float32 {
	if x != nil && x.TotalIop30S != nil {
		return *x.TotalIop30S
	}
	return 0
}

func (x *CDPJobMetrics) GetTotalSpeedBps() float32 {
	if x != nil && x.TotalSpeedBps != nil {
		return *x.TotalSpeedBps
	}
	return 0
}

func (x *CDPJobMetrics) GetTotalAvgSizeBytes() float32 {
	if x != nil && x.TotalAvgSizeBytes != nil {
		return *x.TotalAvgSizeBytes
	}
	return 0
}

func (x *CDPJobMetrics) GetTotalAvgLatencyNs() float32 {
	if x != nil && x.TotalAvgLatencyNs != nil {
		return *x.TotalAvgLatencyNs
	}
	return 0
}

func (x *CDPJobMetrics) GetBlockInflight() uint32 {
	if x != nil && x.BlockInflight != nil {
		return *x.BlockInflight
	}
	return 0
}

func (x *CDPJobMetrics) GetBlockIops() float32 {
	if x != nil && x.BlockIops != nil {
		return *x.BlockIops
	}
	return 0
}

func (x *CDPJobMetrics) GetBlockIop30S() float32 {
	if x != nil && x.BlockIop30S != nil {
		return *x.BlockIop30S
	}
	return 0
}

func (x *CDPJobMetrics) GetBlockSpeedBps() float32 {
	if x != nil && x.BlockSpeedBps != nil {
		return *x.BlockSpeedBps
	}
	return 0
}

func (x *CDPJobMetrics) GetBlockAvgSizeBytes() float32 {
	if x != nil && x.BlockAvgSizeBytes != nil {
		return *x.BlockAvgSizeBytes
	}
	return 0
}

func (x *CDPJobMetrics) GetBlockAvgLatencyNs() float32 {
	if x != nil && x.BlockAvgLatencyNs != nil {
		return *x.BlockAvgLatencyNs
	}
	return 0
}

func (x *CDPJobMetrics) GetUserInflight() uint32 {
	if x != nil && x.UserInflight != nil {
		return *x.UserInflight
	}
	return 0
}

func (x *CDPJobMetrics) GetUserIops() float32 {
	if x != nil && x.UserIops != nil {
		return *x.UserIops
	}
	return 0
}

func (x *CDPJobMetrics) GetUserIop30S() float32 {
	if x != nil && x.UserIop30S != nil {
		return *x.UserIop30S
	}
	return 0
}

func (x *CDPJobMetrics) GetUserSpeedBps() float32 {
	if x != nil && x.UserSpeedBps != nil {
		return *x.UserSpeedBps
	}
	return 0
}

func (x *CDPJobMetrics) GetUserAvgSizeBytes() float32 {
	if x != nil && x.UserAvgSizeBytes != nil {
		return *x.UserAvgSizeBytes
	}
	return 0
}

func (x *CDPJobMetrics) GetUserAvgLatencyNs() float32 {
	if x != nil && x.UserAvgLatencyNs != nil {
		return *x.UserAvgLatencyNs
	}
	return 0
}

var file_cdp_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*CDPJobInfo)(nil),
		ExtensionType: (*CDPJobMetrics)(nil),
		Field:         10001,
		Name:          "zbs.CDPJobMetrics.metrics",
		Tag:           "bytes,10001,opt,name=metrics",
		Filename:      "cdp.proto",
	},
}

// Extension fields to CDPJobInfo.
var (
	// optional zbs.CDPJobMetrics metrics = 10001;
	E_CDPJobMetrics_Metrics = &file_cdp_proto_extTypes[0]
)

var File_cdp_proto protoreflect.FileDescriptor

var file_cdp_proto_rawDesc = []byte{
	0x0a, 0x09, 0x63, 0x64, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x7a, 0x62, 0x73,
	0x1a, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfb, 0x01, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x05, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x49, 0x44, 0x52, 0x05, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b,
	0x0a, 0x12, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x66, 0x63, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f,
	0x7a, 0x65, 0x72, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x6b, 0x69, 0x70,
	0x46, 0x63, 0x57, 0x72, 0x69, 0x74, 0x65, 0x5a, 0x65, 0x72, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x22, 0x50, 0x0a, 0x0d, 0x43, 0x44,
	0x50, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x09, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05,
	0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05,
	0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x22, 0x31, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22,
	0x3a, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x4a, 0x6f,
	0x62, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x22, 0x31, 0x0a, 0x0b, 0x43,
	0x44, 0x50, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x09, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca,
	0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x22, 0xaf,
	0x03, 0x0a, 0x0a, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x05, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x49, 0x44, 0x52, 0x05, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50,
	0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x24, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x2b, 0x0a, 0x12, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x66, 0x63, 0x5f, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x5f, 0x7a, 0x65, 0x72, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x6b,
	0x69, 0x70, 0x46, 0x63, 0x57, 0x72, 0x69, 0x74, 0x65, 0x5a, 0x65, 0x72, 0x6f, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x12, 0x37, 0x0a, 0x18,
	0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x5f, 0x62, 0x69, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x15,
	0x6f, 0x6c, 0x64, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x42, 0x69, 0x74, 0x73, 0x2a, 0x09, 0x08, 0x90, 0x4e, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02,
	0x22, 0x29, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c,
	0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3e, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x42, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x09, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08,
	0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x13, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x15, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05,
	0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x79, 0x6e,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x22, 0x68, 0x0a,
	0x13, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c,
	0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x79, 0x6e, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x12,
	0x26, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x2c, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08,
	0x01, 0x52, 0x02, 0x69, 0x64, 0x22, 0x75, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x73, 0x22, 0x4a, 0x0a, 0x1c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x42, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x08,
	0x63, 0x64, 0x70, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x07, 0x63, 0x64, 0x70, 0x4a, 0x6f, 0x62, 0x73, 0x22, 0x4e, 0x0a, 0x1b, 0x46, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x22, 0x4e, 0x0a, 0x1b, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x22, 0x3a, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x22, 0xbf, 0x01, 0x0a, 0x0c, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x73,
	0x12, 0x39, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x43, 0x44, 0x50,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x73, 0x22, 0x69, 0x0a, 0x0e, 0x43, 0x44, 0x50, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x32, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x3a, 0x03, 0x45, 0x4f,
	0x4b, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x79, 0x0a, 0x11, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x03, 0x65, 0x72, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x03, 0x65, 0x72, 0x72, 0x22, 0x49, 0x0a, 0x10,
	0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x6e, 0x65,
	0x12, 0x35, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x4a, 0x6f,
	0x62, 0x53, 0x74, 0x61, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x09, 0x6a, 0x6f,
	0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xf9, 0x08, 0x0a, 0x0d, 0x43, 0x44, 0x50, 0x4a,
	0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x66, 0x63, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x63, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x73, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x63, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x72, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x46, 0x63, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x63,
	0x6f, 0x70, 0x69, 0x65, 0x64, 0x5f, 0x66, 0x63, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x63, 0x6f, 0x70, 0x69, 0x65, 0x64, 0x46, 0x63, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64,
	0x69, 0x72, 0x74, 0x79, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x69,
	0x72, 0x74, 0x79, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x11, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x6f, 0x70, 0x69, 0x65, 0x64, 0x5f, 0x64, 0x69,
	0x72, 0x74, 0x79, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x11, 0x63, 0x6f, 0x70, 0x69, 0x65, 0x64, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x51, 0x75, 0x65, 0x75, 0x65, 0x44, 0x65, 0x70, 0x74, 0x68, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x67, 0x65, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x69, 0x6e, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6f, 0x70, 0x33, 0x30, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6f, 0x70, 0x33, 0x30, 0x73, 0x12,
	0x26, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62,
	0x70, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x76, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x67, 0x53,
	0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6e, 0x73,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x67,
	0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6f, 0x70, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6f, 0x70, 0x33, 0x30, 0x73, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6f, 0x70, 0x33,
	0x30, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x41,
	0x76, 0x67, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x6e, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x41, 0x76, 0x67, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6f, 0x70, 0x33, 0x30, 0x73, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6f, 0x70, 0x33, 0x30, 0x73, 0x12,
	0x24, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70,
	0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x76,
	0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x67, 0x53, 0x69, 0x7a, 0x65, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x76, 0x67,
	0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6e, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x67, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x4e, 0x73, 0x32, 0x3e, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x0f,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x91, 0x4e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x44, 0x50,
	0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2a, 0x94, 0x02, 0x0a, 0x0b, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x44, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x44, 0x50, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x50, 0x59, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x43, 0x44, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x45,
	0x4c, 0x54, 0x41, 0x5f, 0x43, 0x4f, 0x50, 0x59, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x44,
	0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x49, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03,
	0x12, 0x18, 0x0a, 0x14, 0x43, 0x44, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x41,
	0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x44,
	0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x49, 0x4e,
	0x47, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x44, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x44, 0x50, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x07, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x44, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x44,
	0x4f, 0x4e, 0x45, 0x10, 0x08, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x44, 0x50, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x4e, 0x45, 0x45, 0x44, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x59, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x44, 0x50, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x1f, 0x42, 0x35, 0x5a, 0x2d, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68, 0x2f,
	0x7a, 0x62, 0x73, 0x2d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x7a, 0x62, 0x73, 0x80, 0x01, 0x01, 0x90, 0x01,
	0x01,
}

var (
	file_cdp_proto_rawDescOnce sync.Once
	file_cdp_proto_rawDescData = file_cdp_proto_rawDesc
)

func file_cdp_proto_rawDescGZIP() []byte {
	file_cdp_proto_rawDescOnce.Do(func() {
		file_cdp_proto_rawDescData = protoimpl.X.CompressGZIP(file_cdp_proto_rawDescData)
	})
	return file_cdp_proto_rawDescData
}

var file_cdp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cdp_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_cdp_proto_goTypes = []interface{}{
	(CDPJobStage)(0),                     // 0: zbs.CDPJobStage
	(*CreateCDPJobRequest)(nil),          // 1: zbs.CreateCDPJobRequest
	(*CDPRemoteInfo)(nil),                // 2: zbs.CDPRemoteInfo
	(*ListCDPJobsRequest)(nil),           // 3: zbs.ListCDPJobsRequest
	(*ListCDPJobsResponse)(nil),          // 4: zbs.ListCDPJobsResponse
	(*CDPVolumeID)(nil),                  // 5: zbs.CDPVolumeID
	(*CDPJobInfo)(nil),                   // 6: zbs.CDPJobInfo
	(*GetCDPJobRequest)(nil),             // 7: zbs.GetCDPJobRequest
	(*GetCDPJobByVolumeRequest)(nil),     // 8: zbs.GetCDPJobByVolumeRequest
	(*FinishCDPJobRequest)(nil),          // 9: zbs.FinishCDPJobRequest
	(*CancelCDPJobRequest)(nil),          // 10: zbs.CancelCDPJobRequest
	(*DeleteCDPJobRequest)(nil),          // 11: zbs.DeleteCDPJobRequest
	(*CreateCDPJobsByGroupRequest)(nil),  // 12: zbs.CreateCDPJobsByGroupRequest
	(*CreateCDPJobsByGroupResponse)(nil), // 13: zbs.CreateCDPJobsByGroupResponse
	(*FinishCDPJobsByGroupRequest)(nil),  // 14: zbs.FinishCDPJobsByGroupRequest
	(*CancelCDPJobsByGroupRequest)(nil),  // 15: zbs.CancelCDPJobsByGroupRequest
	(*DeleteCDPJobsByGroupRequest)(nil),  // 16: zbs.DeleteCDPJobsByGroupRequest
	(*CDPJobUpdate)(nil),                 // 17: zbs.CDPJobUpdate
	(*CDPErrorDetail)(nil),               // 18: zbs.CDPErrorDetail
	(*CDPJobStageUpdate)(nil),            // 19: zbs.CDPJobStageUpdate
	(*CDPJobUpdateDone)(nil),             // 20: zbs.CDPJobUpdateDone
	(*CDPJobMetrics)(nil),                // 21: zbs.CDPJobMetrics
	(ErrorCode)(0),                       // 22: zbs.ErrorCode
}
var file_cdp_proto_depIdxs = []int32{
	5,  // 0: zbs.CreateCDPJobRequest.local:type_name -> zbs.CDPVolumeID
	2,  // 1: zbs.CreateCDPJobRequest.remote:type_name -> zbs.CDPRemoteInfo
	6,  // 2: zbs.ListCDPJobsResponse.jobs:type_name -> zbs.CDPJobInfo
	5,  // 3: zbs.CDPJobInfo.local:type_name -> zbs.CDPVolumeID
	2,  // 4: zbs.CDPJobInfo.remote:type_name -> zbs.CDPRemoteInfo
	0,  // 5: zbs.CDPJobInfo.stage:type_name -> zbs.CDPJobStage
	18, // 6: zbs.CDPJobInfo.error:type_name -> zbs.CDPErrorDetail
	1,  // 7: zbs.CreateCDPJobsByGroupRequest.create_reqs:type_name -> zbs.CreateCDPJobRequest
	6,  // 8: zbs.CreateCDPJobsByGroupResponse.cdp_jobs:type_name -> zbs.CDPJobInfo
	1,  // 9: zbs.CDPJobUpdate.create_reqs:type_name -> zbs.CreateCDPJobRequest
	10, // 10: zbs.CDPJobUpdate.cancel_reqs:type_name -> zbs.CancelCDPJobRequest
	9,  // 11: zbs.CDPJobUpdate.finish_reqs:type_name -> zbs.FinishCDPJobRequest
	22, // 12: zbs.CDPErrorDetail.error_code:type_name -> zbs.ErrorCode
	0,  // 13: zbs.CDPJobStageUpdate.stage:type_name -> zbs.CDPJobStage
	18, // 14: zbs.CDPJobStageUpdate.err:type_name -> zbs.CDPErrorDetail
	19, // 15: zbs.CDPJobUpdateDone.job_update:type_name -> zbs.CDPJobStageUpdate
	6,  // 16: zbs.CDPJobMetrics.metrics:extendee -> zbs.CDPJobInfo
	21, // 17: zbs.CDPJobMetrics.metrics:type_name -> zbs.CDPJobMetrics
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	17, // [17:18] is the sub-list for extension type_name
	16, // [16:17] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_cdp_proto_init() }
func file_cdp_proto_init() {
	if File_cdp_proto != nil {
		return
	}
	file_error_proto_init()
	file_options_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_cdp_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCDPJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPRemoteInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCDPJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCDPJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPVolumeID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPJobInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCDPJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCDPJobByVolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishCDPJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelCDPJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCDPJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCDPJobsByGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCDPJobsByGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishCDPJobsByGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelCDPJobsByGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCDPJobsByGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPJobUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPErrorDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPJobStageUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPJobUpdateDone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cdp_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDPJobMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cdp_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   21,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_cdp_proto_goTypes,
		DependencyIndexes: file_cdp_proto_depIdxs,
		EnumInfos:         file_cdp_proto_enumTypes,
		MessageInfos:      file_cdp_proto_msgTypes,
		ExtensionInfos:    file_cdp_proto_extTypes,
	}.Build()
	File_cdp_proto = out.File
	file_cdp_proto_rawDesc = nil
	file_cdp_proto_goTypes = nil
	file_cdp_proto_depIdxs = nil
}
