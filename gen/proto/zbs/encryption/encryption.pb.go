// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: encryption.proto

package encryption

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InternalEncryptMethod int32

const (
	InternalEncryptMethod_INTERNAL_ENCRYPT_UNKNOWN_ALGO InternalEncryptMethod = 0
	InternalEncryptMethod_INTERNAL_ENCRYPT_AES256_CTR   InternalEncryptMethod = 1
	InternalEncryptMethod_INTERNAL_ENCRYPT_SM4_CTR      InternalEncryptMethod = 2
)

// Enum value maps for InternalEncryptMethod.
var (
	InternalEncryptMethod_name = map[int32]string{
		0: "INTERNAL_ENCRYPT_UNKNOWN_ALGO",
		1: "INTERNAL_ENCRYPT_AES256_CTR",
		2: "INTERNAL_ENCRYPT_SM4_CTR",
	}
	InternalEncryptMethod_value = map[string]int32{
		"INTERNAL_ENCRYPT_UNKNOWN_ALGO": 0,
		"INTERNAL_ENCRYPT_AES256_CTR":   1,
		"INTERNAL_ENCRYPT_SM4_CTR":      2,
	}
)

func (x InternalEncryptMethod) Enum() *InternalEncryptMethod {
	p := new(InternalEncryptMethod)
	*p = x
	return p
}

func (x InternalEncryptMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InternalEncryptMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_encryption_proto_enumTypes[0].Descriptor()
}

func (InternalEncryptMethod) Type() protoreflect.EnumType {
	return &file_encryption_proto_enumTypes[0]
}

func (x InternalEncryptMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *InternalEncryptMethod) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = InternalEncryptMethod(num)
	return nil
}

// Deprecated: Use InternalEncryptMethod.Descriptor instead.
func (InternalEncryptMethod) EnumDescriptor() ([]byte, []int) {
	return file_encryption_proto_rawDescGZIP(), []int{0}
}

type EncryptMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MetadataId    *uint64 `protobuf:"varint,1,req,name=metadata_id,json=metadataId" json:"metadata_id,omitempty"`
	Key           []byte  `protobuf:"bytes,2,req,name=key" json:"key,omitempty"`
	InitVec       []byte  `protobuf:"bytes,3,req,name=init_vec,json=initVec" json:"init_vec,omitempty"`
	CmkId         []byte  `protobuf:"bytes,4,req,name=cmk_id,json=cmkId" json:"cmk_id,omitempty"`
	KmipClusterId *string `protobuf:"bytes,5,req,name=kmip_cluster_id,json=kmipClusterId" json:"kmip_cluster_id,omitempty"`
	// cipher_metadata_id is encrpted by plain_key and init_vec from metadata_id. It is used to verify if the plain_key
	// is valid or not for meta and access both.
	CipherMetadataId *uint64                `protobuf:"varint,6,req,name=cipher_metadata_id,json=cipherMetadataId" json:"cipher_metadata_id,omitempty"`
	Method           *InternalEncryptMethod `protobuf:"varint,7,req,name=method,enum=zbs.encryption.InternalEncryptMethod" json:"method,omitempty"`
}

func (x *EncryptMetaData) Reset() {
	*x = EncryptMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_encryption_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EncryptMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncryptMetaData) ProtoMessage() {}

func (x *EncryptMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_encryption_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncryptMetaData.ProtoReflect.Descriptor instead.
func (*EncryptMetaData) Descriptor() ([]byte, []int) {
	return file_encryption_proto_rawDescGZIP(), []int{0}
}

func (x *EncryptMetaData) GetMetadataId() uint64 {
	if x != nil && x.MetadataId != nil {
		return *x.MetadataId
	}
	return 0
}

func (x *EncryptMetaData) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *EncryptMetaData) GetInitVec() []byte {
	if x != nil {
		return x.InitVec
	}
	return nil
}

func (x *EncryptMetaData) GetCmkId() []byte {
	if x != nil {
		return x.CmkId
	}
	return nil
}

func (x *EncryptMetaData) GetKmipClusterId() string {
	if x != nil && x.KmipClusterId != nil {
		return *x.KmipClusterId
	}
	return ""
}

func (x *EncryptMetaData) GetCipherMetadataId() uint64 {
	if x != nil && x.CipherMetadataId != nil {
		return *x.CipherMetadataId
	}
	return 0
}

func (x *EncryptMetaData) GetMethod() InternalEncryptMethod {
	if x != nil && x.Method != nil {
		return *x.Method
	}
	return InternalEncryptMethod_INTERNAL_ENCRYPT_UNKNOWN_ALGO
}

var File_encryption_proto protoreflect.FileDescriptor

var file_encryption_proto_rawDesc = []byte{
	0x0a, 0x10, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x7a, 0x62, 0x73, 0x2e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x8b, 0x02, 0x0a, 0x0f, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x0c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x69,
	0x74, 0x5f, 0x76, 0x65, 0x63, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x07, 0x69, 0x6e, 0x69,
	0x74, 0x56, 0x65, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x6d, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x02, 0x28, 0x0c, 0x52, 0x05, 0x63, 0x6d, 0x6b, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6b,
	0x6d, 0x69, 0x70, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x6d, 0x69, 0x70, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x69, 0x70, 0x68, 0x65, 0x72, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x02, 0x28, 0x04, 0x52,
	0x10, 0x63, 0x69, 0x70, 0x68, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49,
	0x64, 0x12, 0x3d, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x02, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x2a, 0x79, 0x0a, 0x15, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x41, 0x4c, 0x47, 0x4f, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54,
	0x5f, 0x41, 0x45, 0x53, 0x32, 0x35, 0x36, 0x5f, 0x43, 0x54, 0x52, 0x10, 0x01, 0x12, 0x1c, 0x0a,
	0x18, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50,
	0x54, 0x5f, 0x53, 0x4d, 0x34, 0x5f, 0x43, 0x54, 0x52, 0x10, 0x02, 0x42, 0x40, 0x5a, 0x38, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68,
	0x2f, 0x7a, 0x62, 0x73, 0x2d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67,
	0x65, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x7a, 0x62, 0x73, 0x2f, 0x65, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x80, 0x01, 0x01, 0x90, 0x01, 0x01,
}

var (
	file_encryption_proto_rawDescOnce sync.Once
	file_encryption_proto_rawDescData = file_encryption_proto_rawDesc
)

func file_encryption_proto_rawDescGZIP() []byte {
	file_encryption_proto_rawDescOnce.Do(func() {
		file_encryption_proto_rawDescData = protoimpl.X.CompressGZIP(file_encryption_proto_rawDescData)
	})
	return file_encryption_proto_rawDescData
}

var file_encryption_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_encryption_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_encryption_proto_goTypes = []interface{}{
	(InternalEncryptMethod)(0), // 0: zbs.encryption.InternalEncryptMethod
	(*EncryptMetaData)(nil),    // 1: zbs.encryption.EncryptMetaData
}
var file_encryption_proto_depIdxs = []int32{
	0, // 0: zbs.encryption.EncryptMetaData.method:type_name -> zbs.encryption.InternalEncryptMethod
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_encryption_proto_init() }
func file_encryption_proto_init() {
	if File_encryption_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_encryption_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EncryptMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_encryption_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_encryption_proto_goTypes,
		DependencyIndexes: file_encryption_proto_depIdxs,
		EnumInfos:         file_encryption_proto_enumTypes,
		MessageInfos:      file_encryption_proto_msgTypes,
	}.Build()
	File_encryption_proto = out.File
	file_encryption_proto_rawDesc = nil
	file_encryption_proto_goTypes = nil
	file_encryption_proto_depIdxs = nil
}
