// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: event.proto

package zbs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventCode int32

const (
	EventCode_LOCAL_IO_START                     EventCode = 10001
	EventCode_LOCAL_IO_END                       EventCode = 10002
	EventCode_LSM_START_HANDLE                   EventCode = 10003
	EventCode_LSM_COW                            EventCode = 10004
	EventCode_LSM_CACHE_READ_HIT                 EventCode = 10005
	EventCode_LSM_CACHE_READ_MISS                EventCode = 10006
	EventCode_LSM_CACHE_READ_PROMOTE             EventCode = 10007
	EventCode_LSM_CACHE_WRITE_HIT                EventCode = 10008
	EventCode_LSM_CACHE_WRITE_MISS               EventCode = 10009
	EventCode_LSM_CACHE_WRITE_PROMOTE            EventCode = 10010
	EventCode_LSM_WRITE_DURING_RECOVER           EventCode = 10011
	EventCode_PARTITION_WORKER_ENQUEUE           EventCode = 10101
	EventCode_PARTITION_WORKER_IO_START          EventCode = 10102
	EventCode_PARTITION_WORKER_IO_END            EventCode = 10103
	EventCode_PARTITION_WORKER_ALLOC_WRITE       EventCode = 10104
	EventCode_PARTITION_WORKER_WRITE_WITH_ORIGIN EventCode = 10105
	EventCode_PARTITION_WORKER_EVICT             EventCode = 10106
	EventCode_PARTITION_WORKER_UPDATE_INODE      EventCode = 10107
	EventCode_PARTITION_WORKER_WAIT_START        EventCode = 10111
	EventCode_PARTITION_WORKER_WAIT_END          EventCode = 10112
)

// Enum value maps for EventCode.
var (
	EventCode_name = map[int32]string{
		10001: "LOCAL_IO_START",
		10002: "LOCAL_IO_END",
		10003: "LSM_START_HANDLE",
		10004: "LSM_COW",
		10005: "LSM_CACHE_READ_HIT",
		10006: "LSM_CACHE_READ_MISS",
		10007: "LSM_CACHE_READ_PROMOTE",
		10008: "LSM_CACHE_WRITE_HIT",
		10009: "LSM_CACHE_WRITE_MISS",
		10010: "LSM_CACHE_WRITE_PROMOTE",
		10011: "LSM_WRITE_DURING_RECOVER",
		10101: "PARTITION_WORKER_ENQUEUE",
		10102: "PARTITION_WORKER_IO_START",
		10103: "PARTITION_WORKER_IO_END",
		10104: "PARTITION_WORKER_ALLOC_WRITE",
		10105: "PARTITION_WORKER_WRITE_WITH_ORIGIN",
		10106: "PARTITION_WORKER_EVICT",
		10107: "PARTITION_WORKER_UPDATE_INODE",
		10111: "PARTITION_WORKER_WAIT_START",
		10112: "PARTITION_WORKER_WAIT_END",
	}
	EventCode_value = map[string]int32{
		"LOCAL_IO_START":                     10001,
		"LOCAL_IO_END":                       10002,
		"LSM_START_HANDLE":                   10003,
		"LSM_COW":                            10004,
		"LSM_CACHE_READ_HIT":                 10005,
		"LSM_CACHE_READ_MISS":                10006,
		"LSM_CACHE_READ_PROMOTE":             10007,
		"LSM_CACHE_WRITE_HIT":                10008,
		"LSM_CACHE_WRITE_MISS":               10009,
		"LSM_CACHE_WRITE_PROMOTE":            10010,
		"LSM_WRITE_DURING_RECOVER":           10011,
		"PARTITION_WORKER_ENQUEUE":           10101,
		"PARTITION_WORKER_IO_START":          10102,
		"PARTITION_WORKER_IO_END":            10103,
		"PARTITION_WORKER_ALLOC_WRITE":       10104,
		"PARTITION_WORKER_WRITE_WITH_ORIGIN": 10105,
		"PARTITION_WORKER_EVICT":             10106,
		"PARTITION_WORKER_UPDATE_INODE":      10107,
		"PARTITION_WORKER_WAIT_START":        10111,
		"PARTITION_WORKER_WAIT_END":          10112,
	}
)

func (x EventCode) Enum() *EventCode {
	p := new(EventCode)
	*p = x
	return p
}

func (x EventCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventCode) Descriptor() protoreflect.EnumDescriptor {
	return file_event_proto_enumTypes[0].Descriptor()
}

func (EventCode) Type() protoreflect.EnumType {
	return &file_event_proto_enumTypes[0]
}

func (x EventCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EventCode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EventCode(num)
	return nil
}

// Deprecated: Use EventCode.Descriptor instead.
func (EventCode) EnumDescriptor() ([]byte, []int) {
	return file_event_proto_rawDescGZIP(), []int{0}
}

type Event struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  *EventCode `protobuf:"varint,1,req,name=code,enum=zbs.EventCode" json:"code,omitempty"`
	AbsMs *uint64    `protobuf:"varint,2,opt,name=abs_ms,json=absMs" json:"abs_ms,omitempty"`
	RelMs *uint64    `protobuf:"varint,3,opt,name=rel_ms,json=relMs" json:"rel_ms,omitempty"`
}

func (x *Event) Reset() {
	*x = Event{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_event_proto_rawDescGZIP(), []int{0}
}

func (x *Event) GetCode() EventCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return EventCode_LOCAL_IO_START
}

func (x *Event) GetAbsMs() uint64 {
	if x != nil && x.AbsMs != nil {
		return *x.AbsMs
	}
	return 0
}

func (x *Event) GetRelMs() uint64 {
	if x != nil && x.RelMs != nil {
		return *x.RelMs
	}
	return 0
}

var File_event_proto protoreflect.FileDescriptor

var file_event_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x7a,
	0x62, 0x73, 0x22, 0x59, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x61, 0x62, 0x73, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x61, 0x62, 0x73, 0x4d, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x6c, 0x5f, 0x6d, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x72, 0x65, 0x6c, 0x4d, 0x73, 0x2a, 0xc6, 0x04,
	0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x13, 0x0a, 0x0e, 0x4c,
	0x4f, 0x43, 0x41, 0x4c, 0x5f, 0x49, 0x4f, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x91, 0x4e,
	0x12, 0x11, 0x0a, 0x0c, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x5f, 0x49, 0x4f, 0x5f, 0x45, 0x4e, 0x44,
	0x10, 0x92, 0x4e, 0x12, 0x15, 0x0a, 0x10, 0x4c, 0x53, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54,
	0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x10, 0x93, 0x4e, 0x12, 0x0c, 0x0a, 0x07, 0x4c, 0x53,
	0x4d, 0x5f, 0x43, 0x4f, 0x57, 0x10, 0x94, 0x4e, 0x12, 0x17, 0x0a, 0x12, 0x4c, 0x53, 0x4d, 0x5f,
	0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x48, 0x49, 0x54, 0x10, 0x95,
	0x4e, 0x12, 0x18, 0x0a, 0x13, 0x4c, 0x53, 0x4d, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x44, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x10, 0x96, 0x4e, 0x12, 0x1b, 0x0a, 0x16, 0x4c,
	0x53, 0x4d, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x50, 0x52,
	0x4f, 0x4d, 0x4f, 0x54, 0x45, 0x10, 0x97, 0x4e, 0x12, 0x18, 0x0a, 0x13, 0x4c, 0x53, 0x4d, 0x5f,
	0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x48, 0x49, 0x54, 0x10,
	0x98, 0x4e, 0x12, 0x19, 0x0a, 0x14, 0x4c, 0x53, 0x4d, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f,
	0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x10, 0x99, 0x4e, 0x12, 0x1c, 0x0a,
	0x17, 0x4c, 0x53, 0x4d, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x45, 0x10, 0x9a, 0x4e, 0x12, 0x1d, 0x0a, 0x18, 0x4c,
	0x53, 0x4d, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x44, 0x55, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x9b, 0x4e, 0x12, 0x1d, 0x0a, 0x18, 0x50, 0x41,
	0x52, 0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x45,
	0x4e, 0x51, 0x55, 0x45, 0x55, 0x45, 0x10, 0xf5, 0x4e, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x41, 0x52,
	0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x49, 0x4f,
	0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0xf6, 0x4e, 0x12, 0x1c, 0x0a, 0x17, 0x50, 0x41, 0x52,
	0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x49, 0x4f,
	0x5f, 0x45, 0x4e, 0x44, 0x10, 0xf7, 0x4e, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x41, 0x52, 0x54, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x4f,
	0x43, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x10, 0xf8, 0x4e, 0x12, 0x27, 0x0a, 0x22, 0x50, 0x41,
	0x52, 0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x57,
	0x52, 0x49, 0x54, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e,
	0x10, 0xf9, 0x4e, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x45, 0x56, 0x49, 0x43, 0x54, 0x10, 0xfa, 0x4e,
	0x12, 0x22, 0x0a, 0x1d, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f,
	0x52, 0x4b, 0x45, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x4f, 0x44,
	0x45, 0x10, 0xfb, 0x4e, 0x12, 0x20, 0x0a, 0x1b, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x52, 0x54, 0x10, 0xff, 0x4e, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f,
	0x45, 0x4e, 0x44, 0x10, 0x80, 0x4f, 0x42, 0x2f, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68, 0x2f, 0x7a, 0x62, 0x73, 0x2d,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x7a, 0x62, 0x73,
}

var (
	file_event_proto_rawDescOnce sync.Once
	file_event_proto_rawDescData = file_event_proto_rawDesc
)

func file_event_proto_rawDescGZIP() []byte {
	file_event_proto_rawDescOnce.Do(func() {
		file_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_event_proto_rawDescData)
	})
	return file_event_proto_rawDescData
}

var file_event_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_event_proto_goTypes = []interface{}{
	(EventCode)(0), // 0: zbs.EventCode
	(*Event)(nil),  // 1: zbs.Event
}
var file_event_proto_depIdxs = []int32{
	0, // 0: zbs.Event.code:type_name -> zbs.EventCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_event_proto_init() }
func file_event_proto_init() {
	if File_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Event); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_event_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_event_proto_goTypes,
		DependencyIndexes: file_event_proto_depIdxs,
		EnumInfos:         file_event_proto_enumTypes,
		MessageInfos:      file_event_proto_msgTypes,
	}.Build()
	File_event_proto = out.File
	file_event_proto_rawDesc = nil
	file_event_proto_goTypes = nil
	file_event_proto_depIdxs = nil
}
