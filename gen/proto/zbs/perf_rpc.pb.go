// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: perf_rpc.proto

package zbs

import (
	proto "github.com/iomesh/zbs-client-go/gen/proto/zbs/metric/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VolumePerfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId []byte `protobuf:"bytes,2,req,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
}

func (x *VolumePerfRequest) Reset() {
	*x = VolumePerfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumePerfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumePerfRequest) ProtoMessage() {}

func (x *VolumePerfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumePerfRequest.ProtoReflect.Descriptor instead.
func (*VolumePerfRequest) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{0}
}

func (x *VolumePerfRequest) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

type VolumePerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId            []byte   `protobuf:"bytes,1,opt,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
	ReadIops            *float32 `protobuf:"fixed32,2,opt,name=read_iops,json=readIops" json:"read_iops,omitempty"`                                       // io per second
	ReadBandwidth       *float32 `protobuf:"fixed32,3,opt,name=read_bandwidth,json=readBandwidth" json:"read_bandwidth,omitempty"`                        // bytes per second
	ReadLatency         *float32 `protobuf:"fixed32,4,opt,name=read_latency,json=readLatency" json:"read_latency,omitempty"`                              // ns
	WriteIops           *float32 `protobuf:"fixed32,5,opt,name=write_iops,json=writeIops" json:"write_iops,omitempty"`                                    // io per second
	WriteBandwidth      *float32 `protobuf:"fixed32,6,opt,name=write_bandwidth,json=writeBandwidth" json:"write_bandwidth,omitempty"`                     // bytes per second
	WriteLatency        *float32 `protobuf:"fixed32,7,opt,name=write_latency,json=writeLatency" json:"write_latency,omitempty"`                           // ns
	TotalIops           *float32 `protobuf:"fixed32,8,opt,name=total_iops,json=totalIops" json:"total_iops,omitempty"`                                    // io per second
	TotalBandwidth      *float32 `protobuf:"fixed32,9,opt,name=total_bandwidth,json=totalBandwidth" json:"total_bandwidth,omitempty"`                     // bytes per second
	TotalLatency        *float32 `protobuf:"fixed32,10,opt,name=total_latency,json=totalLatency" json:"total_latency,omitempty"`                          // ns
	ReadAvgRequestSize  *float32 `protobuf:"fixed32,11,opt,name=read_avg_request_size,json=readAvgRequestSize" json:"read_avg_request_size,omitempty"`    // Bytes
	WriteAvgRequestSize *float32 `protobuf:"fixed32,12,opt,name=write_avg_request_size,json=writeAvgRequestSize" json:"write_avg_request_size,omitempty"` // Bytes
	TotalAvgRequestSize *float32 `protobuf:"fixed32,13,opt,name=total_avg_request_size,json=totalAvgRequestSize" json:"total_avg_request_size,omitempty"` // Bytes
	TotalIop30S         *float32 `protobuf:"fixed32,14,opt,name=total_iop30s,json=totalIop30s" json:"total_iop30s,omitempty"`
	Iodepth             *float32 `protobuf:"fixed32,15,opt,name=iodepth" json:"iodepth,omitempty"`
	// volume io perf after split
	IoctxLocalReadwriteIops            *float32         `protobuf:"fixed32,30,opt,name=ioctx_local_readwrite_iops,json=ioctxLocalReadwriteIops" json:"ioctx_local_readwrite_iops,omitempty"`
	IoctxLocalReadwriteSpeedBps        *float32         `protobuf:"fixed32,31,opt,name=ioctx_local_readwrite_speed_bps,json=ioctxLocalReadwriteSpeedBps" json:"ioctx_local_readwrite_speed_bps,omitempty"`
	IoctxLocalReadwriteLatency         *float32         `protobuf:"fixed32,32,opt,name=ioctx_local_readwrite_latency,json=ioctxLocalReadwriteLatency" json:"ioctx_local_readwrite_latency,omitempty"`
	IoctxLocalReadIops                 *float32         `protobuf:"fixed32,50,opt,name=ioctx_local_read_iops,json=ioctxLocalReadIops" json:"ioctx_local_read_iops,omitempty"`
	IoctxLocalReadSpeedBps             *float32         `protobuf:"fixed32,51,opt,name=ioctx_local_read_speed_bps,json=ioctxLocalReadSpeedBps" json:"ioctx_local_read_speed_bps,omitempty"`
	IoctxLocalReadLatency              *float32         `protobuf:"fixed32,52,opt,name=ioctx_local_read_latency,json=ioctxLocalReadLatency" json:"ioctx_local_read_latency,omitempty"`
	IoctxLocalWriteIops                *float32         `protobuf:"fixed32,70,opt,name=ioctx_local_write_iops,json=ioctxLocalWriteIops" json:"ioctx_local_write_iops,omitempty"`
	IoctxLocalWriteSpeedBps            *float32         `protobuf:"fixed32,71,opt,name=ioctx_local_write_speed_bps,json=ioctxLocalWriteSpeedBps" json:"ioctx_local_write_speed_bps,omitempty"`
	IoctxLocalWriteLatency             *float32         `protobuf:"fixed32,72,opt,name=ioctx_local_write_latency,json=ioctxLocalWriteLatency" json:"ioctx_local_write_latency,omitempty"`
	IoctxReadwriteIops                 *float32         `protobuf:"fixed32,90,opt,name=ioctx_readwrite_iops,json=ioctxReadwriteIops" json:"ioctx_readwrite_iops,omitempty"`
	IoctxReadwriteSpeedBps             *float32         `protobuf:"fixed32,91,opt,name=ioctx_readwrite_speed_bps,json=ioctxReadwriteSpeedBps" json:"ioctx_readwrite_speed_bps,omitempty"`
	IoctxReadwriteLatency              *float32         `protobuf:"fixed32,92,opt,name=ioctx_readwrite_latency,json=ioctxReadwriteLatency" json:"ioctx_readwrite_latency,omitempty"`
	IoctxReadIops                      *float32         `protobuf:"fixed32,110,opt,name=ioctx_read_iops,json=ioctxReadIops" json:"ioctx_read_iops,omitempty"`
	IoctxReadSpeedBps                  *float32         `protobuf:"fixed32,111,opt,name=ioctx_read_speed_bps,json=ioctxReadSpeedBps" json:"ioctx_read_speed_bps,omitempty"`
	IoctxReadLatency                   *float32         `protobuf:"fixed32,112,opt,name=ioctx_read_latency,json=ioctxReadLatency" json:"ioctx_read_latency,omitempty"`
	IoctxWriteIops                     *float32         `protobuf:"fixed32,130,opt,name=ioctx_write_iops,json=ioctxWriteIops" json:"ioctx_write_iops,omitempty"`
	IoctxWriteSpeedBps                 *float32         `protobuf:"fixed32,131,opt,name=ioctx_write_speed_bps,json=ioctxWriteSpeedBps" json:"ioctx_write_speed_bps,omitempty"`
	IoctxWriteLatency                  *float32         `protobuf:"fixed32,132,opt,name=ioctx_write_latency,json=ioctxWriteLatency" json:"ioctx_write_latency,omitempty"`
	ReadwriteSizeRangeBucket           *proto.Histogram `protobuf:"bytes,190,opt,name=readwrite_size_range_bucket,json=readwriteSizeRangeBucket" json:"readwrite_size_range_bucket,omitempty"`
	ReadSizeRangeBucket                *proto.Histogram `protobuf:"bytes,191,opt,name=read_size_range_bucket,json=readSizeRangeBucket" json:"read_size_range_bucket,omitempty"`
	WriteSizeRangeBucket               *proto.Histogram `protobuf:"bytes,192,opt,name=write_size_range_bucket,json=writeSizeRangeBucket" json:"write_size_range_bucket,omitempty"`
	ReadwriteLogicalOffsetRangeBucket  *proto.Histogram `protobuf:"bytes,193,opt,name=readwrite_logical_offset_range_bucket,json=readwriteLogicalOffsetRangeBucket" json:"readwrite_logical_offset_range_bucket,omitempty"`
	ReadLogicalOffsetRangeBucket       *proto.Histogram `protobuf:"bytes,194,opt,name=read_logical_offset_range_bucket,json=readLogicalOffsetRangeBucket" json:"read_logical_offset_range_bucket,omitempty"`
	WriteLogicalOffsetRangeBucket      *proto.Histogram `protobuf:"bytes,195,opt,name=write_logical_offset_range_bucket,json=writeLogicalOffsetRangeBucket" json:"write_logical_offset_range_bucket,omitempty"`
	ReadwritePhysicalOffsetRangeBucket *proto.Histogram `protobuf:"bytes,196,opt,name=readwrite_physical_offset_range_bucket,json=readwritePhysicalOffsetRangeBucket" json:"readwrite_physical_offset_range_bucket,omitempty"`
	ReadPhysicalOffsetRangeBucket      *proto.Histogram `protobuf:"bytes,198,opt,name=read_physical_offset_range_bucket,json=readPhysicalOffsetRangeBucket" json:"read_physical_offset_range_bucket,omitempty"`
	WritePhysicalOffsetRangeBucket     *proto.Histogram `protobuf:"bytes,199,opt,name=write_physical_offset_range_bucket,json=writePhysicalOffsetRangeBucket" json:"write_physical_offset_range_bucket,omitempty"`
	ReadwriteLatencyRangeBucket        *proto.Histogram `protobuf:"bytes,200,opt,name=readwrite_latency_range_bucket,json=readwriteLatencyRangeBucket" json:"readwrite_latency_range_bucket,omitempty"`
	ReadLatencyRangeBucket             *proto.Histogram `protobuf:"bytes,201,opt,name=read_latency_range_bucket,json=readLatencyRangeBucket" json:"read_latency_range_bucket,omitempty"`
	WriteLatencyRangeBucket            *proto.Histogram `protobuf:"bytes,202,opt,name=write_latency_range_bucket,json=writeLatencyRangeBucket" json:"write_latency_range_bucket,omitempty"`
	// unmap info at volume level
	UnmapIops           *float32 `protobuf:"fixed32,205,opt,name=unmap_iops,json=unmapIops" json:"unmap_iops,omitempty"`                                // count of unmap io per second
	UnmapUnalignedIops  *float32 `protobuf:"fixed32,206,opt,name=unmap_unaligned_iops,json=unmapUnalignedIops" json:"unmap_unaligned_iops,omitempty"`   // count of unaligned unmap io per second
	UnmapTotal          *uint64  `protobuf:"varint,207,opt,name=unmap_total,json=unmapTotal" json:"unmap_total,omitempty"`                              // total count of unmap
	UnmapUnalignedTotal *uint64  `protobuf:"varint,208,opt,name=unmap_unaligned_total,json=unmapUnalignedTotal" json:"unmap_unaligned_total,omitempty"` // total count of unaligned unmap
}

func (x *VolumePerf) Reset() {
	*x = VolumePerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumePerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumePerf) ProtoMessage() {}

func (x *VolumePerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumePerf.ProtoReflect.Descriptor instead.
func (*VolumePerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{1}
}

func (x *VolumePerf) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

func (x *VolumePerf) GetReadIops() float32 {
	if x != nil && x.ReadIops != nil {
		return *x.ReadIops
	}
	return 0
}

func (x *VolumePerf) GetReadBandwidth() float32 {
	if x != nil && x.ReadBandwidth != nil {
		return *x.ReadBandwidth
	}
	return 0
}

func (x *VolumePerf) GetReadLatency() float32 {
	if x != nil && x.ReadLatency != nil {
		return *x.ReadLatency
	}
	return 0
}

func (x *VolumePerf) GetWriteIops() float32 {
	if x != nil && x.WriteIops != nil {
		return *x.WriteIops
	}
	return 0
}

func (x *VolumePerf) GetWriteBandwidth() float32 {
	if x != nil && x.WriteBandwidth != nil {
		return *x.WriteBandwidth
	}
	return 0
}

func (x *VolumePerf) GetWriteLatency() float32 {
	if x != nil && x.WriteLatency != nil {
		return *x.WriteLatency
	}
	return 0
}

func (x *VolumePerf) GetTotalIops() float32 {
	if x != nil && x.TotalIops != nil {
		return *x.TotalIops
	}
	return 0
}

func (x *VolumePerf) GetTotalBandwidth() float32 {
	if x != nil && x.TotalBandwidth != nil {
		return *x.TotalBandwidth
	}
	return 0
}

func (x *VolumePerf) GetTotalLatency() float32 {
	if x != nil && x.TotalLatency != nil {
		return *x.TotalLatency
	}
	return 0
}

func (x *VolumePerf) GetReadAvgRequestSize() float32 {
	if x != nil && x.ReadAvgRequestSize != nil {
		return *x.ReadAvgRequestSize
	}
	return 0
}

func (x *VolumePerf) GetWriteAvgRequestSize() float32 {
	if x != nil && x.WriteAvgRequestSize != nil {
		return *x.WriteAvgRequestSize
	}
	return 0
}

func (x *VolumePerf) GetTotalAvgRequestSize() float32 {
	if x != nil && x.TotalAvgRequestSize != nil {
		return *x.TotalAvgRequestSize
	}
	return 0
}

func (x *VolumePerf) GetTotalIop30S() float32 {
	if x != nil && x.TotalIop30S != nil {
		return *x.TotalIop30S
	}
	return 0
}

func (x *VolumePerf) GetIodepth() float32 {
	if x != nil && x.Iodepth != nil {
		return *x.Iodepth
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalReadwriteIops() float32 {
	if x != nil && x.IoctxLocalReadwriteIops != nil {
		return *x.IoctxLocalReadwriteIops
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalReadwriteSpeedBps() float32 {
	if x != nil && x.IoctxLocalReadwriteSpeedBps != nil {
		return *x.IoctxLocalReadwriteSpeedBps
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalReadwriteLatency() float32 {
	if x != nil && x.IoctxLocalReadwriteLatency != nil {
		return *x.IoctxLocalReadwriteLatency
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalReadIops() float32 {
	if x != nil && x.IoctxLocalReadIops != nil {
		return *x.IoctxLocalReadIops
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalReadSpeedBps() float32 {
	if x != nil && x.IoctxLocalReadSpeedBps != nil {
		return *x.IoctxLocalReadSpeedBps
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalReadLatency() float32 {
	if x != nil && x.IoctxLocalReadLatency != nil {
		return *x.IoctxLocalReadLatency
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalWriteIops() float32 {
	if x != nil && x.IoctxLocalWriteIops != nil {
		return *x.IoctxLocalWriteIops
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalWriteSpeedBps() float32 {
	if x != nil && x.IoctxLocalWriteSpeedBps != nil {
		return *x.IoctxLocalWriteSpeedBps
	}
	return 0
}

func (x *VolumePerf) GetIoctxLocalWriteLatency() float32 {
	if x != nil && x.IoctxLocalWriteLatency != nil {
		return *x.IoctxLocalWriteLatency
	}
	return 0
}

func (x *VolumePerf) GetIoctxReadwriteIops() float32 {
	if x != nil && x.IoctxReadwriteIops != nil {
		return *x.IoctxReadwriteIops
	}
	return 0
}

func (x *VolumePerf) GetIoctxReadwriteSpeedBps() float32 {
	if x != nil && x.IoctxReadwriteSpeedBps != nil {
		return *x.IoctxReadwriteSpeedBps
	}
	return 0
}

func (x *VolumePerf) GetIoctxReadwriteLatency() float32 {
	if x != nil && x.IoctxReadwriteLatency != nil {
		return *x.IoctxReadwriteLatency
	}
	return 0
}

func (x *VolumePerf) GetIoctxReadIops() float32 {
	if x != nil && x.IoctxReadIops != nil {
		return *x.IoctxReadIops
	}
	return 0
}

func (x *VolumePerf) GetIoctxReadSpeedBps() float32 {
	if x != nil && x.IoctxReadSpeedBps != nil {
		return *x.IoctxReadSpeedBps
	}
	return 0
}

func (x *VolumePerf) GetIoctxReadLatency() float32 {
	if x != nil && x.IoctxReadLatency != nil {
		return *x.IoctxReadLatency
	}
	return 0
}

func (x *VolumePerf) GetIoctxWriteIops() float32 {
	if x != nil && x.IoctxWriteIops != nil {
		return *x.IoctxWriteIops
	}
	return 0
}

func (x *VolumePerf) GetIoctxWriteSpeedBps() float32 {
	if x != nil && x.IoctxWriteSpeedBps != nil {
		return *x.IoctxWriteSpeedBps
	}
	return 0
}

func (x *VolumePerf) GetIoctxWriteLatency() float32 {
	if x != nil && x.IoctxWriteLatency != nil {
		return *x.IoctxWriteLatency
	}
	return 0
}

func (x *VolumePerf) GetReadwriteSizeRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadwriteSizeRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetReadSizeRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadSizeRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetWriteSizeRangeBucket() *proto.Histogram {
	if x != nil {
		return x.WriteSizeRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetReadwriteLogicalOffsetRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadwriteLogicalOffsetRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetReadLogicalOffsetRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadLogicalOffsetRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetWriteLogicalOffsetRangeBucket() *proto.Histogram {
	if x != nil {
		return x.WriteLogicalOffsetRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetReadwritePhysicalOffsetRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadwritePhysicalOffsetRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetReadPhysicalOffsetRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadPhysicalOffsetRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetWritePhysicalOffsetRangeBucket() *proto.Histogram {
	if x != nil {
		return x.WritePhysicalOffsetRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetReadwriteLatencyRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadwriteLatencyRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetReadLatencyRangeBucket() *proto.Histogram {
	if x != nil {
		return x.ReadLatencyRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetWriteLatencyRangeBucket() *proto.Histogram {
	if x != nil {
		return x.WriteLatencyRangeBucket
	}
	return nil
}

func (x *VolumePerf) GetUnmapIops() float32 {
	if x != nil && x.UnmapIops != nil {
		return *x.UnmapIops
	}
	return 0
}

func (x *VolumePerf) GetUnmapUnalignedIops() float32 {
	if x != nil && x.UnmapUnalignedIops != nil {
		return *x.UnmapUnalignedIops
	}
	return 0
}

func (x *VolumePerf) GetUnmapTotal() uint64 {
	if x != nil && x.UnmapTotal != nil {
		return *x.UnmapTotal
	}
	return 0
}

func (x *VolumePerf) GetUnmapUnalignedTotal() uint64 {
	if x != nil && x.UnmapUnalignedTotal != nil {
		return *x.UnmapUnalignedTotal
	}
	return 0
}

type VolumesPerfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeIdList [][]byte `protobuf:"bytes,1,rep,name=volume_id_list,json=volumeIdList" json:"volume_id_list,omitempty"`
}

func (x *VolumesPerfRequest) Reset() {
	*x = VolumesPerfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumesPerfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumesPerfRequest) ProtoMessage() {}

func (x *VolumesPerfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumesPerfRequest.ProtoReflect.Descriptor instead.
func (*VolumesPerfRequest) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{2}
}

func (x *VolumesPerfRequest) GetVolumeIdList() [][]byte {
	if x != nil {
		return x.VolumeIdList
	}
	return nil
}

type VolumesPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PerfList []*VolumePerf `protobuf:"bytes,1,rep,name=perf_list,json=perfList" json:"perf_list,omitempty"`
}

func (x *VolumesPerf) Reset() {
	*x = VolumesPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumesPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumesPerf) ProtoMessage() {}

func (x *VolumesPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumesPerf.ProtoReflect.Descriptor instead.
func (*VolumesPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{3}
}

func (x *VolumesPerf) GetPerfList() []*VolumePerf {
	if x != nil {
		return x.PerfList
	}
	return nil
}

type ProbeVolumesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeIdList   [][]byte  `protobuf:"bytes,1,rep,name=volume_id_list,json=volumeIdList" json:"volume_id_list,omitempty"`
	LatencyBuckets []float64 `protobuf:"fixed64,2,rep,name=latency_buckets,json=latencyBuckets" json:"latency_buckets,omitempty"`
	SizeBuckets    []float64 `protobuf:"fixed64,3,rep,name=size_buckets,json=sizeBuckets" json:"size_buckets,omitempty"`
}

func (x *ProbeVolumesRequest) Reset() {
	*x = ProbeVolumesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProbeVolumesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProbeVolumesRequest) ProtoMessage() {}

func (x *ProbeVolumesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProbeVolumesRequest.ProtoReflect.Descriptor instead.
func (*ProbeVolumesRequest) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{4}
}

func (x *ProbeVolumesRequest) GetVolumeIdList() [][]byte {
	if x != nil {
		return x.VolumeIdList
	}
	return nil
}

func (x *ProbeVolumesRequest) GetLatencyBuckets() []float64 {
	if x != nil {
		return x.LatencyBuckets
	}
	return nil
}

func (x *ProbeVolumesRequest) GetSizeBuckets() []float64 {
	if x != nil {
		return x.SizeBuckets
	}
	return nil
}

type DisableProbeVolumesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeIdList [][]byte `protobuf:"bytes,1,rep,name=volume_id_list,json=volumeIdList" json:"volume_id_list,omitempty"`
	DisableAll   *bool    `protobuf:"varint,2,opt,name=disable_all,json=disableAll,def=0" json:"disable_all,omitempty"`
}

// Default values for DisableProbeVolumesRequest fields.
const (
	Default_DisableProbeVolumesRequest_DisableAll = bool(false)
)

func (x *DisableProbeVolumesRequest) Reset() {
	*x = DisableProbeVolumesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableProbeVolumesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableProbeVolumesRequest) ProtoMessage() {}

func (x *DisableProbeVolumesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableProbeVolumesRequest.ProtoReflect.Descriptor instead.
func (*DisableProbeVolumesRequest) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{5}
}

func (x *DisableProbeVolumesRequest) GetVolumeIdList() [][]byte {
	if x != nil {
		return x.VolumeIdList
	}
	return nil
}

func (x *DisableProbeVolumesRequest) GetDisableAll() bool {
	if x != nil && x.DisableAll != nil {
		return *x.DisableAll
	}
	return Default_DisableProbeVolumesRequest_DisableAll
}

type UIOPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReadwriteIops           *float32 `protobuf:"fixed32,1,opt,name=readwrite_iops,json=readwriteIops" json:"readwrite_iops,omitempty"`
	ReadIops                *float32 `protobuf:"fixed32,2,opt,name=read_iops,json=readIops" json:"read_iops,omitempty"`
	WriteIops               *float32 `protobuf:"fixed32,3,opt,name=write_iops,json=writeIops" json:"write_iops,omitempty"`
	ReadwriteSpeedBps       *float32 `protobuf:"fixed32,4,opt,name=readwrite_speed_bps,json=readwriteSpeedBps" json:"readwrite_speed_bps,omitempty"`
	ReadSpeedBps            *float32 `protobuf:"fixed32,5,opt,name=read_speed_bps,json=readSpeedBps" json:"read_speed_bps,omitempty"`
	WriteSpeedBps           *float32 `protobuf:"fixed32,6,opt,name=write_speed_bps,json=writeSpeedBps" json:"write_speed_bps,omitempty"`
	ReadwriteLatency        *float32 `protobuf:"fixed32,7,opt,name=readwrite_latency,json=readwriteLatency" json:"readwrite_latency,omitempty"`
	ReadLatency             *float32 `protobuf:"fixed32,8,opt,name=read_latency,json=readLatency" json:"read_latency,omitempty"`
	WriteLatency            *float32 `protobuf:"fixed32,9,opt,name=write_latency,json=writeLatency" json:"write_latency,omitempty"`
	LocalReadwriteIops      *float32 `protobuf:"fixed32,40,opt,name=local_readwrite_iops,json=localReadwriteIops" json:"local_readwrite_iops,omitempty"`
	LocalReadIops           *float32 `protobuf:"fixed32,41,opt,name=local_read_iops,json=localReadIops" json:"local_read_iops,omitempty"`
	LocalWriteIops          *float32 `protobuf:"fixed32,42,opt,name=local_write_iops,json=localWriteIops" json:"local_write_iops,omitempty"`
	LocalReadwriteSpeedBps  *float32 `protobuf:"fixed32,43,opt,name=local_readwrite_speed_bps,json=localReadwriteSpeedBps" json:"local_readwrite_speed_bps,omitempty"`
	LocalReadSpeedBps       *float32 `protobuf:"fixed32,44,opt,name=local_read_speed_bps,json=localReadSpeedBps" json:"local_read_speed_bps,omitempty"`
	LocalWriteSpeedBps      *float32 `protobuf:"fixed32,45,opt,name=local_write_speed_bps,json=localWriteSpeedBps" json:"local_write_speed_bps,omitempty"`
	LocalReadwriteLatency   *float32 `protobuf:"fixed32,46,opt,name=local_readwrite_latency,json=localReadwriteLatency" json:"local_readwrite_latency,omitempty"`
	LocalReadLatency        *float32 `protobuf:"fixed32,47,opt,name=local_read_latency,json=localReadLatency" json:"local_read_latency,omitempty"`
	LocalWriteLatency       *float32 `protobuf:"fixed32,48,opt,name=local_write_latency,json=localWriteLatency" json:"local_write_latency,omitempty"`
	RetryReadwriteIops      *float32 `protobuf:"fixed32,80,opt,name=retry_readwrite_iops,json=retryReadwriteIops" json:"retry_readwrite_iops,omitempty"`
	RetryReadIops           *float32 `protobuf:"fixed32,81,opt,name=retry_read_iops,json=retryReadIops" json:"retry_read_iops,omitempty"`
	RetryWriteIops          *float32 `protobuf:"fixed32,82,opt,name=retry_write_iops,json=retryWriteIops" json:"retry_write_iops,omitempty"`
	FailedReadwriteIops     *float32 `protobuf:"fixed32,120,opt,name=failed_readwrite_iops,json=failedReadwriteIops" json:"failed_readwrite_iops,omitempty"`
	FailedWriteIops         *float32 `protobuf:"fixed32,121,opt,name=failed_write_iops,json=failedWriteIops" json:"failed_write_iops,omitempty"`
	FailedReadIops          *float32 `protobuf:"fixed32,122,opt,name=failed_read_iops,json=failedReadIops" json:"failed_read_iops,omitempty"`
	RetryQueueReadwriteSize *uint64  `protobuf:"varint,123,opt,name=retry_queue_readwrite_size,json=retryQueueReadwriteSize" json:"retry_queue_readwrite_size,omitempty"`
	RetryQueueReadSize      *uint64  `protobuf:"varint,124,opt,name=retry_queue_read_size,json=retryQueueReadSize" json:"retry_queue_read_size,omitempty"`
	RetryQueueWriteSize     *uint64  `protobuf:"varint,125,opt,name=retry_queue_write_size,json=retryQueueWriteSize" json:"retry_queue_write_size,omitempty"`
	ActiveVolumesSize       *uint64  `protobuf:"varint,161,opt,name=active_volumes_size,json=activeVolumesSize" json:"active_volumes_size,omitempty"`
	ActiveExtentsSize       *uint64  `protobuf:"varint,162,opt,name=active_extents_size,json=activeExtentsSize" json:"active_extents_size,omitempty"`
	WaitingQueueSize        *uint64  `protobuf:"varint,163,opt,name=waiting_queue_size,json=waitingQueueSize" json:"waiting_queue_size,omitempty"`
}

func (x *UIOPerf) Reset() {
	*x = UIOPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIOPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIOPerf) ProtoMessage() {}

func (x *UIOPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIOPerf.ProtoReflect.Descriptor instead.
func (*UIOPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{6}
}

func (x *UIOPerf) GetReadwriteIops() float32 {
	if x != nil && x.ReadwriteIops != nil {
		return *x.ReadwriteIops
	}
	return 0
}

func (x *UIOPerf) GetReadIops() float32 {
	if x != nil && x.ReadIops != nil {
		return *x.ReadIops
	}
	return 0
}

func (x *UIOPerf) GetWriteIops() float32 {
	if x != nil && x.WriteIops != nil {
		return *x.WriteIops
	}
	return 0
}

func (x *UIOPerf) GetReadwriteSpeedBps() float32 {
	if x != nil && x.ReadwriteSpeedBps != nil {
		return *x.ReadwriteSpeedBps
	}
	return 0
}

func (x *UIOPerf) GetReadSpeedBps() float32 {
	if x != nil && x.ReadSpeedBps != nil {
		return *x.ReadSpeedBps
	}
	return 0
}

func (x *UIOPerf) GetWriteSpeedBps() float32 {
	if x != nil && x.WriteSpeedBps != nil {
		return *x.WriteSpeedBps
	}
	return 0
}

func (x *UIOPerf) GetReadwriteLatency() float32 {
	if x != nil && x.ReadwriteLatency != nil {
		return *x.ReadwriteLatency
	}
	return 0
}

func (x *UIOPerf) GetReadLatency() float32 {
	if x != nil && x.ReadLatency != nil {
		return *x.ReadLatency
	}
	return 0
}

func (x *UIOPerf) GetWriteLatency() float32 {
	if x != nil && x.WriteLatency != nil {
		return *x.WriteLatency
	}
	return 0
}

func (x *UIOPerf) GetLocalReadwriteIops() float32 {
	if x != nil && x.LocalReadwriteIops != nil {
		return *x.LocalReadwriteIops
	}
	return 0
}

func (x *UIOPerf) GetLocalReadIops() float32 {
	if x != nil && x.LocalReadIops != nil {
		return *x.LocalReadIops
	}
	return 0
}

func (x *UIOPerf) GetLocalWriteIops() float32 {
	if x != nil && x.LocalWriteIops != nil {
		return *x.LocalWriteIops
	}
	return 0
}

func (x *UIOPerf) GetLocalReadwriteSpeedBps() float32 {
	if x != nil && x.LocalReadwriteSpeedBps != nil {
		return *x.LocalReadwriteSpeedBps
	}
	return 0
}

func (x *UIOPerf) GetLocalReadSpeedBps() float32 {
	if x != nil && x.LocalReadSpeedBps != nil {
		return *x.LocalReadSpeedBps
	}
	return 0
}

func (x *UIOPerf) GetLocalWriteSpeedBps() float32 {
	if x != nil && x.LocalWriteSpeedBps != nil {
		return *x.LocalWriteSpeedBps
	}
	return 0
}

func (x *UIOPerf) GetLocalReadwriteLatency() float32 {
	if x != nil && x.LocalReadwriteLatency != nil {
		return *x.LocalReadwriteLatency
	}
	return 0
}

func (x *UIOPerf) GetLocalReadLatency() float32 {
	if x != nil && x.LocalReadLatency != nil {
		return *x.LocalReadLatency
	}
	return 0
}

func (x *UIOPerf) GetLocalWriteLatency() float32 {
	if x != nil && x.LocalWriteLatency != nil {
		return *x.LocalWriteLatency
	}
	return 0
}

func (x *UIOPerf) GetRetryReadwriteIops() float32 {
	if x != nil && x.RetryReadwriteIops != nil {
		return *x.RetryReadwriteIops
	}
	return 0
}

func (x *UIOPerf) GetRetryReadIops() float32 {
	if x != nil && x.RetryReadIops != nil {
		return *x.RetryReadIops
	}
	return 0
}

func (x *UIOPerf) GetRetryWriteIops() float32 {
	if x != nil && x.RetryWriteIops != nil {
		return *x.RetryWriteIops
	}
	return 0
}

func (x *UIOPerf) GetFailedReadwriteIops() float32 {
	if x != nil && x.FailedReadwriteIops != nil {
		return *x.FailedReadwriteIops
	}
	return 0
}

func (x *UIOPerf) GetFailedWriteIops() float32 {
	if x != nil && x.FailedWriteIops != nil {
		return *x.FailedWriteIops
	}
	return 0
}

func (x *UIOPerf) GetFailedReadIops() float32 {
	if x != nil && x.FailedReadIops != nil {
		return *x.FailedReadIops
	}
	return 0
}

func (x *UIOPerf) GetRetryQueueReadwriteSize() uint64 {
	if x != nil && x.RetryQueueReadwriteSize != nil {
		return *x.RetryQueueReadwriteSize
	}
	return 0
}

func (x *UIOPerf) GetRetryQueueReadSize() uint64 {
	if x != nil && x.RetryQueueReadSize != nil {
		return *x.RetryQueueReadSize
	}
	return 0
}

func (x *UIOPerf) GetRetryQueueWriteSize() uint64 {
	if x != nil && x.RetryQueueWriteSize != nil {
		return *x.RetryQueueWriteSize
	}
	return 0
}

func (x *UIOPerf) GetActiveVolumesSize() uint64 {
	if x != nil && x.ActiveVolumesSize != nil {
		return *x.ActiveVolumesSize
	}
	return 0
}

func (x *UIOPerf) GetActiveExtentsSize() uint64 {
	if x != nil && x.ActiveExtentsSize != nil {
		return *x.ActiveExtentsSize
	}
	return 0
}

func (x *UIOPerf) GetWaitingQueueSize() uint64 {
	if x != nil && x.WaitingQueueSize != nil {
		return *x.WaitingQueueSize
	}
	return 0
}

type LayeredAccessPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessPerf                      *AccessPerf `protobuf:"bytes,1,opt,name=access_perf,json=accessPerf" json:"access_perf,omitempty"` // app + sink + reposition
	AccessCapReplicaAppPerf         *AccessPerf `protobuf:"bytes,2,opt,name=access_cap_replica_app_perf,json=accessCapReplicaAppPerf" json:"access_cap_replica_app_perf,omitempty"`
	AccessPerfReplicaAppPerf        *AccessPerf `protobuf:"bytes,3,opt,name=access_perf_replica_app_perf,json=accessPerfReplicaAppPerf" json:"access_perf_replica_app_perf,omitempty"`
	AccessCapEcAppPerf              *AccessPerf `protobuf:"bytes,4,opt,name=access_cap_ec_app_perf,json=accessCapEcAppPerf" json:"access_cap_ec_app_perf,omitempty"`
	AccessCapReplicaSinkPerf        *AccessPerf `protobuf:"bytes,5,opt,name=access_cap_replica_sink_perf,json=accessCapReplicaSinkPerf" json:"access_cap_replica_sink_perf,omitempty"`
	AccessPerfReplicaSinkPerf       *AccessPerf `protobuf:"bytes,6,opt,name=access_perf_replica_sink_perf,json=accessPerfReplicaSinkPerf" json:"access_perf_replica_sink_perf,omitempty"`
	AccessCapEcSinkPerf             *AccessPerf `protobuf:"bytes,7,opt,name=access_cap_ec_sink_perf,json=accessCapEcSinkPerf" json:"access_cap_ec_sink_perf,omitempty"`
	AccessCapReplicaRepositionPerf  *AccessPerf `protobuf:"bytes,8,opt,name=access_cap_replica_reposition_perf,json=accessCapReplicaRepositionPerf" json:"access_cap_replica_reposition_perf,omitempty"`
	AccessPerfReplicaRepositionPerf *AccessPerf `protobuf:"bytes,9,opt,name=access_perf_replica_reposition_perf,json=accessPerfReplicaRepositionPerf" json:"access_perf_replica_reposition_perf,omitempty"`
	AccessCapEcRepositionPerf       *AccessPerf `protobuf:"bytes,10,opt,name=access_cap_ec_reposition_perf,json=accessCapEcRepositionPerf" json:"access_cap_ec_reposition_perf,omitempty"`
}

func (x *LayeredAccessPerf) Reset() {
	*x = LayeredAccessPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LayeredAccessPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LayeredAccessPerf) ProtoMessage() {}

func (x *LayeredAccessPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LayeredAccessPerf.ProtoReflect.Descriptor instead.
func (*LayeredAccessPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{7}
}

func (x *LayeredAccessPerf) GetAccessPerf() *AccessPerf {
	if x != nil {
		return x.AccessPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessCapReplicaAppPerf() *AccessPerf {
	if x != nil {
		return x.AccessCapReplicaAppPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessPerfReplicaAppPerf() *AccessPerf {
	if x != nil {
		return x.AccessPerfReplicaAppPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessCapEcAppPerf() *AccessPerf {
	if x != nil {
		return x.AccessCapEcAppPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessCapReplicaSinkPerf() *AccessPerf {
	if x != nil {
		return x.AccessCapReplicaSinkPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessPerfReplicaSinkPerf() *AccessPerf {
	if x != nil {
		return x.AccessPerfReplicaSinkPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessCapEcSinkPerf() *AccessPerf {
	if x != nil {
		return x.AccessCapEcSinkPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessCapReplicaRepositionPerf() *AccessPerf {
	if x != nil {
		return x.AccessCapReplicaRepositionPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessPerfReplicaRepositionPerf() *AccessPerf {
	if x != nil {
		return x.AccessPerfReplicaRepositionPerf
	}
	return nil
}

func (x *LayeredAccessPerf) GetAccessCapEcRepositionPerf() *AccessPerf {
	if x != nil {
		return x.AccessCapEcRepositionPerf
	}
	return nil
}

type FlowControllerChunkPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FcCid                   *uint32 `protobuf:"varint,1,req,name=fc_cid,json=fcCid" json:"fc_cid,omitempty"`
	FmCid                   *uint32 `protobuf:"varint,2,req,name=fm_cid,json=fmCid" json:"fm_cid,omitempty"`
	DstPerfThinNotFreeRatio *uint32 `protobuf:"varint,3,opt,name=dst_perf_thin_not_free_ratio,json=dstPerfThinNotFreeRatio" json:"dst_perf_thin_not_free_ratio,omitempty"`
	// tokens info
	CurrentAvailTokens         *uint32 `protobuf:"varint,4,opt,name=current_avail_tokens,json=currentAvailTokens" json:"current_avail_tokens,omitempty"`
	UsedTokensNoWaitLastSec    *uint32 `protobuf:"varint,5,opt,name=used_tokens_no_wait_last_sec,json=usedTokensNoWaitLastSec" json:"used_tokens_no_wait_last_sec,omitempty"`
	UsedTokensAfterWaitLastSec *uint32 `protobuf:"varint,6,opt,name=used_tokens_after_wait_last_sec,json=usedTokensAfterWaitLastSec" json:"used_tokens_after_wait_last_sec,omitempty"`
	OverUsedTokensLastSec      *uint32 `protobuf:"varint,7,opt,name=over_used_tokens_last_sec,json=overUsedTokensLastSec" json:"over_used_tokens_last_sec,omitempty"`
	AvgWaitTokenLatency        *uint32 `protobuf:"varint,8,opt,name=avg_wait_token_latency,json=avgWaitTokenLatency" json:"avg_wait_token_latency,omitempty"`
	AvgWaitTokenNum            *uint32 `protobuf:"varint,9,opt,name=avg_wait_token_num,json=avgWaitTokenNum" json:"avg_wait_token_num,omitempty"`
}

func (x *FlowControllerChunkPerf) Reset() {
	*x = FlowControllerChunkPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowControllerChunkPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowControllerChunkPerf) ProtoMessage() {}

func (x *FlowControllerChunkPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowControllerChunkPerf.ProtoReflect.Descriptor instead.
func (*FlowControllerChunkPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{8}
}

func (x *FlowControllerChunkPerf) GetFcCid() uint32 {
	if x != nil && x.FcCid != nil {
		return *x.FcCid
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetFmCid() uint32 {
	if x != nil && x.FmCid != nil {
		return *x.FmCid
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetDstPerfThinNotFreeRatio() uint32 {
	if x != nil && x.DstPerfThinNotFreeRatio != nil {
		return *x.DstPerfThinNotFreeRatio
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetCurrentAvailTokens() uint32 {
	if x != nil && x.CurrentAvailTokens != nil {
		return *x.CurrentAvailTokens
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetUsedTokensNoWaitLastSec() uint32 {
	if x != nil && x.UsedTokensNoWaitLastSec != nil {
		return *x.UsedTokensNoWaitLastSec
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetUsedTokensAfterWaitLastSec() uint32 {
	if x != nil && x.UsedTokensAfterWaitLastSec != nil {
		return *x.UsedTokensAfterWaitLastSec
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetOverUsedTokensLastSec() uint32 {
	if x != nil && x.OverUsedTokensLastSec != nil {
		return *x.OverUsedTokensLastSec
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetAvgWaitTokenLatency() uint32 {
	if x != nil && x.AvgWaitTokenLatency != nil {
		return *x.AvgWaitTokenLatency
	}
	return 0
}

func (x *FlowControllerChunkPerf) GetAvgWaitTokenNum() uint32 {
	if x != nil && x.AvgWaitTokenNum != nil {
		return *x.AvgWaitTokenNum
	}
	return 0
}

type FlowControllerPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FcChunkPerf []*FlowControllerChunkPerf `protobuf:"bytes,1,rep,name=fc_chunk_perf,json=fcChunkPerf" json:"fc_chunk_perf,omitempty"`
}

func (x *FlowControllerPerf) Reset() {
	*x = FlowControllerPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowControllerPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowControllerPerf) ProtoMessage() {}

func (x *FlowControllerPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowControllerPerf.ProtoReflect.Descriptor instead.
func (*FlowControllerPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{9}
}

func (x *FlowControllerPerf) GetFcChunkPerf() []*FlowControllerChunkPerf {
	if x != nil {
		return x.FcChunkPerf
	}
	return nil
}

type FlowManagerChunkPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FcCid *uint32 `protobuf:"varint,1,req,name=fc_cid,json=fcCid" json:"fc_cid,omitempty"`
	FmCid *uint32 `protobuf:"varint,2,req,name=fm_cid,json=fmCid" json:"fm_cid,omitempty"`
	// tokens info
	RequestedTokensLastSec *uint32 `protobuf:"varint,3,opt,name=requested_tokens_last_sec,json=requestedTokensLastSec" json:"requested_tokens_last_sec,omitempty"`
	UsedTokensLastSec      *uint32 `protobuf:"varint,4,opt,name=used_tokens_last_sec,json=usedTokensLastSec" json:"used_tokens_last_sec,omitempty"`
	OverUsedTokensLastSec  *uint32 `protobuf:"varint,5,opt,name=over_used_tokens_last_sec,json=overUsedTokensLastSec" json:"over_used_tokens_last_sec,omitempty"`
}

func (x *FlowManagerChunkPerf) Reset() {
	*x = FlowManagerChunkPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowManagerChunkPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowManagerChunkPerf) ProtoMessage() {}

func (x *FlowManagerChunkPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowManagerChunkPerf.ProtoReflect.Descriptor instead.
func (*FlowManagerChunkPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{10}
}

func (x *FlowManagerChunkPerf) GetFcCid() uint32 {
	if x != nil && x.FcCid != nil {
		return *x.FcCid
	}
	return 0
}

func (x *FlowManagerChunkPerf) GetFmCid() uint32 {
	if x != nil && x.FmCid != nil {
		return *x.FmCid
	}
	return 0
}

func (x *FlowManagerChunkPerf) GetRequestedTokensLastSec() uint32 {
	if x != nil && x.RequestedTokensLastSec != nil {
		return *x.RequestedTokensLastSec
	}
	return 0
}

func (x *FlowManagerChunkPerf) GetUsedTokensLastSec() uint32 {
	if x != nil && x.UsedTokensLastSec != nil {
		return *x.UsedTokensLastSec
	}
	return 0
}

func (x *FlowManagerChunkPerf) GetOverUsedTokensLastSec() uint32 {
	if x != nil && x.OverUsedTokensLastSec != nil {
		return *x.OverUsedTokensLastSec
	}
	return 0
}

type FlowManagerPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlowControlEnable *bool                   `protobuf:"varint,1,opt,name=flow_control_enable,json=flowControlEnable" json:"flow_control_enable,omitempty"`
	AvailTokens       *uint32                 `protobuf:"varint,2,opt,name=avail_tokens,json=availTokens" json:"avail_tokens,omitempty"`
	PerfThinUsedRatio *uint32                 `protobuf:"varint,3,opt,name=perf_thin_used_ratio,json=perfThinUsedRatio" json:"perf_thin_used_ratio,omitempty"`
	FmChunkPerf       []*FlowManagerChunkPerf `protobuf:"bytes,4,rep,name=fm_chunk_perf,json=fmChunkPerf" json:"fm_chunk_perf,omitempty"`
	PerfThinFreeRatio *uint32                 `protobuf:"varint,5,opt,name=perf_thin_free_ratio,json=perfThinFreeRatio" json:"perf_thin_free_ratio,omitempty"`
}

func (x *FlowManagerPerf) Reset() {
	*x = FlowManagerPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowManagerPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowManagerPerf) ProtoMessage() {}

func (x *FlowManagerPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowManagerPerf.ProtoReflect.Descriptor instead.
func (*FlowManagerPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{11}
}

func (x *FlowManagerPerf) GetFlowControlEnable() bool {
	if x != nil && x.FlowControlEnable != nil {
		return *x.FlowControlEnable
	}
	return false
}

func (x *FlowManagerPerf) GetAvailTokens() uint32 {
	if x != nil && x.AvailTokens != nil {
		return *x.AvailTokens
	}
	return 0
}

func (x *FlowManagerPerf) GetPerfThinUsedRatio() uint32 {
	if x != nil && x.PerfThinUsedRatio != nil {
		return *x.PerfThinUsedRatio
	}
	return 0
}

func (x *FlowManagerPerf) GetFmChunkPerf() []*FlowManagerChunkPerf {
	if x != nil {
		return x.FmChunkPerf
	}
	return nil
}

func (x *FlowManagerPerf) GetPerfThinFreeRatio() uint32 {
	if x != nil && x.PerfThinFreeRatio != nil {
		return *x.PerfThinFreeRatio
	}
	return 0
}

type FlowControlPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlowMgrPerf  *FlowManagerPerf    `protobuf:"bytes,1,opt,name=flow_mgr_perf,json=flowMgrPerf" json:"flow_mgr_perf,omitempty"`
	FlowCtrlPerf *FlowControllerPerf `protobuf:"bytes,2,opt,name=flow_ctrl_perf,json=flowCtrlPerf" json:"flow_ctrl_perf,omitempty"`
	InstanceId   *uint32             `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for FlowControlPerf fields.
const (
	Default_FlowControlPerf_InstanceId = uint32(0)
)

func (x *FlowControlPerf) Reset() {
	*x = FlowControlPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowControlPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowControlPerf) ProtoMessage() {}

func (x *FlowControlPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowControlPerf.ProtoReflect.Descriptor instead.
func (*FlowControlPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{12}
}

func (x *FlowControlPerf) GetFlowMgrPerf() *FlowManagerPerf {
	if x != nil {
		return x.FlowMgrPerf
	}
	return nil
}

func (x *FlowControlPerf) GetFlowCtrlPerf() *FlowControllerPerf {
	if x != nil {
		return x.FlowCtrlPerf
	}
	return nil
}

func (x *FlowControlPerf) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_FlowControlPerf_InstanceId
}

type FlowControlPerfs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*FlowControlPerf `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *FlowControlPerfs) Reset() {
	*x = FlowControlPerfs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowControlPerfs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowControlPerfs) ProtoMessage() {}

func (x *FlowControlPerfs) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowControlPerfs.ProtoReflect.Descriptor instead.
func (*FlowControlPerfs) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{13}
}

func (x *FlowControlPerfs) GetInstancesResponse() []*FlowControlPerf {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type InternalFlowControllerTokenInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AvailTokenNum       *uint32 `protobuf:"varint,1,opt,name=avail_token_num,json=availTokenNum" json:"avail_token_num,omitempty"`
	RemainTokenNum      *uint32 `protobuf:"varint,2,opt,name=remain_token_num,json=remainTokenNum" json:"remain_token_num,omitempty"`
	WaitingIoNum        *uint32 `protobuf:"varint,3,opt,name=waiting_io_num,json=waitingIoNum" json:"waiting_io_num,omitempty"`
	NoWaitNumLastSec    *uint32 `protobuf:"varint,4,opt,name=no_wait_num_last_sec,json=noWaitNumLastSec" json:"no_wait_num_last_sec,omitempty"`
	OverWaitNumLastSec  *uint32 `protobuf:"varint,5,opt,name=over_wait_num_last_sec,json=overWaitNumLastSec" json:"over_wait_num_last_sec,omitempty"`
	AfterWaitNumLastSec *uint32 `protobuf:"varint,6,opt,name=after_wait_num_last_sec,json=afterWaitNumLastSec" json:"after_wait_num_last_sec,omitempty"`
	AvgWaitLatency      *uint32 `protobuf:"varint,7,opt,name=avg_wait_latency,json=avgWaitLatency" json:"avg_wait_latency,omitempty"`
}

func (x *InternalFlowControllerTokenInfo) Reset() {
	*x = InternalFlowControllerTokenInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowControllerTokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowControllerTokenInfo) ProtoMessage() {}

func (x *InternalFlowControllerTokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowControllerTokenInfo.ProtoReflect.Descriptor instead.
func (*InternalFlowControllerTokenInfo) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{14}
}

func (x *InternalFlowControllerTokenInfo) GetAvailTokenNum() uint32 {
	if x != nil && x.AvailTokenNum != nil {
		return *x.AvailTokenNum
	}
	return 0
}

func (x *InternalFlowControllerTokenInfo) GetRemainTokenNum() uint32 {
	if x != nil && x.RemainTokenNum != nil {
		return *x.RemainTokenNum
	}
	return 0
}

func (x *InternalFlowControllerTokenInfo) GetWaitingIoNum() uint32 {
	if x != nil && x.WaitingIoNum != nil {
		return *x.WaitingIoNum
	}
	return 0
}

func (x *InternalFlowControllerTokenInfo) GetNoWaitNumLastSec() uint32 {
	if x != nil && x.NoWaitNumLastSec != nil {
		return *x.NoWaitNumLastSec
	}
	return 0
}

func (x *InternalFlowControllerTokenInfo) GetOverWaitNumLastSec() uint32 {
	if x != nil && x.OverWaitNumLastSec != nil {
		return *x.OverWaitNumLastSec
	}
	return 0
}

func (x *InternalFlowControllerTokenInfo) GetAfterWaitNumLastSec() uint32 {
	if x != nil && x.AfterWaitNumLastSec != nil {
		return *x.AfterWaitNumLastSec
	}
	return 0
}

func (x *InternalFlowControllerTokenInfo) GetAvgWaitLatency() uint32 {
	if x != nil && x.AvgWaitLatency != nil {
		return *x.AvgWaitLatency
	}
	return 0
}

type InternalFlowControllerChunkPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IfmCid        *uint32                          `protobuf:"varint,1,req,name=ifm_cid,json=ifmCid" json:"ifm_cid,omitempty"`
	CapTokenInfo  *InternalFlowControllerTokenInfo `protobuf:"bytes,2,opt,name=cap_token_info,json=capTokenInfo" json:"cap_token_info,omitempty"`
	PerfTokenInfo *InternalFlowControllerTokenInfo `protobuf:"bytes,3,opt,name=perf_token_info,json=perfTokenInfo" json:"perf_token_info,omitempty"`
}

func (x *InternalFlowControllerChunkPerf) Reset() {
	*x = InternalFlowControllerChunkPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowControllerChunkPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowControllerChunkPerf) ProtoMessage() {}

func (x *InternalFlowControllerChunkPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowControllerChunkPerf.ProtoReflect.Descriptor instead.
func (*InternalFlowControllerChunkPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{15}
}

func (x *InternalFlowControllerChunkPerf) GetIfmCid() uint32 {
	if x != nil && x.IfmCid != nil {
		return *x.IfmCid
	}
	return 0
}

func (x *InternalFlowControllerChunkPerf) GetCapTokenInfo() *InternalFlowControllerTokenInfo {
	if x != nil {
		return x.CapTokenInfo
	}
	return nil
}

func (x *InternalFlowControllerChunkPerf) GetPerfTokenInfo() *InternalFlowControllerTokenInfo {
	if x != nil {
		return x.PerfTokenInfo
	}
	return nil
}

type InternalFlowControllerPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IfcEnable        *bool                              `protobuf:"varint,1,opt,name=ifc_enable,json=ifcEnable" json:"ifc_enable,omitempty"`
	IfcCid           *uint32                            `protobuf:"varint,2,opt,name=ifc_cid,json=ifcCid" json:"ifc_cid,omitempty"`
	RequestChunkNum  *uint32                            `protobuf:"varint,3,opt,name=request_chunk_num,json=requestChunkNum" json:"request_chunk_num,omitempty"`
	HighWaitingIoNum *uint32                            `protobuf:"varint,4,opt,name=high_waiting_io_num,json=highWaitingIoNum" json:"high_waiting_io_num,omitempty"`
	MidWaitingIoNum  *uint32                            `protobuf:"varint,5,opt,name=mid_waiting_io_num,json=midWaitingIoNum" json:"mid_waiting_io_num,omitempty"`
	LowWaitingIoNum  *uint32                            `protobuf:"varint,6,opt,name=low_waiting_io_num,json=lowWaitingIoNum" json:"low_waiting_io_num,omitempty"`
	IfcChunkPerf     []*InternalFlowControllerChunkPerf `protobuf:"bytes,7,rep,name=ifc_chunk_perf,json=ifcChunkPerf" json:"ifc_chunk_perf,omitempty"`
}

func (x *InternalFlowControllerPerf) Reset() {
	*x = InternalFlowControllerPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowControllerPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowControllerPerf) ProtoMessage() {}

func (x *InternalFlowControllerPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowControllerPerf.ProtoReflect.Descriptor instead.
func (*InternalFlowControllerPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{16}
}

func (x *InternalFlowControllerPerf) GetIfcEnable() bool {
	if x != nil && x.IfcEnable != nil {
		return *x.IfcEnable
	}
	return false
}

func (x *InternalFlowControllerPerf) GetIfcCid() uint32 {
	if x != nil && x.IfcCid != nil {
		return *x.IfcCid
	}
	return 0
}

func (x *InternalFlowControllerPerf) GetRequestChunkNum() uint32 {
	if x != nil && x.RequestChunkNum != nil {
		return *x.RequestChunkNum
	}
	return 0
}

func (x *InternalFlowControllerPerf) GetHighWaitingIoNum() uint32 {
	if x != nil && x.HighWaitingIoNum != nil {
		return *x.HighWaitingIoNum
	}
	return 0
}

func (x *InternalFlowControllerPerf) GetMidWaitingIoNum() uint32 {
	if x != nil && x.MidWaitingIoNum != nil {
		return *x.MidWaitingIoNum
	}
	return 0
}

func (x *InternalFlowControllerPerf) GetLowWaitingIoNum() uint32 {
	if x != nil && x.LowWaitingIoNum != nil {
		return *x.LowWaitingIoNum
	}
	return 0
}

func (x *InternalFlowControllerPerf) GetIfcChunkPerf() []*InternalFlowControllerChunkPerf {
	if x != nil {
		return x.IfcChunkPerf
	}
	return nil
}

type InternalFlowManagerTokenInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqNumLastSec      *uint32 `protobuf:"varint,1,opt,name=req_num_last_sec,json=reqNumLastSec" json:"req_num_last_sec,omitempty"`
	UsedNumLastSec     *uint32 `protobuf:"varint,2,opt,name=used_num_last_sec,json=usedNumLastSec" json:"used_num_last_sec,omitempty"`
	OverUsedNumLastSec *uint32 `protobuf:"varint,3,opt,name=over_used_num_last_sec,json=overUsedNumLastSec" json:"over_used_num_last_sec,omitempty"`
}

func (x *InternalFlowManagerTokenInfo) Reset() {
	*x = InternalFlowManagerTokenInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowManagerTokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowManagerTokenInfo) ProtoMessage() {}

func (x *InternalFlowManagerTokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowManagerTokenInfo.ProtoReflect.Descriptor instead.
func (*InternalFlowManagerTokenInfo) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{17}
}

func (x *InternalFlowManagerTokenInfo) GetReqNumLastSec() uint32 {
	if x != nil && x.ReqNumLastSec != nil {
		return *x.ReqNumLastSec
	}
	return 0
}

func (x *InternalFlowManagerTokenInfo) GetUsedNumLastSec() uint32 {
	if x != nil && x.UsedNumLastSec != nil {
		return *x.UsedNumLastSec
	}
	return 0
}

func (x *InternalFlowManagerTokenInfo) GetOverUsedNumLastSec() uint32 {
	if x != nil && x.OverUsedNumLastSec != nil {
		return *x.OverUsedNumLastSec
	}
	return 0
}

type InternalFlowManagerChunkPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IfcCid        *uint32                       `protobuf:"varint,1,req,name=ifc_cid,json=ifcCid" json:"ifc_cid,omitempty"`
	CapTokenInfo  *InternalFlowManagerTokenInfo `protobuf:"bytes,2,opt,name=cap_token_info,json=capTokenInfo" json:"cap_token_info,omitempty"`
	PerfTokenInfo *InternalFlowManagerTokenInfo `protobuf:"bytes,3,opt,name=perf_token_info,json=perfTokenInfo" json:"perf_token_info,omitempty"`
}

func (x *InternalFlowManagerChunkPerf) Reset() {
	*x = InternalFlowManagerChunkPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowManagerChunkPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowManagerChunkPerf) ProtoMessage() {}

func (x *InternalFlowManagerChunkPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowManagerChunkPerf.ProtoReflect.Descriptor instead.
func (*InternalFlowManagerChunkPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{18}
}

func (x *InternalFlowManagerChunkPerf) GetIfcCid() uint32 {
	if x != nil && x.IfcCid != nil {
		return *x.IfcCid
	}
	return 0
}

func (x *InternalFlowManagerChunkPerf) GetCapTokenInfo() *InternalFlowManagerTokenInfo {
	if x != nil {
		return x.CapTokenInfo
	}
	return nil
}

func (x *InternalFlowManagerChunkPerf) GetPerfTokenInfo() *InternalFlowManagerTokenInfo {
	if x != nil {
		return x.PerfTokenInfo
	}
	return nil
}

type InternalFlowManagerPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IfmEnable          *bool                           `protobuf:"varint,1,opt,name=ifm_enable,json=ifmEnable" json:"ifm_enable,omitempty"`
	IfmCid             *uint32                         `protobuf:"varint,2,opt,name=ifm_cid,json=ifmCid" json:"ifm_cid,omitempty"`
	CapAvailTokenNum   *uint32                         `protobuf:"varint,3,opt,name=cap_avail_token_num,json=capAvailTokenNum" json:"cap_avail_token_num,omitempty"`
	PerfAvailTokenNum  *uint32                         `protobuf:"varint,4,opt,name=perf_avail_token_num,json=perfAvailTokenNum" json:"perf_avail_token_num,omitempty"`
	CapRemainTokenNum  *uint32                         `protobuf:"varint,5,opt,name=cap_remain_token_num,json=capRemainTokenNum" json:"cap_remain_token_num,omitempty"`
	PerfRemainTokenNum *uint32                         `protobuf:"varint,6,opt,name=perf_remain_token_num,json=perfRemainTokenNum" json:"perf_remain_token_num,omitempty"`
	IfmChunkPerf       []*InternalFlowManagerChunkPerf `protobuf:"bytes,7,rep,name=ifm_chunk_perf,json=ifmChunkPerf" json:"ifm_chunk_perf,omitempty"`
}

func (x *InternalFlowManagerPerf) Reset() {
	*x = InternalFlowManagerPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowManagerPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowManagerPerf) ProtoMessage() {}

func (x *InternalFlowManagerPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowManagerPerf.ProtoReflect.Descriptor instead.
func (*InternalFlowManagerPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{19}
}

func (x *InternalFlowManagerPerf) GetIfmEnable() bool {
	if x != nil && x.IfmEnable != nil {
		return *x.IfmEnable
	}
	return false
}

func (x *InternalFlowManagerPerf) GetIfmCid() uint32 {
	if x != nil && x.IfmCid != nil {
		return *x.IfmCid
	}
	return 0
}

func (x *InternalFlowManagerPerf) GetCapAvailTokenNum() uint32 {
	if x != nil && x.CapAvailTokenNum != nil {
		return *x.CapAvailTokenNum
	}
	return 0
}

func (x *InternalFlowManagerPerf) GetPerfAvailTokenNum() uint32 {
	if x != nil && x.PerfAvailTokenNum != nil {
		return *x.PerfAvailTokenNum
	}
	return 0
}

func (x *InternalFlowManagerPerf) GetCapRemainTokenNum() uint32 {
	if x != nil && x.CapRemainTokenNum != nil {
		return *x.CapRemainTokenNum
	}
	return 0
}

func (x *InternalFlowManagerPerf) GetPerfRemainTokenNum() uint32 {
	if x != nil && x.PerfRemainTokenNum != nil {
		return *x.PerfRemainTokenNum
	}
	return 0
}

func (x *InternalFlowManagerPerf) GetIfmChunkPerf() []*InternalFlowManagerChunkPerf {
	if x != nil {
		return x.IfmChunkPerf
	}
	return nil
}

type InternalFlowControlPerf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IfmPerf    *InternalFlowManagerPerf    `protobuf:"bytes,1,opt,name=ifm_perf,json=ifmPerf" json:"ifm_perf,omitempty"`
	IfcPerf    *InternalFlowControllerPerf `protobuf:"bytes,2,opt,name=ifc_perf,json=ifcPerf" json:"ifc_perf,omitempty"`
	InstanceId *uint32                     `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for InternalFlowControlPerf fields.
const (
	Default_InternalFlowControlPerf_InstanceId = uint32(0)
)

func (x *InternalFlowControlPerf) Reset() {
	*x = InternalFlowControlPerf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowControlPerf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowControlPerf) ProtoMessage() {}

func (x *InternalFlowControlPerf) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowControlPerf.ProtoReflect.Descriptor instead.
func (*InternalFlowControlPerf) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{20}
}

func (x *InternalFlowControlPerf) GetIfmPerf() *InternalFlowManagerPerf {
	if x != nil {
		return x.IfmPerf
	}
	return nil
}

func (x *InternalFlowControlPerf) GetIfcPerf() *InternalFlowControllerPerf {
	if x != nil {
		return x.IfcPerf
	}
	return nil
}

func (x *InternalFlowControlPerf) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_InternalFlowControlPerf_InstanceId
}

type InternalFlowControlPerfs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*InternalFlowControlPerf `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *InternalFlowControlPerfs) Reset() {
	*x = InternalFlowControlPerfs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perf_rpc_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalFlowControlPerfs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalFlowControlPerfs) ProtoMessage() {}

func (x *InternalFlowControlPerfs) ProtoReflect() protoreflect.Message {
	mi := &file_perf_rpc_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalFlowControlPerfs.ProtoReflect.Descriptor instead.
func (*InternalFlowControlPerfs) Descriptor() ([]byte, []int) {
	return file_perf_rpc_proto_rawDescGZIP(), []int{21}
}

func (x *InternalFlowControlPerfs) GetInstancesResponse() []*InternalFlowControlPerf {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

var File_perf_rpc_proto protoreflect.FileDescriptor

var file_perf_rpc_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x03, 0x7a, 0x62, 0x73, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x7a, 0x62, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37, 0x0a, 0x11, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50,
	0x65, 0x72, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x09, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca,
	0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x22, 0xeb,
	0x16, 0x0a, 0x0a, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x66, 0x12, 0x22, 0x0a,
	0x09, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x42, 0x61, 0x6e, 0x64,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c, 0x61,
	0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x72, 0x65, 0x61,
	0x64, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x5f, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0e, 0x77, 0x72, 0x69, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x23, 0x0a, 0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61,
	0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69,
	0x6f, 0x70, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x49, 0x6f, 0x70, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x61,
	0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x23, 0x0a,
	0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x12, 0x72, 0x65, 0x61, 0x64, 0x41, 0x76, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x61,
	0x76, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x77, 0x72, 0x69, 0x74, 0x65, 0x41, 0x76, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x41, 0x76, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6f, 0x70, 0x33, 0x30, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6f, 0x70, 0x33,
	0x30, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6f, 0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x07, 0x69, 0x6f, 0x64, 0x65, 0x70, 0x74, 0x68, 0x12, 0x3b, 0x0a, 0x1a,
	0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x17, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x64,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x44, 0x0a, 0x1f, 0x69, 0x6f, 0x63,
	0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x1b, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65,
	0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12,
	0x41, 0x0a, 0x1d, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1a, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x31, 0x0a, 0x15, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x32, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x12, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61,
	0x64, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x3a, 0x0a, 0x1a, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f,
	0x62, 0x70, 0x73, 0x18, 0x33, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x69, 0x6f, 0x63, 0x74, 0x78,
	0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70,
	0x73, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x34, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x15, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x52,
	0x65, 0x61, 0x64, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x33, 0x0a, 0x16, 0x69, 0x6f,
	0x63, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f,
	0x69, 0x6f, 0x70, 0x73, 0x18, 0x46, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x69, 0x6f, 0x63, 0x74,
	0x78, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12,
	0x3c, 0x0a, 0x1b, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x47,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x17, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x57, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x39, 0x0a,
	0x19, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x48, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x16, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6f, 0x63, 0x74,
	0x78, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73,
	0x18, 0x5a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x52, 0x65, 0x61,
	0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x69, 0x6f,
	0x63, 0x74, 0x78, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x70,
	0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x69,
	0x6f, 0x63, 0x74, 0x78, 0x52, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x72,
	0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x5c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x52, 0x65, 0x61,
	0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x26, 0x0a,
	0x0f, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x6f, 0x70, 0x73,
	0x18, 0x6e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x52, 0x65, 0x61,
	0x64, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x72,
	0x65, 0x61, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x6f, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x11, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x52, 0x65, 0x61, 0x64, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f,
	0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x70, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x10, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x52, 0x65, 0x61, 0x64, 0x4c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x82, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0e, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x57, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12,
	0x32, 0x0a, 0x15, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x83, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x12, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x57, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x42, 0x70, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x5f, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x84, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x11, 0x69, 0x6f, 0x63, 0x74, 0x78, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x5b, 0x0a, 0x1b, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x18, 0xbe, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x18, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x12, 0x51, 0x0a, 0x16, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0xbf, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x13, 0x72, 0x65, 0x61, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x53, 0x0a, 0x17, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18,
	0xc0, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x52, 0x14, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x6e, 0x0a, 0x25, 0x72, 0x65, 0x61,
	0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x18, 0xc1, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x21, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x64, 0x0a, 0x20, 0x72, 0x65, 0x61,
	0x64, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0xc2, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x52, 0x1c, 0x72, 0x65, 0x61, 0x64, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12,
	0x66, 0x0a, 0x21, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c,
	0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x18, 0xc3, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x1d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c,
	0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x70, 0x0a, 0x26, 0x72, 0x65, 0x61, 0x64, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x18, 0xc4, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x22, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x66, 0x0a, 0x21, 0x72, 0x65, 0x61,
	0x64, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0xc6,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x52, 0x1d, 0x72, 0x65, 0x61, 0x64, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c,
	0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x12, 0x68, 0x0a, 0x22, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69,
	0x63, 0x61, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0xc7, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x1e, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x61, 0x0a, 0x1e, 0x72,
	0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0xc8, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x52, 0x1b, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x57,
	0x0a, 0x19, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0xc9, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x16, 0x72, 0x65, 0x61, 0x64, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x59, 0x0a, 0x1a, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0xca, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x17, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6f, 0x70, 0x73,
	0x18, 0xcd, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x49, 0x6f,
	0x70, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x5f, 0x75, 0x6e, 0x61, 0x6c,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0xce, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x12, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x55, 0x6e, 0x61, 0x6c, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0xcf, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x75, 0x6e, 0x6d,
	0x61, 0x70, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x33, 0x0a, 0x15, 0x75, 0x6e, 0x6d, 0x61, 0x70,
	0x5f, 0x75, 0x6e, 0x61, 0x6c, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0xd0, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x55, 0x6e,
	0x61, 0x6c, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x41, 0x0a, 0x12,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0e, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08,
	0x01, 0x52, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x3b, 0x0a, 0x0b, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x2c,
	0x0a, 0x09, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x66, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x8e, 0x01, 0x0a,
	0x13, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0e, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69,
	0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49,
	0x02, 0x08, 0x01, 0x52, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x6c, 0x61, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x01,
	0x52, 0x0b, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x22, 0x71, 0x0a,
	0x1a, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0e, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c,
	0x22, 0xdc, 0x0a, 0x0a, 0x07, 0x55, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x12, 0x25, 0x0a, 0x0e,
	0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x49,
	0x6f, 0x70, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x6f, 0x70, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x49, 0x6f, 0x70, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12,
	0x2e, 0x0a, 0x13, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x70, 0x65,
	0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x72, 0x65,
	0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12,
	0x24, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x2b, 0x0a,
	0x11, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x61, 0x64, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a,
	0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x28, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x49, 0x6f, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x61, 0x64, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x28, 0x0a, 0x10,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73,
	0x18, 0x2a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f,
	0x62, 0x70, 0x73, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x52, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x70,
	0x73, 0x12, 0x2f, 0x0a, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x11, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42,
	0x70, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x2d, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x42, 0x70, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x2e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61,
	0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x2c, 0x0a,
	0x12, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c, 0x61, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x52, 0x65, 0x61, 0x64, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x30, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x57,
	0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69,
	0x6f, 0x70, 0x73, 0x18, 0x50, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x52, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x6f, 0x70, 0x73,
	0x18, 0x51, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x61,
	0x64, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x52, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x57, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12,
	0x32, 0x0a, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x78, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x49,
	0x6f, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x79, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x57, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12,
	0x28, 0x0a, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69,
	0x6f, 0x70, 0x73, 0x18, 0x7a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x72, 0x65, 0x74,
	0x72, 0x79, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x7b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x17, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x7c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x72, 0x65, 0x74, 0x72, 0x79, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x52, 0x65, 0x61, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x72, 0x65, 0x74,
	0x72, 0x79, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x7d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x72, 0x69, 0x74, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2f,
	0x0a, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0xa1, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x2f, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74,
	0x73, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0xa2, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x2d, 0x0a, 0x12, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0xa3, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x77,
	0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0xa4, 0x06, 0x0a, 0x11, 0x4c, 0x61, 0x79, 0x65, 0x72, 0x65, 0x64, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x30, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x65, 0x72, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x0a, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x4d, 0x0a, 0x1b, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x17, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x41,
	0x70, 0x70, 0x50, 0x65, 0x72, 0x66, 0x12, 0x4f, 0x0a, 0x1c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x18, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x41, 0x70, 0x70, 0x50, 0x65, 0x72, 0x66, 0x12, 0x43, 0x0a, 0x16, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x65, 0x63, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72,
	0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x70, 0x45, 0x63, 0x41, 0x70, 0x70, 0x50, 0x65, 0x72, 0x66, 0x12, 0x4f, 0x0a, 0x1c,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x5f, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50,
	0x65, 0x72, 0x66, 0x52, 0x18, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x70, 0x52, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x53, 0x69, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x12, 0x51, 0x0a,
	0x1d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x72, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x5f, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x19, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72,
	0x66, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x53, 0x69, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66,
	0x12, 0x45, 0x0a, 0x17, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x65,
	0x63, 0x5f, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x72, 0x66, 0x52, 0x13, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x70, 0x45, 0x63, 0x53,
	0x69, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x12, 0x5b, 0x0a, 0x22, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x50, 0x65, 0x72, 0x66, 0x52, 0x1e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x70, 0x52,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x65, 0x72, 0x66, 0x12, 0x5d, 0x0a, 0x23, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70,
	0x65, 0x72, 0x66, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x72, 0x66, 0x52, 0x1f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x65, 0x72, 0x66, 0x12, 0x51, 0x0a, 0x1d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61,
	0x70, 0x5f, 0x65, 0x63, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x70, 0x65, 0x72, 0x66, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x52, 0x19, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x61, 0x70, 0x45, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x65, 0x72, 0x66, 0x22, 0xd8, 0x03, 0x0a, 0x17, 0x46, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65,
	0x72, 0x66, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x63, 0x5f, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0d, 0x52, 0x05, 0x66, 0x63, 0x43, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x6d, 0x5f,
	0x63, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x05, 0x66, 0x6d, 0x43, 0x69, 0x64,
	0x12, 0x3d, 0x0a, 0x1c, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x74, 0x68, 0x69,
	0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x64, 0x73, 0x74, 0x50, 0x65, 0x72, 0x66, 0x54,
	0x68, 0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12,
	0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x73, 0x12, 0x3d, 0x0a, 0x1c, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73,
	0x5f, 0x6e, 0x6f, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65,
	0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x75, 0x73, 0x65, 0x64, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x73, 0x4e, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63,
	0x12, 0x43, 0x0a, 0x1f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x5f,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x73, 0x65, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1a, 0x75, 0x73, 0x65, 0x64, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x41, 0x66, 0x74, 0x65, 0x72, 0x57, 0x61, 0x69, 0x74, 0x4c, 0x61,
	0x73, 0x74, 0x53, 0x65, 0x63, 0x12, 0x38, 0x0a, 0x19, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x73,
	0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73,
	0x65, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73,
	0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x12,
	0x33, 0x0a, 0x16, 0x61, 0x76, 0x67, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x13, 0x61, 0x76, 0x67, 0x57, 0x61, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x2b, 0x0a, 0x12, 0x61, 0x76, 0x67, 0x5f, 0x77, 0x61, 0x69, 0x74,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0f, 0x61, 0x76, 0x67, 0x57, 0x61, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x75,
	0x6d, 0x22, 0x56, 0x0a, 0x12, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x12, 0x40, 0x0a, 0x0d, 0x66, 0x63, 0x5f, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x52, 0x0b, 0x66, 0x63,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x22, 0xea, 0x01, 0x0a, 0x14, 0x46, 0x6c,
	0x6f, 0x77, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65,
	0x72, 0x66, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x63, 0x5f, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0d, 0x52, 0x05, 0x66, 0x63, 0x43, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x6d, 0x5f,
	0x63, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x05, 0x66, 0x6d, 0x43, 0x69, 0x64,
	0x12, 0x39, 0x0a, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x16, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x12, 0x2f, 0x0a, 0x14, 0x75,
	0x73, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x73, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x75, 0x73, 0x65, 0x64, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x12, 0x38, 0x0a, 0x19,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73,
	0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x15, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x4c,
	0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x22, 0x85, 0x02, 0x0a, 0x0f, 0x46, 0x6c, 0x6f, 0x77, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0b, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x2f, 0x0a,
	0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x70, 0x65, 0x72,
	0x66, 0x54, 0x68, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x64, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x3d,
	0x0a, 0x0d, 0x66, 0x6d, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x46, 0x6c, 0x6f, 0x77,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66,
	0x52, 0x0b, 0x66, 0x6d, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x12, 0x2f, 0x0a,
	0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x70, 0x65, 0x72,
	0x66, 0x54, 0x68, 0x69, 0x6e, 0x46, 0x72, 0x65, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0xae,
	0x01, 0x0a, 0x0f, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65,
	0x72, 0x66, 0x12, 0x38, 0x0a, 0x0d, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6d, 0x67, 0x72, 0x5f, 0x70,
	0x65, 0x72, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x46, 0x6c, 0x6f, 0x77, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x52,
	0x0b, 0x66, 0x6c, 0x6f, 0x77, 0x4d, 0x67, 0x72, 0x50, 0x65, 0x72, 0x66, 0x12, 0x3d, 0x0a, 0x0e,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x74, 0x72, 0x6c, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x52, 0x0c, 0x66,
	0x6c, 0x6f, 0x77, 0x43, 0x74, 0x72, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x12, 0x22, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d,
	0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22,
	0x57, 0x0a, 0x10, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65,
	0x72, 0x66, 0x73, 0x12, 0x43, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x50, 0x65, 0x72, 0x66, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xdd, 0x02, 0x0a, 0x1f, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x24,
	0x0a, 0x0e, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x49,
	0x6f, 0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x14, 0x6e, 0x6f, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x10, 0x6e, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x4e, 0x75, 0x6d, 0x4c, 0x61, 0x73,
	0x74, 0x53, 0x65, 0x63, 0x12, 0x32, 0x0a, 0x16, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x77, 0x61, 0x69,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x6f, 0x76, 0x65, 0x72, 0x57, 0x61, 0x69, 0x74, 0x4e, 0x75,
	0x6d, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x12, 0x34, 0x0a, 0x17, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x73, 0x65, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x57, 0x61, 0x69, 0x74, 0x4e, 0x75, 0x6d, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x12, 0x28,
	0x0a, 0x10, 0x61, 0x76, 0x67, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x76, 0x67, 0x57, 0x61, 0x69,
	0x74, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x22, 0xd4, 0x01, 0x0a, 0x1f, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x66, 0x6d, 0x5f, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06, 0x69,
	0x66, 0x6d, 0x43, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x4c, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0d, 0x70, 0x65, 0x72, 0x66, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0xd5, 0x02, 0x0a, 0x1a, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x66, 0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x66, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x66, 0x63, 0x5f, 0x63, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x69, 0x66, 0x63, 0x43, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4e,
	0x75, 0x6d, 0x12, 0x2d, 0x0a, 0x13, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x10, 0x68, 0x69, 0x67, 0x68, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6f, 0x4e, 0x75,
	0x6d, 0x12, 0x2b, 0x0a, 0x12, 0x6d, 0x69, 0x64, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x6d,
	0x69, 0x64, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6f, 0x4e, 0x75, 0x6d, 0x12, 0x2b,
	0x0a, 0x12, 0x6c, 0x6f, 0x77, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6f,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x6c, 0x6f, 0x77, 0x57,
	0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6f, 0x4e, 0x75, 0x6d, 0x12, 0x4a, 0x0a, 0x0e, 0x69,
	0x66, 0x63, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x52, 0x0c, 0x69, 0x66, 0x63, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x22, 0xa6, 0x01, 0x0a, 0x1c, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x5f,
	0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65,
	0x63, 0x12, 0x29, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x75, 0x73,
	0x65, 0x64, 0x4e, 0x75, 0x6d, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x12, 0x32, 0x0a, 0x16,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x6f, 0x76,
	0x65, 0x72, 0x55, 0x73, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63,
	0x22, 0xcb, 0x01, 0x0a, 0x1c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f,
	0x77, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72,
	0x66, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x66, 0x63, 0x5f, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0d, 0x52, 0x06, 0x69, 0x66, 0x63, 0x43, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x0e, 0x63, 0x61,
	0x70, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0d, 0x70, 0x65, 0x72, 0x66, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xde,
	0x02, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x66,
	0x6d, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x66, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x66, 0x6d,
	0x5f, 0x63, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x69, 0x66, 0x6d, 0x43,
	0x69, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x63, 0x61, 0x70, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x10, 0x63, 0x61, 0x70, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x75,
	0x6d, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x11, 0x70, 0x65, 0x72, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e,
	0x75, 0x6d, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61, 0x70, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x11, 0x63, 0x61, 0x70, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x4e, 0x75, 0x6d, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x72, 0x65, 0x6d, 0x61,
	0x69, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x12, 0x70, 0x65, 0x72, 0x66, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x47, 0x0a, 0x0e, 0x69, 0x66, 0x6d, 0x5f, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f,
	0x77, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72,
	0x66, 0x52, 0x0c, 0x69, 0x66, 0x6d, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x22,
	0xb2, 0x01, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x12, 0x37, 0x0a, 0x08, 0x69,
	0x66, 0x6d, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x52, 0x07, 0x69, 0x66, 0x6d,
	0x50, 0x65, 0x72, 0x66, 0x12, 0x3a, 0x0a, 0x08, 0x69, 0x66, 0x63, 0x5f, 0x70, 0x65, 0x72, 0x66,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x50, 0x65, 0x72, 0x66, 0x52, 0x07, 0x69, 0x66, 0x63, 0x50, 0x65, 0x72, 0x66,
	0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x18, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x73,
	0x12, 0x4b, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xba, 0x02,
	0x0a, 0x11, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x66, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x50, 0x65, 0x72, 0x66, 0x12, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x66, 0x12, 0x3b, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12,
	0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x50, 0x65, 0x72,
	0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x30, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12,
	0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x10, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x33, 0x0a, 0x0c,
	0x50, 0x72, 0x6f, 0x62, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x18, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69,
	0x64, 0x12, 0x41, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x62,
	0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x56, 0x6f, 0x69, 0x64, 0x1a, 0x04, 0xc0, 0x3e, 0x8a, 0x27, 0x32, 0x84, 0x03, 0x0a, 0x10, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x2e, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x55, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x12, 0x12, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x1a, 0x0c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x55, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x12,
	0x34, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66,
	0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x1a, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x2e, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4c, 0x53, 0x4d, 0x50,
	0x65, 0x72, 0x66, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x0c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x4c, 0x53,
	0x4d, 0x50, 0x65, 0x72, 0x66, 0x12, 0x42, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x79, 0x65,
	0x72, 0x65, 0x64, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x12, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x1a, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x4c, 0x61, 0x79, 0x65, 0x72, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x66, 0x12, 0x3f, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x12,
	0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x1a, 0x15, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x73, 0x12, 0x4f, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x1d, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x73, 0x1a, 0x04, 0xc0, 0x3e, 0x8b,
	0x27, 0x42, 0x35, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x69, 0x6f, 0x6d, 0x65, 0x73, 0x68, 0x2f, 0x7a, 0x62, 0x73, 0x2d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2d, 0x67, 0x6f, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x7a,
	0x62, 0x73, 0x80, 0x01, 0x01, 0x90, 0x01, 0x01,
}

var (
	file_perf_rpc_proto_rawDescOnce sync.Once
	file_perf_rpc_proto_rawDescData = file_perf_rpc_proto_rawDesc
)

func file_perf_rpc_proto_rawDescGZIP() []byte {
	file_perf_rpc_proto_rawDescOnce.Do(func() {
		file_perf_rpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_perf_rpc_proto_rawDescData)
	})
	return file_perf_rpc_proto_rawDescData
}

var file_perf_rpc_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_perf_rpc_proto_goTypes = []interface{}{
	(*VolumePerfRequest)(nil),               // 0: zbs.VolumePerfRequest
	(*VolumePerf)(nil),                      // 1: zbs.VolumePerf
	(*VolumesPerfRequest)(nil),              // 2: zbs.VolumesPerfRequest
	(*VolumesPerf)(nil),                     // 3: zbs.VolumesPerf
	(*ProbeVolumesRequest)(nil),             // 4: zbs.ProbeVolumesRequest
	(*DisableProbeVolumesRequest)(nil),      // 5: zbs.DisableProbeVolumesRequest
	(*UIOPerf)(nil),                         // 6: zbs.UIOPerf
	(*LayeredAccessPerf)(nil),               // 7: zbs.LayeredAccessPerf
	(*FlowControllerChunkPerf)(nil),         // 8: zbs.FlowControllerChunkPerf
	(*FlowControllerPerf)(nil),              // 9: zbs.FlowControllerPerf
	(*FlowManagerChunkPerf)(nil),            // 10: zbs.FlowManagerChunkPerf
	(*FlowManagerPerf)(nil),                 // 11: zbs.FlowManagerPerf
	(*FlowControlPerf)(nil),                 // 12: zbs.FlowControlPerf
	(*FlowControlPerfs)(nil),                // 13: zbs.FlowControlPerfs
	(*InternalFlowControllerTokenInfo)(nil), // 14: zbs.InternalFlowControllerTokenInfo
	(*InternalFlowControllerChunkPerf)(nil), // 15: zbs.InternalFlowControllerChunkPerf
	(*InternalFlowControllerPerf)(nil),      // 16: zbs.InternalFlowControllerPerf
	(*InternalFlowManagerTokenInfo)(nil),    // 17: zbs.InternalFlowManagerTokenInfo
	(*InternalFlowManagerChunkPerf)(nil),    // 18: zbs.InternalFlowManagerChunkPerf
	(*InternalFlowManagerPerf)(nil),         // 19: zbs.InternalFlowManagerPerf
	(*InternalFlowControlPerf)(nil),         // 20: zbs.InternalFlowControlPerf
	(*InternalFlowControlPerfs)(nil),        // 21: zbs.InternalFlowControlPerfs
	(*proto.Histogram)(nil),                 // 22: zbs.metric.proto.Histogram
	(*AccessPerf)(nil),                      // 23: zbs.AccessPerf
	(*Void)(nil),                            // 24: zbs.Void
	(*ChunkInstance)(nil),                   // 25: zbs.ChunkInstance
	(*LSMPerf)(nil),                         // 26: zbs.LSMPerf
}
var file_perf_rpc_proto_depIdxs = []int32{
	22, // 0: zbs.VolumePerf.readwrite_size_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 1: zbs.VolumePerf.read_size_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 2: zbs.VolumePerf.write_size_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 3: zbs.VolumePerf.readwrite_logical_offset_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 4: zbs.VolumePerf.read_logical_offset_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 5: zbs.VolumePerf.write_logical_offset_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 6: zbs.VolumePerf.readwrite_physical_offset_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 7: zbs.VolumePerf.read_physical_offset_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 8: zbs.VolumePerf.write_physical_offset_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 9: zbs.VolumePerf.readwrite_latency_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 10: zbs.VolumePerf.read_latency_range_bucket:type_name -> zbs.metric.proto.Histogram
	22, // 11: zbs.VolumePerf.write_latency_range_bucket:type_name -> zbs.metric.proto.Histogram
	1,  // 12: zbs.VolumesPerf.perf_list:type_name -> zbs.VolumePerf
	23, // 13: zbs.LayeredAccessPerf.access_perf:type_name -> zbs.AccessPerf
	23, // 14: zbs.LayeredAccessPerf.access_cap_replica_app_perf:type_name -> zbs.AccessPerf
	23, // 15: zbs.LayeredAccessPerf.access_perf_replica_app_perf:type_name -> zbs.AccessPerf
	23, // 16: zbs.LayeredAccessPerf.access_cap_ec_app_perf:type_name -> zbs.AccessPerf
	23, // 17: zbs.LayeredAccessPerf.access_cap_replica_sink_perf:type_name -> zbs.AccessPerf
	23, // 18: zbs.LayeredAccessPerf.access_perf_replica_sink_perf:type_name -> zbs.AccessPerf
	23, // 19: zbs.LayeredAccessPerf.access_cap_ec_sink_perf:type_name -> zbs.AccessPerf
	23, // 20: zbs.LayeredAccessPerf.access_cap_replica_reposition_perf:type_name -> zbs.AccessPerf
	23, // 21: zbs.LayeredAccessPerf.access_perf_replica_reposition_perf:type_name -> zbs.AccessPerf
	23, // 22: zbs.LayeredAccessPerf.access_cap_ec_reposition_perf:type_name -> zbs.AccessPerf
	8,  // 23: zbs.FlowControllerPerf.fc_chunk_perf:type_name -> zbs.FlowControllerChunkPerf
	10, // 24: zbs.FlowManagerPerf.fm_chunk_perf:type_name -> zbs.FlowManagerChunkPerf
	11, // 25: zbs.FlowControlPerf.flow_mgr_perf:type_name -> zbs.FlowManagerPerf
	9,  // 26: zbs.FlowControlPerf.flow_ctrl_perf:type_name -> zbs.FlowControllerPerf
	12, // 27: zbs.FlowControlPerfs.instances_response:type_name -> zbs.FlowControlPerf
	14, // 28: zbs.InternalFlowControllerChunkPerf.cap_token_info:type_name -> zbs.InternalFlowControllerTokenInfo
	14, // 29: zbs.InternalFlowControllerChunkPerf.perf_token_info:type_name -> zbs.InternalFlowControllerTokenInfo
	15, // 30: zbs.InternalFlowControllerPerf.ifc_chunk_perf:type_name -> zbs.InternalFlowControllerChunkPerf
	17, // 31: zbs.InternalFlowManagerChunkPerf.cap_token_info:type_name -> zbs.InternalFlowManagerTokenInfo
	17, // 32: zbs.InternalFlowManagerChunkPerf.perf_token_info:type_name -> zbs.InternalFlowManagerTokenInfo
	18, // 33: zbs.InternalFlowManagerPerf.ifm_chunk_perf:type_name -> zbs.InternalFlowManagerChunkPerf
	19, // 34: zbs.InternalFlowControlPerf.ifm_perf:type_name -> zbs.InternalFlowManagerPerf
	16, // 35: zbs.InternalFlowControlPerf.ifc_perf:type_name -> zbs.InternalFlowControllerPerf
	20, // 36: zbs.InternalFlowControlPerfs.instances_response:type_name -> zbs.InternalFlowControlPerf
	0,  // 37: zbs.VolumePerfService.GetVolumePerf:input_type -> zbs.VolumePerfRequest
	2,  // 38: zbs.VolumePerfService.GetVolumesPerf:input_type -> zbs.VolumesPerfRequest
	24, // 39: zbs.VolumePerfService.GetAllVolumesPerf:input_type -> zbs.Void
	4,  // 40: zbs.VolumePerfService.ProbeVolumes:input_type -> zbs.ProbeVolumesRequest
	5,  // 41: zbs.VolumePerfService.DisableProbeVolumes:input_type -> zbs.DisableProbeVolumesRequest
	25, // 42: zbs.ChunkPerfService.GetUIOPerf:input_type -> zbs.ChunkInstance
	25, // 43: zbs.ChunkPerfService.GetAccessPerf:input_type -> zbs.ChunkInstance
	25, // 44: zbs.ChunkPerfService.GetLSMPerf:input_type -> zbs.ChunkInstance
	25, // 45: zbs.ChunkPerfService.GetLayeredAccessPerf:input_type -> zbs.ChunkInstance
	25, // 46: zbs.ChunkPerfService.GetFlowControlPerf:input_type -> zbs.ChunkInstance
	25, // 47: zbs.ChunkPerfService.GetInternalFlowControlPerf:input_type -> zbs.ChunkInstance
	1,  // 48: zbs.VolumePerfService.GetVolumePerf:output_type -> zbs.VolumePerf
	3,  // 49: zbs.VolumePerfService.GetVolumesPerf:output_type -> zbs.VolumesPerf
	3,  // 50: zbs.VolumePerfService.GetAllVolumesPerf:output_type -> zbs.VolumesPerf
	24, // 51: zbs.VolumePerfService.ProbeVolumes:output_type -> zbs.Void
	24, // 52: zbs.VolumePerfService.DisableProbeVolumes:output_type -> zbs.Void
	6,  // 53: zbs.ChunkPerfService.GetUIOPerf:output_type -> zbs.UIOPerf
	23, // 54: zbs.ChunkPerfService.GetAccessPerf:output_type -> zbs.AccessPerf
	26, // 55: zbs.ChunkPerfService.GetLSMPerf:output_type -> zbs.LSMPerf
	7,  // 56: zbs.ChunkPerfService.GetLayeredAccessPerf:output_type -> zbs.LayeredAccessPerf
	13, // 57: zbs.ChunkPerfService.GetFlowControlPerf:output_type -> zbs.FlowControlPerfs
	21, // 58: zbs.ChunkPerfService.GetInternalFlowControlPerf:output_type -> zbs.InternalFlowControlPerfs
	48, // [48:59] is the sub-list for method output_type
	37, // [37:48] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_perf_rpc_proto_init() }
func file_perf_rpc_proto_init() {
	if File_perf_rpc_proto != nil {
		return
	}
	file_common_proto_init()
	file_options_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_perf_rpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumePerfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumePerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumesPerfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumesPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProbeVolumesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisableProbeVolumesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIOPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LayeredAccessPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowControllerChunkPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowControllerPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowManagerChunkPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowManagerPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowControlPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowControlPerfs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowControllerTokenInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowControllerChunkPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowControllerPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowManagerTokenInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowManagerChunkPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowManagerPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowControlPerf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perf_rpc_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalFlowControlPerfs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_perf_rpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_perf_rpc_proto_goTypes,
		DependencyIndexes: file_perf_rpc_proto_depIdxs,
		MessageInfos:      file_perf_rpc_proto_msgTypes,
	}.Build()
	File_perf_rpc_proto = out.File
	file_perf_rpc_proto_rawDesc = nil
	file_perf_rpc_proto_goTypes = nil
	file_perf_rpc_proto_depIdxs = nil
}
