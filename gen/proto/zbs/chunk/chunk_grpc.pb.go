// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.12
// source: chunk.proto

package chunk

import (
	context "context"
	zbs "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	block "github.com/iomesh/zbs-client-go/gen/proto/zbs/block"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ChunkService_GetZbsAddress_FullMethodName                 = "/zbs.chunk.ChunkService/GetZbsAddress"
	ChunkService_FormatPartition_FullMethodName               = "/zbs.chunk.ChunkService/FormatPartition"
	ChunkService_MountPartition_FullMethodName                = "/zbs.chunk.ChunkService/MountPartition"
	ChunkService_UmountPartition_FullMethodName               = "/zbs.chunk.ChunkService/UmountPartition"
	ChunkService_MountCache_FullMethodName                    = "/zbs.chunk.ChunkService/MountCache"
	ChunkService_UmountCache_FullMethodName                   = "/zbs.chunk.ChunkService/UmountCache"
	ChunkService_FormatJournal_FullMethodName                 = "/zbs.chunk.ChunkService/FormatJournal"
	ChunkService_MountJournal_FullMethodName                  = "/zbs.chunk.ChunkService/MountJournal"
	ChunkService_UmountJournal_FullMethodName                 = "/zbs.chunk.ChunkService/UmountJournal"
	ChunkService_FlushAllJournals_FullMethodName              = "/zbs.chunk.ChunkService/FlushAllJournals"
	ChunkService_ListPartition_FullMethodName                 = "/zbs.chunk.ChunkService/ListPartition"
	ChunkService_ListJournal_FullMethodName                   = "/zbs.chunk.ChunkService/ListJournal"
	ChunkService_ListCache_FullMethodName                     = "/zbs.chunk.ChunkService/ListCache"
	ChunkService_StopServer_FullMethodName                    = "/zbs.chunk.ChunkService/StopServer"
	ChunkService_ListClient_FullMethodName                    = "/zbs.chunk.ChunkService/ListClient"
	ChunkService_ListRecover_FullMethodName                   = "/zbs.chunk.ChunkService/ListRecover"
	ChunkService_ListMigrate_FullMethodName                   = "/zbs.chunk.ChunkService/ListMigrate"
	ChunkService_ListExtent_FullMethodName                    = "/zbs.chunk.ChunkService/ListExtent"
	ChunkService_CheckExtent_FullMethodName                   = "/zbs.chunk.ChunkService/CheckExtent"
	ChunkService_InvalidateCache_FullMethodName               = "/zbs.chunk.ChunkService/InvalidateCache"
	ChunkService_CheckAllExtents_FullMethodName               = "/zbs.chunk.ChunkService/CheckAllExtents"
	ChunkService_ShowExtent_FullMethodName                    = "/zbs.chunk.ChunkService/ShowExtent"
	ChunkService_PromoteExtent_FullMethodName                 = "/zbs.chunk.ChunkService/PromoteExtent"
	ChunkService_GetDiskInfo_FullMethodName                   = "/zbs.chunk.ChunkService/GetDiskInfo"
	ChunkService_InvalidateExtent_FullMethodName              = "/zbs.chunk.ChunkService/InvalidateExtent"
	ChunkService_SetVerifyMode_FullMethodName                 = "/zbs.chunk.ChunkService/SetVerifyMode"
	ChunkService_UpdateSecondaryDataIP_FullMethodName         = "/zbs.chunk.ChunkService/UpdateSecondaryDataIP"
	ChunkService_SetUnhealthyPartition_FullMethodName         = "/zbs.chunk.ChunkService/SetUnhealthyPartition"
	ChunkService_SetHealthyPartition_FullMethodName           = "/zbs.chunk.ChunkService/SetHealthyPartition"
	ChunkService_SetUnhealthyCache_FullMethodName             = "/zbs.chunk.ChunkService/SetUnhealthyCache"
	ChunkService_SetHealthyCache_FullMethodName               = "/zbs.chunk.ChunkService/SetHealthyCache"
	ChunkService_SetUnhealthyJournal_FullMethodName           = "/zbs.chunk.ChunkService/SetUnhealthyJournal"
	ChunkService_SetHealthyJournal_FullMethodName             = "/zbs.chunk.ChunkService/SetHealthyJournal"
	ChunkService_FormatCache_FullMethodName                   = "/zbs.chunk.ChunkService/FormatCache"
	ChunkService_QueryDisk_FullMethodName                     = "/zbs.chunk.ChunkService/QueryDisk"
	ChunkService_SummaryInfo_FullMethodName                   = "/zbs.chunk.ChunkService/SummaryInfo"
	ChunkService_UpdateScvmModeHostDataIP_FullMethodName      = "/zbs.chunk.ChunkService/UpdateScvmModeHostDataIP"
	ChunkService_GetChunkServiceStat_FullMethodName           = "/zbs.chunk.ChunkService/GetChunkServiceStat"
	ChunkService_ReloadConfig_FullMethodName                  = "/zbs.chunk.ChunkService/ReloadConfig"
	ChunkService_GetZkHosts_FullMethodName                    = "/zbs.chunk.ChunkService/GetZkHosts"
	ChunkService_CancelUmountPartition_FullMethodName         = "/zbs.chunk.ChunkService/CancelUmountPartition"
	ChunkService_CancelUmountCache_FullMethodName             = "/zbs.chunk.ChunkService/CancelUmountCache"
	ChunkService_InvalidatePartition_FullMethodName           = "/zbs.chunk.ChunkService/InvalidatePartition"
	ChunkService_ListRejectedDisks_FullMethodName             = "/zbs.chunk.ChunkService/ListRejectedDisks"
	ChunkService_AcceptDisk_FullMethodName                    = "/zbs.chunk.ChunkService/AcceptDisk"
	ChunkService_IsolatePartition_FullMethodName              = "/zbs.chunk.ChunkService/IsolatePartition"
	ChunkService_StartAurora_FullMethodName                   = "/zbs.chunk.ChunkService/StartAurora"
	ChunkService_StopAurora_FullMethodName                    = "/zbs.chunk.ChunkService/StopAurora"
	ChunkService_ReloadHostName_FullMethodName                = "/zbs.chunk.ChunkService/ReloadHostName"
	ChunkService_GetDiskInspectorStats_FullMethodName         = "/zbs.chunk.ChunkService/GetDiskInspectorStats"
	ChunkService_StartDiskInspectorPatrol_FullMethodName      = "/zbs.chunk.ChunkService/StartDiskInspectorPatrol"
	ChunkService_StopDiskInspectorPatrol_FullMethodName       = "/zbs.chunk.ChunkService/StopDiskInspectorPatrol"
	ChunkService_StartDirtyBlockTracking_FullMethodName       = "/zbs.chunk.ChunkService/StartDirtyBlockTracking"
	ChunkService_StopDirtyBlockTracking_FullMethodName        = "/zbs.chunk.ChunkService/StopDirtyBlockTracking"
	ChunkService_GetDirtyBlocks_FullMethodName                = "/zbs.chunk.ChunkService/GetDirtyBlocks"
	ChunkService_GetCDPJob_FullMethodName                     = "/zbs.chunk.ChunkService/GetCDPJob"
	ChunkService_ListCDPJobs_FullMethodName                   = "/zbs.chunk.ChunkService/ListCDPJobs"
	ChunkService_GetPollingStats_FullMethodName               = "/zbs.chunk.ChunkService/GetPollingStats"
	ChunkService_SetVolumeIOLatencyInjection_FullMethodName   = "/zbs.chunk.ChunkService/SetVolumeIOLatencyInjection"
	ChunkService_ClearVolumeIOLatencyInjection_FullMethodName = "/zbs.chunk.ChunkService/ClearVolumeIOLatencyInjection"
	ChunkService_ListVolumeIOLatencyInjection_FullMethodName  = "/zbs.chunk.ChunkService/ListVolumeIOLatencyInjection"
	ChunkService_GetTemporaryExtentSummary_FullMethodName     = "/zbs.chunk.ChunkService/GetTemporaryExtentSummary"
	ChunkService_GetTemporaryExtent_FullMethodName            = "/zbs.chunk.ChunkService/GetTemporaryExtent"
	ChunkService_ListTemporaryExtent_FullMethodName           = "/zbs.chunk.ChunkService/ListTemporaryExtent"
	ChunkService_ComparePExtent_FullMethodName                = "/zbs.chunk.ChunkService/ComparePExtent"
	ChunkService_ListSinkInfo_FullMethodName                  = "/zbs.chunk.ChunkService/ListSinkInfo"
	ChunkService_SetSinkParams_FullMethodName                 = "/zbs.chunk.ChunkService/SetSinkParams"
	ChunkService_SetCapIOThrottle_FullMethodName              = "/zbs.chunk.ChunkService/SetCapIOThrottle"
	ChunkService_GetCapIOThrottle_FullMethodName              = "/zbs.chunk.ChunkService/GetCapIOThrottle"
	ChunkService_SetInternalIOThrottle_FullMethodName         = "/zbs.chunk.ChunkService/SetInternalIOThrottle"
	ChunkService_GetInternalIOThrottle_FullMethodName         = "/zbs.chunk.ChunkService/GetInternalIOThrottle"
	ChunkService_IsolateCache_FullMethodName                  = "/zbs.chunk.ChunkService/IsolateCache"
	ChunkService_GetRouteForRDMAUD_FullMethodName             = "/zbs.chunk.ChunkService/GetRouteForRDMAUD"
	ChunkService_GetDCManagerInfo_FullMethodName              = "/zbs.chunk.ChunkService/GetDCManagerInfo"
	ChunkService_GetDCServerInfo_FullMethodName               = "/zbs.chunk.ChunkService/GetDCServerInfo"
	ChunkService_GetZbsAddressV2_FullMethodName               = "/zbs.chunk.ChunkService/GetZbsAddressV2"
	ChunkService_ListPartitionV2_FullMethodName               = "/zbs.chunk.ChunkService/ListPartitionV2"
	ChunkService_ListJournalV2_FullMethodName                 = "/zbs.chunk.ChunkService/ListJournalV2"
	ChunkService_ListCacheV2_FullMethodName                   = "/zbs.chunk.ChunkService/ListCacheV2"
	ChunkService_ListRecoverV2_FullMethodName                 = "/zbs.chunk.ChunkService/ListRecoverV2"
	ChunkService_ListMigrateV2_FullMethodName                 = "/zbs.chunk.ChunkService/ListMigrateV2"
	ChunkService_SummaryInfoV2_FullMethodName                 = "/zbs.chunk.ChunkService/SummaryInfoV2"
	ChunkService_ListRejectedDisksV2_FullMethodName           = "/zbs.chunk.ChunkService/ListRejectedDisksV2"
	ChunkService_GetDiskInspectorStatsV2_FullMethodName       = "/zbs.chunk.ChunkService/GetDiskInspectorStatsV2"
	ChunkService_GetTemporaryExtentSummaryV2_FullMethodName   = "/zbs.chunk.ChunkService/GetTemporaryExtentSummaryV2"
	ChunkService_ListSinkInfoV2_FullMethodName                = "/zbs.chunk.ChunkService/ListSinkInfoV2"
	ChunkService_GetCapIOThrottleV2_FullMethodName            = "/zbs.chunk.ChunkService/GetCapIOThrottleV2"
	ChunkService_GetInternalIOThrottleV2_FullMethodName       = "/zbs.chunk.ChunkService/GetInternalIOThrottleV2"
)

// ChunkServiceClient is the client API for ChunkService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChunkServiceClient interface {
	GetZbsAddress(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ZbsAddress, error)
	FormatPartition(ctx context.Context, in *FormatPartitionRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	MountPartition(ctx context.Context, in *MountPartitionRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	UmountPartition(ctx context.Context, in *UmountDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	MountCache(ctx context.Context, in *MountCacheRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	UmountCache(ctx context.Context, in *UmountDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	FormatJournal(ctx context.Context, in *FormatJournalRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	MountJournal(ctx context.Context, in *MountJournalRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	UmountJournal(ctx context.Context, in *UmountDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	FlushAllJournals(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	ListPartition(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListPartitionResponse, error)
	ListJournal(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListJournalResponse, error)
	ListCache(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListCacheResponse, error)
	StopServer(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	ListClient(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListClientResponse, error)
	ListRecover(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.ListRecoverResponse, error)
	ListMigrate(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.ListRecoverResponse, error)
	ListExtent(ctx context.Context, in *ListPExtentRequest, opts ...grpc.CallOption) (*ListExtentResponse, error)
	CheckExtent(ctx context.Context, in *PExtentRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	InvalidateCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	CheckAllExtents(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	ShowExtent(ctx context.Context, in *ShowExtentRequest, opts ...grpc.CallOption) (*ShowExtentResponse, error)
	PromoteExtent(ctx context.Context, in *PromoteExtentRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	GetDiskInfo(ctx context.Context, in *GetDiskInfoRequest, opts ...grpc.CallOption) (*GetDiskInfoResponse, error)
	InvalidateExtent(ctx context.Context, in *InvalidateExtentRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetVerifyMode(ctx context.Context, in *SetVerifyModeRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	UpdateSecondaryDataIP(ctx context.Context, in *UpdateSecondaryDataIPRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetUnhealthyPartition(ctx context.Context, in *SetUnhealthyRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetHealthyPartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetUnhealthyCache(ctx context.Context, in *SetUnhealthyRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetHealthyCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetUnhealthyJournal(ctx context.Context, in *SetUnhealthyRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetHealthyJournal(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	FormatCache(ctx context.Context, in *FormatCacheRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	QueryDisk(ctx context.Context, in *QueryDiskRequest, opts ...grpc.CallOption) (*QueryDiskResponse, error)
	SummaryInfo(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*SummaryInfoResponse, error)
	UpdateScvmModeHostDataIP(ctx context.Context, in *UpdateScvmModeHostDataIPRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	GetChunkServiceStat(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ChunkServiceStat, error)
	ReloadConfig(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	GetZkHosts(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetZkHostsResponse, error)
	CancelUmountPartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	CancelUmountCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	InvalidatePartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	ListRejectedDisks(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListRejectedDisksResponse, error)
	AcceptDisk(ctx context.Context, in *AcceptDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	IsolatePartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	StartAurora(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	StopAurora(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	ReloadHostName(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	GetDiskInspectorStats(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetDiskInspectorStatsResponse, error)
	StartDiskInspectorPatrol(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	StopDiskInspectorPatrol(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error)
	StartDirtyBlockTracking(ctx context.Context, in *DirtyBlockRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	StopDirtyBlockTracking(ctx context.Context, in *DirtyBlockRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	GetDirtyBlocks(ctx context.Context, in *DirtyBlockRequest, opts ...grpc.CallOption) (*DirtyBlockResponse, error)
	GetCDPJob(ctx context.Context, in *zbs.GetCDPJobRequest, opts ...grpc.CallOption) (*zbs.CDPJobInfo, error)
	ListCDPJobs(ctx context.Context, in *zbs.ListCDPJobsRequest, opts ...grpc.CallOption) (*zbs.ListCDPJobsResponse, error)
	GetPollingStats(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetPollingStatsResponse, error)
	SetVolumeIOLatencyInjection(ctx context.Context, in *SetVolumeIOLatencyInjectionRequest, opts ...grpc.CallOption) (*SetVolumeIOLatencyInjectionResponse, error)
	ClearVolumeIOLatencyInjection(ctx context.Context, in *ClearVolumeIOLatencyInjectionRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	ListVolumeIOLatencyInjection(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListVolumeIOLatencyInjectionResponse, error)
	GetTemporaryExtentSummary(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetTemporaryExtentSummaryResponse, error)
	GetTemporaryExtent(ctx context.Context, in *GetTemporaryExtentRequest, opts ...grpc.CallOption) (*GetTemporaryExtentResponse, error)
	ListTemporaryExtent(ctx context.Context, in *ListTemporaryExtentRequest, opts ...grpc.CallOption) (*ListTemporaryExtentResponse, error)
	ComparePExtent(ctx context.Context, in *ComparePExtentRequest, opts ...grpc.CallOption) (*block.CompareExtentResponse, error)
	ListSinkInfo(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListSinkInfoResponse, error)
	SetSinkParams(ctx context.Context, in *SetSinkParamsRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	SetCapIOThrottle(ctx context.Context, in *SetCapIOThrottleRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	GetCapIOThrottle(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetCapIOThrottleResponse, error)
	SetInternalIOThrottle(ctx context.Context, in *InternalIOThrottleInfo, opts ...grpc.CallOption) (*zbs.Void, error)
	GetInternalIOThrottle(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*InternalIOThrottleInfo, error)
	IsolateCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error)
	GetRouteForRDMAUD(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*RDMAUDRouteInfo, error)
	GetDCManagerInfo(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*DCManagerInfos, error)
	GetDCServerInfo(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*DCServerInfos, error)
	// The following V2 version RPCs are introduced for multi chunk instances mode.
	// These RPCs are used to obtain some information about Chunk, and they can return all instances' responses now.
	GetZbsAddressV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ZbsAddressV2, error)
	ListPartitionV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListPartitionResponseV2, error)
	ListJournalV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListJournalResponseV2, error)
	ListCacheV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListCacheResponseV2, error)
	ListRecoverV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*zbs.ListRecoverResponseV2, error)
	ListMigrateV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*zbs.ListRecoverResponseV2, error)
	SummaryInfoV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*SummaryInfoResponseV2, error)
	ListRejectedDisksV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListRejectedDisksResponseV2, error)
	GetDiskInspectorStatsV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*GetDiskInspectorStatsResponseV2, error)
	GetTemporaryExtentSummaryV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*GetTemporaryExtentSummaryResponseV2, error)
	ListSinkInfoV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListSinkInfoResponseV2, error)
	GetCapIOThrottleV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*GetCapIOThrottleResponseV2, error)
	GetInternalIOThrottleV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*InternalIOThrottleInfoV2, error)
}

type chunkServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewChunkServiceClient(cc grpc.ClientConnInterface) ChunkServiceClient {
	return &chunkServiceClient{cc}
}

func (c *chunkServiceClient) GetZbsAddress(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ZbsAddress, error) {
	out := new(ZbsAddress)
	err := c.cc.Invoke(ctx, ChunkService_GetZbsAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) FormatPartition(ctx context.Context, in *FormatPartitionRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_FormatPartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) MountPartition(ctx context.Context, in *MountPartitionRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_MountPartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) UmountPartition(ctx context.Context, in *UmountDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_UmountPartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) MountCache(ctx context.Context, in *MountCacheRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_MountCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) UmountCache(ctx context.Context, in *UmountDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_UmountCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) FormatJournal(ctx context.Context, in *FormatJournalRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_FormatJournal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) MountJournal(ctx context.Context, in *MountJournalRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_MountJournal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) UmountJournal(ctx context.Context, in *UmountDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_UmountJournal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) FlushAllJournals(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_FlushAllJournals_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListPartition(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListPartitionResponse, error) {
	out := new(ListPartitionResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListPartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListJournal(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListJournalResponse, error) {
	out := new(ListJournalResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListJournal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListCache(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListCacheResponse, error) {
	out := new(ListCacheResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) StopServer(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_StopServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListClient(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListClientResponse, error) {
	out := new(ListClientResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListClient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListRecover(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.ListRecoverResponse, error) {
	out := new(zbs.ListRecoverResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListRecover_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListMigrate(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.ListRecoverResponse, error) {
	out := new(zbs.ListRecoverResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListMigrate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListExtent(ctx context.Context, in *ListPExtentRequest, opts ...grpc.CallOption) (*ListExtentResponse, error) {
	out := new(ListExtentResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) CheckExtent(ctx context.Context, in *PExtentRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_CheckExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) InvalidateCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_InvalidateCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) CheckAllExtents(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_CheckAllExtents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ShowExtent(ctx context.Context, in *ShowExtentRequest, opts ...grpc.CallOption) (*ShowExtentResponse, error) {
	out := new(ShowExtentResponse)
	err := c.cc.Invoke(ctx, ChunkService_ShowExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) PromoteExtent(ctx context.Context, in *PromoteExtentRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_PromoteExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetDiskInfo(ctx context.Context, in *GetDiskInfoRequest, opts ...grpc.CallOption) (*GetDiskInfoResponse, error) {
	out := new(GetDiskInfoResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetDiskInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) InvalidateExtent(ctx context.Context, in *InvalidateExtentRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_InvalidateExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetVerifyMode(ctx context.Context, in *SetVerifyModeRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetVerifyMode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) UpdateSecondaryDataIP(ctx context.Context, in *UpdateSecondaryDataIPRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_UpdateSecondaryDataIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetUnhealthyPartition(ctx context.Context, in *SetUnhealthyRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetUnhealthyPartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetHealthyPartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetHealthyPartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetUnhealthyCache(ctx context.Context, in *SetUnhealthyRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetUnhealthyCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetHealthyCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetHealthyCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetUnhealthyJournal(ctx context.Context, in *SetUnhealthyRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetUnhealthyJournal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetHealthyJournal(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetHealthyJournal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) FormatCache(ctx context.Context, in *FormatCacheRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_FormatCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) QueryDisk(ctx context.Context, in *QueryDiskRequest, opts ...grpc.CallOption) (*QueryDiskResponse, error) {
	out := new(QueryDiskResponse)
	err := c.cc.Invoke(ctx, ChunkService_QueryDisk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SummaryInfo(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*SummaryInfoResponse, error) {
	out := new(SummaryInfoResponse)
	err := c.cc.Invoke(ctx, ChunkService_SummaryInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) UpdateScvmModeHostDataIP(ctx context.Context, in *UpdateScvmModeHostDataIPRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_UpdateScvmModeHostDataIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetChunkServiceStat(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ChunkServiceStat, error) {
	out := new(ChunkServiceStat)
	err := c.cc.Invoke(ctx, ChunkService_GetChunkServiceStat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ReloadConfig(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_ReloadConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetZkHosts(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetZkHostsResponse, error) {
	out := new(GetZkHostsResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetZkHosts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) CancelUmountPartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_CancelUmountPartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) CancelUmountCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_CancelUmountCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) InvalidatePartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_InvalidatePartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListRejectedDisks(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListRejectedDisksResponse, error) {
	out := new(ListRejectedDisksResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListRejectedDisks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) AcceptDisk(ctx context.Context, in *AcceptDiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_AcceptDisk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) IsolatePartition(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_IsolatePartition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) StartAurora(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_StartAurora_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) StopAurora(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_StopAurora_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ReloadHostName(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_ReloadHostName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetDiskInspectorStats(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetDiskInspectorStatsResponse, error) {
	out := new(GetDiskInspectorStatsResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetDiskInspectorStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) StartDiskInspectorPatrol(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_StartDiskInspectorPatrol_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) StopDiskInspectorPatrol(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_StopDiskInspectorPatrol_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) StartDirtyBlockTracking(ctx context.Context, in *DirtyBlockRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_StartDirtyBlockTracking_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) StopDirtyBlockTracking(ctx context.Context, in *DirtyBlockRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_StopDirtyBlockTracking_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetDirtyBlocks(ctx context.Context, in *DirtyBlockRequest, opts ...grpc.CallOption) (*DirtyBlockResponse, error) {
	out := new(DirtyBlockResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetDirtyBlocks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetCDPJob(ctx context.Context, in *zbs.GetCDPJobRequest, opts ...grpc.CallOption) (*zbs.CDPJobInfo, error) {
	out := new(zbs.CDPJobInfo)
	err := c.cc.Invoke(ctx, ChunkService_GetCDPJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListCDPJobs(ctx context.Context, in *zbs.ListCDPJobsRequest, opts ...grpc.CallOption) (*zbs.ListCDPJobsResponse, error) {
	out := new(zbs.ListCDPJobsResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListCDPJobs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetPollingStats(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetPollingStatsResponse, error) {
	out := new(GetPollingStatsResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetPollingStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetVolumeIOLatencyInjection(ctx context.Context, in *SetVolumeIOLatencyInjectionRequest, opts ...grpc.CallOption) (*SetVolumeIOLatencyInjectionResponse, error) {
	out := new(SetVolumeIOLatencyInjectionResponse)
	err := c.cc.Invoke(ctx, ChunkService_SetVolumeIOLatencyInjection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ClearVolumeIOLatencyInjection(ctx context.Context, in *ClearVolumeIOLatencyInjectionRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_ClearVolumeIOLatencyInjection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListVolumeIOLatencyInjection(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListVolumeIOLatencyInjectionResponse, error) {
	out := new(ListVolumeIOLatencyInjectionResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListVolumeIOLatencyInjection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetTemporaryExtentSummary(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetTemporaryExtentSummaryResponse, error) {
	out := new(GetTemporaryExtentSummaryResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetTemporaryExtentSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetTemporaryExtent(ctx context.Context, in *GetTemporaryExtentRequest, opts ...grpc.CallOption) (*GetTemporaryExtentResponse, error) {
	out := new(GetTemporaryExtentResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetTemporaryExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListTemporaryExtent(ctx context.Context, in *ListTemporaryExtentRequest, opts ...grpc.CallOption) (*ListTemporaryExtentResponse, error) {
	out := new(ListTemporaryExtentResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListTemporaryExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ComparePExtent(ctx context.Context, in *ComparePExtentRequest, opts ...grpc.CallOption) (*block.CompareExtentResponse, error) {
	out := new(block.CompareExtentResponse)
	err := c.cc.Invoke(ctx, ChunkService_ComparePExtent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListSinkInfo(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*ListSinkInfoResponse, error) {
	out := new(ListSinkInfoResponse)
	err := c.cc.Invoke(ctx, ChunkService_ListSinkInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetSinkParams(ctx context.Context, in *SetSinkParamsRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetSinkParams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetCapIOThrottle(ctx context.Context, in *SetCapIOThrottleRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetCapIOThrottle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetCapIOThrottle(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*GetCapIOThrottleResponse, error) {
	out := new(GetCapIOThrottleResponse)
	err := c.cc.Invoke(ctx, ChunkService_GetCapIOThrottle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SetInternalIOThrottle(ctx context.Context, in *InternalIOThrottleInfo, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_SetInternalIOThrottle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetInternalIOThrottle(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*InternalIOThrottleInfo, error) {
	out := new(InternalIOThrottleInfo)
	err := c.cc.Invoke(ctx, ChunkService_GetInternalIOThrottle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) IsolateCache(ctx context.Context, in *DiskRequest, opts ...grpc.CallOption) (*zbs.Void, error) {
	out := new(zbs.Void)
	err := c.cc.Invoke(ctx, ChunkService_IsolateCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetRouteForRDMAUD(ctx context.Context, in *zbs.Void, opts ...grpc.CallOption) (*RDMAUDRouteInfo, error) {
	out := new(RDMAUDRouteInfo)
	err := c.cc.Invoke(ctx, ChunkService_GetRouteForRDMAUD_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetDCManagerInfo(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*DCManagerInfos, error) {
	out := new(DCManagerInfos)
	err := c.cc.Invoke(ctx, ChunkService_GetDCManagerInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetDCServerInfo(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*DCServerInfos, error) {
	out := new(DCServerInfos)
	err := c.cc.Invoke(ctx, ChunkService_GetDCServerInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetZbsAddressV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ZbsAddressV2, error) {
	out := new(ZbsAddressV2)
	err := c.cc.Invoke(ctx, ChunkService_GetZbsAddressV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListPartitionV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListPartitionResponseV2, error) {
	out := new(ListPartitionResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_ListPartitionV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListJournalV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListJournalResponseV2, error) {
	out := new(ListJournalResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_ListJournalV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListCacheV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListCacheResponseV2, error) {
	out := new(ListCacheResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_ListCacheV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListRecoverV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*zbs.ListRecoverResponseV2, error) {
	out := new(zbs.ListRecoverResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_ListRecoverV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListMigrateV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*zbs.ListRecoverResponseV2, error) {
	out := new(zbs.ListRecoverResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_ListMigrateV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) SummaryInfoV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*SummaryInfoResponseV2, error) {
	out := new(SummaryInfoResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_SummaryInfoV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListRejectedDisksV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListRejectedDisksResponseV2, error) {
	out := new(ListRejectedDisksResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_ListRejectedDisksV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetDiskInspectorStatsV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*GetDiskInspectorStatsResponseV2, error) {
	out := new(GetDiskInspectorStatsResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_GetDiskInspectorStatsV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetTemporaryExtentSummaryV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*GetTemporaryExtentSummaryResponseV2, error) {
	out := new(GetTemporaryExtentSummaryResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_GetTemporaryExtentSummaryV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) ListSinkInfoV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*ListSinkInfoResponseV2, error) {
	out := new(ListSinkInfoResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_ListSinkInfoV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetCapIOThrottleV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*GetCapIOThrottleResponseV2, error) {
	out := new(GetCapIOThrottleResponseV2)
	err := c.cc.Invoke(ctx, ChunkService_GetCapIOThrottleV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chunkServiceClient) GetInternalIOThrottleV2(ctx context.Context, in *zbs.ChunkInstance, opts ...grpc.CallOption) (*InternalIOThrottleInfoV2, error) {
	out := new(InternalIOThrottleInfoV2)
	err := c.cc.Invoke(ctx, ChunkService_GetInternalIOThrottleV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChunkServiceServer is the server API for ChunkService service.
// All implementations must embed UnimplementedChunkServiceServer
// for forward compatibility
type ChunkServiceServer interface {
	GetZbsAddress(context.Context, *zbs.Void) (*ZbsAddress, error)
	FormatPartition(context.Context, *FormatPartitionRequest) (*zbs.Void, error)
	MountPartition(context.Context, *MountPartitionRequest) (*zbs.Void, error)
	UmountPartition(context.Context, *UmountDiskRequest) (*zbs.Void, error)
	MountCache(context.Context, *MountCacheRequest) (*zbs.Void, error)
	UmountCache(context.Context, *UmountDiskRequest) (*zbs.Void, error)
	FormatJournal(context.Context, *FormatJournalRequest) (*zbs.Void, error)
	MountJournal(context.Context, *MountJournalRequest) (*zbs.Void, error)
	UmountJournal(context.Context, *UmountDiskRequest) (*zbs.Void, error)
	FlushAllJournals(context.Context, *zbs.Void) (*zbs.Void, error)
	ListPartition(context.Context, *zbs.Void) (*ListPartitionResponse, error)
	ListJournal(context.Context, *zbs.Void) (*ListJournalResponse, error)
	ListCache(context.Context, *zbs.Void) (*ListCacheResponse, error)
	StopServer(context.Context, *zbs.Void) (*zbs.Void, error)
	ListClient(context.Context, *zbs.Void) (*ListClientResponse, error)
	ListRecover(context.Context, *zbs.Void) (*zbs.ListRecoverResponse, error)
	ListMigrate(context.Context, *zbs.Void) (*zbs.ListRecoverResponse, error)
	ListExtent(context.Context, *ListPExtentRequest) (*ListExtentResponse, error)
	CheckExtent(context.Context, *PExtentRequest) (*zbs.Void, error)
	InvalidateCache(context.Context, *DiskRequest) (*zbs.Void, error)
	CheckAllExtents(context.Context, *zbs.Void) (*zbs.Void, error)
	ShowExtent(context.Context, *ShowExtentRequest) (*ShowExtentResponse, error)
	PromoteExtent(context.Context, *PromoteExtentRequest) (*zbs.Void, error)
	GetDiskInfo(context.Context, *GetDiskInfoRequest) (*GetDiskInfoResponse, error)
	InvalidateExtent(context.Context, *InvalidateExtentRequest) (*zbs.Void, error)
	SetVerifyMode(context.Context, *SetVerifyModeRequest) (*zbs.Void, error)
	UpdateSecondaryDataIP(context.Context, *UpdateSecondaryDataIPRequest) (*zbs.Void, error)
	SetUnhealthyPartition(context.Context, *SetUnhealthyRequest) (*zbs.Void, error)
	SetHealthyPartition(context.Context, *DiskRequest) (*zbs.Void, error)
	SetUnhealthyCache(context.Context, *SetUnhealthyRequest) (*zbs.Void, error)
	SetHealthyCache(context.Context, *DiskRequest) (*zbs.Void, error)
	SetUnhealthyJournal(context.Context, *SetUnhealthyRequest) (*zbs.Void, error)
	SetHealthyJournal(context.Context, *DiskRequest) (*zbs.Void, error)
	FormatCache(context.Context, *FormatCacheRequest) (*zbs.Void, error)
	QueryDisk(context.Context, *QueryDiskRequest) (*QueryDiskResponse, error)
	SummaryInfo(context.Context, *zbs.Void) (*SummaryInfoResponse, error)
	UpdateScvmModeHostDataIP(context.Context, *UpdateScvmModeHostDataIPRequest) (*zbs.Void, error)
	GetChunkServiceStat(context.Context, *zbs.Void) (*ChunkServiceStat, error)
	ReloadConfig(context.Context, *zbs.Void) (*zbs.Void, error)
	GetZkHosts(context.Context, *zbs.Void) (*GetZkHostsResponse, error)
	CancelUmountPartition(context.Context, *DiskRequest) (*zbs.Void, error)
	CancelUmountCache(context.Context, *DiskRequest) (*zbs.Void, error)
	InvalidatePartition(context.Context, *DiskRequest) (*zbs.Void, error)
	ListRejectedDisks(context.Context, *zbs.Void) (*ListRejectedDisksResponse, error)
	AcceptDisk(context.Context, *AcceptDiskRequest) (*zbs.Void, error)
	IsolatePartition(context.Context, *DiskRequest) (*zbs.Void, error)
	StartAurora(context.Context, *zbs.Void) (*zbs.Void, error)
	StopAurora(context.Context, *zbs.Void) (*zbs.Void, error)
	ReloadHostName(context.Context, *zbs.Void) (*zbs.Void, error)
	GetDiskInspectorStats(context.Context, *zbs.Void) (*GetDiskInspectorStatsResponse, error)
	StartDiskInspectorPatrol(context.Context, *zbs.Void) (*zbs.Void, error)
	StopDiskInspectorPatrol(context.Context, *zbs.Void) (*zbs.Void, error)
	StartDirtyBlockTracking(context.Context, *DirtyBlockRequest) (*zbs.Void, error)
	StopDirtyBlockTracking(context.Context, *DirtyBlockRequest) (*zbs.Void, error)
	GetDirtyBlocks(context.Context, *DirtyBlockRequest) (*DirtyBlockResponse, error)
	GetCDPJob(context.Context, *zbs.GetCDPJobRequest) (*zbs.CDPJobInfo, error)
	ListCDPJobs(context.Context, *zbs.ListCDPJobsRequest) (*zbs.ListCDPJobsResponse, error)
	GetPollingStats(context.Context, *zbs.Void) (*GetPollingStatsResponse, error)
	SetVolumeIOLatencyInjection(context.Context, *SetVolumeIOLatencyInjectionRequest) (*SetVolumeIOLatencyInjectionResponse, error)
	ClearVolumeIOLatencyInjection(context.Context, *ClearVolumeIOLatencyInjectionRequest) (*zbs.Void, error)
	ListVolumeIOLatencyInjection(context.Context, *zbs.Void) (*ListVolumeIOLatencyInjectionResponse, error)
	GetTemporaryExtentSummary(context.Context, *zbs.Void) (*GetTemporaryExtentSummaryResponse, error)
	GetTemporaryExtent(context.Context, *GetTemporaryExtentRequest) (*GetTemporaryExtentResponse, error)
	ListTemporaryExtent(context.Context, *ListTemporaryExtentRequest) (*ListTemporaryExtentResponse, error)
	ComparePExtent(context.Context, *ComparePExtentRequest) (*block.CompareExtentResponse, error)
	ListSinkInfo(context.Context, *zbs.Void) (*ListSinkInfoResponse, error)
	SetSinkParams(context.Context, *SetSinkParamsRequest) (*zbs.Void, error)
	SetCapIOThrottle(context.Context, *SetCapIOThrottleRequest) (*zbs.Void, error)
	GetCapIOThrottle(context.Context, *zbs.Void) (*GetCapIOThrottleResponse, error)
	SetInternalIOThrottle(context.Context, *InternalIOThrottleInfo) (*zbs.Void, error)
	GetInternalIOThrottle(context.Context, *zbs.Void) (*InternalIOThrottleInfo, error)
	IsolateCache(context.Context, *DiskRequest) (*zbs.Void, error)
	GetRouteForRDMAUD(context.Context, *zbs.Void) (*RDMAUDRouteInfo, error)
	GetDCManagerInfo(context.Context, *zbs.ChunkInstance) (*DCManagerInfos, error)
	GetDCServerInfo(context.Context, *zbs.ChunkInstance) (*DCServerInfos, error)
	// The following V2 version RPCs are introduced for multi chunk instances mode.
	// These RPCs are used to obtain some information about Chunk, and they can return all instances' responses now.
	GetZbsAddressV2(context.Context, *zbs.ChunkInstance) (*ZbsAddressV2, error)
	ListPartitionV2(context.Context, *zbs.ChunkInstance) (*ListPartitionResponseV2, error)
	ListJournalV2(context.Context, *zbs.ChunkInstance) (*ListJournalResponseV2, error)
	ListCacheV2(context.Context, *zbs.ChunkInstance) (*ListCacheResponseV2, error)
	ListRecoverV2(context.Context, *zbs.ChunkInstance) (*zbs.ListRecoverResponseV2, error)
	ListMigrateV2(context.Context, *zbs.ChunkInstance) (*zbs.ListRecoverResponseV2, error)
	SummaryInfoV2(context.Context, *zbs.ChunkInstance) (*SummaryInfoResponseV2, error)
	ListRejectedDisksV2(context.Context, *zbs.ChunkInstance) (*ListRejectedDisksResponseV2, error)
	GetDiskInspectorStatsV2(context.Context, *zbs.ChunkInstance) (*GetDiskInspectorStatsResponseV2, error)
	GetTemporaryExtentSummaryV2(context.Context, *zbs.ChunkInstance) (*GetTemporaryExtentSummaryResponseV2, error)
	ListSinkInfoV2(context.Context, *zbs.ChunkInstance) (*ListSinkInfoResponseV2, error)
	GetCapIOThrottleV2(context.Context, *zbs.ChunkInstance) (*GetCapIOThrottleResponseV2, error)
	GetInternalIOThrottleV2(context.Context, *zbs.ChunkInstance) (*InternalIOThrottleInfoV2, error)
	mustEmbedUnimplementedChunkServiceServer()
}

// UnimplementedChunkServiceServer must be embedded to have forward compatible implementations.
type UnimplementedChunkServiceServer struct {
}

func (UnimplementedChunkServiceServer) GetZbsAddress(context.Context, *zbs.Void) (*ZbsAddress, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetZbsAddress not implemented")
}
func (UnimplementedChunkServiceServer) FormatPartition(context.Context, *FormatPartitionRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FormatPartition not implemented")
}
func (UnimplementedChunkServiceServer) MountPartition(context.Context, *MountPartitionRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MountPartition not implemented")
}
func (UnimplementedChunkServiceServer) UmountPartition(context.Context, *UmountDiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UmountPartition not implemented")
}
func (UnimplementedChunkServiceServer) MountCache(context.Context, *MountCacheRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MountCache not implemented")
}
func (UnimplementedChunkServiceServer) UmountCache(context.Context, *UmountDiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UmountCache not implemented")
}
func (UnimplementedChunkServiceServer) FormatJournal(context.Context, *FormatJournalRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FormatJournal not implemented")
}
func (UnimplementedChunkServiceServer) MountJournal(context.Context, *MountJournalRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MountJournal not implemented")
}
func (UnimplementedChunkServiceServer) UmountJournal(context.Context, *UmountDiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UmountJournal not implemented")
}
func (UnimplementedChunkServiceServer) FlushAllJournals(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlushAllJournals not implemented")
}
func (UnimplementedChunkServiceServer) ListPartition(context.Context, *zbs.Void) (*ListPartitionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPartition not implemented")
}
func (UnimplementedChunkServiceServer) ListJournal(context.Context, *zbs.Void) (*ListJournalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJournal not implemented")
}
func (UnimplementedChunkServiceServer) ListCache(context.Context, *zbs.Void) (*ListCacheResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCache not implemented")
}
func (UnimplementedChunkServiceServer) StopServer(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopServer not implemented")
}
func (UnimplementedChunkServiceServer) ListClient(context.Context, *zbs.Void) (*ListClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClient not implemented")
}
func (UnimplementedChunkServiceServer) ListRecover(context.Context, *zbs.Void) (*zbs.ListRecoverResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRecover not implemented")
}
func (UnimplementedChunkServiceServer) ListMigrate(context.Context, *zbs.Void) (*zbs.ListRecoverResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMigrate not implemented")
}
func (UnimplementedChunkServiceServer) ListExtent(context.Context, *ListPExtentRequest) (*ListExtentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExtent not implemented")
}
func (UnimplementedChunkServiceServer) CheckExtent(context.Context, *PExtentRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckExtent not implemented")
}
func (UnimplementedChunkServiceServer) InvalidateCache(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvalidateCache not implemented")
}
func (UnimplementedChunkServiceServer) CheckAllExtents(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAllExtents not implemented")
}
func (UnimplementedChunkServiceServer) ShowExtent(context.Context, *ShowExtentRequest) (*ShowExtentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShowExtent not implemented")
}
func (UnimplementedChunkServiceServer) PromoteExtent(context.Context, *PromoteExtentRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PromoteExtent not implemented")
}
func (UnimplementedChunkServiceServer) GetDiskInfo(context.Context, *GetDiskInfoRequest) (*GetDiskInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiskInfo not implemented")
}
func (UnimplementedChunkServiceServer) InvalidateExtent(context.Context, *InvalidateExtentRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvalidateExtent not implemented")
}
func (UnimplementedChunkServiceServer) SetVerifyMode(context.Context, *SetVerifyModeRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetVerifyMode not implemented")
}
func (UnimplementedChunkServiceServer) UpdateSecondaryDataIP(context.Context, *UpdateSecondaryDataIPRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSecondaryDataIP not implemented")
}
func (UnimplementedChunkServiceServer) SetUnhealthyPartition(context.Context, *SetUnhealthyRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUnhealthyPartition not implemented")
}
func (UnimplementedChunkServiceServer) SetHealthyPartition(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHealthyPartition not implemented")
}
func (UnimplementedChunkServiceServer) SetUnhealthyCache(context.Context, *SetUnhealthyRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUnhealthyCache not implemented")
}
func (UnimplementedChunkServiceServer) SetHealthyCache(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHealthyCache not implemented")
}
func (UnimplementedChunkServiceServer) SetUnhealthyJournal(context.Context, *SetUnhealthyRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUnhealthyJournal not implemented")
}
func (UnimplementedChunkServiceServer) SetHealthyJournal(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHealthyJournal not implemented")
}
func (UnimplementedChunkServiceServer) FormatCache(context.Context, *FormatCacheRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FormatCache not implemented")
}
func (UnimplementedChunkServiceServer) QueryDisk(context.Context, *QueryDiskRequest) (*QueryDiskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryDisk not implemented")
}
func (UnimplementedChunkServiceServer) SummaryInfo(context.Context, *zbs.Void) (*SummaryInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SummaryInfo not implemented")
}
func (UnimplementedChunkServiceServer) UpdateScvmModeHostDataIP(context.Context, *UpdateScvmModeHostDataIPRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateScvmModeHostDataIP not implemented")
}
func (UnimplementedChunkServiceServer) GetChunkServiceStat(context.Context, *zbs.Void) (*ChunkServiceStat, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChunkServiceStat not implemented")
}
func (UnimplementedChunkServiceServer) ReloadConfig(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadConfig not implemented")
}
func (UnimplementedChunkServiceServer) GetZkHosts(context.Context, *zbs.Void) (*GetZkHostsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetZkHosts not implemented")
}
func (UnimplementedChunkServiceServer) CancelUmountPartition(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelUmountPartition not implemented")
}
func (UnimplementedChunkServiceServer) CancelUmountCache(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelUmountCache not implemented")
}
func (UnimplementedChunkServiceServer) InvalidatePartition(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvalidatePartition not implemented")
}
func (UnimplementedChunkServiceServer) ListRejectedDisks(context.Context, *zbs.Void) (*ListRejectedDisksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRejectedDisks not implemented")
}
func (UnimplementedChunkServiceServer) AcceptDisk(context.Context, *AcceptDiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptDisk not implemented")
}
func (UnimplementedChunkServiceServer) IsolatePartition(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsolatePartition not implemented")
}
func (UnimplementedChunkServiceServer) StartAurora(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAurora not implemented")
}
func (UnimplementedChunkServiceServer) StopAurora(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopAurora not implemented")
}
func (UnimplementedChunkServiceServer) ReloadHostName(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadHostName not implemented")
}
func (UnimplementedChunkServiceServer) GetDiskInspectorStats(context.Context, *zbs.Void) (*GetDiskInspectorStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiskInspectorStats not implemented")
}
func (UnimplementedChunkServiceServer) StartDiskInspectorPatrol(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDiskInspectorPatrol not implemented")
}
func (UnimplementedChunkServiceServer) StopDiskInspectorPatrol(context.Context, *zbs.Void) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDiskInspectorPatrol not implemented")
}
func (UnimplementedChunkServiceServer) StartDirtyBlockTracking(context.Context, *DirtyBlockRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDirtyBlockTracking not implemented")
}
func (UnimplementedChunkServiceServer) StopDirtyBlockTracking(context.Context, *DirtyBlockRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDirtyBlockTracking not implemented")
}
func (UnimplementedChunkServiceServer) GetDirtyBlocks(context.Context, *DirtyBlockRequest) (*DirtyBlockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDirtyBlocks not implemented")
}
func (UnimplementedChunkServiceServer) GetCDPJob(context.Context, *zbs.GetCDPJobRequest) (*zbs.CDPJobInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCDPJob not implemented")
}
func (UnimplementedChunkServiceServer) ListCDPJobs(context.Context, *zbs.ListCDPJobsRequest) (*zbs.ListCDPJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCDPJobs not implemented")
}
func (UnimplementedChunkServiceServer) GetPollingStats(context.Context, *zbs.Void) (*GetPollingStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPollingStats not implemented")
}
func (UnimplementedChunkServiceServer) SetVolumeIOLatencyInjection(context.Context, *SetVolumeIOLatencyInjectionRequest) (*SetVolumeIOLatencyInjectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetVolumeIOLatencyInjection not implemented")
}
func (UnimplementedChunkServiceServer) ClearVolumeIOLatencyInjection(context.Context, *ClearVolumeIOLatencyInjectionRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearVolumeIOLatencyInjection not implemented")
}
func (UnimplementedChunkServiceServer) ListVolumeIOLatencyInjection(context.Context, *zbs.Void) (*ListVolumeIOLatencyInjectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVolumeIOLatencyInjection not implemented")
}
func (UnimplementedChunkServiceServer) GetTemporaryExtentSummary(context.Context, *zbs.Void) (*GetTemporaryExtentSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemporaryExtentSummary not implemented")
}
func (UnimplementedChunkServiceServer) GetTemporaryExtent(context.Context, *GetTemporaryExtentRequest) (*GetTemporaryExtentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemporaryExtent not implemented")
}
func (UnimplementedChunkServiceServer) ListTemporaryExtent(context.Context, *ListTemporaryExtentRequest) (*ListTemporaryExtentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemporaryExtent not implemented")
}
func (UnimplementedChunkServiceServer) ComparePExtent(context.Context, *ComparePExtentRequest) (*block.CompareExtentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComparePExtent not implemented")
}
func (UnimplementedChunkServiceServer) ListSinkInfo(context.Context, *zbs.Void) (*ListSinkInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSinkInfo not implemented")
}
func (UnimplementedChunkServiceServer) SetSinkParams(context.Context, *SetSinkParamsRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSinkParams not implemented")
}
func (UnimplementedChunkServiceServer) SetCapIOThrottle(context.Context, *SetCapIOThrottleRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCapIOThrottle not implemented")
}
func (UnimplementedChunkServiceServer) GetCapIOThrottle(context.Context, *zbs.Void) (*GetCapIOThrottleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCapIOThrottle not implemented")
}
func (UnimplementedChunkServiceServer) SetInternalIOThrottle(context.Context, *InternalIOThrottleInfo) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetInternalIOThrottle not implemented")
}
func (UnimplementedChunkServiceServer) GetInternalIOThrottle(context.Context, *zbs.Void) (*InternalIOThrottleInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternalIOThrottle not implemented")
}
func (UnimplementedChunkServiceServer) IsolateCache(context.Context, *DiskRequest) (*zbs.Void, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsolateCache not implemented")
}
func (UnimplementedChunkServiceServer) GetRouteForRDMAUD(context.Context, *zbs.Void) (*RDMAUDRouteInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRouteForRDMAUD not implemented")
}
func (UnimplementedChunkServiceServer) GetDCManagerInfo(context.Context, *zbs.ChunkInstance) (*DCManagerInfos, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDCManagerInfo not implemented")
}
func (UnimplementedChunkServiceServer) GetDCServerInfo(context.Context, *zbs.ChunkInstance) (*DCServerInfos, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDCServerInfo not implemented")
}
func (UnimplementedChunkServiceServer) GetZbsAddressV2(context.Context, *zbs.ChunkInstance) (*ZbsAddressV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetZbsAddressV2 not implemented")
}
func (UnimplementedChunkServiceServer) ListPartitionV2(context.Context, *zbs.ChunkInstance) (*ListPartitionResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPartitionV2 not implemented")
}
func (UnimplementedChunkServiceServer) ListJournalV2(context.Context, *zbs.ChunkInstance) (*ListJournalResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJournalV2 not implemented")
}
func (UnimplementedChunkServiceServer) ListCacheV2(context.Context, *zbs.ChunkInstance) (*ListCacheResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCacheV2 not implemented")
}
func (UnimplementedChunkServiceServer) ListRecoverV2(context.Context, *zbs.ChunkInstance) (*zbs.ListRecoverResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRecoverV2 not implemented")
}
func (UnimplementedChunkServiceServer) ListMigrateV2(context.Context, *zbs.ChunkInstance) (*zbs.ListRecoverResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMigrateV2 not implemented")
}
func (UnimplementedChunkServiceServer) SummaryInfoV2(context.Context, *zbs.ChunkInstance) (*SummaryInfoResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SummaryInfoV2 not implemented")
}
func (UnimplementedChunkServiceServer) ListRejectedDisksV2(context.Context, *zbs.ChunkInstance) (*ListRejectedDisksResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRejectedDisksV2 not implemented")
}
func (UnimplementedChunkServiceServer) GetDiskInspectorStatsV2(context.Context, *zbs.ChunkInstance) (*GetDiskInspectorStatsResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiskInspectorStatsV2 not implemented")
}
func (UnimplementedChunkServiceServer) GetTemporaryExtentSummaryV2(context.Context, *zbs.ChunkInstance) (*GetTemporaryExtentSummaryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemporaryExtentSummaryV2 not implemented")
}
func (UnimplementedChunkServiceServer) ListSinkInfoV2(context.Context, *zbs.ChunkInstance) (*ListSinkInfoResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSinkInfoV2 not implemented")
}
func (UnimplementedChunkServiceServer) GetCapIOThrottleV2(context.Context, *zbs.ChunkInstance) (*GetCapIOThrottleResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCapIOThrottleV2 not implemented")
}
func (UnimplementedChunkServiceServer) GetInternalIOThrottleV2(context.Context, *zbs.ChunkInstance) (*InternalIOThrottleInfoV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternalIOThrottleV2 not implemented")
}
func (UnimplementedChunkServiceServer) mustEmbedUnimplementedChunkServiceServer() {}

// UnsafeChunkServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChunkServiceServer will
// result in compilation errors.
type UnsafeChunkServiceServer interface {
	mustEmbedUnimplementedChunkServiceServer()
}

func RegisterChunkServiceServer(s grpc.ServiceRegistrar, srv ChunkServiceServer) {
	s.RegisterService(&ChunkService_ServiceDesc, srv)
}

func _ChunkService_GetZbsAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetZbsAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetZbsAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetZbsAddress(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_FormatPartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FormatPartitionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).FormatPartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_FormatPartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).FormatPartition(ctx, req.(*FormatPartitionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_MountPartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MountPartitionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).MountPartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_MountPartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).MountPartition(ctx, req.(*MountPartitionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_UmountPartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmountDiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).UmountPartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_UmountPartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).UmountPartition(ctx, req.(*UmountDiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_MountCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MountCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).MountCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_MountCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).MountCache(ctx, req.(*MountCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_UmountCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmountDiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).UmountCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_UmountCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).UmountCache(ctx, req.(*UmountDiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_FormatJournal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FormatJournalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).FormatJournal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_FormatJournal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).FormatJournal(ctx, req.(*FormatJournalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_MountJournal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MountJournalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).MountJournal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_MountJournal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).MountJournal(ctx, req.(*MountJournalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_UmountJournal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmountDiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).UmountJournal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_UmountJournal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).UmountJournal(ctx, req.(*UmountDiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_FlushAllJournals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).FlushAllJournals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_FlushAllJournals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).FlushAllJournals(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListPartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListPartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListPartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListPartition(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListJournal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListJournal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListJournal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListJournal(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListCache(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_StopServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).StopServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_StopServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).StopServer(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListClient(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListRecover_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListRecover(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListRecover_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListRecover(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListMigrate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListMigrate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListMigrate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListMigrate(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListExtent(ctx, req.(*ListPExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_CheckExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).CheckExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_CheckExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).CheckExtent(ctx, req.(*PExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_InvalidateCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).InvalidateCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_InvalidateCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).InvalidateCache(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_CheckAllExtents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).CheckAllExtents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_CheckAllExtents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).CheckAllExtents(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ShowExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ShowExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ShowExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ShowExtent(ctx, req.(*ShowExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_PromoteExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromoteExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).PromoteExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_PromoteExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).PromoteExtent(ctx, req.(*PromoteExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetDiskInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiskInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetDiskInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetDiskInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetDiskInfo(ctx, req.(*GetDiskInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_InvalidateExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvalidateExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).InvalidateExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_InvalidateExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).InvalidateExtent(ctx, req.(*InvalidateExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetVerifyMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVerifyModeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetVerifyMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetVerifyMode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetVerifyMode(ctx, req.(*SetVerifyModeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_UpdateSecondaryDataIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSecondaryDataIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).UpdateSecondaryDataIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_UpdateSecondaryDataIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).UpdateSecondaryDataIP(ctx, req.(*UpdateSecondaryDataIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetUnhealthyPartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUnhealthyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetUnhealthyPartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetUnhealthyPartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetUnhealthyPartition(ctx, req.(*SetUnhealthyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetHealthyPartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetHealthyPartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetHealthyPartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetHealthyPartition(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetUnhealthyCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUnhealthyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetUnhealthyCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetUnhealthyCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetUnhealthyCache(ctx, req.(*SetUnhealthyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetHealthyCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetHealthyCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetHealthyCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetHealthyCache(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetUnhealthyJournal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUnhealthyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetUnhealthyJournal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetUnhealthyJournal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetUnhealthyJournal(ctx, req.(*SetUnhealthyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetHealthyJournal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetHealthyJournal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetHealthyJournal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetHealthyJournal(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_FormatCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FormatCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).FormatCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_FormatCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).FormatCache(ctx, req.(*FormatCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_QueryDisk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryDiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).QueryDisk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_QueryDisk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).QueryDisk(ctx, req.(*QueryDiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SummaryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SummaryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SummaryInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SummaryInfo(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_UpdateScvmModeHostDataIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateScvmModeHostDataIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).UpdateScvmModeHostDataIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_UpdateScvmModeHostDataIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).UpdateScvmModeHostDataIP(ctx, req.(*UpdateScvmModeHostDataIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetChunkServiceStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetChunkServiceStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetChunkServiceStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetChunkServiceStat(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ReloadConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ReloadConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ReloadConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ReloadConfig(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetZkHosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetZkHosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetZkHosts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetZkHosts(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_CancelUmountPartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).CancelUmountPartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_CancelUmountPartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).CancelUmountPartition(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_CancelUmountCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).CancelUmountCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_CancelUmountCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).CancelUmountCache(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_InvalidatePartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).InvalidatePartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_InvalidatePartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).InvalidatePartition(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListRejectedDisks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListRejectedDisks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListRejectedDisks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListRejectedDisks(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_AcceptDisk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptDiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).AcceptDisk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_AcceptDisk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).AcceptDisk(ctx, req.(*AcceptDiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_IsolatePartition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).IsolatePartition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_IsolatePartition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).IsolatePartition(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_StartAurora_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).StartAurora(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_StartAurora_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).StartAurora(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_StopAurora_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).StopAurora(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_StopAurora_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).StopAurora(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ReloadHostName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ReloadHostName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ReloadHostName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ReloadHostName(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetDiskInspectorStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetDiskInspectorStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetDiskInspectorStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetDiskInspectorStats(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_StartDiskInspectorPatrol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).StartDiskInspectorPatrol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_StartDiskInspectorPatrol_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).StartDiskInspectorPatrol(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_StopDiskInspectorPatrol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).StopDiskInspectorPatrol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_StopDiskInspectorPatrol_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).StopDiskInspectorPatrol(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_StartDirtyBlockTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirtyBlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).StartDirtyBlockTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_StartDirtyBlockTracking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).StartDirtyBlockTracking(ctx, req.(*DirtyBlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_StopDirtyBlockTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirtyBlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).StopDirtyBlockTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_StopDirtyBlockTracking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).StopDirtyBlockTracking(ctx, req.(*DirtyBlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetDirtyBlocks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirtyBlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetDirtyBlocks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetDirtyBlocks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetDirtyBlocks(ctx, req.(*DirtyBlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetCDPJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.GetCDPJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetCDPJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetCDPJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetCDPJob(ctx, req.(*zbs.GetCDPJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListCDPJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ListCDPJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListCDPJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListCDPJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListCDPJobs(ctx, req.(*zbs.ListCDPJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetPollingStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetPollingStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetPollingStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetPollingStats(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetVolumeIOLatencyInjection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVolumeIOLatencyInjectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetVolumeIOLatencyInjection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetVolumeIOLatencyInjection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetVolumeIOLatencyInjection(ctx, req.(*SetVolumeIOLatencyInjectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ClearVolumeIOLatencyInjection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearVolumeIOLatencyInjectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ClearVolumeIOLatencyInjection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ClearVolumeIOLatencyInjection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ClearVolumeIOLatencyInjection(ctx, req.(*ClearVolumeIOLatencyInjectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListVolumeIOLatencyInjection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListVolumeIOLatencyInjection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListVolumeIOLatencyInjection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListVolumeIOLatencyInjection(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetTemporaryExtentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetTemporaryExtentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetTemporaryExtentSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetTemporaryExtentSummary(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetTemporaryExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemporaryExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetTemporaryExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetTemporaryExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetTemporaryExtent(ctx, req.(*GetTemporaryExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListTemporaryExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemporaryExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListTemporaryExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListTemporaryExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListTemporaryExtent(ctx, req.(*ListTemporaryExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ComparePExtent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComparePExtentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ComparePExtent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ComparePExtent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ComparePExtent(ctx, req.(*ComparePExtentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListSinkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListSinkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListSinkInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListSinkInfo(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetSinkParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSinkParamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetSinkParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetSinkParams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetSinkParams(ctx, req.(*SetSinkParamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetCapIOThrottle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCapIOThrottleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetCapIOThrottle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetCapIOThrottle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetCapIOThrottle(ctx, req.(*SetCapIOThrottleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetCapIOThrottle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetCapIOThrottle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetCapIOThrottle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetCapIOThrottle(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SetInternalIOThrottle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalIOThrottleInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SetInternalIOThrottle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SetInternalIOThrottle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SetInternalIOThrottle(ctx, req.(*InternalIOThrottleInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetInternalIOThrottle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetInternalIOThrottle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetInternalIOThrottle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetInternalIOThrottle(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_IsolateCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).IsolateCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_IsolateCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).IsolateCache(ctx, req.(*DiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetRouteForRDMAUD_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.Void)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetRouteForRDMAUD(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetRouteForRDMAUD_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetRouteForRDMAUD(ctx, req.(*zbs.Void))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetDCManagerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetDCManagerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetDCManagerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetDCManagerInfo(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetDCServerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetDCServerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetDCServerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetDCServerInfo(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetZbsAddressV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetZbsAddressV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetZbsAddressV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetZbsAddressV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListPartitionV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListPartitionV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListPartitionV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListPartitionV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListJournalV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListJournalV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListJournalV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListJournalV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListCacheV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListCacheV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListCacheV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListCacheV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListRecoverV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListRecoverV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListRecoverV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListRecoverV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListMigrateV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListMigrateV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListMigrateV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListMigrateV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_SummaryInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).SummaryInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_SummaryInfoV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).SummaryInfoV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListRejectedDisksV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListRejectedDisksV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListRejectedDisksV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListRejectedDisksV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetDiskInspectorStatsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetDiskInspectorStatsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetDiskInspectorStatsV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetDiskInspectorStatsV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetTemporaryExtentSummaryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetTemporaryExtentSummaryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetTemporaryExtentSummaryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetTemporaryExtentSummaryV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_ListSinkInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).ListSinkInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_ListSinkInfoV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).ListSinkInfoV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetCapIOThrottleV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetCapIOThrottleV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetCapIOThrottleV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetCapIOThrottleV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChunkService_GetInternalIOThrottleV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zbs.ChunkInstance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChunkServiceServer).GetInternalIOThrottleV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChunkService_GetInternalIOThrottleV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChunkServiceServer).GetInternalIOThrottleV2(ctx, req.(*zbs.ChunkInstance))
	}
	return interceptor(ctx, in, info, handler)
}

// ChunkService_ServiceDesc is the grpc.ServiceDesc for ChunkService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChunkService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zbs.chunk.ChunkService",
	HandlerType: (*ChunkServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetZbsAddress",
			Handler:    _ChunkService_GetZbsAddress_Handler,
		},
		{
			MethodName: "FormatPartition",
			Handler:    _ChunkService_FormatPartition_Handler,
		},
		{
			MethodName: "MountPartition",
			Handler:    _ChunkService_MountPartition_Handler,
		},
		{
			MethodName: "UmountPartition",
			Handler:    _ChunkService_UmountPartition_Handler,
		},
		{
			MethodName: "MountCache",
			Handler:    _ChunkService_MountCache_Handler,
		},
		{
			MethodName: "UmountCache",
			Handler:    _ChunkService_UmountCache_Handler,
		},
		{
			MethodName: "FormatJournal",
			Handler:    _ChunkService_FormatJournal_Handler,
		},
		{
			MethodName: "MountJournal",
			Handler:    _ChunkService_MountJournal_Handler,
		},
		{
			MethodName: "UmountJournal",
			Handler:    _ChunkService_UmountJournal_Handler,
		},
		{
			MethodName: "FlushAllJournals",
			Handler:    _ChunkService_FlushAllJournals_Handler,
		},
		{
			MethodName: "ListPartition",
			Handler:    _ChunkService_ListPartition_Handler,
		},
		{
			MethodName: "ListJournal",
			Handler:    _ChunkService_ListJournal_Handler,
		},
		{
			MethodName: "ListCache",
			Handler:    _ChunkService_ListCache_Handler,
		},
		{
			MethodName: "StopServer",
			Handler:    _ChunkService_StopServer_Handler,
		},
		{
			MethodName: "ListClient",
			Handler:    _ChunkService_ListClient_Handler,
		},
		{
			MethodName: "ListRecover",
			Handler:    _ChunkService_ListRecover_Handler,
		},
		{
			MethodName: "ListMigrate",
			Handler:    _ChunkService_ListMigrate_Handler,
		},
		{
			MethodName: "ListExtent",
			Handler:    _ChunkService_ListExtent_Handler,
		},
		{
			MethodName: "CheckExtent",
			Handler:    _ChunkService_CheckExtent_Handler,
		},
		{
			MethodName: "InvalidateCache",
			Handler:    _ChunkService_InvalidateCache_Handler,
		},
		{
			MethodName: "CheckAllExtents",
			Handler:    _ChunkService_CheckAllExtents_Handler,
		},
		{
			MethodName: "ShowExtent",
			Handler:    _ChunkService_ShowExtent_Handler,
		},
		{
			MethodName: "PromoteExtent",
			Handler:    _ChunkService_PromoteExtent_Handler,
		},
		{
			MethodName: "GetDiskInfo",
			Handler:    _ChunkService_GetDiskInfo_Handler,
		},
		{
			MethodName: "InvalidateExtent",
			Handler:    _ChunkService_InvalidateExtent_Handler,
		},
		{
			MethodName: "SetVerifyMode",
			Handler:    _ChunkService_SetVerifyMode_Handler,
		},
		{
			MethodName: "UpdateSecondaryDataIP",
			Handler:    _ChunkService_UpdateSecondaryDataIP_Handler,
		},
		{
			MethodName: "SetUnhealthyPartition",
			Handler:    _ChunkService_SetUnhealthyPartition_Handler,
		},
		{
			MethodName: "SetHealthyPartition",
			Handler:    _ChunkService_SetHealthyPartition_Handler,
		},
		{
			MethodName: "SetUnhealthyCache",
			Handler:    _ChunkService_SetUnhealthyCache_Handler,
		},
		{
			MethodName: "SetHealthyCache",
			Handler:    _ChunkService_SetHealthyCache_Handler,
		},
		{
			MethodName: "SetUnhealthyJournal",
			Handler:    _ChunkService_SetUnhealthyJournal_Handler,
		},
		{
			MethodName: "SetHealthyJournal",
			Handler:    _ChunkService_SetHealthyJournal_Handler,
		},
		{
			MethodName: "FormatCache",
			Handler:    _ChunkService_FormatCache_Handler,
		},
		{
			MethodName: "QueryDisk",
			Handler:    _ChunkService_QueryDisk_Handler,
		},
		{
			MethodName: "SummaryInfo",
			Handler:    _ChunkService_SummaryInfo_Handler,
		},
		{
			MethodName: "UpdateScvmModeHostDataIP",
			Handler:    _ChunkService_UpdateScvmModeHostDataIP_Handler,
		},
		{
			MethodName: "GetChunkServiceStat",
			Handler:    _ChunkService_GetChunkServiceStat_Handler,
		},
		{
			MethodName: "ReloadConfig",
			Handler:    _ChunkService_ReloadConfig_Handler,
		},
		{
			MethodName: "GetZkHosts",
			Handler:    _ChunkService_GetZkHosts_Handler,
		},
		{
			MethodName: "CancelUmountPartition",
			Handler:    _ChunkService_CancelUmountPartition_Handler,
		},
		{
			MethodName: "CancelUmountCache",
			Handler:    _ChunkService_CancelUmountCache_Handler,
		},
		{
			MethodName: "InvalidatePartition",
			Handler:    _ChunkService_InvalidatePartition_Handler,
		},
		{
			MethodName: "ListRejectedDisks",
			Handler:    _ChunkService_ListRejectedDisks_Handler,
		},
		{
			MethodName: "AcceptDisk",
			Handler:    _ChunkService_AcceptDisk_Handler,
		},
		{
			MethodName: "IsolatePartition",
			Handler:    _ChunkService_IsolatePartition_Handler,
		},
		{
			MethodName: "StartAurora",
			Handler:    _ChunkService_StartAurora_Handler,
		},
		{
			MethodName: "StopAurora",
			Handler:    _ChunkService_StopAurora_Handler,
		},
		{
			MethodName: "ReloadHostName",
			Handler:    _ChunkService_ReloadHostName_Handler,
		},
		{
			MethodName: "GetDiskInspectorStats",
			Handler:    _ChunkService_GetDiskInspectorStats_Handler,
		},
		{
			MethodName: "StartDiskInspectorPatrol",
			Handler:    _ChunkService_StartDiskInspectorPatrol_Handler,
		},
		{
			MethodName: "StopDiskInspectorPatrol",
			Handler:    _ChunkService_StopDiskInspectorPatrol_Handler,
		},
		{
			MethodName: "StartDirtyBlockTracking",
			Handler:    _ChunkService_StartDirtyBlockTracking_Handler,
		},
		{
			MethodName: "StopDirtyBlockTracking",
			Handler:    _ChunkService_StopDirtyBlockTracking_Handler,
		},
		{
			MethodName: "GetDirtyBlocks",
			Handler:    _ChunkService_GetDirtyBlocks_Handler,
		},
		{
			MethodName: "GetCDPJob",
			Handler:    _ChunkService_GetCDPJob_Handler,
		},
		{
			MethodName: "ListCDPJobs",
			Handler:    _ChunkService_ListCDPJobs_Handler,
		},
		{
			MethodName: "GetPollingStats",
			Handler:    _ChunkService_GetPollingStats_Handler,
		},
		{
			MethodName: "SetVolumeIOLatencyInjection",
			Handler:    _ChunkService_SetVolumeIOLatencyInjection_Handler,
		},
		{
			MethodName: "ClearVolumeIOLatencyInjection",
			Handler:    _ChunkService_ClearVolumeIOLatencyInjection_Handler,
		},
		{
			MethodName: "ListVolumeIOLatencyInjection",
			Handler:    _ChunkService_ListVolumeIOLatencyInjection_Handler,
		},
		{
			MethodName: "GetTemporaryExtentSummary",
			Handler:    _ChunkService_GetTemporaryExtentSummary_Handler,
		},
		{
			MethodName: "GetTemporaryExtent",
			Handler:    _ChunkService_GetTemporaryExtent_Handler,
		},
		{
			MethodName: "ListTemporaryExtent",
			Handler:    _ChunkService_ListTemporaryExtent_Handler,
		},
		{
			MethodName: "ComparePExtent",
			Handler:    _ChunkService_ComparePExtent_Handler,
		},
		{
			MethodName: "ListSinkInfo",
			Handler:    _ChunkService_ListSinkInfo_Handler,
		},
		{
			MethodName: "SetSinkParams",
			Handler:    _ChunkService_SetSinkParams_Handler,
		},
		{
			MethodName: "SetCapIOThrottle",
			Handler:    _ChunkService_SetCapIOThrottle_Handler,
		},
		{
			MethodName: "GetCapIOThrottle",
			Handler:    _ChunkService_GetCapIOThrottle_Handler,
		},
		{
			MethodName: "SetInternalIOThrottle",
			Handler:    _ChunkService_SetInternalIOThrottle_Handler,
		},
		{
			MethodName: "GetInternalIOThrottle",
			Handler:    _ChunkService_GetInternalIOThrottle_Handler,
		},
		{
			MethodName: "IsolateCache",
			Handler:    _ChunkService_IsolateCache_Handler,
		},
		{
			MethodName: "GetRouteForRDMAUD",
			Handler:    _ChunkService_GetRouteForRDMAUD_Handler,
		},
		{
			MethodName: "GetDCManagerInfo",
			Handler:    _ChunkService_GetDCManagerInfo_Handler,
		},
		{
			MethodName: "GetDCServerInfo",
			Handler:    _ChunkService_GetDCServerInfo_Handler,
		},
		{
			MethodName: "GetZbsAddressV2",
			Handler:    _ChunkService_GetZbsAddressV2_Handler,
		},
		{
			MethodName: "ListPartitionV2",
			Handler:    _ChunkService_ListPartitionV2_Handler,
		},
		{
			MethodName: "ListJournalV2",
			Handler:    _ChunkService_ListJournalV2_Handler,
		},
		{
			MethodName: "ListCacheV2",
			Handler:    _ChunkService_ListCacheV2_Handler,
		},
		{
			MethodName: "ListRecoverV2",
			Handler:    _ChunkService_ListRecoverV2_Handler,
		},
		{
			MethodName: "ListMigrateV2",
			Handler:    _ChunkService_ListMigrateV2_Handler,
		},
		{
			MethodName: "SummaryInfoV2",
			Handler:    _ChunkService_SummaryInfoV2_Handler,
		},
		{
			MethodName: "ListRejectedDisksV2",
			Handler:    _ChunkService_ListRejectedDisksV2_Handler,
		},
		{
			MethodName: "GetDiskInspectorStatsV2",
			Handler:    _ChunkService_GetDiskInspectorStatsV2_Handler,
		},
		{
			MethodName: "GetTemporaryExtentSummaryV2",
			Handler:    _ChunkService_GetTemporaryExtentSummaryV2_Handler,
		},
		{
			MethodName: "ListSinkInfoV2",
			Handler:    _ChunkService_ListSinkInfoV2_Handler,
		},
		{
			MethodName: "GetCapIOThrottleV2",
			Handler:    _ChunkService_GetCapIOThrottleV2_Handler,
		},
		{
			MethodName: "GetInternalIOThrottleV2",
			Handler:    _ChunkService_GetInternalIOThrottleV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "chunk.proto",
}
