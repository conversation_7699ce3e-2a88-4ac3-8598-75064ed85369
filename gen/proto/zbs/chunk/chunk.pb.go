// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: chunk.proto

package chunk

import (
	zbs "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	block "github.com/iomesh/zbs-client-go/gen/proto/zbs/block"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PartitionStatus int32

const (
	PartitionStatus_PARTITION_MOUNTED   PartitionStatus = 0
	PartitionStatus_PARTITION_STAGING   PartitionStatus = 1
	PartitionStatus_PARTITION_MIGRATING PartitionStatus = 2
)

// Enum value maps for PartitionStatus.
var (
	PartitionStatus_name = map[int32]string{
		0: "PARTITION_MOUNTED",
		1: "PARTITION_STAGING",
		2: "PARTITION_MIGRATING",
	}
	PartitionStatus_value = map[string]int32{
		"PARTITION_MOUNTED":   0,
		"PARTITION_STAGING":   1,
		"PARTITION_MIGRATING": 2,
	}
)

func (x PartitionStatus) Enum() *PartitionStatus {
	p := new(PartitionStatus)
	*p = x
	return p
}

func (x PartitionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PartitionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[0].Descriptor()
}

func (PartitionStatus) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[0]
}

func (x PartitionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PartitionStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PartitionStatus(num)
	return nil
}

// Deprecated: Use PartitionStatus.Descriptor instead.
func (PartitionStatus) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{0}
}

type DiskErrFlags int32

const (
	DiskErrFlags_DISK_ERR_SMART_FAIL                DiskErrFlags = 1
	DiskErrFlags___DEPRECATED_DISK_ERR_HIGH_LAT     DiskErrFlags = 2
	DiskErrFlags_DISK_ERR_IO                        DiskErrFlags = 4
	DiskErrFlags_DISK_ERR_CHECKSUM                  DiskErrFlags = 8
	DiskErrFlags___DEPRECATED_DISK_ERR_UMOUNTING    DiskErrFlags = 16
	DiskErrFlags___DEPRECATED_DISK_ERR_FORCE_UMOUNT DiskErrFlags = 32
	DiskErrFlags_DISK_ERR_SYNC                      DiskErrFlags = 64
)

// Enum value maps for DiskErrFlags.
var (
	DiskErrFlags_name = map[int32]string{
		1:  "DISK_ERR_SMART_FAIL",
		2:  "__DEPRECATED_DISK_ERR_HIGH_LAT",
		4:  "DISK_ERR_IO",
		8:  "DISK_ERR_CHECKSUM",
		16: "__DEPRECATED_DISK_ERR_UMOUNTING",
		32: "__DEPRECATED_DISK_ERR_FORCE_UMOUNT",
		64: "DISK_ERR_SYNC",
	}
	DiskErrFlags_value = map[string]int32{
		"DISK_ERR_SMART_FAIL":                1,
		"__DEPRECATED_DISK_ERR_HIGH_LAT":     2,
		"DISK_ERR_IO":                        4,
		"DISK_ERR_CHECKSUM":                  8,
		"__DEPRECATED_DISK_ERR_UMOUNTING":    16,
		"__DEPRECATED_DISK_ERR_FORCE_UMOUNT": 32,
		"DISK_ERR_SYNC":                      64,
	}
)

func (x DiskErrFlags) Enum() *DiskErrFlags {
	p := new(DiskErrFlags)
	*p = x
	return p
}

func (x DiskErrFlags) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiskErrFlags) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[1].Descriptor()
}

func (DiskErrFlags) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[1]
}

func (x DiskErrFlags) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DiskErrFlags) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DiskErrFlags(num)
	return nil
}

// Deprecated: Use DiskErrFlags.Descriptor instead.
func (DiskErrFlags) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{1}
}

type DiskWarnFlags int32

const (
	DiskWarnFlags_DISK_WARN_HIGH_LAT DiskWarnFlags = 1
)

// Enum value maps for DiskWarnFlags.
var (
	DiskWarnFlags_name = map[int32]string{
		1: "DISK_WARN_HIGH_LAT",
	}
	DiskWarnFlags_value = map[string]int32{
		"DISK_WARN_HIGH_LAT": 1,
	}
)

func (x DiskWarnFlags) Enum() *DiskWarnFlags {
	p := new(DiskWarnFlags)
	*p = x
	return p
}

func (x DiskWarnFlags) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiskWarnFlags) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[2].Descriptor()
}

func (DiskWarnFlags) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[2]
}

func (x DiskWarnFlags) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DiskWarnFlags) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DiskWarnFlags(num)
	return nil
}

// Deprecated: Use DiskWarnFlags.Descriptor instead.
func (DiskWarnFlags) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{2}
}

type LSMCapability int32

const (
	// all defined capabilities should be pow of 2
	LSMCapability_LSM_CAP_DISK_SAFE_UMOUNT      LSMCapability = 1
	LSMCapability_LSM_CAP_DISK_REJECT_UNHEALTHY LSMCapability = 2
	LSMCapability_LSM_CAP_PARTITION_ISOLATE     LSMCapability = 4
	LSMCapability_LSM_CAP_COMPARE_EXTENT        LSMCapability = 8
)

// Enum value maps for LSMCapability.
var (
	LSMCapability_name = map[int32]string{
		1: "LSM_CAP_DISK_SAFE_UMOUNT",
		2: "LSM_CAP_DISK_REJECT_UNHEALTHY",
		4: "LSM_CAP_PARTITION_ISOLATE",
		8: "LSM_CAP_COMPARE_EXTENT",
	}
	LSMCapability_value = map[string]int32{
		"LSM_CAP_DISK_SAFE_UMOUNT":      1,
		"LSM_CAP_DISK_REJECT_UNHEALTHY": 2,
		"LSM_CAP_PARTITION_ISOLATE":     4,
		"LSM_CAP_COMPARE_EXTENT":        8,
	}
)

func (x LSMCapability) Enum() *LSMCapability {
	p := new(LSMCapability)
	*p = x
	return p
}

func (x LSMCapability) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LSMCapability) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[3].Descriptor()
}

func (LSMCapability) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[3]
}

func (x LSMCapability) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *LSMCapability) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = LSMCapability(num)
	return nil
}

// Deprecated: Use LSMCapability.Descriptor instead.
func (LSMCapability) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{3}
}

type CacheStatus int32

const (
	CacheStatus_CACHE_MOUNTED   CacheStatus = 0
	CacheStatus_CACHE_STAGING   CacheStatus = 1
	CacheStatus_CACHE_MIGRATING CacheStatus = 2
)

// Enum value maps for CacheStatus.
var (
	CacheStatus_name = map[int32]string{
		0: "CACHE_MOUNTED",
		1: "CACHE_STAGING",
		2: "CACHE_MIGRATING",
	}
	CacheStatus_value = map[string]int32{
		"CACHE_MOUNTED":   0,
		"CACHE_STAGING":   1,
		"CACHE_MIGRATING": 2,
	}
)

func (x CacheStatus) Enum() *CacheStatus {
	p := new(CacheStatus)
	*p = x
	return p
}

func (x CacheStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CacheStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[4].Descriptor()
}

func (CacheStatus) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[4]
}

func (x CacheStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CacheStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CacheStatus(num)
	return nil
}

// Deprecated: Use CacheStatus.Descriptor instead.
func (CacheStatus) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{4}
}

type JournalStatus int32

const (
	JournalStatus_JOURNAL_IDLE      JournalStatus = 0
	JournalStatus_JOURNAL_FULL      JournalStatus = 1
	JournalStatus_JOURNAL_BUSY      JournalStatus = 2
	JournalStatus_JOURNAL_UMOUNTING JournalStatus = 3
	JournalStatus_JOURNAL_ERROR     JournalStatus = 4
	JournalStatus_JOURNAL_INIT      JournalStatus = 999
)

// Enum value maps for JournalStatus.
var (
	JournalStatus_name = map[int32]string{
		0:   "JOURNAL_IDLE",
		1:   "JOURNAL_FULL",
		2:   "JOURNAL_BUSY",
		3:   "JOURNAL_UMOUNTING",
		4:   "JOURNAL_ERROR",
		999: "JOURNAL_INIT",
	}
	JournalStatus_value = map[string]int32{
		"JOURNAL_IDLE":      0,
		"JOURNAL_FULL":      1,
		"JOURNAL_BUSY":      2,
		"JOURNAL_UMOUNTING": 3,
		"JOURNAL_ERROR":     4,
		"JOURNAL_INIT":      999,
	}
)

func (x JournalStatus) Enum() *JournalStatus {
	p := new(JournalStatus)
	*p = x
	return p
}

func (x JournalStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JournalStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[5].Descriptor()
}

func (JournalStatus) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[5]
}

func (x JournalStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *JournalStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = JournalStatus(num)
	return nil
}

// Deprecated: Use JournalStatus.Descriptor instead.
func (JournalStatus) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{5}
}

type JournalGroupStatus int32

const (
	JournalGroupStatus_JOURNALGROUP_MOUNTED JournalGroupStatus = 0
	JournalGroupStatus_JOURNALGROUP_STAGING JournalGroupStatus = 1
)

// Enum value maps for JournalGroupStatus.
var (
	JournalGroupStatus_name = map[int32]string{
		0: "JOURNALGROUP_MOUNTED",
		1: "JOURNALGROUP_STAGING",
	}
	JournalGroupStatus_value = map[string]int32{
		"JOURNALGROUP_MOUNTED": 0,
		"JOURNALGROUP_STAGING": 1,
	}
)

func (x JournalGroupStatus) Enum() *JournalGroupStatus {
	p := new(JournalGroupStatus)
	*p = x
	return p
}

func (x JournalGroupStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JournalGroupStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[6].Descriptor()
}

func (JournalGroupStatus) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[6]
}

func (x JournalGroupStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *JournalGroupStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = JournalGroupStatus(num)
	return nil
}

// Deprecated: Use JournalGroupStatus.Descriptor instead.
func (JournalGroupStatus) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{6}
}

type DiskStatus int32

const (
	DiskStatus_DISK_MOUNTED   DiskStatus = 0
	DiskStatus_DISK_AVAILABLE DiskStatus = 1
	DiskStatus_DISK_STAGING   DiskStatus = 2
)

// Enum value maps for DiskStatus.
var (
	DiskStatus_name = map[int32]string{
		0: "DISK_MOUNTED",
		1: "DISK_AVAILABLE",
		2: "DISK_STAGING",
	}
	DiskStatus_value = map[string]int32{
		"DISK_MOUNTED":   0,
		"DISK_AVAILABLE": 1,
		"DISK_STAGING":   2,
	}
)

func (x DiskStatus) Enum() *DiskStatus {
	p := new(DiskStatus)
	*p = x
	return p
}

func (x DiskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[7].Descriptor()
}

func (DiskStatus) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[7]
}

func (x DiskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DiskStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DiskStatus(num)
	return nil
}

// Deprecated: Use DiskStatus.Descriptor instead.
func (DiskStatus) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{7}
}

type DiskType int32

const (
	DiskType_DISK_TYPE_PARTITION DiskType = 0
	DiskType_DISK_TYPE_JOURNAL   DiskType = 1
	DiskType_DISK_TYPE_CACHE     DiskType = 2
	DiskType_DISK_TYPE_UNKNOWN   DiskType = 3
)

// Enum value maps for DiskType.
var (
	DiskType_name = map[int32]string{
		0: "DISK_TYPE_PARTITION",
		1: "DISK_TYPE_JOURNAL",
		2: "DISK_TYPE_CACHE",
		3: "DISK_TYPE_UNKNOWN",
	}
	DiskType_value = map[string]int32{
		"DISK_TYPE_PARTITION": 0,
		"DISK_TYPE_JOURNAL":   1,
		"DISK_TYPE_CACHE":     2,
		"DISK_TYPE_UNKNOWN":   3,
	}
)

func (x DiskType) Enum() *DiskType {
	p := new(DiskType)
	*p = x
	return p
}

func (x DiskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiskType) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[8].Descriptor()
}

func (DiskType) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[8]
}

func (x DiskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DiskType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DiskType(num)
	return nil
}

// Deprecated: Use DiskType.Descriptor instead.
func (DiskType) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{8}
}

type DiskUmountMode int32

const (
	DiskUmountMode_DISK_UMOUNT_AUTO    DiskUmountMode = 0
	DiskUmountMode_DISK_UMOUNT_MIGRATE DiskUmountMode = 1
	DiskUmountMode_DISK_UMOUNT_OFFLINE DiskUmountMode = 2
)

// Enum value maps for DiskUmountMode.
var (
	DiskUmountMode_name = map[int32]string{
		0: "DISK_UMOUNT_AUTO",
		1: "DISK_UMOUNT_MIGRATE",
		2: "DISK_UMOUNT_OFFLINE",
	}
	DiskUmountMode_value = map[string]int32{
		"DISK_UMOUNT_AUTO":    0,
		"DISK_UMOUNT_MIGRATE": 1,
		"DISK_UMOUNT_OFFLINE": 2,
	}
)

func (x DiskUmountMode) Enum() *DiskUmountMode {
	p := new(DiskUmountMode)
	*p = x
	return p
}

func (x DiskUmountMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiskUmountMode) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[9].Descriptor()
}

func (DiskUmountMode) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[9]
}

func (x DiskUmountMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DiskUmountMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DiskUmountMode(num)
	return nil
}

// Deprecated: Use DiskUmountMode.Descriptor instead.
func (DiskUmountMode) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{9}
}

type DiskNameScheme int32

const (
	DiskNameScheme_DISK_NAME_BY_UUID DiskNameScheme = 0 // uuid lsm writes on super block
	DiskNameScheme_DISK_NAME_BY_PATH DiskNameScheme = 1
)

// Enum value maps for DiskNameScheme.
var (
	DiskNameScheme_name = map[int32]string{
		0: "DISK_NAME_BY_UUID",
		1: "DISK_NAME_BY_PATH",
	}
	DiskNameScheme_value = map[string]int32{
		"DISK_NAME_BY_UUID": 0,
		"DISK_NAME_BY_PATH": 1,
	}
)

func (x DiskNameScheme) Enum() *DiskNameScheme {
	p := new(DiskNameScheme)
	*p = x
	return p
}

func (x DiskNameScheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiskNameScheme) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[10].Descriptor()
}

func (DiskNameScheme) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[10]
}

func (x DiskNameScheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DiskNameScheme) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DiskNameScheme(num)
	return nil
}

// Deprecated: Use DiskNameScheme.Descriptor instead.
func (DiskNameScheme) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{10}
}

type AuroraStatus int32

const (
	AuroraStatus_AURORA_STATUS_INITIALIZING AuroraStatus = 1
	AuroraStatus_AURORA_STATUS_RUNNING      AuroraStatus = 2
	AuroraStatus_AURORA_STATUS_STOPPING     AuroraStatus = 3
	AuroraStatus_AURORA_STATUS_STOPPED      AuroraStatus = 4
)

// Enum value maps for AuroraStatus.
var (
	AuroraStatus_name = map[int32]string{
		1: "AURORA_STATUS_INITIALIZING",
		2: "AURORA_STATUS_RUNNING",
		3: "AURORA_STATUS_STOPPING",
		4: "AURORA_STATUS_STOPPED",
	}
	AuroraStatus_value = map[string]int32{
		"AURORA_STATUS_INITIALIZING": 1,
		"AURORA_STATUS_RUNNING":      2,
		"AURORA_STATUS_STOPPING":     3,
		"AURORA_STATUS_STOPPED":      4,
	}
)

func (x AuroraStatus) Enum() *AuroraStatus {
	p := new(AuroraStatus)
	*p = x
	return p
}

func (x AuroraStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuroraStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[11].Descriptor()
}

func (AuroraStatus) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[11]
}

func (x AuroraStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AuroraStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AuroraStatus(num)
	return nil
}

// Deprecated: Use AuroraStatus.Descriptor instead.
func (AuroraStatus) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{11}
}

type SinkTaskType int32

const (
	SinkTaskType_UNKNOWN_TYPE SinkTaskType = 0
	SinkTaskType_SINK_EXTENT  SinkTaskType = 1
	SinkTaskType_SINK_BLOCK   SinkTaskType = 2
	SinkTaskType_DRAIN_EXTENT SinkTaskType = 3
)

// Enum value maps for SinkTaskType.
var (
	SinkTaskType_name = map[int32]string{
		0: "UNKNOWN_TYPE",
		1: "SINK_EXTENT",
		2: "SINK_BLOCK",
		3: "DRAIN_EXTENT",
	}
	SinkTaskType_value = map[string]int32{
		"UNKNOWN_TYPE": 0,
		"SINK_EXTENT":  1,
		"SINK_BLOCK":   2,
		"DRAIN_EXTENT": 3,
	}
)

func (x SinkTaskType) Enum() *SinkTaskType {
	p := new(SinkTaskType)
	*p = x
	return p
}

func (x SinkTaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SinkTaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[12].Descriptor()
}

func (SinkTaskType) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[12]
}

func (x SinkTaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *SinkTaskType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = SinkTaskType(num)
	return nil
}

// Deprecated: Use SinkTaskType.Descriptor instead.
func (SinkTaskType) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{12}
}

type TransportType int32

const (
	TransportType_TRANSPORT_TYPE_UNIX TransportType = 1
	TransportType_TRANSPORT_TYPE_TCP  TransportType = 2
	TransportType_TRANSPORT_TYPE_RDMA TransportType = 3
)

// Enum value maps for TransportType.
var (
	TransportType_name = map[int32]string{
		1: "TRANSPORT_TYPE_UNIX",
		2: "TRANSPORT_TYPE_TCP",
		3: "TRANSPORT_TYPE_RDMA",
	}
	TransportType_value = map[string]int32{
		"TRANSPORT_TYPE_UNIX": 1,
		"TRANSPORT_TYPE_TCP":  2,
		"TRANSPORT_TYPE_RDMA": 3,
	}
)

func (x TransportType) Enum() *TransportType {
	p := new(TransportType)
	*p = x
	return p
}

func (x TransportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransportType) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[13].Descriptor()
}

func (TransportType) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[13]
}

func (x TransportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TransportType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TransportType(num)
	return nil
}

// Deprecated: Use TransportType.Descriptor instead.
func (TransportType) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{13}
}

type DataChannelVersion int32

const (
	DataChannelVersion_DATA_CHANNEL_V1 DataChannelVersion = 1
	DataChannelVersion_DATA_CHANNEL_V2 DataChannelVersion = 2
	DataChannelVersion_DATA_CHANNEL_V3 DataChannelVersion = 3
)

// Enum value maps for DataChannelVersion.
var (
	DataChannelVersion_name = map[int32]string{
		1: "DATA_CHANNEL_V1",
		2: "DATA_CHANNEL_V2",
		3: "DATA_CHANNEL_V3",
	}
	DataChannelVersion_value = map[string]int32{
		"DATA_CHANNEL_V1": 1,
		"DATA_CHANNEL_V2": 2,
		"DATA_CHANNEL_V3": 3,
	}
)

func (x DataChannelVersion) Enum() *DataChannelVersion {
	p := new(DataChannelVersion)
	*p = x
	return p
}

func (x DataChannelVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataChannelVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_chunk_proto_enumTypes[14].Descriptor()
}

func (DataChannelVersion) Type() protoreflect.EnumType {
	return &file_chunk_proto_enumTypes[14]
}

func (x DataChannelVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DataChannelVersion) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DataChannelVersion(num)
	return nil
}

// Deprecated: Use DataChannelVersion.Descriptor instead.
func (DataChannelVersion) EnumDescriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{14}
}

type PartitionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path              *string          `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	DeviceId          *string          `protobuf:"bytes,11,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	Uuid              *string          `protobuf:"bytes,12,opt,name=uuid" json:"uuid,omitempty"`
	TotalSize         *uint64          `protobuf:"varint,3,opt,name=total_size,json=totalSize" json:"total_size,omitempty"` // Bytes
	UsedSize          *uint64          `protobuf:"varint,4,opt,name=used_size,json=usedSize" json:"used_size,omitempty"`    // Bytes
	NumChecksumErrors *uint64          `protobuf:"varint,5,opt,name=num_checksum_errors,json=numChecksumErrors" json:"num_checksum_errors,omitempty"`
	NumIoErrors       *uint64          `protobuf:"varint,6,opt,name=num_io_errors,json=numIoErrors" json:"num_io_errors,omitempty"`
	Status            *PartitionStatus `protobuf:"varint,7,opt,name=status,enum=zbs.chunk.PartitionStatus" json:"status,omitempty"`
	Errflags          *uint32          `protobuf:"varint,8,opt,name=errflags" json:"errflags,omitempty"`
	NumSlowIo         *uint64          `protobuf:"varint,9,opt,name=num_slow_io,json=numSlowIo" json:"num_slow_io,omitempty"`
	PartUuid          *string          `protobuf:"bytes,14,opt,name=part_uuid,json=partUuid" json:"part_uuid,omitempty"`
	Warnflags         *uint32          `protobuf:"varint,15,opt,name=warnflags" json:"warnflags,omitempty"`
	ChecksumEnable    *uint32          `protobuf:"varint,20,opt,name=checksum_enable,json=checksumEnable" json:"checksum_enable,omitempty"`
}

func (x *PartitionInfo) Reset() {
	*x = PartitionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartitionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartitionInfo) ProtoMessage() {}

func (x *PartitionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartitionInfo.ProtoReflect.Descriptor instead.
func (*PartitionInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{0}
}

func (x *PartitionInfo) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *PartitionInfo) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

func (x *PartitionInfo) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *PartitionInfo) GetTotalSize() uint64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

func (x *PartitionInfo) GetUsedSize() uint64 {
	if x != nil && x.UsedSize != nil {
		return *x.UsedSize
	}
	return 0
}

func (x *PartitionInfo) GetNumChecksumErrors() uint64 {
	if x != nil && x.NumChecksumErrors != nil {
		return *x.NumChecksumErrors
	}
	return 0
}

func (x *PartitionInfo) GetNumIoErrors() uint64 {
	if x != nil && x.NumIoErrors != nil {
		return *x.NumIoErrors
	}
	return 0
}

func (x *PartitionInfo) GetStatus() PartitionStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return PartitionStatus_PARTITION_MOUNTED
}

func (x *PartitionInfo) GetErrflags() uint32 {
	if x != nil && x.Errflags != nil {
		return *x.Errflags
	}
	return 0
}

func (x *PartitionInfo) GetNumSlowIo() uint64 {
	if x != nil && x.NumSlowIo != nil {
		return *x.NumSlowIo
	}
	return 0
}

func (x *PartitionInfo) GetPartUuid() string {
	if x != nil && x.PartUuid != nil {
		return *x.PartUuid
	}
	return ""
}

func (x *PartitionInfo) GetWarnflags() uint32 {
	if x != nil && x.Warnflags != nil {
		return *x.Warnflags
	}
	return 0
}

func (x *PartitionInfo) GetChecksumEnable() uint32 {
	if x != nil && x.ChecksumEnable != nil {
		return *x.ChecksumEnable
	}
	return 0
}

type CacheInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path        *string      `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	DeviceId    *string      `protobuf:"bytes,11,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	Uuid        *string      `protobuf:"bytes,12,opt,name=uuid" json:"uuid,omitempty"`
	NumBlocks   *uint64      `protobuf:"varint,2,opt,name=num_blocks,json=numBlocks" json:"num_blocks,omitempty"`       // deprecated
	NumActive   *uint64      `protobuf:"varint,3,opt,name=num_active,json=numActive" json:"num_active,omitempty"`       // deprecated
	NumInactive *uint64      `protobuf:"varint,4,opt,name=num_inactive,json=numInactive" json:"num_inactive,omitempty"` // deprecated
	NumFree     *uint64      `protobuf:"varint,5,opt,name=num_free,json=numFree" json:"num_free,omitempty"`             // deprecated
	NumIoErrors *uint64      `protobuf:"varint,6,opt,name=num_io_errors,json=numIoErrors" json:"num_io_errors,omitempty"`
	Status      *CacheStatus `protobuf:"varint,7,opt,name=status,enum=zbs.chunk.CacheStatus" json:"status,omitempty"`
	Errflags    *uint32      `protobuf:"varint,8,opt,name=errflags" json:"errflags,omitempty"`
	NumSlowIo   *uint64      `protobuf:"varint,9,opt,name=num_slow_io,json=numSlowIo" json:"num_slow_io,omitempty"`
	PartUuid    *string      `protobuf:"bytes,13,opt,name=part_uuid,json=partUuid" json:"part_uuid,omitempty"`
	NumUsed     *uint64      `protobuf:"varint,14,opt,name=num_used,json=numUsed" json:"num_used,omitempty"` // deprecated
	Warnflags   *uint32      `protobuf:"varint,15,opt,name=warnflags" json:"warnflags,omitempty"`
	TotalSize   *uint64      `protobuf:"varint,21,opt,name=total_size,json=totalSize" json:"total_size,omitempty"`
	UsedSize    *uint64      `protobuf:"varint,22,opt,name=used_size,json=usedSize" json:"used_size,omitempty"`
}

func (x *CacheInfo) Reset() {
	*x = CacheInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheInfo) ProtoMessage() {}

func (x *CacheInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheInfo.ProtoReflect.Descriptor instead.
func (*CacheInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{1}
}

func (x *CacheInfo) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *CacheInfo) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

func (x *CacheInfo) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *CacheInfo) GetNumBlocks() uint64 {
	if x != nil && x.NumBlocks != nil {
		return *x.NumBlocks
	}
	return 0
}

func (x *CacheInfo) GetNumActive() uint64 {
	if x != nil && x.NumActive != nil {
		return *x.NumActive
	}
	return 0
}

func (x *CacheInfo) GetNumInactive() uint64 {
	if x != nil && x.NumInactive != nil {
		return *x.NumInactive
	}
	return 0
}

func (x *CacheInfo) GetNumFree() uint64 {
	if x != nil && x.NumFree != nil {
		return *x.NumFree
	}
	return 0
}

func (x *CacheInfo) GetNumIoErrors() uint64 {
	if x != nil && x.NumIoErrors != nil {
		return *x.NumIoErrors
	}
	return 0
}

func (x *CacheInfo) GetStatus() CacheStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return CacheStatus_CACHE_MOUNTED
}

func (x *CacheInfo) GetErrflags() uint32 {
	if x != nil && x.Errflags != nil {
		return *x.Errflags
	}
	return 0
}

func (x *CacheInfo) GetNumSlowIo() uint64 {
	if x != nil && x.NumSlowIo != nil {
		return *x.NumSlowIo
	}
	return 0
}

func (x *CacheInfo) GetPartUuid() string {
	if x != nil && x.PartUuid != nil {
		return *x.PartUuid
	}
	return ""
}

func (x *CacheInfo) GetNumUsed() uint64 {
	if x != nil && x.NumUsed != nil {
		return *x.NumUsed
	}
	return 0
}

func (x *CacheInfo) GetWarnflags() uint32 {
	if x != nil && x.Warnflags != nil {
		return *x.Warnflags
	}
	return 0
}

func (x *CacheInfo) GetTotalSize() uint64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

func (x *CacheInfo) GetUsedSize() uint64 {
	if x != nil && x.UsedSize != nil {
		return *x.UsedSize
	}
	return 0
}

type JournalGroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path        *string             `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	DeviceId    *string             `protobuf:"bytes,11,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	Uuid        *string             `protobuf:"bytes,12,opt,name=uuid" json:"uuid,omitempty"`
	PartUuid    *string             `protobuf:"bytes,13,opt,name=part_uuid,json=partUuid" json:"part_uuid,omitempty"`
	Errflags    *uint32             `protobuf:"varint,21,opt,name=errflags" json:"errflags,omitempty"`
	NumSlowIo   *uint64             `protobuf:"varint,22,opt,name=num_slow_io,json=numSlowIo" json:"num_slow_io,omitempty"`
	Status      *JournalGroupStatus `protobuf:"varint,23,opt,name=status,enum=zbs.chunk.JournalGroupStatus" json:"status,omitempty"`
	NumIoErrors *uint64             `protobuf:"varint,24,opt,name=num_io_errors,json=numIoErrors" json:"num_io_errors,omitempty"`
	Warnflags   *uint32             `protobuf:"varint,25,opt,name=warnflags" json:"warnflags,omitempty"`
	Id          *uint32             `protobuf:"varint,30,opt,name=id,def=0" json:"id,omitempty"`
	// DISK_ERR_UMOUNTING is enough to represent JournalGroup need umount.
	XDeprecatedAutoDelete *bool `protobuf:"varint,31,opt,name=__deprecated_auto_delete,json=DeprecatedAutoDelete,def=0" json:"__deprecated_auto_delete,omitempty"`
}

// Default values for JournalGroupInfo fields.
const (
	Default_JournalGroupInfo_Id                    = uint32(0)
	Default_JournalGroupInfo_XDeprecatedAutoDelete = bool(false)
)

func (x *JournalGroupInfo) Reset() {
	*x = JournalGroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JournalGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JournalGroupInfo) ProtoMessage() {}

func (x *JournalGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JournalGroupInfo.ProtoReflect.Descriptor instead.
func (*JournalGroupInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{2}
}

func (x *JournalGroupInfo) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *JournalGroupInfo) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

func (x *JournalGroupInfo) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *JournalGroupInfo) GetPartUuid() string {
	if x != nil && x.PartUuid != nil {
		return *x.PartUuid
	}
	return ""
}

func (x *JournalGroupInfo) GetErrflags() uint32 {
	if x != nil && x.Errflags != nil {
		return *x.Errflags
	}
	return 0
}

func (x *JournalGroupInfo) GetNumSlowIo() uint64 {
	if x != nil && x.NumSlowIo != nil {
		return *x.NumSlowIo
	}
	return 0
}

func (x *JournalGroupInfo) GetStatus() JournalGroupStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return JournalGroupStatus_JOURNALGROUP_MOUNTED
}

func (x *JournalGroupInfo) GetNumIoErrors() uint64 {
	if x != nil && x.NumIoErrors != nil {
		return *x.NumIoErrors
	}
	return 0
}

func (x *JournalGroupInfo) GetWarnflags() uint32 {
	if x != nil && x.Warnflags != nil {
		return *x.Warnflags
	}
	return 0
}

func (x *JournalGroupInfo) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return Default_JournalGroupInfo_Id
}

func (x *JournalGroupInfo) GetXDeprecatedAutoDelete() bool {
	if x != nil && x.XDeprecatedAutoDelete != nil {
		return *x.XDeprecatedAutoDelete
	}
	return Default_JournalGroupInfo_XDeprecatedAutoDelete
}

type JournalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path            *string        `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	ReservedEntries *uint64        `protobuf:"varint,3,opt,name=reserved_entries,json=reservedEntries" json:"reserved_entries,omitempty"`
	MaxEntries      *uint64        `protobuf:"varint,4,opt,name=max_entries,json=maxEntries" json:"max_entries,omitempty"`
	SeqNo           *uint64        `protobuf:"varint,10,opt,name=seq_no,json=seqNo" json:"seq_no,omitempty"`
	Status          *JournalStatus `protobuf:"varint,11,opt,name=status,enum=zbs.chunk.JournalStatus" json:"status,omitempty"`
	NumIoErrors     *uint64        `protobuf:"varint,13,opt,name=num_io_errors,json=numIoErrors" json:"num_io_errors,omitempty"`
}

func (x *JournalInfo) Reset() {
	*x = JournalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JournalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JournalInfo) ProtoMessage() {}

func (x *JournalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JournalInfo.ProtoReflect.Descriptor instead.
func (*JournalInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{3}
}

func (x *JournalInfo) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *JournalInfo) GetReservedEntries() uint64 {
	if x != nil && x.ReservedEntries != nil {
		return *x.ReservedEntries
	}
	return 0
}

func (x *JournalInfo) GetMaxEntries() uint64 {
	if x != nil && x.MaxEntries != nil {
		return *x.MaxEntries
	}
	return 0
}

func (x *JournalInfo) GetSeqNo() uint64 {
	if x != nil && x.SeqNo != nil {
		return *x.SeqNo
	}
	return 0
}

func (x *JournalInfo) GetStatus() JournalStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return JournalStatus_JOURNAL_IDLE
}

func (x *JournalInfo) GetNumIoErrors() uint64 {
	if x != nil && x.NumIoErrors != nil {
		return *x.NumIoErrors
	}
	return 0
}

type MetaAddr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MetaIp    *uint32 `protobuf:"varint,1,req,name=meta_ip,json=metaIp,def=0" json:"meta_ip,omitempty"`
	MetaPort  *uint32 `protobuf:"varint,2,req,name=meta_port,json=metaPort,def=0" json:"meta_port,omitempty"`
	MyChunkId *uint32 `protobuf:"varint,3,req,name=my_chunk_id,json=myChunkId,def=0" json:"my_chunk_id,omitempty"`
}

// Default values for MetaAddr fields.
const (
	Default_MetaAddr_MetaIp    = uint32(0)
	Default_MetaAddr_MetaPort  = uint32(0)
	Default_MetaAddr_MyChunkId = uint32(0)
)

func (x *MetaAddr) Reset() {
	*x = MetaAddr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaAddr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaAddr) ProtoMessage() {}

func (x *MetaAddr) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaAddr.ProtoReflect.Descriptor instead.
func (*MetaAddr) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{4}
}

func (x *MetaAddr) GetMetaIp() uint32 {
	if x != nil && x.MetaIp != nil {
		return *x.MetaIp
	}
	return Default_MetaAddr_MetaIp
}

func (x *MetaAddr) GetMetaPort() uint32 {
	if x != nil && x.MetaPort != nil {
		return *x.MetaPort
	}
	return Default_MetaAddr_MetaPort
}

func (x *MetaAddr) GetMyChunkId() uint32 {
	if x != nil && x.MyChunkId != nil {
		return *x.MyChunkId
	}
	return Default_MetaAddr_MyChunkId
}

type ClientInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Socket           *string `protobuf:"bytes,1,req,name=socket" json:"socket,omitempty"`
	Writable         *bool   `protobuf:"varint,2,req,name=writable" json:"writable,omitempty"`
	HandlingRequests *uint32 `protobuf:"varint,3,req,name=handling_requests,json=handlingRequests" json:"handling_requests,omitempty"`
	SenderQueueEmpty *bool   `protobuf:"varint,4,req,name=sender_queue_empty,json=senderQueueEmpty" json:"sender_queue_empty,omitempty"`
	Closed           *bool   `protobuf:"varint,5,req,name=closed" json:"closed,omitempty"`
}

func (x *ClientInfo) Reset() {
	*x = ClientInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientInfo) ProtoMessage() {}

func (x *ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientInfo.ProtoReflect.Descriptor instead.
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{5}
}

func (x *ClientInfo) GetSocket() string {
	if x != nil && x.Socket != nil {
		return *x.Socket
	}
	return ""
}

func (x *ClientInfo) GetWritable() bool {
	if x != nil && x.Writable != nil {
		return *x.Writable
	}
	return false
}

func (x *ClientInfo) GetHandlingRequests() uint32 {
	if x != nil && x.HandlingRequests != nil {
		return *x.HandlingRequests
	}
	return 0
}

func (x *ClientInfo) GetSenderQueueEmpty() bool {
	if x != nil && x.SenderQueueEmpty != nil {
		return *x.SenderQueueEmpty
	}
	return false
}

func (x *ClientInfo) GetClosed() bool {
	if x != nil && x.Closed != nil {
		return *x.Closed
	}
	return false
}

type ListClientResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clients []*ClientInfo `protobuf:"bytes,1,rep,name=clients" json:"clients,omitempty"`
	Total   *uint64       `protobuf:"varint,2,opt,name=total" json:"total,omitempty"`
}

func (x *ListClientResponse) Reset() {
	*x = ListClientResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientResponse) ProtoMessage() {}

func (x *ListClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientResponse.ProtoReflect.Descriptor instead.
func (*ListClientResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{6}
}

func (x *ListClientResponse) GetClients() []*ClientInfo {
	if x != nil {
		return x.Clients
	}
	return nil
}

func (x *ListClientResponse) GetTotal() uint64 {
	if x != nil && x.Total != nil {
		return *x.Total
	}
	return 0
}

type ZbsAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RpcIp              *string `protobuf:"bytes,2,opt,name=rpc_ip,json=rpcIp" json:"rpc_ip,omitempty"`
	RpcPort            *int32  `protobuf:"varint,3,opt,name=rpc_port,json=rpcPort" json:"rpc_port,omitempty"`
	DataIp             *string `protobuf:"bytes,4,opt,name=data_ip,json=dataIp" json:"data_ip,omitempty"`
	DataPort           *int32  `protobuf:"varint,5,opt,name=data_port,json=dataPort" json:"data_port,omitempty"`
	DataUnixPath       *string `protobuf:"bytes,6,opt,name=data_unix_path,json=dataUnixPath" json:"data_unix_path,omitempty"`
	HeartbeatIp        *string `protobuf:"bytes,7,opt,name=heartbeat_ip,json=heartbeatIp" json:"heartbeat_ip,omitempty"`
	HeartbeatPort      *int32  `protobuf:"varint,8,opt,name=heartbeat_port,json=heartbeatPort" json:"heartbeat_port,omitempty"`
	MetaIp             *string `protobuf:"bytes,9,opt,name=meta_ip,json=metaIp" json:"meta_ip,omitempty"`
	MetaPort           *int32  `protobuf:"varint,10,opt,name=meta_port,json=metaPort" json:"meta_port,omitempty"`
	ChunkId            *uint32 `protobuf:"varint,11,opt,name=chunk_id,json=chunkId" json:"chunk_id,omitempty"`
	SecondaryDataIp    *string `protobuf:"bytes,12,opt,name=secondary_data_ip,json=secondaryDataIp" json:"secondary_data_ip,omitempty"`
	ScvmModeHostDataIp *string `protobuf:"bytes,13,opt,name=scvm_mode_host_data_ip,json=scvmModeHostDataIp" json:"scvm_mode_host_data_ip,omitempty"`
	InstanceId         *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for ZbsAddress fields.
const (
	Default_ZbsAddress_InstanceId = uint32(0)
)

func (x *ZbsAddress) Reset() {
	*x = ZbsAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZbsAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZbsAddress) ProtoMessage() {}

func (x *ZbsAddress) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZbsAddress.ProtoReflect.Descriptor instead.
func (*ZbsAddress) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{7}
}

func (x *ZbsAddress) GetRpcIp() string {
	if x != nil && x.RpcIp != nil {
		return *x.RpcIp
	}
	return ""
}

func (x *ZbsAddress) GetRpcPort() int32 {
	if x != nil && x.RpcPort != nil {
		return *x.RpcPort
	}
	return 0
}

func (x *ZbsAddress) GetDataIp() string {
	if x != nil && x.DataIp != nil {
		return *x.DataIp
	}
	return ""
}

func (x *ZbsAddress) GetDataPort() int32 {
	if x != nil && x.DataPort != nil {
		return *x.DataPort
	}
	return 0
}

func (x *ZbsAddress) GetDataUnixPath() string {
	if x != nil && x.DataUnixPath != nil {
		return *x.DataUnixPath
	}
	return ""
}

func (x *ZbsAddress) GetHeartbeatIp() string {
	if x != nil && x.HeartbeatIp != nil {
		return *x.HeartbeatIp
	}
	return ""
}

func (x *ZbsAddress) GetHeartbeatPort() int32 {
	if x != nil && x.HeartbeatPort != nil {
		return *x.HeartbeatPort
	}
	return 0
}

func (x *ZbsAddress) GetMetaIp() string {
	if x != nil && x.MetaIp != nil {
		return *x.MetaIp
	}
	return ""
}

func (x *ZbsAddress) GetMetaPort() int32 {
	if x != nil && x.MetaPort != nil {
		return *x.MetaPort
	}
	return 0
}

func (x *ZbsAddress) GetChunkId() uint32 {
	if x != nil && x.ChunkId != nil {
		return *x.ChunkId
	}
	return 0
}

func (x *ZbsAddress) GetSecondaryDataIp() string {
	if x != nil && x.SecondaryDataIp != nil {
		return *x.SecondaryDataIp
	}
	return ""
}

func (x *ZbsAddress) GetScvmModeHostDataIp() string {
	if x != nil && x.ScvmModeHostDataIp != nil {
		return *x.ScvmModeHostDataIp
	}
	return ""
}

func (x *ZbsAddress) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ZbsAddress_InstanceId
}

type ZbsAddressV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*ZbsAddress `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *ZbsAddressV2) Reset() {
	*x = ZbsAddressV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZbsAddressV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZbsAddressV2) ProtoMessage() {}

func (x *ZbsAddressV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZbsAddressV2.ProtoReflect.Descriptor instead.
func (*ZbsAddressV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{8}
}

func (x *ZbsAddressV2) GetInstancesResponse() []*ZbsAddress {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type GetZkHostsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZkHosts *string `protobuf:"bytes,1,req,name=zk_hosts,json=zkHosts" json:"zk_hosts,omitempty"`
}

func (x *GetZkHostsResponse) Reset() {
	*x = GetZkHostsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetZkHostsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetZkHostsResponse) ProtoMessage() {}

func (x *GetZkHostsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetZkHostsResponse.ProtoReflect.Descriptor instead.
func (*GetZkHostsResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{9}
}

func (x *GetZkHostsResponse) GetZkHosts() string {
	if x != nil && x.ZkHosts != nil {
		return *x.ZkHosts
	}
	return ""
}

// deprecated
type GetDiskInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
}

func (x *GetDiskInfoRequest) Reset() {
	*x = GetDiskInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiskInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiskInfoRequest) ProtoMessage() {}

func (x *GetDiskInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiskInfoRequest.ProtoReflect.Descriptor instead.
func (*GetDiskInfoRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{10}
}

func (x *GetDiskInfoRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

// deprecated
type GetDiskInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *DiskStatus `protobuf:"varint,1,req,name=status,enum=zbs.chunk.DiskStatus" json:"status,omitempty"`
	DiskType *DiskType   `protobuf:"varint,2,opt,name=disk_type,json=diskType,enum=zbs.chunk.DiskType" json:"disk_type,omitempty"`
	// Defined by disk_type
	Version   *string `protobuf:"bytes,3,opt,name=version" json:"version,omitempty"`
	DeviceId  *string `protobuf:"bytes,4,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	Uuid      *string `protobuf:"bytes,5,opt,name=uuid" json:"uuid,omitempty"`
	UsedSize  *uint64 `protobuf:"varint,6,opt,name=used_size,json=usedSize" json:"used_size,omitempty"`
	TotalSize *uint64 `protobuf:"varint,7,opt,name=total_size,json=totalSize" json:"total_size,omitempty"`
}

func (x *GetDiskInfoResponse) Reset() {
	*x = GetDiskInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiskInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiskInfoResponse) ProtoMessage() {}

func (x *GetDiskInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiskInfoResponse.ProtoReflect.Descriptor instead.
func (*GetDiskInfoResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{11}
}

func (x *GetDiskInfoResponse) GetStatus() DiskStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return DiskStatus_DISK_MOUNTED
}

func (x *GetDiskInfoResponse) GetDiskType() DiskType {
	if x != nil && x.DiskType != nil {
		return *x.DiskType
	}
	return DiskType_DISK_TYPE_PARTITION
}

func (x *GetDiskInfoResponse) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *GetDiskInfoResponse) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

func (x *GetDiskInfoResponse) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *GetDiskInfoResponse) GetUsedSize() uint64 {
	if x != nil && x.UsedSize != nil {
		return *x.UsedSize
	}
	return 0
}

func (x *GetDiskInfoResponse) GetTotalSize() uint64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

type UpdateSecondaryDataIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewIp *string `protobuf:"bytes,1,req,name=new_ip,json=newIp" json:"new_ip,omitempty"`
}

func (x *UpdateSecondaryDataIPRequest) Reset() {
	*x = UpdateSecondaryDataIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSecondaryDataIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSecondaryDataIPRequest) ProtoMessage() {}

func (x *UpdateSecondaryDataIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSecondaryDataIPRequest.ProtoReflect.Descriptor instead.
func (*UpdateSecondaryDataIPRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateSecondaryDataIPRequest) GetNewIp() string {
	if x != nil && x.NewIp != nil {
		return *x.NewIp
	}
	return ""
}

type UpdateScvmModeHostDataIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewIp *string `protobuf:"bytes,1,req,name=new_ip,json=newIp" json:"new_ip,omitempty"`
}

func (x *UpdateScvmModeHostDataIPRequest) Reset() {
	*x = UpdateScvmModeHostDataIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateScvmModeHostDataIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateScvmModeHostDataIPRequest) ProtoMessage() {}

func (x *UpdateScvmModeHostDataIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateScvmModeHostDataIPRequest.ProtoReflect.Descriptor instead.
func (*UpdateScvmModeHostDataIPRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateScvmModeHostDataIPRequest) GetNewIp() string {
	if x != nil && x.NewIp != nil {
		return *x.NewIp
	}
	return ""
}

type FormatPartitionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	// if the partition has already formatted, force re-format.
	Force              *bool   `protobuf:"varint,3,opt,name=force,def=0" json:"force,omitempty"`
	IgnoreDataChecksum *bool   `protobuf:"varint,4,opt,name=ignore_data_checksum,json=ignoreDataChecksum,def=0" json:"ignore_data_checksum,omitempty"`
	InstanceId         *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for FormatPartitionRequest fields.
const (
	Default_FormatPartitionRequest_Force              = bool(false)
	Default_FormatPartitionRequest_IgnoreDataChecksum = bool(false)
	Default_FormatPartitionRequest_InstanceId         = uint32(1)
)

func (x *FormatPartitionRequest) Reset() {
	*x = FormatPartitionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormatPartitionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormatPartitionRequest) ProtoMessage() {}

func (x *FormatPartitionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormatPartitionRequest.ProtoReflect.Descriptor instead.
func (*FormatPartitionRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{14}
}

func (x *FormatPartitionRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *FormatPartitionRequest) GetForce() bool {
	if x != nil && x.Force != nil {
		return *x.Force
	}
	return Default_FormatPartitionRequest_Force
}

func (x *FormatPartitionRequest) GetIgnoreDataChecksum() bool {
	if x != nil && x.IgnoreDataChecksum != nil {
		return *x.IgnoreDataChecksum
	}
	return Default_FormatPartitionRequest_IgnoreDataChecksum
}

func (x *FormatPartitionRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_FormatPartitionRequest_InstanceId
}

type MountPartitionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	// for mount, if the partition has data, force should be true.
	Force      *bool   `protobuf:"varint,3,opt,name=force,def=0" json:"force,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for MountPartitionRequest fields.
const (
	Default_MountPartitionRequest_Force      = bool(false)
	Default_MountPartitionRequest_InstanceId = uint32(1)
)

func (x *MountPartitionRequest) Reset() {
	*x = MountPartitionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MountPartitionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountPartitionRequest) ProtoMessage() {}

func (x *MountPartitionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountPartitionRequest.ProtoReflect.Descriptor instead.
func (*MountPartitionRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{15}
}

func (x *MountPartitionRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *MountPartitionRequest) GetForce() bool {
	if x != nil && x.Force != nil {
		return *x.Force
	}
	return Default_MountPartitionRequest_Force
}

func (x *MountPartitionRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_MountPartitionRequest_InstanceId
}

type FormatCacheRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	// for cache, if the cache has data, force should be true.
	Force      *bool   `protobuf:"varint,3,opt,name=force,def=0" json:"force,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for FormatCacheRequest fields.
const (
	Default_FormatCacheRequest_Force      = bool(false)
	Default_FormatCacheRequest_InstanceId = uint32(1)
)

func (x *FormatCacheRequest) Reset() {
	*x = FormatCacheRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormatCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormatCacheRequest) ProtoMessage() {}

func (x *FormatCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormatCacheRequest.ProtoReflect.Descriptor instead.
func (*FormatCacheRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{16}
}

func (x *FormatCacheRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *FormatCacheRequest) GetForce() bool {
	if x != nil && x.Force != nil {
		return *x.Force
	}
	return Default_FormatCacheRequest_Force
}

func (x *FormatCacheRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_FormatCacheRequest_InstanceId
}

type MountCacheRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path       *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for MountCacheRequest fields.
const (
	Default_MountCacheRequest_InstanceId = uint32(1)
)

func (x *MountCacheRequest) Reset() {
	*x = MountCacheRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MountCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountCacheRequest) ProtoMessage() {}

func (x *MountCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountCacheRequest.ProtoReflect.Descriptor instead.
func (*MountCacheRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{17}
}

func (x *MountCacheRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *MountCacheRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_MountCacheRequest_InstanceId
}

type FormatJournalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	// if the partition has already formatted, force re-format.
	Force      *bool   `protobuf:"varint,3,opt,name=force,def=0" json:"force,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for FormatJournalRequest fields.
const (
	Default_FormatJournalRequest_Force      = bool(false)
	Default_FormatJournalRequest_InstanceId = uint32(1)
)

func (x *FormatJournalRequest) Reset() {
	*x = FormatJournalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormatJournalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormatJournalRequest) ProtoMessage() {}

func (x *FormatJournalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormatJournalRequest.ProtoReflect.Descriptor instead.
func (*FormatJournalRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{18}
}

func (x *FormatJournalRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *FormatJournalRequest) GetForce() bool {
	if x != nil && x.Force != nil {
		return *x.Force
	}
	return Default_FormatJournalRequest_Force
}

func (x *FormatJournalRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_FormatJournalRequest_InstanceId
}

type MountJournalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path       *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for MountJournalRequest fields.
const (
	Default_MountJournalRequest_InstanceId = uint32(1)
)

func (x *MountJournalRequest) Reset() {
	*x = MountJournalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MountJournalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountJournalRequest) ProtoMessage() {}

func (x *MountJournalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountJournalRequest.ProtoReflect.Descriptor instead.
func (*MountJournalRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{19}
}

func (x *MountJournalRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *MountJournalRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_MountJournalRequest_InstanceId
}

type WorkerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkerId  *uint32 `protobuf:"varint,1,req,name=worker_id,json=workerId" json:"worker_id,omitempty"`
	QueueSize *uint64 `protobuf:"varint,2,req,name=queue_size,json=queueSize" json:"queue_size,omitempty"`
}

func (x *WorkerInfo) Reset() {
	*x = WorkerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerInfo) ProtoMessage() {}

func (x *WorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerInfo.ProtoReflect.Descriptor instead.
func (*WorkerInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{20}
}

func (x *WorkerInfo) GetWorkerId() uint32 {
	if x != nil && x.WorkerId != nil {
		return *x.WorkerId
	}
	return 0
}

func (x *WorkerInfo) GetQueueSize() uint64 {
	if x != nil && x.QueueSize != nil {
		return *x.QueueSize
	}
	return 0
}

type ListPartitionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Partitions []*PartitionInfo `protobuf:"bytes,2,rep,name=partitions" json:"partitions,omitempty"`
	Workers    []*WorkerInfo    `protobuf:"bytes,3,rep,name=workers" json:"workers,omitempty"`
	InstanceId *uint32          `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for ListPartitionResponse fields.
const (
	Default_ListPartitionResponse_InstanceId = uint32(0)
)

func (x *ListPartitionResponse) Reset() {
	*x = ListPartitionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPartitionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPartitionResponse) ProtoMessage() {}

func (x *ListPartitionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPartitionResponse.ProtoReflect.Descriptor instead.
func (*ListPartitionResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{21}
}

func (x *ListPartitionResponse) GetPartitions() []*PartitionInfo {
	if x != nil {
		return x.Partitions
	}
	return nil
}

func (x *ListPartitionResponse) GetWorkers() []*WorkerInfo {
	if x != nil {
		return x.Workers
	}
	return nil
}

func (x *ListPartitionResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ListPartitionResponse_InstanceId
}

type ListPartitionResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*ListPartitionResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *ListPartitionResponseV2) Reset() {
	*x = ListPartitionResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPartitionResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPartitionResponseV2) ProtoMessage() {}

func (x *ListPartitionResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPartitionResponseV2.ProtoReflect.Descriptor instead.
func (*ListPartitionResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{22}
}

func (x *ListPartitionResponseV2) GetInstancesResponse() []*ListPartitionResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type SetUnhealthyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DiskName *string         `protobuf:"bytes,1,req,name=disk_name,json=diskName" json:"disk_name,omitempty"`
	Scheme   *DiskNameScheme `protobuf:"varint,5,opt,name=scheme,enum=zbs.chunk.DiskNameScheme,def=1" json:"scheme,omitempty"`
	Errflag  *DiskErrFlags   `protobuf:"varint,2,opt,name=errflag,enum=zbs.chunk.DiskErrFlags,def=1" json:"errflag,omitempty"`
}

// Default values for SetUnhealthyRequest fields.
const (
	Default_SetUnhealthyRequest_Scheme  = DiskNameScheme_DISK_NAME_BY_PATH
	Default_SetUnhealthyRequest_Errflag = DiskErrFlags_DISK_ERR_SMART_FAIL
)

func (x *SetUnhealthyRequest) Reset() {
	*x = SetUnhealthyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUnhealthyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUnhealthyRequest) ProtoMessage() {}

func (x *SetUnhealthyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUnhealthyRequest.ProtoReflect.Descriptor instead.
func (*SetUnhealthyRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{23}
}

func (x *SetUnhealthyRequest) GetDiskName() string {
	if x != nil && x.DiskName != nil {
		return *x.DiskName
	}
	return ""
}

func (x *SetUnhealthyRequest) GetScheme() DiskNameScheme {
	if x != nil && x.Scheme != nil {
		return *x.Scheme
	}
	return Default_SetUnhealthyRequest_Scheme
}

func (x *SetUnhealthyRequest) GetErrflag() DiskErrFlags {
	if x != nil && x.Errflag != nil {
		return *x.Errflag
	}
	return Default_SetUnhealthyRequest_Errflag
}

type CacheStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HitRate       *float64 `protobuf:"fixed64,1,opt,name=hit_rate,json=hitRate" json:"hit_rate,omitempty"`
	PromotionBps  *uint64  `protobuf:"varint,11,opt,name=promotion_bps,json=promotionBps" json:"promotion_bps,omitempty"`
	WritebackBps  *uint64  `protobuf:"varint,12,opt,name=writeback_bps,json=writebackBps" json:"writeback_bps,omitempty"`
	UsedBytes     *uint64  `protobuf:"varint,21,opt,name=used_bytes,json=usedBytes" json:"used_bytes,omitempty"`
	TotalBytes    *uint64  `protobuf:"varint,22,opt,name=total_bytes,json=totalBytes" json:"total_bytes,omitempty"`
	ActiveBytes   *uint64  `protobuf:"varint,23,opt,name=active_bytes,json=activeBytes" json:"active_bytes,omitempty"`
	InactiveBytes *uint64  `protobuf:"varint,24,opt,name=inactive_bytes,json=inactiveBytes" json:"inactive_bytes,omitempty"`
	CleanBytes    *uint64  `protobuf:"varint,25,opt,name=clean_bytes,json=cleanBytes" json:"clean_bytes,omitempty"`
}

func (x *CacheStat) Reset() {
	*x = CacheStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheStat) ProtoMessage() {}

func (x *CacheStat) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheStat.ProtoReflect.Descriptor instead.
func (*CacheStat) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{24}
}

func (x *CacheStat) GetHitRate() float64 {
	if x != nil && x.HitRate != nil {
		return *x.HitRate
	}
	return 0
}

func (x *CacheStat) GetPromotionBps() uint64 {
	if x != nil && x.PromotionBps != nil {
		return *x.PromotionBps
	}
	return 0
}

func (x *CacheStat) GetWritebackBps() uint64 {
	if x != nil && x.WritebackBps != nil {
		return *x.WritebackBps
	}
	return 0
}

func (x *CacheStat) GetUsedBytes() uint64 {
	if x != nil && x.UsedBytes != nil {
		return *x.UsedBytes
	}
	return 0
}

func (x *CacheStat) GetTotalBytes() uint64 {
	if x != nil && x.TotalBytes != nil {
		return *x.TotalBytes
	}
	return 0
}

func (x *CacheStat) GetActiveBytes() uint64 {
	if x != nil && x.ActiveBytes != nil {
		return *x.ActiveBytes
	}
	return 0
}

func (x *CacheStat) GetInactiveBytes() uint64 {
	if x != nil && x.InactiveBytes != nil {
		return *x.InactiveBytes
	}
	return 0
}

func (x *CacheStat) GetCleanBytes() uint64 {
	if x != nil && x.CleanBytes != nil {
		return *x.CleanBytes
	}
	return 0
}

type ListCacheResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caches        []*CacheInfo `protobuf:"bytes,2,rep,name=caches" json:"caches,omitempty"`
	HitRate       *float64     `protobuf:"fixed64,3,opt,name=hit_rate,json=hitRate" json:"hit_rate,omitempty"`                   // deprecated
	TotalActive   *uint64      `protobuf:"varint,11,opt,name=total_active,json=totalActive" json:"total_active,omitempty"`       // deprecated
	TotalInactive *uint64      `protobuf:"varint,12,opt,name=total_inactive,json=totalInactive" json:"total_inactive,omitempty"` // deprecated
	TotalClean    *uint64      `protobuf:"varint,13,opt,name=total_clean,json=totalClean" json:"total_clean,omitempty"`          // deprecated
	CacheStat     *CacheStat   `protobuf:"bytes,21,opt,name=cache_stat,json=cacheStat" json:"cache_stat,omitempty"`
	InstanceId    *uint32      `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for ListCacheResponse fields.
const (
	Default_ListCacheResponse_InstanceId = uint32(0)
)

func (x *ListCacheResponse) Reset() {
	*x = ListCacheResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCacheResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCacheResponse) ProtoMessage() {}

func (x *ListCacheResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCacheResponse.ProtoReflect.Descriptor instead.
func (*ListCacheResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{25}
}

func (x *ListCacheResponse) GetCaches() []*CacheInfo {
	if x != nil {
		return x.Caches
	}
	return nil
}

func (x *ListCacheResponse) GetHitRate() float64 {
	if x != nil && x.HitRate != nil {
		return *x.HitRate
	}
	return 0
}

func (x *ListCacheResponse) GetTotalActive() uint64 {
	if x != nil && x.TotalActive != nil {
		return *x.TotalActive
	}
	return 0
}

func (x *ListCacheResponse) GetTotalInactive() uint64 {
	if x != nil && x.TotalInactive != nil {
		return *x.TotalInactive
	}
	return 0
}

func (x *ListCacheResponse) GetTotalClean() uint64 {
	if x != nil && x.TotalClean != nil {
		return *x.TotalClean
	}
	return 0
}

func (x *ListCacheResponse) GetCacheStat() *CacheStat {
	if x != nil {
		return x.CacheStat
	}
	return nil
}

func (x *ListCacheResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ListCacheResponse_InstanceId
}

type ListCacheResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*ListCacheResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *ListCacheResponseV2) Reset() {
	*x = ListCacheResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCacheResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCacheResponseV2) ProtoMessage() {}

func (x *ListCacheResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCacheResponseV2.ProtoReflect.Descriptor instead.
func (*ListCacheResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{26}
}

func (x *ListCacheResponseV2) GetInstancesResponse() []*ListCacheResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type ListJournalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Journals []*JournalInfo `protobuf:"bytes,2,rep,name=journals" json:"journals,omitempty"`
	// Deprecated: Marked as deprecated in chunk.proto.
	DisabledGroupNo *uint32             `protobuf:"varint,3,opt,name=disabled_group_no,json=disabledGroupNo" json:"disabled_group_no,omitempty"`
	Groups          []*JournalGroupInfo `protobuf:"bytes,4,rep,name=groups" json:"groups,omitempty"`
	InstanceId      *uint32             `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for ListJournalResponse fields.
const (
	Default_ListJournalResponse_InstanceId = uint32(0)
)

func (x *ListJournalResponse) Reset() {
	*x = ListJournalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJournalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJournalResponse) ProtoMessage() {}

func (x *ListJournalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJournalResponse.ProtoReflect.Descriptor instead.
func (*ListJournalResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{27}
}

func (x *ListJournalResponse) GetJournals() []*JournalInfo {
	if x != nil {
		return x.Journals
	}
	return nil
}

// Deprecated: Marked as deprecated in chunk.proto.
func (x *ListJournalResponse) GetDisabledGroupNo() uint32 {
	if x != nil && x.DisabledGroupNo != nil {
		return *x.DisabledGroupNo
	}
	return 0
}

func (x *ListJournalResponse) GetGroups() []*JournalGroupInfo {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *ListJournalResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ListJournalResponse_InstanceId
}

type ListJournalResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*ListJournalResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *ListJournalResponseV2) Reset() {
	*x = ListJournalResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJournalResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJournalResponseV2) ProtoMessage() {}

func (x *ListJournalResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJournalResponseV2.ProtoReflect.Descriptor instead.
func (*ListJournalResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{28}
}

func (x *ListJournalResponseV2) GetInstancesResponse() []*ListJournalResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type DiskInspectorStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path           *string   `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	Uuid           *string   `protobuf:"bytes,2,req,name=uuid" json:"uuid,omitempty"`
	Type           *DiskType `protobuf:"varint,3,req,name=type,enum=zbs.chunk.DiskType" json:"type,omitempty"` // cache, partition
	PatrolStarted  *bool     `protobuf:"varint,4,opt,name=patrol_started,json=patrolStarted,def=0" json:"patrol_started,omitempty"`
	PatrolProgress *uint32   `protobuf:"varint,5,opt,name=patrol_progress,json=patrolProgress,def=0" json:"patrol_progress,omitempty"`
	InspectIops    *uint64   `protobuf:"varint,6,opt,name=inspect_iops,json=inspectIops,def=0" json:"inspect_iops,omitempty"`
	InspectBw      *uint64   `protobuf:"varint,7,opt,name=inspect_bw,json=inspectBw,def=0" json:"inspect_bw,omitempty"`
}

// Default values for DiskInspectorStat fields.
const (
	Default_DiskInspectorStat_PatrolStarted  = bool(false)
	Default_DiskInspectorStat_PatrolProgress = uint32(0)
	Default_DiskInspectorStat_InspectIops    = uint64(0)
	Default_DiskInspectorStat_InspectBw      = uint64(0)
)

func (x *DiskInspectorStat) Reset() {
	*x = DiskInspectorStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiskInspectorStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiskInspectorStat) ProtoMessage() {}

func (x *DiskInspectorStat) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiskInspectorStat.ProtoReflect.Descriptor instead.
func (*DiskInspectorStat) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{29}
}

func (x *DiskInspectorStat) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *DiskInspectorStat) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *DiskInspectorStat) GetType() DiskType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return DiskType_DISK_TYPE_PARTITION
}

func (x *DiskInspectorStat) GetPatrolStarted() bool {
	if x != nil && x.PatrolStarted != nil {
		return *x.PatrolStarted
	}
	return Default_DiskInspectorStat_PatrolStarted
}

func (x *DiskInspectorStat) GetPatrolProgress() uint32 {
	if x != nil && x.PatrolProgress != nil {
		return *x.PatrolProgress
	}
	return Default_DiskInspectorStat_PatrolProgress
}

func (x *DiskInspectorStat) GetInspectIops() uint64 {
	if x != nil && x.InspectIops != nil {
		return *x.InspectIops
	}
	return Default_DiskInspectorStat_InspectIops
}

func (x *DiskInspectorStat) GetInspectBw() uint64 {
	if x != nil && x.InspectBw != nil {
		return *x.InspectBw
	}
	return Default_DiskInspectorStat_InspectBw
}

type GetDiskInspectorStatsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stats      []*DiskInspectorStat `protobuf:"bytes,1,rep,name=stats" json:"stats,omitempty"`
	InstanceId *uint32              `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for GetDiskInspectorStatsResponse fields.
const (
	Default_GetDiskInspectorStatsResponse_InstanceId = uint32(0)
)

func (x *GetDiskInspectorStatsResponse) Reset() {
	*x = GetDiskInspectorStatsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiskInspectorStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiskInspectorStatsResponse) ProtoMessage() {}

func (x *GetDiskInspectorStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiskInspectorStatsResponse.ProtoReflect.Descriptor instead.
func (*GetDiskInspectorStatsResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{30}
}

func (x *GetDiskInspectorStatsResponse) GetStats() []*DiskInspectorStat {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetDiskInspectorStatsResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_GetDiskInspectorStatsResponse_InstanceId
}

type GetDiskInspectorStatsResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*GetDiskInspectorStatsResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *GetDiskInspectorStatsResponseV2) Reset() {
	*x = GetDiskInspectorStatsResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiskInspectorStatsResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiskInspectorStatsResponseV2) ProtoMessage() {}

func (x *GetDiskInspectorStatsResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiskInspectorStatsResponseV2.ProtoReflect.Descriptor instead.
func (*GetDiskInspectorStatsResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{31}
}

func (x *GetDiskInspectorStatsResponseV2) GetInstancesResponse() []*GetDiskInspectorStatsResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type PathRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
}

func (x *PathRequest) Reset() {
	*x = PathRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PathRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathRequest) ProtoMessage() {}

func (x *PathRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathRequest.ProtoReflect.Descriptor instead.
func (*PathRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{32}
}

func (x *PathRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

// must be compatible with PathRequest and MountPartitionRequest
type UmountDiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DiskName *string         `protobuf:"bytes,1,req,name=disk_name,json=diskName" json:"disk_name,omitempty"`
	Scheme   *DiskNameScheme `protobuf:"varint,5,opt,name=scheme,enum=zbs.chunk.DiskNameScheme,def=1" json:"scheme,omitempty"`
	// Deprecated: Marked as deprecated in chunk.proto.
	XDeprecatedForce *bool           `protobuf:"varint,3,opt,name=__deprecated_force,json=DeprecatedForce" json:"__deprecated_force,omitempty"`
	Mode             *DiskUmountMode `protobuf:"varint,4,opt,name=mode,enum=zbs.chunk.DiskUmountMode,def=0" json:"mode,omitempty"`
}

// Default values for UmountDiskRequest fields.
const (
	Default_UmountDiskRequest_Scheme = DiskNameScheme_DISK_NAME_BY_PATH
	Default_UmountDiskRequest_Mode   = DiskUmountMode_DISK_UMOUNT_AUTO
)

func (x *UmountDiskRequest) Reset() {
	*x = UmountDiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UmountDiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmountDiskRequest) ProtoMessage() {}

func (x *UmountDiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmountDiskRequest.ProtoReflect.Descriptor instead.
func (*UmountDiskRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{33}
}

func (x *UmountDiskRequest) GetDiskName() string {
	if x != nil && x.DiskName != nil {
		return *x.DiskName
	}
	return ""
}

func (x *UmountDiskRequest) GetScheme() DiskNameScheme {
	if x != nil && x.Scheme != nil {
		return *x.Scheme
	}
	return Default_UmountDiskRequest_Scheme
}

// Deprecated: Marked as deprecated in chunk.proto.
func (x *UmountDiskRequest) GetXDeprecatedForce() bool {
	if x != nil && x.XDeprecatedForce != nil {
		return *x.XDeprecatedForce
	}
	return false
}

func (x *UmountDiskRequest) GetMode() DiskUmountMode {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return Default_UmountDiskRequest_Mode
}

// must be compatible with PathRequest
type DiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DiskName *string         `protobuf:"bytes,1,req,name=disk_name,json=diskName" json:"disk_name,omitempty"`
	Scheme   *DiskNameScheme `protobuf:"varint,5,opt,name=scheme,enum=zbs.chunk.DiskNameScheme,def=1" json:"scheme,omitempty"`
}

// Default values for DiskRequest fields.
const (
	Default_DiskRequest_Scheme = DiskNameScheme_DISK_NAME_BY_PATH
)

func (x *DiskRequest) Reset() {
	*x = DiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiskRequest) ProtoMessage() {}

func (x *DiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiskRequest.ProtoReflect.Descriptor instead.
func (*DiskRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{34}
}

func (x *DiskRequest) GetDiskName() string {
	if x != nil && x.DiskName != nil {
		return *x.DiskName
	}
	return ""
}

func (x *DiskRequest) GetScheme() DiskNameScheme {
	if x != nil && x.Scheme != nil {
		return *x.Scheme
	}
	return Default_DiskRequest_Scheme
}

type ChunkBlockInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockNo   *uint32 `protobuf:"varint,1,req,name=block_no,json=blockNo" json:"block_no,omitempty"`
	HddPath   *string `protobuf:"bytes,2,req,name=hdd_path,json=hddPath" json:"hdd_path,omitempty"`
	HddOffset *uint64 `protobuf:"varint,3,req,name=hdd_offset,json=hddOffset" json:"hdd_offset,omitempty"`
	SsdPath   *string `protobuf:"bytes,4,opt,name=ssd_path,json=ssdPath" json:"ssd_path,omitempty"`
	SsdOffset *uint64 `protobuf:"varint,5,opt,name=ssd_offset,json=ssdOffset" json:"ssd_offset,omitempty"`
}

func (x *ChunkBlockInfo) Reset() {
	*x = ChunkBlockInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkBlockInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkBlockInfo) ProtoMessage() {}

func (x *ChunkBlockInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkBlockInfo.ProtoReflect.Descriptor instead.
func (*ChunkBlockInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{35}
}

func (x *ChunkBlockInfo) GetBlockNo() uint32 {
	if x != nil && x.BlockNo != nil {
		return *x.BlockNo
	}
	return 0
}

func (x *ChunkBlockInfo) GetHddPath() string {
	if x != nil && x.HddPath != nil {
		return *x.HddPath
	}
	return ""
}

func (x *ChunkBlockInfo) GetHddOffset() uint64 {
	if x != nil && x.HddOffset != nil {
		return *x.HddOffset
	}
	return 0
}

func (x *ChunkBlockInfo) GetSsdPath() string {
	if x != nil && x.SsdPath != nil {
		return *x.SsdPath
	}
	return ""
}

func (x *ChunkBlockInfo) GetSsdOffset() uint64 {
	if x != nil && x.SsdOffset != nil {
		return *x.SsdOffset
	}
	return 0
}

type ChunkExtentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid            *uint32           `protobuf:"varint,1,req,name=pid" json:"pid,omitempty"`
	Status         *uint32           `protobuf:"varint,2,req,name=status" json:"status,omitempty"`
	NumChildren    *uint32           `protobuf:"varint,3,req,name=num_children,json=numChildren" json:"num_children,omitempty"`
	PartId         *uint32           `protobuf:"varint,4,req,name=part_id,json=partId" json:"part_id,omitempty"`
	ExtentNo       *uint32           `protobuf:"varint,5,req,name=extent_no,json=extentNo" json:"extent_no,omitempty"`
	OriginPid      *uint32           `protobuf:"varint,6,req,name=origin_pid,json=originPid" json:"origin_pid,omitempty"`
	Bitmap         []byte            `protobuf:"bytes,7,opt,name=bitmap" json:"bitmap,omitempty"`
	Epoch          *uint64           `protobuf:"varint,8,opt,name=epoch" json:"epoch,omitempty"`
	OriginEpoch    *uint64           `protobuf:"varint,9,opt,name=origin_epoch,json=originEpoch" json:"origin_epoch,omitempty"`
	Gen            *uint64           `protobuf:"varint,10,opt,name=gen" json:"gen,omitempty"`
	ThickProvision *bool             `protobuf:"varint,11,opt,name=thick_provision,json=thickProvision,def=0" json:"thick_provision,omitempty"`
	Perf           *bool             `protobuf:"varint,12,opt,name=perf,def=0" json:"perf,omitempty"`
	Blocks         []*ChunkBlockInfo `protobuf:"bytes,21,rep,name=blocks" json:"blocks,omitempty"`
}

// Default values for ChunkExtentInfo fields.
const (
	Default_ChunkExtentInfo_ThickProvision = bool(false)
	Default_ChunkExtentInfo_Perf           = bool(false)
)

func (x *ChunkExtentInfo) Reset() {
	*x = ChunkExtentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkExtentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkExtentInfo) ProtoMessage() {}

func (x *ChunkExtentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkExtentInfo.ProtoReflect.Descriptor instead.
func (*ChunkExtentInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{36}
}

func (x *ChunkExtentInfo) GetPid() uint32 {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return 0
}

func (x *ChunkExtentInfo) GetStatus() uint32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *ChunkExtentInfo) GetNumChildren() uint32 {
	if x != nil && x.NumChildren != nil {
		return *x.NumChildren
	}
	return 0
}

func (x *ChunkExtentInfo) GetPartId() uint32 {
	if x != nil && x.PartId != nil {
		return *x.PartId
	}
	return 0
}

func (x *ChunkExtentInfo) GetExtentNo() uint32 {
	if x != nil && x.ExtentNo != nil {
		return *x.ExtentNo
	}
	return 0
}

func (x *ChunkExtentInfo) GetOriginPid() uint32 {
	if x != nil && x.OriginPid != nil {
		return *x.OriginPid
	}
	return 0
}

func (x *ChunkExtentInfo) GetBitmap() []byte {
	if x != nil {
		return x.Bitmap
	}
	return nil
}

func (x *ChunkExtentInfo) GetEpoch() uint64 {
	if x != nil && x.Epoch != nil {
		return *x.Epoch
	}
	return 0
}

func (x *ChunkExtentInfo) GetOriginEpoch() uint64 {
	if x != nil && x.OriginEpoch != nil {
		return *x.OriginEpoch
	}
	return 0
}

func (x *ChunkExtentInfo) GetGen() uint64 {
	if x != nil && x.Gen != nil {
		return *x.Gen
	}
	return 0
}

func (x *ChunkExtentInfo) GetThickProvision() bool {
	if x != nil && x.ThickProvision != nil {
		return *x.ThickProvision
	}
	return Default_ChunkExtentInfo_ThickProvision
}

func (x *ChunkExtentInfo) GetPerf() bool {
	if x != nil && x.Perf != nil {
		return *x.Perf
	}
	return Default_ChunkExtentInfo_Perf
}

func (x *ChunkExtentInfo) GetBlocks() []*ChunkBlockInfo {
	if x != nil {
		return x.Blocks
	}
	return nil
}

type ListExtentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos []*ChunkExtentInfo `protobuf:"bytes,1,rep,name=infos" json:"infos,omitempty"`
}

func (x *ListExtentResponse) Reset() {
	*x = ListExtentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExtentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExtentResponse) ProtoMessage() {}

func (x *ListExtentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExtentResponse.ProtoReflect.Descriptor instead.
func (*ListExtentResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{37}
}

func (x *ListExtentResponse) GetInfos() []*ChunkExtentInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

type PExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid *uint32 `protobuf:"varint,1,req,name=pid" json:"pid,omitempty"`
}

func (x *PExtentRequest) Reset() {
	*x = PExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PExtentRequest) ProtoMessage() {}

func (x *PExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PExtentRequest.ProtoReflect.Descriptor instead.
func (*PExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{38}
}

func (x *PExtentRequest) GetPid() uint32 {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return 0
}

type ListPExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start      *uint32 `protobuf:"varint,1,opt,name=start,def=1" json:"start,omitempty"`
	Length     *uint32 `protobuf:"varint,2,opt,name=length,def=1024" json:"length,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for ListPExtentRequest fields.
const (
	Default_ListPExtentRequest_Start      = uint32(1)
	Default_ListPExtentRequest_Length     = uint32(1024)
	Default_ListPExtentRequest_InstanceId = uint32(1)
)

func (x *ListPExtentRequest) Reset() {
	*x = ListPExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPExtentRequest) ProtoMessage() {}

func (x *ListPExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPExtentRequest.ProtoReflect.Descriptor instead.
func (*ListPExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{39}
}

func (x *ListPExtentRequest) GetStart() uint32 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return Default_ListPExtentRequest_Start
}

func (x *ListPExtentRequest) GetLength() uint32 {
	if x != nil && x.Length != nil {
		return *x.Length
	}
	return Default_ListPExtentRequest_Length
}

func (x *ListPExtentRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ListPExtentRequest_InstanceId
}

type InvalidateExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid        *uint32 `protobuf:"varint,1,req,name=pid" json:"pid,omitempty"`
	Recursive  *bool   `protobuf:"varint,2,req,name=recursive" json:"recursive,omitempty"`
	Epoch      *uint64 `protobuf:"varint,3,opt,name=epoch" json:"epoch,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for InvalidateExtentRequest fields.
const (
	Default_InvalidateExtentRequest_InstanceId = uint32(0)
)

func (x *InvalidateExtentRequest) Reset() {
	*x = InvalidateExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvalidateExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvalidateExtentRequest) ProtoMessage() {}

func (x *InvalidateExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvalidateExtentRequest.ProtoReflect.Descriptor instead.
func (*InvalidateExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{40}
}

func (x *InvalidateExtentRequest) GetPid() uint32 {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return 0
}

func (x *InvalidateExtentRequest) GetRecursive() bool {
	if x != nil && x.Recursive != nil {
		return *x.Recursive
	}
	return false
}

func (x *InvalidateExtentRequest) GetEpoch() uint64 {
	if x != nil && x.Epoch != nil {
		return *x.Epoch
	}
	return 0
}

func (x *InvalidateExtentRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_InvalidateExtentRequest_InstanceId
}

type SetVerifyModeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode *string `protobuf:"bytes,1,req,name=mode" json:"mode,omitempty"`
}

func (x *SetVerifyModeRequest) Reset() {
	*x = SetVerifyModeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetVerifyModeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetVerifyModeRequest) ProtoMessage() {}

func (x *SetVerifyModeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetVerifyModeRequest.ProtoReflect.Descriptor instead.
func (*SetVerifyModeRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{41}
}

func (x *SetVerifyModeRequest) GetMode() string {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return ""
}

type QueryDiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
}

func (x *QueryDiskRequest) Reset() {
	*x = QueryDiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDiskRequest) ProtoMessage() {}

func (x *QueryDiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDiskRequest.ProtoReflect.Descriptor instead.
func (*QueryDiskRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{42}
}

func (x *QueryDiskRequest) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

type QueryDiskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InUse     *bool   `protobuf:"varint,1,opt,name=in_use,json=inUse" json:"in_use,omitempty"`
	Exist     *bool   `protobuf:"varint,2,opt,name=exist" json:"exist,omitempty"`
	Type      *string `protobuf:"bytes,3,opt,name=type" json:"type,omitempty"` // cache, journal, partition
	Errflags  *uint32 `protobuf:"varint,4,opt,name=errflags" json:"errflags,omitempty"`
	Formatted *bool   `protobuf:"varint,5,opt,name=formatted" json:"formatted,omitempty"`
	MatchHost *bool   `protobuf:"varint,6,opt,name=match_host,json=matchHost" json:"match_host,omitempty"`
	DeviceId  *string `protobuf:"bytes,7,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	// The query may not find the disk, in this case, the instance_id is set to 0 here.
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for QueryDiskResponse fields.
const (
	Default_QueryDiskResponse_InstanceId = uint32(0)
)

func (x *QueryDiskResponse) Reset() {
	*x = QueryDiskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDiskResponse) ProtoMessage() {}

func (x *QueryDiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDiskResponse.ProtoReflect.Descriptor instead.
func (*QueryDiskResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{43}
}

func (x *QueryDiskResponse) GetInUse() bool {
	if x != nil && x.InUse != nil {
		return *x.InUse
	}
	return false
}

func (x *QueryDiskResponse) GetExist() bool {
	if x != nil && x.Exist != nil {
		return *x.Exist
	}
	return false
}

func (x *QueryDiskResponse) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *QueryDiskResponse) GetErrflags() uint32 {
	if x != nil && x.Errflags != nil {
		return *x.Errflags
	}
	return 0
}

func (x *QueryDiskResponse) GetFormatted() bool {
	if x != nil && x.Formatted != nil {
		return *x.Formatted
	}
	return false
}

func (x *QueryDiskResponse) GetMatchHost() bool {
	if x != nil && x.MatchHost != nil {
		return *x.MatchHost
	}
	return false
}

func (x *QueryDiskResponse) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

func (x *QueryDiskResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_QueryDiskResponse_InstanceId
}

type RejectedDiskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            *string   `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	DeviceId      *string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	LastMountType *DiskType `protobuf:"varint,3,opt,name=last_mount_type,json=lastMountType,enum=zbs.chunk.DiskType" json:"last_mount_type,omitempty"`
	Errflags      *uint32   `protobuf:"varint,4,opt,name=errflags" json:"errflags,omitempty"`
	Warnflags     *uint32   `protobuf:"varint,5,opt,name=warnflags" json:"warnflags,omitempty"`
}

func (x *RejectedDiskInfo) Reset() {
	*x = RejectedDiskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RejectedDiskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectedDiskInfo) ProtoMessage() {}

func (x *RejectedDiskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectedDiskInfo.ProtoReflect.Descriptor instead.
func (*RejectedDiskInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{44}
}

func (x *RejectedDiskInfo) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *RejectedDiskInfo) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

func (x *RejectedDiskInfo) GetLastMountType() DiskType {
	if x != nil && x.LastMountType != nil {
		return *x.LastMountType
	}
	return DiskType_DISK_TYPE_PARTITION
}

func (x *RejectedDiskInfo) GetErrflags() uint32 {
	if x != nil && x.Errflags != nil {
		return *x.Errflags
	}
	return 0
}

func (x *RejectedDiskInfo) GetWarnflags() uint32 {
	if x != nil && x.Warnflags != nil {
		return *x.Warnflags
	}
	return 0
}

type ListRejectedDisksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RejectedDisks []*RejectedDiskInfo `protobuf:"bytes,1,rep,name=rejected_disks,json=rejectedDisks" json:"rejected_disks,omitempty"`
	InstanceId    *uint32             `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for ListRejectedDisksResponse fields.
const (
	Default_ListRejectedDisksResponse_InstanceId = uint32(0)
)

func (x *ListRejectedDisksResponse) Reset() {
	*x = ListRejectedDisksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRejectedDisksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRejectedDisksResponse) ProtoMessage() {}

func (x *ListRejectedDisksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRejectedDisksResponse.ProtoReflect.Descriptor instead.
func (*ListRejectedDisksResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{45}
}

func (x *ListRejectedDisksResponse) GetRejectedDisks() []*RejectedDiskInfo {
	if x != nil {
		return x.RejectedDisks
	}
	return nil
}

func (x *ListRejectedDisksResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ListRejectedDisksResponse_InstanceId
}

type ListRejectedDisksResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*ListRejectedDisksResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *ListRejectedDisksResponseV2) Reset() {
	*x = ListRejectedDisksResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRejectedDisksResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRejectedDisksResponseV2) ProtoMessage() {}

func (x *ListRejectedDisksResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRejectedDisksResponseV2.ProtoReflect.Descriptor instead.
func (*ListRejectedDisksResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{46}
}

func (x *ListRejectedDisksResponseV2) GetInstancesResponse() []*ListRejectedDisksResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type AcceptDiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
}

func (x *AcceptDiskRequest) Reset() {
	*x = AcceptDiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptDiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptDiskRequest) ProtoMessage() {}

func (x *AcceptDiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptDiskRequest.ProtoReflect.Descriptor instead.
func (*AcceptDiskRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{47}
}

func (x *AcceptDiskRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

type DataChannelServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UseRdma *bool `protobuf:"varint,1,opt,name=use_rdma,json=useRdma,def=0" json:"use_rdma,omitempty"`
}

// Default values for DataChannelServerInfo fields.
const (
	Default_DataChannelServerInfo_UseRdma = bool(false)
)

func (x *DataChannelServerInfo) Reset() {
	*x = DataChannelServerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataChannelServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataChannelServerInfo) ProtoMessage() {}

func (x *DataChannelServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataChannelServerInfo.ProtoReflect.Descriptor instead.
func (*DataChannelServerInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{48}
}

func (x *DataChannelServerInfo) GetUseRdma() bool {
	if x != nil && x.UseRdma != nil {
		return *x.UseRdma
	}
	return Default_DataChannelServerInfo_UseRdma
}

type ChunkCapability struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgileRecover *bool `protobuf:"varint,1,opt,name=agile_recover,json=agileRecover,def=0" json:"agile_recover,omitempty"`
}

// Default values for ChunkCapability fields.
const (
	Default_ChunkCapability_AgileRecover = bool(false)
)

func (x *ChunkCapability) Reset() {
	*x = ChunkCapability{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkCapability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkCapability) ProtoMessage() {}

func (x *ChunkCapability) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkCapability.ProtoReflect.Descriptor instead.
func (*ChunkCapability) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{49}
}

func (x *ChunkCapability) GetAgileRecover() bool {
	if x != nil && x.AgileRecover != nil {
		return *x.AgileRecover
	}
	return Default_ChunkCapability_AgileRecover
}

type GatewayServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VhostStarted *bool `protobuf:"varint,1,opt,name=vhost_started,json=vhostStarted,def=0" json:"vhost_started,omitempty"`
	NvmfStarted  *bool `protobuf:"varint,2,opt,name=nvmf_started,json=nvmfStarted,def=0" json:"nvmf_started,omitempty"`
	IscsiStarted *bool `protobuf:"varint,3,opt,name=iscsi_started,json=iscsiStarted,def=0" json:"iscsi_started,omitempty"`
	NfsStarted   *bool `protobuf:"varint,4,opt,name=nfs_started,json=nfsStarted,def=0" json:"nfs_started,omitempty"`
}

// Default values for GatewayServerInfo fields.
const (
	Default_GatewayServerInfo_VhostStarted = bool(false)
	Default_GatewayServerInfo_NvmfStarted  = bool(false)
	Default_GatewayServerInfo_IscsiStarted = bool(false)
	Default_GatewayServerInfo_NfsStarted   = bool(false)
)

func (x *GatewayServerInfo) Reset() {
	*x = GatewayServerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayServerInfo) ProtoMessage() {}

func (x *GatewayServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayServerInfo.ProtoReflect.Descriptor instead.
func (*GatewayServerInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{50}
}

func (x *GatewayServerInfo) GetVhostStarted() bool {
	if x != nil && x.VhostStarted != nil {
		return *x.VhostStarted
	}
	return Default_GatewayServerInfo_VhostStarted
}

func (x *GatewayServerInfo) GetNvmfStarted() bool {
	if x != nil && x.NvmfStarted != nil {
		return *x.NvmfStarted
	}
	return Default_GatewayServerInfo_NvmfStarted
}

func (x *GatewayServerInfo) GetIscsiStarted() bool {
	if x != nil && x.IscsiStarted != nil {
		return *x.IscsiStarted
	}
	return Default_GatewayServerInfo_IscsiStarted
}

func (x *GatewayServerInfo) GetNfsStarted() bool {
	if x != nil && x.NfsStarted != nil {
		return *x.NfsStarted
	}
	return Default_GatewayServerInfo_NfsStarted
}

type SummaryInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LsmMeta         *zbs.LSMMeta           `protobuf:"bytes,1,opt,name=lsm_meta,json=lsmMeta" json:"lsm_meta,omitempty"`
	LsmCapability   *uint64                `protobuf:"fixed64,2,opt,name=lsm_capability,json=lsmCapability,def=0" json:"lsm_capability,omitempty"`
	SpaceInfo       *zbs.ChunkSpaceInfo    `protobuf:"bytes,31,opt,name=space_info,json=spaceInfo" json:"space_info,omitempty"`
	MergeIops       *uint64                `protobuf:"varint,11,opt,name=merge_iops,json=mergeIops" json:"merge_iops,omitempty"`
	MergeBw         *uint64                `protobuf:"varint,12,opt,name=merge_bw,json=mergeBw" json:"merge_bw,omitempty"`
	WritebackIops   *uint64                `protobuf:"varint,13,opt,name=writeback_iops,json=writebackIops" json:"writeback_iops,omitempty"`
	WritebackBw     *uint64                `protobuf:"varint,14,opt,name=writeback_bw,json=writebackBw" json:"writeback_bw,omitempty"`
	LsmIops         *uint64                `protobuf:"varint,15,opt,name=lsm_iops,json=lsmIops" json:"lsm_iops,omitempty"`
	LsmBw           *uint64                `protobuf:"varint,16,opt,name=lsm_bw,json=lsmBw" json:"lsm_bw,omitempty"`
	WaitReclaim     *uint64                `protobuf:"varint,20,opt,name=wait_reclaim,json=waitReclaim" json:"wait_reclaim,omitempty"`
	WaitMerge       *uint64                `protobuf:"varint,21,opt,name=wait_merge,json=waitMerge" json:"wait_merge,omitempty"`
	UnmapIops       *uint64                `protobuf:"varint,22,opt,name=unmap_iops,json=unmapIops" json:"unmap_iops,omitempty"`
	UnmapBw         *uint64                `protobuf:"varint,23,opt,name=unmap_bw,json=unmapBw" json:"unmap_bw,omitempty"`
	DcsInfo         *DataChannelServerInfo `protobuf:"bytes,41,opt,name=dcs_info,json=dcsInfo" json:"dcs_info,omitempty"`
	ChunkCapability *ChunkCapability       `protobuf:"bytes,42,opt,name=chunk_capability,json=chunkCapability" json:"chunk_capability,omitempty"`
	GatewayInfo     *GatewayServerInfo     `protobuf:"bytes,43,opt,name=gateway_info,json=gatewayInfo" json:"gateway_info,omitempty"`
	MetricOn        *bool                  `protobuf:"varint,50,opt,name=metric_on,json=metricOn" json:"metric_on,omitempty"`
	TraceOn         *bool                  `protobuf:"varint,51,opt,name=trace_on,json=traceOn" json:"trace_on,omitempty"`
	AdaptiveTraceOn *bool                  `protobuf:"varint,52,opt,name=adaptive_trace_on,json=adaptiveTraceOn" json:"adaptive_trace_on,omitempty"`
	AccelCopyMode   *zbs.AccelCopyMode     `protobuf:"varint,53,opt,name=accel_copy_mode,json=accelCopyMode,enum=zbs.AccelCopyMode,def=0" json:"accel_copy_mode,omitempty"`
	InstanceId      *uint32                `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for SummaryInfoResponse fields.
const (
	Default_SummaryInfoResponse_LsmCapability = uint64(0)
	Default_SummaryInfoResponse_AccelCopyMode = zbs.AccelCopyMode(0) // zbs.AccelCopyMode_ACCEL_COPY_DISABLED
	Default_SummaryInfoResponse_InstanceId    = uint32(0)
)

func (x *SummaryInfoResponse) Reset() {
	*x = SummaryInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SummaryInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SummaryInfoResponse) ProtoMessage() {}

func (x *SummaryInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SummaryInfoResponse.ProtoReflect.Descriptor instead.
func (*SummaryInfoResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{51}
}

func (x *SummaryInfoResponse) GetLsmMeta() *zbs.LSMMeta {
	if x != nil {
		return x.LsmMeta
	}
	return nil
}

func (x *SummaryInfoResponse) GetLsmCapability() uint64 {
	if x != nil && x.LsmCapability != nil {
		return *x.LsmCapability
	}
	return Default_SummaryInfoResponse_LsmCapability
}

func (x *SummaryInfoResponse) GetSpaceInfo() *zbs.ChunkSpaceInfo {
	if x != nil {
		return x.SpaceInfo
	}
	return nil
}

func (x *SummaryInfoResponse) GetMergeIops() uint64 {
	if x != nil && x.MergeIops != nil {
		return *x.MergeIops
	}
	return 0
}

func (x *SummaryInfoResponse) GetMergeBw() uint64 {
	if x != nil && x.MergeBw != nil {
		return *x.MergeBw
	}
	return 0
}

func (x *SummaryInfoResponse) GetWritebackIops() uint64 {
	if x != nil && x.WritebackIops != nil {
		return *x.WritebackIops
	}
	return 0
}

func (x *SummaryInfoResponse) GetWritebackBw() uint64 {
	if x != nil && x.WritebackBw != nil {
		return *x.WritebackBw
	}
	return 0
}

func (x *SummaryInfoResponse) GetLsmIops() uint64 {
	if x != nil && x.LsmIops != nil {
		return *x.LsmIops
	}
	return 0
}

func (x *SummaryInfoResponse) GetLsmBw() uint64 {
	if x != nil && x.LsmBw != nil {
		return *x.LsmBw
	}
	return 0
}

func (x *SummaryInfoResponse) GetWaitReclaim() uint64 {
	if x != nil && x.WaitReclaim != nil {
		return *x.WaitReclaim
	}
	return 0
}

func (x *SummaryInfoResponse) GetWaitMerge() uint64 {
	if x != nil && x.WaitMerge != nil {
		return *x.WaitMerge
	}
	return 0
}

func (x *SummaryInfoResponse) GetUnmapIops() uint64 {
	if x != nil && x.UnmapIops != nil {
		return *x.UnmapIops
	}
	return 0
}

func (x *SummaryInfoResponse) GetUnmapBw() uint64 {
	if x != nil && x.UnmapBw != nil {
		return *x.UnmapBw
	}
	return 0
}

func (x *SummaryInfoResponse) GetDcsInfo() *DataChannelServerInfo {
	if x != nil {
		return x.DcsInfo
	}
	return nil
}

func (x *SummaryInfoResponse) GetChunkCapability() *ChunkCapability {
	if x != nil {
		return x.ChunkCapability
	}
	return nil
}

func (x *SummaryInfoResponse) GetGatewayInfo() *GatewayServerInfo {
	if x != nil {
		return x.GatewayInfo
	}
	return nil
}

func (x *SummaryInfoResponse) GetMetricOn() bool {
	if x != nil && x.MetricOn != nil {
		return *x.MetricOn
	}
	return false
}

func (x *SummaryInfoResponse) GetTraceOn() bool {
	if x != nil && x.TraceOn != nil {
		return *x.TraceOn
	}
	return false
}

func (x *SummaryInfoResponse) GetAdaptiveTraceOn() bool {
	if x != nil && x.AdaptiveTraceOn != nil {
		return *x.AdaptiveTraceOn
	}
	return false
}

func (x *SummaryInfoResponse) GetAccelCopyMode() zbs.AccelCopyMode {
	if x != nil && x.AccelCopyMode != nil {
		return *x.AccelCopyMode
	}
	return Default_SummaryInfoResponse_AccelCopyMode
}

func (x *SummaryInfoResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_SummaryInfoResponse_InstanceId
}

type SummaryInfoResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*SummaryInfoResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
	ChunkInstancesNum *uint32                `protobuf:"varint,2,opt,name=chunk_instances_num,json=chunkInstancesNum,def=1" json:"chunk_instances_num,omitempty"`
}

// Default values for SummaryInfoResponseV2 fields.
const (
	Default_SummaryInfoResponseV2_ChunkInstancesNum = uint32(1)
)

func (x *SummaryInfoResponseV2) Reset() {
	*x = SummaryInfoResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SummaryInfoResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SummaryInfoResponseV2) ProtoMessage() {}

func (x *SummaryInfoResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SummaryInfoResponseV2.ProtoReflect.Descriptor instead.
func (*SummaryInfoResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{52}
}

func (x *SummaryInfoResponseV2) GetInstancesResponse() []*SummaryInfoResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

func (x *SummaryInfoResponseV2) GetChunkInstancesNum() uint32 {
	if x != nil && x.ChunkInstancesNum != nil {
		return *x.ChunkInstancesNum
	}
	return Default_SummaryInfoResponseV2_ChunkInstancesNum
}

type ChunkServiceStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessHealthy  *bool         `protobuf:"varint,1,req,name=access_healthy,json=accessHealthy,def=0" json:"access_healthy,omitempty"` // deprecated
	AuroraState    *bool         `protobuf:"varint,2,req,name=aurora_state,json=auroraState,def=0" json:"aurora_state,omitempty"`       // deprecated
	AccessAliveSec *uint64       `protobuf:"varint,3,opt,name=access_alive_sec,json=accessAliveSec,def=0" json:"access_alive_sec,omitempty"`
	AuroraStatus   *AuroraStatus `protobuf:"varint,4,opt,name=aurora_status,json=auroraStatus,enum=zbs.chunk.AuroraStatus" json:"aurora_status,omitempty"`
	AuroraIsolated *bool         `protobuf:"varint,5,opt,name=aurora_isolated,json=auroraIsolated,def=0" json:"aurora_isolated,omitempty"`
}

// Default values for ChunkServiceStat fields.
const (
	Default_ChunkServiceStat_AccessHealthy  = bool(false)
	Default_ChunkServiceStat_AuroraState    = bool(false)
	Default_ChunkServiceStat_AccessAliveSec = uint64(0)
	Default_ChunkServiceStat_AuroraIsolated = bool(false)
)

func (x *ChunkServiceStat) Reset() {
	*x = ChunkServiceStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkServiceStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkServiceStat) ProtoMessage() {}

func (x *ChunkServiceStat) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkServiceStat.ProtoReflect.Descriptor instead.
func (*ChunkServiceStat) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{53}
}

func (x *ChunkServiceStat) GetAccessHealthy() bool {
	if x != nil && x.AccessHealthy != nil {
		return *x.AccessHealthy
	}
	return Default_ChunkServiceStat_AccessHealthy
}

func (x *ChunkServiceStat) GetAuroraState() bool {
	if x != nil && x.AuroraState != nil {
		return *x.AuroraState
	}
	return Default_ChunkServiceStat_AuroraState
}

func (x *ChunkServiceStat) GetAccessAliveSec() uint64 {
	if x != nil && x.AccessAliveSec != nil {
		return *x.AccessAliveSec
	}
	return Default_ChunkServiceStat_AccessAliveSec
}

func (x *ChunkServiceStat) GetAuroraStatus() AuroraStatus {
	if x != nil && x.AuroraStatus != nil {
		return *x.AuroraStatus
	}
	return AuroraStatus_AURORA_STATUS_INITIALIZING
}

func (x *ChunkServiceStat) GetAuroraIsolated() bool {
	if x != nil && x.AuroraIsolated != nil {
		return *x.AuroraIsolated
	}
	return Default_ChunkServiceStat_AuroraIsolated
}

type DirtyBlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId []byte `protobuf:"bytes,1,req,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
	DryRun   *bool  `protobuf:"varint,2,opt,name=dry_run,json=dryRun,def=0" json:"dry_run,omitempty"`
}

// Default values for DirtyBlockRequest fields.
const (
	Default_DirtyBlockRequest_DryRun = bool(false)
)

func (x *DirtyBlockRequest) Reset() {
	*x = DirtyBlockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirtyBlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirtyBlockRequest) ProtoMessage() {}

func (x *DirtyBlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirtyBlockRequest.ProtoReflect.Descriptor instead.
func (*DirtyBlockRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{54}
}

func (x *DirtyBlockRequest) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

func (x *DirtyBlockRequest) GetDryRun() bool {
	if x != nil && x.DryRun != nil {
		return *x.DryRun
	}
	return Default_DirtyBlockRequest_DryRun
}

type DirtyBlockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Blocks []uint64 `protobuf:"varint,1,rep,name=blocks" json:"blocks,omitempty"`
}

func (x *DirtyBlockResponse) Reset() {
	*x = DirtyBlockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirtyBlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirtyBlockResponse) ProtoMessage() {}

func (x *DirtyBlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirtyBlockResponse.ProtoReflect.Descriptor instead.
func (*DirtyBlockResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{55}
}

func (x *DirtyBlockResponse) GetBlocks() []uint64 {
	if x != nil {
		return x.Blocks
	}
	return nil
}

type DiskRefInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *string `protobuf:"bytes,1,req,name=path" json:"path,omitempty"`
	Num  *uint32 `protobuf:"varint,2,req,name=num" json:"num,omitempty"`
}

func (x *DiskRefInfo) Reset() {
	*x = DiskRefInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiskRefInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiskRefInfo) ProtoMessage() {}

func (x *DiskRefInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiskRefInfo.ProtoReflect.Descriptor instead.
func (*DiskRefInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{56}
}

func (x *DiskRefInfo) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *DiskRefInfo) GetNum() uint32 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

type ShowExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid        *uint32 `protobuf:"varint,1,req,name=pid" json:"pid,omitempty"`
	ShowDisk   *bool   `protobuf:"varint,2,opt,name=show_disk,json=showDisk" json:"show_disk,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for ShowExtentRequest fields.
const (
	Default_ShowExtentRequest_InstanceId = uint32(0)
)

func (x *ShowExtentRequest) Reset() {
	*x = ShowExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowExtentRequest) ProtoMessage() {}

func (x *ShowExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowExtentRequest.ProtoReflect.Descriptor instead.
func (*ShowExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{57}
}

func (x *ShowExtentRequest) GetPid() uint32 {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return 0
}

func (x *ShowExtentRequest) GetShowDisk() bool {
	if x != nil && x.ShowDisk != nil {
		return *x.ShowDisk
	}
	return false
}

func (x *ShowExtentRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ShowExtentRequest_InstanceId
}

type ShowExtentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid            *uint32        `protobuf:"varint,1,req,name=pid" json:"pid,omitempty"`
	Epoch          *uint64        `protobuf:"varint,2,req,name=epoch" json:"epoch,omitempty"`
	Generation     *uint64        `protobuf:"varint,3,req,name=generation" json:"generation,omitempty"`
	Status         *uint32        `protobuf:"varint,4,req,name=status" json:"status,omitempty"`
	PrivateBlobNum *uint32        `protobuf:"varint,5,opt,name=private_blob_num,json=privateBlobNum" json:"private_blob_num,omitempty"`
	SharedBlobNum  *uint32        `protobuf:"varint,6,opt,name=shared_blob_num,json=sharedBlobNum" json:"shared_blob_num,omitempty"`
	DiskRefs       []*DiskRefInfo `protobuf:"bytes,7,rep,name=disk_refs,json=diskRefs" json:"disk_refs,omitempty"`
	ThickProvision *bool          `protobuf:"varint,8,opt,name=thick_provision,json=thickProvision,def=0" json:"thick_provision,omitempty"`
	Perf           *bool          `protobuf:"varint,9,opt,name=perf,def=0" json:"perf,omitempty"`
	AdditionalInfo *string        `protobuf:"bytes,11,opt,name=additional_info,json=additionalInfo" json:"additional_info,omitempty"`
	InstanceIds    []uint32       `protobuf:"varint,99,rep,name=instance_ids,json=instanceIds" json:"instance_ids,omitempty"`
}

// Default values for ShowExtentResponse fields.
const (
	Default_ShowExtentResponse_ThickProvision = bool(false)
	Default_ShowExtentResponse_Perf           = bool(false)
)

func (x *ShowExtentResponse) Reset() {
	*x = ShowExtentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowExtentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowExtentResponse) ProtoMessage() {}

func (x *ShowExtentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowExtentResponse.ProtoReflect.Descriptor instead.
func (*ShowExtentResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{58}
}

func (x *ShowExtentResponse) GetPid() uint32 {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return 0
}

func (x *ShowExtentResponse) GetEpoch() uint64 {
	if x != nil && x.Epoch != nil {
		return *x.Epoch
	}
	return 0
}

func (x *ShowExtentResponse) GetGeneration() uint64 {
	if x != nil && x.Generation != nil {
		return *x.Generation
	}
	return 0
}

func (x *ShowExtentResponse) GetStatus() uint32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *ShowExtentResponse) GetPrivateBlobNum() uint32 {
	if x != nil && x.PrivateBlobNum != nil {
		return *x.PrivateBlobNum
	}
	return 0
}

func (x *ShowExtentResponse) GetSharedBlobNum() uint32 {
	if x != nil && x.SharedBlobNum != nil {
		return *x.SharedBlobNum
	}
	return 0
}

func (x *ShowExtentResponse) GetDiskRefs() []*DiskRefInfo {
	if x != nil {
		return x.DiskRefs
	}
	return nil
}

func (x *ShowExtentResponse) GetThickProvision() bool {
	if x != nil && x.ThickProvision != nil {
		return *x.ThickProvision
	}
	return Default_ShowExtentResponse_ThickProvision
}

func (x *ShowExtentResponse) GetPerf() bool {
	if x != nil && x.Perf != nil {
		return *x.Perf
	}
	return Default_ShowExtentResponse_Perf
}

func (x *ShowExtentResponse) GetAdditionalInfo() string {
	if x != nil && x.AdditionalInfo != nil {
		return *x.AdditionalInfo
	}
	return ""
}

func (x *ShowExtentResponse) GetInstanceIds() []uint32 {
	if x != nil {
		return x.InstanceIds
	}
	return nil
}

type PromoteExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid        *uint32 `protobuf:"varint,1,req,name=pid" json:"pid,omitempty"`
	Offset     *uint64 `protobuf:"varint,2,opt,name=offset" json:"offset,omitempty"`
	Length     *uint64 `protobuf:"varint,3,opt,name=length,def=268435456" json:"length,omitempty"`
	InstanceId *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for PromoteExtentRequest fields.
const (
	Default_PromoteExtentRequest_Length     = uint64(268435456)
	Default_PromoteExtentRequest_InstanceId = uint32(0)
)

func (x *PromoteExtentRequest) Reset() {
	*x = PromoteExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PromoteExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PromoteExtentRequest) ProtoMessage() {}

func (x *PromoteExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PromoteExtentRequest.ProtoReflect.Descriptor instead.
func (*PromoteExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{59}
}

func (x *PromoteExtentRequest) GetPid() uint32 {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return 0
}

func (x *PromoteExtentRequest) GetOffset() uint64 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *PromoteExtentRequest) GetLength() uint64 {
	if x != nil && x.Length != nil {
		return *x.Length
	}
	return Default_PromoteExtentRequest_Length
}

func (x *PromoteExtentRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_PromoteExtentRequest_InstanceId
}

type PollingThreadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid     *int32  `protobuf:"varint,1,req,name=tid" json:"tid,omitempty"`
	Name    *string `protobuf:"bytes,2,req,name=name" json:"name,omitempty"`
	BusyTsc *uint64 `protobuf:"varint,3,req,name=busy_tsc,json=busyTsc" json:"busy_tsc,omitempty"`
	IdleTsc *uint64 `protobuf:"varint,4,req,name=idle_tsc,json=idleTsc" json:"idle_tsc,omitempty"`
}

func (x *PollingThreadInfo) Reset() {
	*x = PollingThreadInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollingThreadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollingThreadInfo) ProtoMessage() {}

func (x *PollingThreadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollingThreadInfo.ProtoReflect.Descriptor instead.
func (*PollingThreadInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{60}
}

func (x *PollingThreadInfo) GetTid() int32 {
	if x != nil && x.Tid != nil {
		return *x.Tid
	}
	return 0
}

func (x *PollingThreadInfo) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *PollingThreadInfo) GetBusyTsc() uint64 {
	if x != nil && x.BusyTsc != nil {
		return *x.BusyTsc
	}
	return 0
}

func (x *PollingThreadInfo) GetIdleTsc() uint64 {
	if x != nil && x.IdleTsc != nil {
		return *x.IdleTsc
	}
	return 0
}

type GetPollingStatsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PollingStats []*PollingThreadInfo `protobuf:"bytes,1,rep,name=polling_stats,json=pollingStats" json:"polling_stats,omitempty"`
}

func (x *GetPollingStatsResponse) Reset() {
	*x = GetPollingStatsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPollingStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPollingStatsResponse) ProtoMessage() {}

func (x *GetPollingStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPollingStatsResponse.ProtoReflect.Descriptor instead.
func (*GetPollingStatsResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{61}
}

func (x *GetPollingStatsResponse) GetPollingStats() []*PollingThreadInfo {
	if x != nil {
		return x.PollingStats
	}
	return nil
}

type VolumeIOLatencyInjection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId           []byte  `protobuf:"bytes,1,req,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
	ReadLatencyMs      *uint64 `protobuf:"varint,2,opt,name=read_latency_ms,json=readLatencyMs,def=0" json:"read_latency_ms,omitempty"`
	WriteLatencyMs     *uint64 `protobuf:"varint,3,opt,name=write_latency_ms,json=writeLatencyMs,def=0" json:"write_latency_ms,omitempty"`
	ReadwriteLatencyMs *uint64 `protobuf:"varint,4,opt,name=readwrite_latency_ms,json=readwriteLatencyMs,def=0" json:"readwrite_latency_ms,omitempty"`
}

// Default values for VolumeIOLatencyInjection fields.
const (
	Default_VolumeIOLatencyInjection_ReadLatencyMs      = uint64(0)
	Default_VolumeIOLatencyInjection_WriteLatencyMs     = uint64(0)
	Default_VolumeIOLatencyInjection_ReadwriteLatencyMs = uint64(0)
)

func (x *VolumeIOLatencyInjection) Reset() {
	*x = VolumeIOLatencyInjection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeIOLatencyInjection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeIOLatencyInjection) ProtoMessage() {}

func (x *VolumeIOLatencyInjection) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeIOLatencyInjection.ProtoReflect.Descriptor instead.
func (*VolumeIOLatencyInjection) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{62}
}

func (x *VolumeIOLatencyInjection) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

func (x *VolumeIOLatencyInjection) GetReadLatencyMs() uint64 {
	if x != nil && x.ReadLatencyMs != nil {
		return *x.ReadLatencyMs
	}
	return Default_VolumeIOLatencyInjection_ReadLatencyMs
}

func (x *VolumeIOLatencyInjection) GetWriteLatencyMs() uint64 {
	if x != nil && x.WriteLatencyMs != nil {
		return *x.WriteLatencyMs
	}
	return Default_VolumeIOLatencyInjection_WriteLatencyMs
}

func (x *VolumeIOLatencyInjection) GetReadwriteLatencyMs() uint64 {
	if x != nil && x.ReadwriteLatencyMs != nil {
		return *x.ReadwriteLatencyMs
	}
	return Default_VolumeIOLatencyInjection_ReadwriteLatencyMs
}

type SetVolumeIOLatencyInjectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId           []byte  `protobuf:"bytes,1,req,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
	ReadLatencyMs      *uint64 `protobuf:"varint,2,opt,name=read_latency_ms,json=readLatencyMs,def=0" json:"read_latency_ms,omitempty"`
	WriteLatencyMs     *uint64 `protobuf:"varint,3,opt,name=write_latency_ms,json=writeLatencyMs,def=0" json:"write_latency_ms,omitempty"`
	ReadwriteLatencyMs *uint64 `protobuf:"varint,4,opt,name=readwrite_latency_ms,json=readwriteLatencyMs,def=0" json:"readwrite_latency_ms,omitempty"`
}

// Default values for SetVolumeIOLatencyInjectionRequest fields.
const (
	Default_SetVolumeIOLatencyInjectionRequest_ReadLatencyMs      = uint64(0)
	Default_SetVolumeIOLatencyInjectionRequest_WriteLatencyMs     = uint64(0)
	Default_SetVolumeIOLatencyInjectionRequest_ReadwriteLatencyMs = uint64(0)
)

func (x *SetVolumeIOLatencyInjectionRequest) Reset() {
	*x = SetVolumeIOLatencyInjectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetVolumeIOLatencyInjectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetVolumeIOLatencyInjectionRequest) ProtoMessage() {}

func (x *SetVolumeIOLatencyInjectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetVolumeIOLatencyInjectionRequest.ProtoReflect.Descriptor instead.
func (*SetVolumeIOLatencyInjectionRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{63}
}

func (x *SetVolumeIOLatencyInjectionRequest) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

func (x *SetVolumeIOLatencyInjectionRequest) GetReadLatencyMs() uint64 {
	if x != nil && x.ReadLatencyMs != nil {
		return *x.ReadLatencyMs
	}
	return Default_SetVolumeIOLatencyInjectionRequest_ReadLatencyMs
}

func (x *SetVolumeIOLatencyInjectionRequest) GetWriteLatencyMs() uint64 {
	if x != nil && x.WriteLatencyMs != nil {
		return *x.WriteLatencyMs
	}
	return Default_SetVolumeIOLatencyInjectionRequest_WriteLatencyMs
}

func (x *SetVolumeIOLatencyInjectionRequest) GetReadwriteLatencyMs() uint64 {
	if x != nil && x.ReadwriteLatencyMs != nil {
		return *x.ReadwriteLatencyMs
	}
	return Default_SetVolumeIOLatencyInjectionRequest_ReadwriteLatencyMs
}

type SetVolumeIOLatencyInjectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Injection *VolumeIOLatencyInjection `protobuf:"bytes,1,req,name=injection" json:"injection,omitempty"`
}

func (x *SetVolumeIOLatencyInjectionResponse) Reset() {
	*x = SetVolumeIOLatencyInjectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetVolumeIOLatencyInjectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetVolumeIOLatencyInjectionResponse) ProtoMessage() {}

func (x *SetVolumeIOLatencyInjectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetVolumeIOLatencyInjectionResponse.ProtoReflect.Descriptor instead.
func (*SetVolumeIOLatencyInjectionResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{64}
}

func (x *SetVolumeIOLatencyInjectionResponse) GetInjection() *VolumeIOLatencyInjection {
	if x != nil {
		return x.Injection
	}
	return nil
}

type ListVolumeIOLatencyInjectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Injections []*VolumeIOLatencyInjection `protobuf:"bytes,1,rep,name=injections" json:"injections,omitempty"`
}

func (x *ListVolumeIOLatencyInjectionResponse) Reset() {
	*x = ListVolumeIOLatencyInjectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVolumeIOLatencyInjectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVolumeIOLatencyInjectionResponse) ProtoMessage() {}

func (x *ListVolumeIOLatencyInjectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVolumeIOLatencyInjectionResponse.ProtoReflect.Descriptor instead.
func (*ListVolumeIOLatencyInjectionResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{65}
}

func (x *ListVolumeIOLatencyInjectionResponse) GetInjections() []*VolumeIOLatencyInjection {
	if x != nil {
		return x.Injections
	}
	return nil
}

type ClearVolumeIOLatencyInjectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VolumeId []byte `protobuf:"bytes,1,opt,name=volume_id,json=volumeId" json:"volume_id,omitempty"`
	ClearAll *bool  `protobuf:"varint,2,opt,name=clear_all,json=clearAll" json:"clear_all,omitempty"`
}

func (x *ClearVolumeIOLatencyInjectionRequest) Reset() {
	*x = ClearVolumeIOLatencyInjectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearVolumeIOLatencyInjectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearVolumeIOLatencyInjectionRequest) ProtoMessage() {}

func (x *ClearVolumeIOLatencyInjectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearVolumeIOLatencyInjectionRequest.ProtoReflect.Descriptor instead.
func (*ClearVolumeIOLatencyInjectionRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{66}
}

func (x *ClearVolumeIOLatencyInjectionRequest) GetVolumeId() []byte {
	if x != nil {
		return x.VolumeId
	}
	return nil
}

func (x *ClearVolumeIOLatencyInjectionRequest) GetClearAll() bool {
	if x != nil && x.ClearAll != nil {
		return *x.ClearAll
	}
	return false
}

type TemporaryExtent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid                   *uint32 `protobuf:"varint,1,req,name=pid" json:"pid,omitempty"`
	Epoch                 *uint64 `protobuf:"varint,2,req,name=epoch" json:"epoch,omitempty"`
	TemporaryPid          *uint32 `protobuf:"varint,3,req,name=temporary_pid,json=temporaryPid" json:"temporary_pid,omitempty"`
	TemporaryEpoch        *uint64 `protobuf:"varint,4,req,name=temporary_epoch,json=temporaryEpoch" json:"temporary_epoch,omitempty"`
	BasePextentGeneration *uint64 `protobuf:"varint,5,req,name=base_pextent_generation,json=basePextentGeneration" json:"base_pextent_generation,omitempty"`
	PextentGeneration     *uint64 `protobuf:"varint,6,req,name=pextent_generation,json=pextentGeneration" json:"pextent_generation,omitempty"`
	UnmapTimes            *uint32 `protobuf:"varint,7,opt,name=unmap_times,json=unmapTimes" json:"unmap_times,omitempty"`
}

func (x *TemporaryExtent) Reset() {
	*x = TemporaryExtent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemporaryExtent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporaryExtent) ProtoMessage() {}

func (x *TemporaryExtent) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporaryExtent.ProtoReflect.Descriptor instead.
func (*TemporaryExtent) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{67}
}

func (x *TemporaryExtent) GetPid() uint32 {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return 0
}

func (x *TemporaryExtent) GetEpoch() uint64 {
	if x != nil && x.Epoch != nil {
		return *x.Epoch
	}
	return 0
}

func (x *TemporaryExtent) GetTemporaryPid() uint32 {
	if x != nil && x.TemporaryPid != nil {
		return *x.TemporaryPid
	}
	return 0
}

func (x *TemporaryExtent) GetTemporaryEpoch() uint64 {
	if x != nil && x.TemporaryEpoch != nil {
		return *x.TemporaryEpoch
	}
	return 0
}

func (x *TemporaryExtent) GetBasePextentGeneration() uint64 {
	if x != nil && x.BasePextentGeneration != nil {
		return *x.BasePextentGeneration
	}
	return 0
}

func (x *TemporaryExtent) GetPextentGeneration() uint64 {
	if x != nil && x.PextentGeneration != nil {
		return *x.PextentGeneration
	}
	return 0
}

func (x *TemporaryExtent) GetUnmapTimes() uint32 {
	if x != nil && x.UnmapTimes != nil {
		return *x.UnmapTimes
	}
	return 0
}

type TemporaryUnmapBitmapSegment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Segment        *uint32 `protobuf:"varint,1,req,name=segment,def=0" json:"segment,omitempty"`
	TemporaryPid   *uint32 `protobuf:"varint,2,req,name=temporary_pid,json=temporaryPid,def=0" json:"temporary_pid,omitempty"`
	TemporaryEpoch *uint32 `protobuf:"varint,3,req,name=temporary_epoch,json=temporaryEpoch,def=0" json:"temporary_epoch,omitempty"`
	UnmapBitmap    *uint64 `protobuf:"varint,4,req,name=unmap_bitmap,json=unmapBitmap,def=0" json:"unmap_bitmap,omitempty"`
}

// Default values for TemporaryUnmapBitmapSegment fields.
const (
	Default_TemporaryUnmapBitmapSegment_Segment        = uint32(0)
	Default_TemporaryUnmapBitmapSegment_TemporaryPid   = uint32(0)
	Default_TemporaryUnmapBitmapSegment_TemporaryEpoch = uint32(0)
	Default_TemporaryUnmapBitmapSegment_UnmapBitmap    = uint64(0)
)

func (x *TemporaryUnmapBitmapSegment) Reset() {
	*x = TemporaryUnmapBitmapSegment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemporaryUnmapBitmapSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporaryUnmapBitmapSegment) ProtoMessage() {}

func (x *TemporaryUnmapBitmapSegment) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporaryUnmapBitmapSegment.ProtoReflect.Descriptor instead.
func (*TemporaryUnmapBitmapSegment) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{68}
}

func (x *TemporaryUnmapBitmapSegment) GetSegment() uint32 {
	if x != nil && x.Segment != nil {
		return *x.Segment
	}
	return Default_TemporaryUnmapBitmapSegment_Segment
}

func (x *TemporaryUnmapBitmapSegment) GetTemporaryPid() uint32 {
	if x != nil && x.TemporaryPid != nil {
		return *x.TemporaryPid
	}
	return Default_TemporaryUnmapBitmapSegment_TemporaryPid
}

func (x *TemporaryUnmapBitmapSegment) GetTemporaryEpoch() uint32 {
	if x != nil && x.TemporaryEpoch != nil {
		return *x.TemporaryEpoch
	}
	return Default_TemporaryUnmapBitmapSegment_TemporaryEpoch
}

func (x *TemporaryUnmapBitmapSegment) GetUnmapBitmap() uint64 {
	if x != nil && x.UnmapBitmap != nil {
		return *x.UnmapBitmap
	}
	return Default_TemporaryUnmapBitmapSegment_UnmapBitmap
}

type BytesBitmap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SectorNo *uint32 `protobuf:"varint,1,req,name=sector_no,json=sectorNo" json:"sector_no,omitempty"`
	Bitmap   []byte  `protobuf:"bytes,2,req,name=bitmap" json:"bitmap,omitempty"`
}

func (x *BytesBitmap) Reset() {
	*x = BytesBitmap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BytesBitmap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BytesBitmap) ProtoMessage() {}

func (x *BytesBitmap) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BytesBitmap.ProtoReflect.Descriptor instead.
func (*BytesBitmap) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{69}
}

func (x *BytesBitmap) GetSectorNo() uint32 {
	if x != nil && x.SectorNo != nil {
		return *x.SectorNo
	}
	return 0
}

func (x *BytesBitmap) GetBitmap() []byte {
	if x != nil {
		return x.Bitmap
	}
	return nil
}

type TemporaryExtentSegmentBitmapV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemporaryPid   *uint32        `protobuf:"varint,1,req,name=temporary_pid,json=temporaryPid" json:"temporary_pid,omitempty"`
	TemporaryEpoch *uint64        `protobuf:"varint,2,req,name=temporary_epoch,json=temporaryEpoch" json:"temporary_epoch,omitempty"`
	BlockBitmap    []byte         `protobuf:"bytes,3,req,name=block_bitmap,json=blockBitmap" json:"block_bitmap,omitempty"`
	SectorBitmaps  []byte         `protobuf:"bytes,4,opt,name=sector_bitmaps,json=sectorBitmaps" json:"sector_bitmaps,omitempty"`
	BytesBitmaps   []*BytesBitmap `protobuf:"bytes,5,rep,name=bytes_bitmaps,json=bytesBitmaps" json:"bytes_bitmaps,omitempty"`
	Segment        *uint32        `protobuf:"varint,6,req,name=segment" json:"segment,omitempty"`
}

func (x *TemporaryExtentSegmentBitmapV2) Reset() {
	*x = TemporaryExtentSegmentBitmapV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemporaryExtentSegmentBitmapV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporaryExtentSegmentBitmapV2) ProtoMessage() {}

func (x *TemporaryExtentSegmentBitmapV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporaryExtentSegmentBitmapV2.ProtoReflect.Descriptor instead.
func (*TemporaryExtentSegmentBitmapV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{70}
}

func (x *TemporaryExtentSegmentBitmapV2) GetTemporaryPid() uint32 {
	if x != nil && x.TemporaryPid != nil {
		return *x.TemporaryPid
	}
	return 0
}

func (x *TemporaryExtentSegmentBitmapV2) GetTemporaryEpoch() uint64 {
	if x != nil && x.TemporaryEpoch != nil {
		return *x.TemporaryEpoch
	}
	return 0
}

func (x *TemporaryExtentSegmentBitmapV2) GetBlockBitmap() []byte {
	if x != nil {
		return x.BlockBitmap
	}
	return nil
}

func (x *TemporaryExtentSegmentBitmapV2) GetSectorBitmaps() []byte {
	if x != nil {
		return x.SectorBitmaps
	}
	return nil
}

func (x *TemporaryExtentSegmentBitmapV2) GetBytesBitmaps() []*BytesBitmap {
	if x != nil {
		return x.BytesBitmaps
	}
	return nil
}

func (x *TemporaryExtentSegmentBitmapV2) GetSegment() uint32 {
	if x != nil && x.Segment != nil {
		return *x.Segment
	}
	return 0
}

type SectorBitmap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SectorNo *uint32 `protobuf:"varint,1,req,name=sector_no,json=sectorNo" json:"sector_no,omitempty"`
	Bitmap   []byte  `protobuf:"bytes,2,req,name=bitmap" json:"bitmap,omitempty"`
}

func (x *SectorBitmap) Reset() {
	*x = SectorBitmap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectorBitmap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectorBitmap) ProtoMessage() {}

func (x *SectorBitmap) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectorBitmap.ProtoReflect.Descriptor instead.
func (*SectorBitmap) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{71}
}

func (x *SectorBitmap) GetSectorNo() uint32 {
	if x != nil && x.SectorNo != nil {
		return *x.SectorNo
	}
	return 0
}

func (x *SectorBitmap) GetBitmap() []byte {
	if x != nil {
		return x.Bitmap
	}
	return nil
}

type TemporaryExtentBitmapV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemporaryPid   *uint32         `protobuf:"varint,1,req,name=temporary_pid,json=temporaryPid" json:"temporary_pid,omitempty"`
	TemporaryEpoch *uint64         `protobuf:"varint,2,req,name=temporary_epoch,json=temporaryEpoch" json:"temporary_epoch,omitempty"`
	BlockBitmap    []byte          `protobuf:"bytes,3,req,name=block_bitmap,json=blockBitmap" json:"block_bitmap,omitempty"`
	SectorBitmaps  []*SectorBitmap `protobuf:"bytes,4,rep,name=sector_bitmaps,json=sectorBitmaps" json:"sector_bitmaps,omitempty"`
}

func (x *TemporaryExtentBitmapV1) Reset() {
	*x = TemporaryExtentBitmapV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemporaryExtentBitmapV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporaryExtentBitmapV1) ProtoMessage() {}

func (x *TemporaryExtentBitmapV1) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporaryExtentBitmapV1.ProtoReflect.Descriptor instead.
func (*TemporaryExtentBitmapV1) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{72}
}

func (x *TemporaryExtentBitmapV1) GetTemporaryPid() uint32 {
	if x != nil && x.TemporaryPid != nil {
		return *x.TemporaryPid
	}
	return 0
}

func (x *TemporaryExtentBitmapV1) GetTemporaryEpoch() uint64 {
	if x != nil && x.TemporaryEpoch != nil {
		return *x.TemporaryEpoch
	}
	return 0
}

func (x *TemporaryExtentBitmapV1) GetBlockBitmap() []byte {
	if x != nil {
		return x.BlockBitmap
	}
	return nil
}

func (x *TemporaryExtentBitmapV1) GetSectorBitmaps() []*SectorBitmap {
	if x != nil {
		return x.SectorBitmaps
	}
	return nil
}

type TemporaryExtentBitmapV3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemporaryPid   *uint32 `protobuf:"varint,1,req,name=temporary_pid,json=temporaryPid" json:"temporary_pid,omitempty"`
	TemporaryEpoch *uint64 `protobuf:"varint,2,req,name=temporary_epoch,json=temporaryEpoch" json:"temporary_epoch,omitempty"`
	BlockBitmap    []byte  `protobuf:"bytes,3,req,name=block_bitmap,json=blockBitmap" json:"block_bitmap,omitempty"`
	UnmapBitmap    []byte  `protobuf:"bytes,4,opt,name=unmap_bitmap,json=unmapBitmap" json:"unmap_bitmap,omitempty"`
	InvalidBitmap  *bool   `protobuf:"varint,5,opt,name=invalid_bitmap,json=invalidBitmap" json:"invalid_bitmap,omitempty"`
}

func (x *TemporaryExtentBitmapV3) Reset() {
	*x = TemporaryExtentBitmapV3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemporaryExtentBitmapV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporaryExtentBitmapV3) ProtoMessage() {}

func (x *TemporaryExtentBitmapV3) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporaryExtentBitmapV3.ProtoReflect.Descriptor instead.
func (*TemporaryExtentBitmapV3) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{73}
}

func (x *TemporaryExtentBitmapV3) GetTemporaryPid() uint32 {
	if x != nil && x.TemporaryPid != nil {
		return *x.TemporaryPid
	}
	return 0
}

func (x *TemporaryExtentBitmapV3) GetTemporaryEpoch() uint64 {
	if x != nil && x.TemporaryEpoch != nil {
		return *x.TemporaryEpoch
	}
	return 0
}

func (x *TemporaryExtentBitmapV3) GetBlockBitmap() []byte {
	if x != nil {
		return x.BlockBitmap
	}
	return nil
}

func (x *TemporaryExtentBitmapV3) GetUnmapBitmap() []byte {
	if x != nil {
		return x.UnmapBitmap
	}
	return nil
}

func (x *TemporaryExtentBitmapV3) GetInvalidBitmap() bool {
	if x != nil && x.InvalidBitmap != nil {
		return *x.InvalidBitmap
	}
	return false
}

type GetTemporaryExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemporaryPid   *uint32 `protobuf:"varint,1,req,name=temporary_pid,json=temporaryPid" json:"temporary_pid,omitempty"`
	TemporaryEpoch *uint64 `protobuf:"varint,2,req,name=temporary_epoch,json=temporaryEpoch" json:"temporary_epoch,omitempty"`
}

func (x *GetTemporaryExtentRequest) Reset() {
	*x = GetTemporaryExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemporaryExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemporaryExtentRequest) ProtoMessage() {}

func (x *GetTemporaryExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemporaryExtentRequest.ProtoReflect.Descriptor instead.
func (*GetTemporaryExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{74}
}

func (x *GetTemporaryExtentRequest) GetTemporaryPid() uint32 {
	if x != nil && x.TemporaryPid != nil {
		return *x.TemporaryPid
	}
	return 0
}

func (x *GetTemporaryExtentRequest) GetTemporaryEpoch() uint64 {
	if x != nil && x.TemporaryEpoch != nil {
		return *x.TemporaryEpoch
	}
	return 0
}

type GetTemporaryExtentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemporaryExtent       *TemporaryExtent         `protobuf:"bytes,1,req,name=temporary_extent,json=temporaryExtent" json:"temporary_extent,omitempty"`
	TemporaryExtentBitmap *TemporaryExtentBitmapV3 `protobuf:"bytes,2,opt,name=temporary_extent_bitmap,json=temporaryExtentBitmap" json:"temporary_extent_bitmap,omitempty"`
	TemporaryGeneration   *uint64                  `protobuf:"varint,3,opt,name=temporary_generation,json=temporaryGeneration" json:"temporary_generation,omitempty"`
	InstanceId            *uint32                  `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for GetTemporaryExtentResponse fields.
const (
	Default_GetTemporaryExtentResponse_InstanceId = uint32(1)
)

func (x *GetTemporaryExtentResponse) Reset() {
	*x = GetTemporaryExtentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemporaryExtentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemporaryExtentResponse) ProtoMessage() {}

func (x *GetTemporaryExtentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemporaryExtentResponse.ProtoReflect.Descriptor instead.
func (*GetTemporaryExtentResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{75}
}

func (x *GetTemporaryExtentResponse) GetTemporaryExtent() *TemporaryExtent {
	if x != nil {
		return x.TemporaryExtent
	}
	return nil
}

func (x *GetTemporaryExtentResponse) GetTemporaryExtentBitmap() *TemporaryExtentBitmapV3 {
	if x != nil {
		return x.TemporaryExtentBitmap
	}
	return nil
}

func (x *GetTemporaryExtentResponse) GetTemporaryGeneration() uint64 {
	if x != nil && x.TemporaryGeneration != nil {
		return *x.TemporaryGeneration
	}
	return 0
}

func (x *GetTemporaryExtentResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_GetTemporaryExtentResponse_InstanceId
}

type ListTemporaryExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTemporaryPid *uint32 `protobuf:"varint,1,req,name=start_temporary_pid,json=startTemporaryPid,def=0" json:"start_temporary_pid,omitempty"`
	MaxNum            *uint32 `protobuf:"varint,2,opt,name=max_num,json=maxNum,def=2048" json:"max_num,omitempty"`
	InstanceId        *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=1" json:"instance_id,omitempty"`
}

// Default values for ListTemporaryExtentRequest fields.
const (
	Default_ListTemporaryExtentRequest_StartTemporaryPid = uint32(0)
	Default_ListTemporaryExtentRequest_MaxNum            = uint32(2048)
	Default_ListTemporaryExtentRequest_InstanceId        = uint32(1)
)

func (x *ListTemporaryExtentRequest) Reset() {
	*x = ListTemporaryExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemporaryExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemporaryExtentRequest) ProtoMessage() {}

func (x *ListTemporaryExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemporaryExtentRequest.ProtoReflect.Descriptor instead.
func (*ListTemporaryExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{76}
}

func (x *ListTemporaryExtentRequest) GetStartTemporaryPid() uint32 {
	if x != nil && x.StartTemporaryPid != nil {
		return *x.StartTemporaryPid
	}
	return Default_ListTemporaryExtentRequest_StartTemporaryPid
}

func (x *ListTemporaryExtentRequest) GetMaxNum() uint32 {
	if x != nil && x.MaxNum != nil {
		return *x.MaxNum
	}
	return Default_ListTemporaryExtentRequest_MaxNum
}

func (x *ListTemporaryExtentRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ListTemporaryExtentRequest_InstanceId
}

type ListTemporaryExtentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemporaryExtents []*TemporaryExtent `protobuf:"bytes,1,rep,name=temporary_extents,json=temporaryExtents" json:"temporary_extents,omitempty"`
}

func (x *ListTemporaryExtentResponse) Reset() {
	*x = ListTemporaryExtentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemporaryExtentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemporaryExtentResponse) ProtoMessage() {}

func (x *ListTemporaryExtentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemporaryExtentResponse.ProtoReflect.Descriptor instead.
func (*ListTemporaryExtentResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{77}
}

func (x *ListTemporaryExtentResponse) GetTemporaryExtents() []*TemporaryExtent {
	if x != nil {
		return x.TemporaryExtents
	}
	return nil
}

type GetTemporaryExtentSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemporaryExtentNum *uint32 `protobuf:"varint,1,req,name=temporary_extent_num,json=temporaryExtentNum" json:"temporary_extent_num,omitempty"`
	InstanceId         *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for GetTemporaryExtentSummaryResponse fields.
const (
	Default_GetTemporaryExtentSummaryResponse_InstanceId = uint32(0)
)

func (x *GetTemporaryExtentSummaryResponse) Reset() {
	*x = GetTemporaryExtentSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemporaryExtentSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemporaryExtentSummaryResponse) ProtoMessage() {}

func (x *GetTemporaryExtentSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemporaryExtentSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetTemporaryExtentSummaryResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{78}
}

func (x *GetTemporaryExtentSummaryResponse) GetTemporaryExtentNum() uint32 {
	if x != nil && x.TemporaryExtentNum != nil {
		return *x.TemporaryExtentNum
	}
	return 0
}

func (x *GetTemporaryExtentSummaryResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_GetTemporaryExtentSummaryResponse_InstanceId
}

type GetTemporaryExtentSummaryResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*GetTemporaryExtentSummaryResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *GetTemporaryExtentSummaryResponseV2) Reset() {
	*x = GetTemporaryExtentSummaryResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemporaryExtentSummaryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemporaryExtentSummaryResponseV2) ProtoMessage() {}

func (x *GetTemporaryExtentSummaryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemporaryExtentSummaryResponseV2.ProtoReflect.Descriptor instead.
func (*GetTemporaryExtentSummaryResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{79}
}

func (x *GetTemporaryExtentSummaryResponseV2) GetInstancesResponse() []*GetTemporaryExtentSummaryResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type ComparePExtentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid1 *uint32 `protobuf:"varint,1,req,name=pid1" json:"pid1,omitempty"`
	Pid2 *uint32 `protobuf:"varint,2,req,name=pid2" json:"pid2,omitempty"`
	// caller_version of ComparePExtent regulates the requirement of caller and the cluster behavior.
	// caller_version = 1 means caller don't know tiering and compare proxy;
	// caller_version = 2 means caller knows compare proxy but not tiering (54x >= 544 or 55x >= 5.5.2);
	// caller_version = 3 means caller knows tiering (560 or after);
	CallerVersion *uint32 `protobuf:"varint,3,opt,name=caller_version,json=callerVersion,def=1" json:"caller_version,omitempty"`
}

// Default values for ComparePExtentRequest fields.
const (
	Default_ComparePExtentRequest_CallerVersion = uint32(1)
)

func (x *ComparePExtentRequest) Reset() {
	*x = ComparePExtentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComparePExtentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComparePExtentRequest) ProtoMessage() {}

func (x *ComparePExtentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComparePExtentRequest.ProtoReflect.Descriptor instead.
func (*ComparePExtentRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{80}
}

func (x *ComparePExtentRequest) GetPid1() uint32 {
	if x != nil && x.Pid1 != nil {
		return *x.Pid1
	}
	return 0
}

func (x *ComparePExtentRequest) GetPid2() uint32 {
	if x != nil && x.Pid2 != nil {
		return *x.Pid2
	}
	return 0
}

func (x *ComparePExtentRequest) GetCallerVersion() uint32 {
	if x != nil && x.CallerVersion != nil {
		return *x.CallerVersion
	}
	return Default_ComparePExtentRequest_CallerVersion
}

type SinkTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lid          *uint32       `protobuf:"varint,1,req,name=lid" json:"lid,omitempty"`
	Lepoch       *uint64       `protobuf:"varint,2,opt,name=lepoch" json:"lepoch,omitempty"`
	PerfPid      *uint32       `protobuf:"varint,3,opt,name=perf_pid,json=perfPid" json:"perf_pid,omitempty"`
	PerfEpoch    *uint64       `protobuf:"varint,4,opt,name=perf_epoch,json=perfEpoch" json:"perf_epoch,omitempty"`
	PerfLocation *uint32       `protobuf:"varint,5,opt,name=perf_location,json=perfLocation" json:"perf_location,omitempty"`
	BlockNo      *uint32       `protobuf:"varint,6,opt,name=block_no,json=blockNo" json:"block_no,omitempty"`
	Type         *SinkTaskType `protobuf:"varint,7,opt,name=type,enum=zbs.chunk.SinkTaskType" json:"type,omitempty"`
}

func (x *SinkTask) Reset() {
	*x = SinkTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SinkTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SinkTask) ProtoMessage() {}

func (x *SinkTask) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SinkTask.ProtoReflect.Descriptor instead.
func (*SinkTask) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{81}
}

func (x *SinkTask) GetLid() uint32 {
	if x != nil && x.Lid != nil {
		return *x.Lid
	}
	return 0
}

func (x *SinkTask) GetLepoch() uint64 {
	if x != nil && x.Lepoch != nil {
		return *x.Lepoch
	}
	return 0
}

func (x *SinkTask) GetPerfPid() uint32 {
	if x != nil && x.PerfPid != nil {
		return *x.PerfPid
	}
	return 0
}

func (x *SinkTask) GetPerfEpoch() uint64 {
	if x != nil && x.PerfEpoch != nil {
		return *x.PerfEpoch
	}
	return 0
}

func (x *SinkTask) GetPerfLocation() uint32 {
	if x != nil && x.PerfLocation != nil {
		return *x.PerfLocation
	}
	return 0
}

func (x *SinkTask) GetBlockNo() uint32 {
	if x != nil && x.BlockNo != nil {
		return *x.BlockNo
	}
	return 0
}

func (x *SinkTask) GetType() SinkTaskType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return SinkTaskType_UNKNOWN_TYPE
}

type SetSinkParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SinkTaskNum       *uint64 `protobuf:"varint,1,opt,name=sink_task_num,json=sinkTaskNum" json:"sink_task_num,omitempty"`
	SinkIoConcurrency *uint64 `protobuf:"varint,2,opt,name=sink_io_concurrency,json=sinkIoConcurrency" json:"sink_io_concurrency,omitempty"`
	InstanceId        *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for SetSinkParamsRequest fields.
const (
	Default_SetSinkParamsRequest_InstanceId = uint32(0)
)

func (x *SetSinkParamsRequest) Reset() {
	*x = SetSinkParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetSinkParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSinkParamsRequest) ProtoMessage() {}

func (x *SetSinkParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSinkParamsRequest.ProtoReflect.Descriptor instead.
func (*SetSinkParamsRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{82}
}

func (x *SetSinkParamsRequest) GetSinkTaskNum() uint64 {
	if x != nil && x.SinkTaskNum != nil {
		return *x.SinkTaskNum
	}
	return 0
}

func (x *SetSinkParamsRequest) GetSinkIoConcurrency() uint64 {
	if x != nil && x.SinkIoConcurrency != nil {
		return *x.SinkIoConcurrency
	}
	return 0
}

func (x *SetSinkParamsRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_SetSinkParamsRequest_InstanceId
}

type ListSinkInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurSinkCmd               *zbs.SinkCmd                       `protobuf:"bytes,1,opt,name=cur_sink_cmd,json=curSinkCmd" json:"cur_sink_cmd,omitempty"`
	SinkTasks                []*SinkTask                        `protobuf:"bytes,2,rep,name=sink_tasks,json=sinkTasks" json:"sink_tasks,omitempty"`
	SinkableLruInfo          *ListSinkInfoResponse_BlockLRUInfo `protobuf:"bytes,3,opt,name=sinkable_lru_info,json=sinkableLruInfo" json:"sinkable_lru_info,omitempty"`
	PotentialSinkableLruInfo *ListSinkInfoResponse_BlockLRUInfo `protobuf:"bytes,4,opt,name=potential_sinkable_lru_info,json=potentialSinkableLruInfo" json:"potential_sinkable_lru_info,omitempty"`
	PendingInactiveExtents   *uint64                            `protobuf:"varint,5,opt,name=pending_inactive_extents,json=pendingInactiveExtents" json:"pending_inactive_extents,omitempty"`
	AccelerateCids           []uint32                           `protobuf:"varint,6,rep,name=accelerate_cids,json=accelerateCids" json:"accelerate_cids,omitempty"`
	PendingAccelerateBlocks  *uint32                            `protobuf:"varint,7,opt,name=pending_accelerate_blocks,json=pendingAccelerateBlocks" json:"pending_accelerate_blocks,omitempty"`
	ReserveBlockNum          *uint64                            `protobuf:"varint,8,opt,name=reserve_block_num,json=reserveBlockNum" json:"reserve_block_num,omitempty"`
	MaxSinkTaskNum           *uint64                            `protobuf:"varint,9,opt,name=max_sink_task_num,json=maxSinkTaskNum" json:"max_sink_task_num,omitempty"`
	MaxSinkIoConcurrency     *uint64                            `protobuf:"varint,10,opt,name=max_sink_io_concurrency,json=maxSinkIoConcurrency" json:"max_sink_io_concurrency,omitempty"`
	PendingChildExtents      *uint64                            `protobuf:"varint,11,opt,name=pending_child_extents,json=pendingChildExtents" json:"pending_child_extents,omitempty"`
	PendingDrainCmds         *uint64                            `protobuf:"varint,12,opt,name=pending_drain_cmds,json=pendingDrainCmds" json:"pending_drain_cmds,omitempty"`
	PendingInactiveBlocks    *uint64                            `protobuf:"varint,13,opt,name=pending_inactive_blocks,json=pendingInactiveBlocks" json:"pending_inactive_blocks,omitempty"`
	InstanceId               *uint32                            `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for ListSinkInfoResponse fields.
const (
	Default_ListSinkInfoResponse_InstanceId = uint32(0)
)

func (x *ListSinkInfoResponse) Reset() {
	*x = ListSinkInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSinkInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSinkInfoResponse) ProtoMessage() {}

func (x *ListSinkInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSinkInfoResponse.ProtoReflect.Descriptor instead.
func (*ListSinkInfoResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{83}
}

func (x *ListSinkInfoResponse) GetCurSinkCmd() *zbs.SinkCmd {
	if x != nil {
		return x.CurSinkCmd
	}
	return nil
}

func (x *ListSinkInfoResponse) GetSinkTasks() []*SinkTask {
	if x != nil {
		return x.SinkTasks
	}
	return nil
}

func (x *ListSinkInfoResponse) GetSinkableLruInfo() *ListSinkInfoResponse_BlockLRUInfo {
	if x != nil {
		return x.SinkableLruInfo
	}
	return nil
}

func (x *ListSinkInfoResponse) GetPotentialSinkableLruInfo() *ListSinkInfoResponse_BlockLRUInfo {
	if x != nil {
		return x.PotentialSinkableLruInfo
	}
	return nil
}

func (x *ListSinkInfoResponse) GetPendingInactiveExtents() uint64 {
	if x != nil && x.PendingInactiveExtents != nil {
		return *x.PendingInactiveExtents
	}
	return 0
}

func (x *ListSinkInfoResponse) GetAccelerateCids() []uint32 {
	if x != nil {
		return x.AccelerateCids
	}
	return nil
}

func (x *ListSinkInfoResponse) GetPendingAccelerateBlocks() uint32 {
	if x != nil && x.PendingAccelerateBlocks != nil {
		return *x.PendingAccelerateBlocks
	}
	return 0
}

func (x *ListSinkInfoResponse) GetReserveBlockNum() uint64 {
	if x != nil && x.ReserveBlockNum != nil {
		return *x.ReserveBlockNum
	}
	return 0
}

func (x *ListSinkInfoResponse) GetMaxSinkTaskNum() uint64 {
	if x != nil && x.MaxSinkTaskNum != nil {
		return *x.MaxSinkTaskNum
	}
	return 0
}

func (x *ListSinkInfoResponse) GetMaxSinkIoConcurrency() uint64 {
	if x != nil && x.MaxSinkIoConcurrency != nil {
		return *x.MaxSinkIoConcurrency
	}
	return 0
}

func (x *ListSinkInfoResponse) GetPendingChildExtents() uint64 {
	if x != nil && x.PendingChildExtents != nil {
		return *x.PendingChildExtents
	}
	return 0
}

func (x *ListSinkInfoResponse) GetPendingDrainCmds() uint64 {
	if x != nil && x.PendingDrainCmds != nil {
		return *x.PendingDrainCmds
	}
	return 0
}

func (x *ListSinkInfoResponse) GetPendingInactiveBlocks() uint64 {
	if x != nil && x.PendingInactiveBlocks != nil {
		return *x.PendingInactiveBlocks
	}
	return 0
}

func (x *ListSinkInfoResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_ListSinkInfoResponse_InstanceId
}

type ListSinkInfoResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*ListSinkInfoResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *ListSinkInfoResponseV2) Reset() {
	*x = ListSinkInfoResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSinkInfoResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSinkInfoResponseV2) ProtoMessage() {}

func (x *ListSinkInfoResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSinkInfoResponseV2.ProtoReflect.Descriptor instead.
func (*ListSinkInfoResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{84}
}

func (x *ListSinkInfoResponseV2) GetInstancesResponse() []*ListSinkInfoResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type SetCapIOThrottleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxCapIoDepthLimitPerSataHdd *uint32 `protobuf:"varint,1,opt,name=max_cap_io_depth_limit_per_sata_hdd,json=maxCapIoDepthLimitPerSataHdd" json:"max_cap_io_depth_limit_per_sata_hdd,omitempty"`
	Limit_256KIo                 *bool   `protobuf:"varint,2,opt,name=limit_256k_io,json=limit256kIo" json:"limit_256k_io,omitempty"`
	ReservedCapIoDepth           *string `protobuf:"bytes,3,opt,name=reserved_cap_io_depth,json=reservedCapIoDepth" json:"reserved_cap_io_depth,omitempty"`
	CapIoDepthShare              *string `protobuf:"bytes,4,opt,name=cap_io_depth_share,json=capIoDepthShare" json:"cap_io_depth_share,omitempty"`
	LimitAppIo                   *bool   `protobuf:"varint,5,opt,name=limit_app_io,json=limitAppIo" json:"limit_app_io,omitempty"`
	InstanceId                   *uint32 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for SetCapIOThrottleRequest fields.
const (
	Default_SetCapIOThrottleRequest_InstanceId = uint32(0)
)

func (x *SetCapIOThrottleRequest) Reset() {
	*x = SetCapIOThrottleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCapIOThrottleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCapIOThrottleRequest) ProtoMessage() {}

func (x *SetCapIOThrottleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCapIOThrottleRequest.ProtoReflect.Descriptor instead.
func (*SetCapIOThrottleRequest) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{85}
}

func (x *SetCapIOThrottleRequest) GetMaxCapIoDepthLimitPerSataHdd() uint32 {
	if x != nil && x.MaxCapIoDepthLimitPerSataHdd != nil {
		return *x.MaxCapIoDepthLimitPerSataHdd
	}
	return 0
}

func (x *SetCapIOThrottleRequest) GetLimit_256KIo() bool {
	if x != nil && x.Limit_256KIo != nil {
		return *x.Limit_256KIo
	}
	return false
}

func (x *SetCapIOThrottleRequest) GetReservedCapIoDepth() string {
	if x != nil && x.ReservedCapIoDepth != nil {
		return *x.ReservedCapIoDepth
	}
	return ""
}

func (x *SetCapIOThrottleRequest) GetCapIoDepthShare() string {
	if x != nil && x.CapIoDepthShare != nil {
		return *x.CapIoDepthShare
	}
	return ""
}

func (x *SetCapIOThrottleRequest) GetLimitAppIo() bool {
	if x != nil && x.LimitAppIo != nil {
		return *x.LimitAppIo
	}
	return false
}

func (x *SetCapIOThrottleRequest) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_SetCapIOThrottleRequest_InstanceId
}

type CapIOThrottleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IoType          *string `protobuf:"bytes,1,opt,name=io_type,json=ioType" json:"io_type,omitempty"`
	CurIoDepth      *uint32 `protobuf:"varint,2,opt,name=cur_io_depth,json=curIoDepth" json:"cur_io_depth,omitempty"`
	IoDepthLimit    *uint32 `protobuf:"varint,3,opt,name=io_depth_limit,json=ioDepthLimit" json:"io_depth_limit,omitempty"`
	ReservedIoDepth *uint32 `protobuf:"varint,4,opt,name=reserved_io_depth,json=reservedIoDepth" json:"reserved_io_depth,omitempty"`
	IoDepthShare    *uint32 `protobuf:"varint,5,opt,name=io_depth_share,json=ioDepthShare" json:"io_depth_share,omitempty"`
	PendingRwIoNum  *uint32 `protobuf:"varint,6,opt,name=pending_rw_io_num,json=pendingRwIoNum" json:"pending_rw_io_num,omitempty"`
	Iops            *uint64 `protobuf:"varint,7,opt,name=iops" json:"iops,omitempty"`
	AvgLatencyNs    *uint64 `protobuf:"varint,8,opt,name=avg_latency_ns,json=avgLatencyNs" json:"avg_latency_ns,omitempty"`
}

func (x *CapIOThrottleInfo) Reset() {
	*x = CapIOThrottleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CapIOThrottleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapIOThrottleInfo) ProtoMessage() {}

func (x *CapIOThrottleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapIOThrottleInfo.ProtoReflect.Descriptor instead.
func (*CapIOThrottleInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{86}
}

func (x *CapIOThrottleInfo) GetIoType() string {
	if x != nil && x.IoType != nil {
		return *x.IoType
	}
	return ""
}

func (x *CapIOThrottleInfo) GetCurIoDepth() uint32 {
	if x != nil && x.CurIoDepth != nil {
		return *x.CurIoDepth
	}
	return 0
}

func (x *CapIOThrottleInfo) GetIoDepthLimit() uint32 {
	if x != nil && x.IoDepthLimit != nil {
		return *x.IoDepthLimit
	}
	return 0
}

func (x *CapIOThrottleInfo) GetReservedIoDepth() uint32 {
	if x != nil && x.ReservedIoDepth != nil {
		return *x.ReservedIoDepth
	}
	return 0
}

func (x *CapIOThrottleInfo) GetIoDepthShare() uint32 {
	if x != nil && x.IoDepthShare != nil {
		return *x.IoDepthShare
	}
	return 0
}

func (x *CapIOThrottleInfo) GetPendingRwIoNum() uint32 {
	if x != nil && x.PendingRwIoNum != nil {
		return *x.PendingRwIoNum
	}
	return 0
}

func (x *CapIOThrottleInfo) GetIops() uint64 {
	if x != nil && x.Iops != nil {
		return *x.Iops
	}
	return 0
}

func (x *CapIOThrottleInfo) GetAvgLatencyNs() uint64 {
	if x != nil && x.AvgLatencyNs != nil {
		return *x.AvgLatencyNs
	}
	return 0
}

type GetCapIOThrottleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limit_256KIo      *bool                `protobuf:"varint,1,opt,name=limit_256k_io,json=limit256kIo" json:"limit_256k_io,omitempty"`
	TotalIoDepthLimit *uint32              `protobuf:"varint,2,opt,name=total_io_depth_limit,json=totalIoDepthLimit" json:"total_io_depth_limit,omitempty"`
	PendingRwIoNum    *uint32              `protobuf:"varint,3,opt,name=pending_rw_io_num,json=pendingRwIoNum" json:"pending_rw_io_num,omitempty"`
	PendingIoNum      *uint32              `protobuf:"varint,4,opt,name=pending_io_num,json=pendingIoNum" json:"pending_io_num,omitempty"`
	LimitAppIo        *bool                `protobuf:"varint,5,opt,name=limit_app_io,json=limitAppIo" json:"limit_app_io,omitempty"`
	Infos             []*CapIOThrottleInfo `protobuf:"bytes,10,rep,name=infos" json:"infos,omitempty"`
	InstanceId        *uint32              `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for GetCapIOThrottleResponse fields.
const (
	Default_GetCapIOThrottleResponse_InstanceId = uint32(0)
)

func (x *GetCapIOThrottleResponse) Reset() {
	*x = GetCapIOThrottleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCapIOThrottleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCapIOThrottleResponse) ProtoMessage() {}

func (x *GetCapIOThrottleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCapIOThrottleResponse.ProtoReflect.Descriptor instead.
func (*GetCapIOThrottleResponse) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{87}
}

func (x *GetCapIOThrottleResponse) GetLimit_256KIo() bool {
	if x != nil && x.Limit_256KIo != nil {
		return *x.Limit_256KIo
	}
	return false
}

func (x *GetCapIOThrottleResponse) GetTotalIoDepthLimit() uint32 {
	if x != nil && x.TotalIoDepthLimit != nil {
		return *x.TotalIoDepthLimit
	}
	return 0
}

func (x *GetCapIOThrottleResponse) GetPendingRwIoNum() uint32 {
	if x != nil && x.PendingRwIoNum != nil {
		return *x.PendingRwIoNum
	}
	return 0
}

func (x *GetCapIOThrottleResponse) GetPendingIoNum() uint32 {
	if x != nil && x.PendingIoNum != nil {
		return *x.PendingIoNum
	}
	return 0
}

func (x *GetCapIOThrottleResponse) GetLimitAppIo() bool {
	if x != nil && x.LimitAppIo != nil {
		return *x.LimitAppIo
	}
	return false
}

func (x *GetCapIOThrottleResponse) GetInfos() []*CapIOThrottleInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

func (x *GetCapIOThrottleResponse) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_GetCapIOThrottleResponse_InstanceId
}

type GetCapIOThrottleResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*GetCapIOThrottleResponse `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *GetCapIOThrottleResponseV2) Reset() {
	*x = GetCapIOThrottleResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCapIOThrottleResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCapIOThrottleResponseV2) ProtoMessage() {}

func (x *GetCapIOThrottleResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCapIOThrottleResponseV2.ProtoReflect.Descriptor instead.
func (*GetCapIOThrottleResponseV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{88}
}

func (x *GetCapIOThrottleResponseV2) GetInstancesResponse() []*GetCapIOThrottleResponse {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type IOPerfStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Iops        *uint64 `protobuf:"varint,1,opt,name=iops" json:"iops,omitempty"`
	Bps         *uint64 `protobuf:"varint,2,opt,name=bps" json:"bps,omitempty"`
	BpsLimit    *uint64 `protobuf:"varint,3,opt,name=bps_limit,json=bpsLimit" json:"bps_limit,omitempty"`
	MaxBpsLimit *uint64 `protobuf:"varint,4,opt,name=max_bps_limit,json=maxBpsLimit" json:"max_bps_limit,omitempty"`
}

func (x *IOPerfStats) Reset() {
	*x = IOPerfStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IOPerfStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IOPerfStats) ProtoMessage() {}

func (x *IOPerfStats) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IOPerfStats.ProtoReflect.Descriptor instead.
func (*IOPerfStats) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{89}
}

func (x *IOPerfStats) GetIops() uint64 {
	if x != nil && x.Iops != nil {
		return *x.Iops
	}
	return 0
}

func (x *IOPerfStats) GetBps() uint64 {
	if x != nil && x.Bps != nil {
		return *x.Bps
	}
	return 0
}

func (x *IOPerfStats) GetBpsLimit() uint64 {
	if x != nil && x.BpsLimit != nil {
		return *x.BpsLimit
	}
	return 0
}

func (x *IOPerfStats) GetMaxBpsLimit() uint64 {
	if x != nil && x.MaxBpsLimit != nil {
		return *x.MaxBpsLimit
	}
	return 0
}

type IOPerfReadWriteDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromLocalRead   *IOPerfStats `protobuf:"bytes,1,opt,name=from_local_read,json=fromLocalRead" json:"from_local_read,omitempty"`
	FromRemoteRead  *IOPerfStats `protobuf:"bytes,2,opt,name=from_remote_read,json=fromRemoteRead" json:"from_remote_read,omitempty"`
	FromLocalWrite  *IOPerfStats `protobuf:"bytes,3,opt,name=from_local_write,json=fromLocalWrite" json:"from_local_write,omitempty"`
	FromRemoteWrite *IOPerfStats `protobuf:"bytes,4,opt,name=from_remote_write,json=fromRemoteWrite" json:"from_remote_write,omitempty"`
}

func (x *IOPerfReadWriteDetails) Reset() {
	*x = IOPerfReadWriteDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IOPerfReadWriteDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IOPerfReadWriteDetails) ProtoMessage() {}

func (x *IOPerfReadWriteDetails) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IOPerfReadWriteDetails.ProtoReflect.Descriptor instead.
func (*IOPerfReadWriteDetails) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{90}
}

func (x *IOPerfReadWriteDetails) GetFromLocalRead() *IOPerfStats {
	if x != nil {
		return x.FromLocalRead
	}
	return nil
}

func (x *IOPerfReadWriteDetails) GetFromRemoteRead() *IOPerfStats {
	if x != nil {
		return x.FromRemoteRead
	}
	return nil
}

func (x *IOPerfReadWriteDetails) GetFromLocalWrite() *IOPerfStats {
	if x != nil {
		return x.FromLocalWrite
	}
	return nil
}

func (x *IOPerfReadWriteDetails) GetFromRemoteWrite() *IOPerfStats {
	if x != nil {
		return x.FromRemoteWrite
	}
	return nil
}

type ThrottleBucketStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentLevel *uint64 `protobuf:"varint,1,opt,name=current_level,json=currentLevel" json:"current_level,omitempty"`
	CurrentSize  *uint64 `protobuf:"varint,2,opt,name=current_size,json=currentSize" json:"current_size,omitempty"`
	ReleaseSpeed *uint64 `protobuf:"varint,3,opt,name=release_speed,json=releaseSpeed" json:"release_speed,omitempty"`
	WaitingIoNum *uint64 `protobuf:"varint,4,opt,name=waiting_io_num,json=waitingIoNum" json:"waiting_io_num,omitempty"`
}

func (x *ThrottleBucketStats) Reset() {
	*x = ThrottleBucketStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThrottleBucketStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThrottleBucketStats) ProtoMessage() {}

func (x *ThrottleBucketStats) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThrottleBucketStats.ProtoReflect.Descriptor instead.
func (*ThrottleBucketStats) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{91}
}

func (x *ThrottleBucketStats) GetCurrentLevel() uint64 {
	if x != nil && x.CurrentLevel != nil {
		return *x.CurrentLevel
	}
	return 0
}

func (x *ThrottleBucketStats) GetCurrentSize() uint64 {
	if x != nil && x.CurrentSize != nil {
		return *x.CurrentSize
	}
	return 0
}

func (x *ThrottleBucketStats) GetReleaseSpeed() uint64 {
	if x != nil && x.ReleaseSpeed != nil {
		return *x.ReleaseSpeed
	}
	return 0
}

func (x *ThrottleBucketStats) GetWaitingIoNum() uint64 {
	if x != nil && x.WaitingIoNum != nil {
		return *x.WaitingIoNum
	}
	return 0
}

type InternalIOThrottleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InternalIoLimitPerSataHdd        *uint64      `protobuf:"varint,1,opt,name=internal_io_limit_per_sata_hdd,json=internalIoLimitPerSataHdd" json:"internal_io_limit_per_sata_hdd,omitempty"`
	AppIoBusyBpsPerSataHdd           *uint64      `protobuf:"varint,2,opt,name=app_io_busy_bps_per_sata_hdd,json=appIoBusyBpsPerSataHdd" json:"app_io_busy_bps_per_sata_hdd,omitempty"`
	AppIoBusyIopsPerSataHdd          *uint64      `protobuf:"varint,3,opt,name=app_io_busy_iops_per_sata_hdd,json=appIoBusyIopsPerSataHdd" json:"app_io_busy_iops_per_sata_hdd,omitempty"`
	InternalFlowMgrReleaseLevelRatio *float64     `protobuf:"fixed64,4,opt,name=internal_flow_mgr_release_level_ratio,json=internalFlowMgrReleaseLevelRatio" json:"internal_flow_mgr_release_level_ratio,omitempty"`
	AppPerfPerf                      *IOPerfStats `protobuf:"bytes,5,opt,name=app_perf_perf,json=appPerfPerf" json:"app_perf_perf,omitempty"`
	AppCapPerf                       *IOPerfStats `protobuf:"bytes,6,opt,name=app_cap_perf,json=appCapPerf" json:"app_cap_perf,omitempty"`
	InternalPerfPerf                 *IOPerfStats `protobuf:"bytes,7,opt,name=internal_perf_perf,json=internalPerfPerf" json:"internal_perf_perf,omitempty"`
	InternalCapPerf                  *IOPerfStats `protobuf:"bytes,8,opt,name=internal_cap_perf,json=internalCapPerf" json:"internal_cap_perf,omitempty"`
	// internal io throttle bucket details
	InternalPerfBucketStats *ThrottleBucketStats `protobuf:"bytes,9,opt,name=internal_perf_bucket_stats,json=internalPerfBucketStats" json:"internal_perf_bucket_stats,omitempty"`
	InternalCapBucketStats  *ThrottleBucketStats `protobuf:"bytes,10,opt,name=internal_cap_bucket_stats,json=internalCapBucketStats" json:"internal_cap_bucket_stats,omitempty"`
	// internal io stats details
	RepositionPerfReplicaPerfDetails *IOPerfReadWriteDetails `protobuf:"bytes,11,opt,name=reposition_perf_replica_perf_details,json=repositionPerfReplicaPerfDetails" json:"reposition_perf_replica_perf_details,omitempty"`
	RepositionCapReplicaPerfDetails  *IOPerfReadWriteDetails `protobuf:"bytes,12,opt,name=reposition_cap_replica_perf_details,json=repositionCapReplicaPerfDetails" json:"reposition_cap_replica_perf_details,omitempty"`
	RepositionCapEcPerfDetails       *IOPerfReadWriteDetails `protobuf:"bytes,13,opt,name=reposition_cap_ec_perf_details,json=repositionCapEcPerfDetails" json:"reposition_cap_ec_perf_details,omitempty"`
	SinkPerfReplicaPerfDetails       *IOPerfReadWriteDetails `protobuf:"bytes,14,opt,name=sink_perf_replica_perf_details,json=sinkPerfReplicaPerfDetails" json:"sink_perf_replica_perf_details,omitempty"`
	SinkCapReplicaPerfDetails        *IOPerfReadWriteDetails `protobuf:"bytes,15,opt,name=sink_cap_replica_perf_details,json=sinkCapReplicaPerfDetails" json:"sink_cap_replica_perf_details,omitempty"`
	SinkCapEcPerfDetails             *IOPerfReadWriteDetails `protobuf:"bytes,16,opt,name=sink_cap_ec_perf_details,json=sinkCapEcPerfDetails" json:"sink_cap_ec_perf_details,omitempty"`
	MaxRepositionWaitingTimeRatio    *float64                `protobuf:"fixed64,17,opt,name=max_reposition_waiting_time_ratio,json=maxRepositionWaitingTimeRatio" json:"max_reposition_waiting_time_ratio,omitempty"`
	InstanceId                       *uint32                 `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for InternalIOThrottleInfo fields.
const (
	Default_InternalIOThrottleInfo_InstanceId = uint32(0)
)

func (x *InternalIOThrottleInfo) Reset() {
	*x = InternalIOThrottleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalIOThrottleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalIOThrottleInfo) ProtoMessage() {}

func (x *InternalIOThrottleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalIOThrottleInfo.ProtoReflect.Descriptor instead.
func (*InternalIOThrottleInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{92}
}

func (x *InternalIOThrottleInfo) GetInternalIoLimitPerSataHdd() uint64 {
	if x != nil && x.InternalIoLimitPerSataHdd != nil {
		return *x.InternalIoLimitPerSataHdd
	}
	return 0
}

func (x *InternalIOThrottleInfo) GetAppIoBusyBpsPerSataHdd() uint64 {
	if x != nil && x.AppIoBusyBpsPerSataHdd != nil {
		return *x.AppIoBusyBpsPerSataHdd
	}
	return 0
}

func (x *InternalIOThrottleInfo) GetAppIoBusyIopsPerSataHdd() uint64 {
	if x != nil && x.AppIoBusyIopsPerSataHdd != nil {
		return *x.AppIoBusyIopsPerSataHdd
	}
	return 0
}

func (x *InternalIOThrottleInfo) GetInternalFlowMgrReleaseLevelRatio() float64 {
	if x != nil && x.InternalFlowMgrReleaseLevelRatio != nil {
		return *x.InternalFlowMgrReleaseLevelRatio
	}
	return 0
}

func (x *InternalIOThrottleInfo) GetAppPerfPerf() *IOPerfStats {
	if x != nil {
		return x.AppPerfPerf
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetAppCapPerf() *IOPerfStats {
	if x != nil {
		return x.AppCapPerf
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetInternalPerfPerf() *IOPerfStats {
	if x != nil {
		return x.InternalPerfPerf
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetInternalCapPerf() *IOPerfStats {
	if x != nil {
		return x.InternalCapPerf
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetInternalPerfBucketStats() *ThrottleBucketStats {
	if x != nil {
		return x.InternalPerfBucketStats
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetInternalCapBucketStats() *ThrottleBucketStats {
	if x != nil {
		return x.InternalCapBucketStats
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetRepositionPerfReplicaPerfDetails() *IOPerfReadWriteDetails {
	if x != nil {
		return x.RepositionPerfReplicaPerfDetails
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetRepositionCapReplicaPerfDetails() *IOPerfReadWriteDetails {
	if x != nil {
		return x.RepositionCapReplicaPerfDetails
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetRepositionCapEcPerfDetails() *IOPerfReadWriteDetails {
	if x != nil {
		return x.RepositionCapEcPerfDetails
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetSinkPerfReplicaPerfDetails() *IOPerfReadWriteDetails {
	if x != nil {
		return x.SinkPerfReplicaPerfDetails
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetSinkCapReplicaPerfDetails() *IOPerfReadWriteDetails {
	if x != nil {
		return x.SinkCapReplicaPerfDetails
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetSinkCapEcPerfDetails() *IOPerfReadWriteDetails {
	if x != nil {
		return x.SinkCapEcPerfDetails
	}
	return nil
}

func (x *InternalIOThrottleInfo) GetMaxRepositionWaitingTimeRatio() float64 {
	if x != nil && x.MaxRepositionWaitingTimeRatio != nil {
		return *x.MaxRepositionWaitingTimeRatio
	}
	return 0
}

func (x *InternalIOThrottleInfo) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_InternalIOThrottleInfo_InstanceId
}

type InternalIOThrottleInfoV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*InternalIOThrottleInfo `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *InternalIOThrottleInfoV2) Reset() {
	*x = InternalIOThrottleInfoV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalIOThrottleInfoV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalIOThrottleInfoV2) ProtoMessage() {}

func (x *InternalIOThrottleInfoV2) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalIOThrottleInfoV2.ProtoReflect.Descriptor instead.
func (*InternalIOThrottleInfoV2) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{93}
}

func (x *InternalIOThrottleInfoV2) GetInstancesResponse() []*InternalIOThrottleInfo {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type RDMAUDRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lid *uint32 `protobuf:"varint,1,opt,name=lid" json:"lid,omitempty"`
	Qpn *uint32 `protobuf:"varint,2,opt,name=qpn" json:"qpn,omitempty"`
	Gid *string `protobuf:"bytes,3,opt,name=gid" json:"gid,omitempty"`
}

func (x *RDMAUDRoute) Reset() {
	*x = RDMAUDRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RDMAUDRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RDMAUDRoute) ProtoMessage() {}

func (x *RDMAUDRoute) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RDMAUDRoute.ProtoReflect.Descriptor instead.
func (*RDMAUDRoute) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{94}
}

func (x *RDMAUDRoute) GetLid() uint32 {
	if x != nil && x.Lid != nil {
		return *x.Lid
	}
	return 0
}

func (x *RDMAUDRoute) GetQpn() uint32 {
	if x != nil && x.Qpn != nil {
		return *x.Qpn
	}
	return 0
}

func (x *RDMAUDRoute) GetGid() string {
	if x != nil && x.Gid != nil {
		return *x.Gid
	}
	return ""
}

type RDMAUDRouteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Route []*RDMAUDRoute `protobuf:"bytes,1,rep,name=route" json:"route,omitempty"`
}

func (x *RDMAUDRouteInfo) Reset() {
	*x = RDMAUDRouteInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RDMAUDRouteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RDMAUDRouteInfo) ProtoMessage() {}

func (x *RDMAUDRouteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RDMAUDRouteInfo.ProtoReflect.Descriptor instead.
func (*RDMAUDRouteInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{95}
}

func (x *RDMAUDRouteInfo) GetRoute() []*RDMAUDRoute {
	if x != nil {
		return x.Route
	}
	return nil
}

type MultipathInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalSendIf  *string `protobuf:"bytes,1,opt,name=local_send_if,json=localSendIf" json:"local_send_if,omitempty"`
	LocalRecvIf  *string `protobuf:"bytes,2,opt,name=local_recv_if,json=localRecvIf" json:"local_recv_if,omitempty"`
	RemoteSendIf *string `protobuf:"bytes,3,opt,name=remote_send_if,json=remoteSendIf" json:"remote_send_if,omitempty"`
	RemoteRecvIf *string `protobuf:"bytes,4,opt,name=remote_recv_if,json=remoteRecvIf" json:"remote_recv_if,omitempty"`
	PidNum       *uint64 `protobuf:"varint,5,opt,name=pid_num,json=pidNum" json:"pid_num,omitempty"`
	FlowLabel    *uint32 `protobuf:"varint,6,opt,name=flow_label,json=flowLabel" json:"flow_label,omitempty"`
}

func (x *MultipathInfo) Reset() {
	*x = MultipathInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultipathInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultipathInfo) ProtoMessage() {}

func (x *MultipathInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultipathInfo.ProtoReflect.Descriptor instead.
func (*MultipathInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{96}
}

func (x *MultipathInfo) GetLocalSendIf() string {
	if x != nil && x.LocalSendIf != nil {
		return *x.LocalSendIf
	}
	return ""
}

func (x *MultipathInfo) GetLocalRecvIf() string {
	if x != nil && x.LocalRecvIf != nil {
		return *x.LocalRecvIf
	}
	return ""
}

func (x *MultipathInfo) GetRemoteSendIf() string {
	if x != nil && x.RemoteSendIf != nil {
		return *x.RemoteSendIf
	}
	return ""
}

func (x *MultipathInfo) GetRemoteRecvIf() string {
	if x != nil && x.RemoteRecvIf != nil {
		return *x.RemoteRecvIf
	}
	return ""
}

func (x *MultipathInfo) GetPidNum() uint64 {
	if x != nil && x.PidNum != nil {
		return *x.PidNum
	}
	return 0
}

func (x *MultipathInfo) GetFlowLabel() uint32 {
	if x != nil && x.FlowLabel != nil {
		return *x.FlowLabel
	}
	return 0
}

type TransportInfo struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	LocalAddress  *string        `protobuf:"bytes,1,opt,name=local_address,json=localAddress" json:"local_address,omitempty"`
	RemoteAddress *string        `protobuf:"bytes,2,opt,name=remote_address,json=remoteAddress" json:"remote_address,omitempty"`
	TransportType *TransportType `protobuf:"varint,3,opt,name=transport_type,json=transportType,enum=zbs.chunk.TransportType" json:"transport_type,omitempty"`
	UpTime        *uint64        `protobuf:"varint,4,opt,name=up_time,json=upTime" json:"up_time,omitempty"` // in seconds
	MpathInfo     *MultipathInfo `protobuf:"bytes,5,opt,name=mpath_info,json=mpathInfo" json:"mpath_info,omitempty"`
}

func (x *TransportInfo) Reset() {
	*x = TransportInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransportInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportInfo) ProtoMessage() {}

func (x *TransportInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportInfo.ProtoReflect.Descriptor instead.
func (*TransportInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{97}
}

func (x *TransportInfo) GetLocalAddress() string {
	if x != nil && x.LocalAddress != nil {
		return *x.LocalAddress
	}
	return ""
}

func (x *TransportInfo) GetRemoteAddress() string {
	if x != nil && x.RemoteAddress != nil {
		return *x.RemoteAddress
	}
	return ""
}

func (x *TransportInfo) GetTransportType() TransportType {
	if x != nil && x.TransportType != nil {
		return *x.TransportType
	}
	return TransportType_TRANSPORT_TYPE_UNIX
}

func (x *TransportInfo) GetUpTime() uint64 {
	if x != nil && x.UpTime != nil {
		return *x.UpTime
	}
	return 0
}

func (x *TransportInfo) GetMpathInfo() *MultipathInfo {
	if x != nil {
		return x.MpathInfo
	}
	return nil
}

type RDMATransportInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumHandlingSendWr        *uint64                   `protobuf:"varint,1,opt,name=num_handling_send_wr,json=numHandlingSendWr" json:"num_handling_send_wr,omitempty"`
	NumHandlingRecvWr        *uint64                   `protobuf:"varint,2,opt,name=num_handling_recv_wr,json=numHandlingRecvWr" json:"num_handling_recv_wr,omitempty"`
	NumSendOpsInLastInterval *uint64                   `protobuf:"varint,3,opt,name=num_send_ops_in_last_interval,json=numSendOpsInLastInterval" json:"num_send_ops_in_last_interval,omitempty"`
	NumRecvOpsInLastInterval *uint64                   `protobuf:"varint,4,opt,name=num_recv_ops_in_last_interval,json=numRecvOpsInLastInterval" json:"num_recv_ops_in_last_interval,omitempty"`
	SendRate                 *RDMATransportInfo_IORate `protobuf:"bytes,5,opt,name=send_rate,json=sendRate" json:"send_rate,omitempty"`
	ReadRate                 *RDMATransportInfo_IORate `protobuf:"bytes,6,opt,name=read_rate,json=readRate" json:"read_rate,omitempty"`
	WriteRate                *RDMATransportInfo_IORate `protobuf:"bytes,7,opt,name=write_rate,json=writeRate" json:"write_rate,omitempty"`
}

func (x *RDMATransportInfo) Reset() {
	*x = RDMATransportInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RDMATransportInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RDMATransportInfo) ProtoMessage() {}

func (x *RDMATransportInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RDMATransportInfo.ProtoReflect.Descriptor instead.
func (*RDMATransportInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{98}
}

func (x *RDMATransportInfo) GetNumHandlingSendWr() uint64 {
	if x != nil && x.NumHandlingSendWr != nil {
		return *x.NumHandlingSendWr
	}
	return 0
}

func (x *RDMATransportInfo) GetNumHandlingRecvWr() uint64 {
	if x != nil && x.NumHandlingRecvWr != nil {
		return *x.NumHandlingRecvWr
	}
	return 0
}

func (x *RDMATransportInfo) GetNumSendOpsInLastInterval() uint64 {
	if x != nil && x.NumSendOpsInLastInterval != nil {
		return *x.NumSendOpsInLastInterval
	}
	return 0
}

func (x *RDMATransportInfo) GetNumRecvOpsInLastInterval() uint64 {
	if x != nil && x.NumRecvOpsInLastInterval != nil {
		return *x.NumRecvOpsInLastInterval
	}
	return 0
}

func (x *RDMATransportInfo) GetSendRate() *RDMATransportInfo_IORate {
	if x != nil {
		return x.SendRate
	}
	return nil
}

func (x *RDMATransportInfo) GetReadRate() *RDMATransportInfo_IORate {
	if x != nil {
		return x.ReadRate
	}
	return nil
}

func (x *RDMATransportInfo) GetWriteRate() *RDMATransportInfo_IORate {
	if x != nil {
		return x.WriteRate
	}
	return nil
}

type DCServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transports []*TransportInfo    `protobuf:"bytes,1,rep,name=transports" json:"transports,omitempty"`
	Version    *DataChannelVersion `protobuf:"varint,2,opt,name=version,enum=zbs.chunk.DataChannelVersion" json:"version,omitempty"`
	InstanceId *uint32             `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for DCServerInfo fields.
const (
	Default_DCServerInfo_InstanceId = uint32(0)
)

func (x *DCServerInfo) Reset() {
	*x = DCServerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCServerInfo) ProtoMessage() {}

func (x *DCServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCServerInfo.ProtoReflect.Descriptor instead.
func (*DCServerInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{99}
}

func (x *DCServerInfo) GetTransports() []*TransportInfo {
	if x != nil {
		return x.Transports
	}
	return nil
}

func (x *DCServerInfo) GetVersion() DataChannelVersion {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return DataChannelVersion_DATA_CHANNEL_V1
}

func (x *DCServerInfo) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_DCServerInfo_InstanceId
}

type DCServerInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*DCServerInfo `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *DCServerInfos) Reset() {
	*x = DCServerInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCServerInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCServerInfos) ProtoMessage() {}

func (x *DCServerInfos) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCServerInfos.ProtoReflect.Descriptor instead.
func (*DCServerInfos) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{100}
}

func (x *DCServerInfos) GetInstancesResponse() []*DCServerInfo {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type DCClientInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if data channel client use multipath, one client may have multiple transports
	Transports               []*TransportInfo `protobuf:"bytes,1,rep,name=transports" json:"transports,omitempty"`
	AggrServerResponseTimes  *uint32          `protobuf:"varint,2,opt,name=aggr_server_response_times,json=aggrServerResponseTimes" json:"aggr_server_response_times,omitempty"`
	AggrAccumulateErrorTimes *uint32          `protobuf:"varint,3,opt,name=aggr_accumulate_error_times,json=aggrAccumulateErrorTimes" json:"aggr_accumulate_error_times,omitempty"`
	AggrLastSecondErrorTimes *uint32          `protobuf:"varint,4,opt,name=aggr_last_second_error_times,json=aggrLastSecondErrorTimes" json:"aggr_last_second_error_times,omitempty"`
}

func (x *DCClientInfo) Reset() {
	*x = DCClientInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCClientInfo) ProtoMessage() {}

func (x *DCClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCClientInfo.ProtoReflect.Descriptor instead.
func (*DCClientInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{101}
}

func (x *DCClientInfo) GetTransports() []*TransportInfo {
	if x != nil {
		return x.Transports
	}
	return nil
}

func (x *DCClientInfo) GetAggrServerResponseTimes() uint32 {
	if x != nil && x.AggrServerResponseTimes != nil {
		return *x.AggrServerResponseTimes
	}
	return 0
}

func (x *DCClientInfo) GetAggrAccumulateErrorTimes() uint32 {
	if x != nil && x.AggrAccumulateErrorTimes != nil {
		return *x.AggrAccumulateErrorTimes
	}
	return 0
}

func (x *DCClientInfo) GetAggrLastSecondErrorTimes() uint32 {
	if x != nil && x.AggrLastSecondErrorTimes != nil {
		return *x.AggrLastSecondErrorTimes
	}
	return 0
}

type DCManagerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DcClients  []*DCClientInfo     `protobuf:"bytes,1,rep,name=dc_clients,json=dcClients" json:"dc_clients,omitempty"`
	Version    *DataChannelVersion `protobuf:"varint,2,opt,name=version,enum=zbs.chunk.DataChannelVersion" json:"version,omitempty"`
	InstanceId *uint32             `protobuf:"varint,99,opt,name=instance_id,json=instanceId,def=0" json:"instance_id,omitempty"`
}

// Default values for DCManagerInfo fields.
const (
	Default_DCManagerInfo_InstanceId = uint32(0)
)

func (x *DCManagerInfo) Reset() {
	*x = DCManagerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCManagerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCManagerInfo) ProtoMessage() {}

func (x *DCManagerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCManagerInfo.ProtoReflect.Descriptor instead.
func (*DCManagerInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{102}
}

func (x *DCManagerInfo) GetDcClients() []*DCClientInfo {
	if x != nil {
		return x.DcClients
	}
	return nil
}

func (x *DCManagerInfo) GetVersion() DataChannelVersion {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return DataChannelVersion_DATA_CHANNEL_V1
}

func (x *DCManagerInfo) GetInstanceId() uint32 {
	if x != nil && x.InstanceId != nil {
		return *x.InstanceId
	}
	return Default_DCManagerInfo_InstanceId
}

type DCManagerInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstancesResponse []*DCManagerInfo `protobuf:"bytes,1,rep,name=instances_response,json=instancesResponse" json:"instances_response,omitempty"`
}

func (x *DCManagerInfos) Reset() {
	*x = DCManagerInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCManagerInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCManagerInfos) ProtoMessage() {}

func (x *DCManagerInfos) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCManagerInfos.ProtoReflect.Descriptor instead.
func (*DCManagerInfos) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{103}
}

func (x *DCManagerInfos) GetInstancesResponse() []*DCManagerInfo {
	if x != nil {
		return x.InstancesResponse
	}
	return nil
}

type DataChannelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DcsInfo *DCServerInfo  `protobuf:"bytes,1,opt,name=dcs_info,json=dcsInfo" json:"dcs_info,omitempty"`
	DcmInfo *DCManagerInfo `protobuf:"bytes,2,opt,name=dcm_info,json=dcmInfo" json:"dcm_info,omitempty"`
}

func (x *DataChannelInfo) Reset() {
	*x = DataChannelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataChannelInfo) ProtoMessage() {}

func (x *DataChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataChannelInfo.ProtoReflect.Descriptor instead.
func (*DataChannelInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{104}
}

func (x *DataChannelInfo) GetDcsInfo() *DCServerInfo {
	if x != nil {
		return x.DcsInfo
	}
	return nil
}

func (x *DataChannelInfo) GetDcmInfo() *DCManagerInfo {
	if x != nil {
		return x.DcmInfo
	}
	return nil
}

type ListSinkInfoResponse_BlockLRUInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActiveBlocks    *uint64 `protobuf:"varint,1,opt,name=active_blocks,json=activeBlocks" json:"active_blocks,omitempty"`
	InactiveBlocks  *uint64 `protobuf:"varint,2,opt,name=inactive_blocks,json=inactiveBlocks" json:"inactive_blocks,omitempty"`
	CleanBlocks     *uint64 `protobuf:"varint,3,opt,name=clean_blocks,json=cleanBlocks" json:"clean_blocks,omitempty"`
	ReserveBlockNum *uint64 `protobuf:"varint,4,opt,name=reserve_block_num,json=reserveBlockNum" json:"reserve_block_num,omitempty"`
}

func (x *ListSinkInfoResponse_BlockLRUInfo) Reset() {
	*x = ListSinkInfoResponse_BlockLRUInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSinkInfoResponse_BlockLRUInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSinkInfoResponse_BlockLRUInfo) ProtoMessage() {}

func (x *ListSinkInfoResponse_BlockLRUInfo) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSinkInfoResponse_BlockLRUInfo.ProtoReflect.Descriptor instead.
func (*ListSinkInfoResponse_BlockLRUInfo) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{83, 0}
}

func (x *ListSinkInfoResponse_BlockLRUInfo) GetActiveBlocks() uint64 {
	if x != nil && x.ActiveBlocks != nil {
		return *x.ActiveBlocks
	}
	return 0
}

func (x *ListSinkInfoResponse_BlockLRUInfo) GetInactiveBlocks() uint64 {
	if x != nil && x.InactiveBlocks != nil {
		return *x.InactiveBlocks
	}
	return 0
}

func (x *ListSinkInfoResponse_BlockLRUInfo) GetCleanBlocks() uint64 {
	if x != nil && x.CleanBlocks != nil {
		return *x.CleanBlocks
	}
	return 0
}

func (x *ListSinkInfoResponse_BlockLRUInfo) GetReserveBlockNum() uint64 {
	if x != nil && x.ReserveBlockNum != nil {
		return *x.ReserveBlockNum
	}
	return 0
}

type RDMATransportInfo_IORate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalIoBytes *uint64 `protobuf:"varint,1,opt,name=total_io_bytes,json=totalIoBytes" json:"total_io_bytes,omitempty"`
	IoRatePacket *uint64 `protobuf:"varint,2,opt,name=io_rate_packet,json=ioRatePacket" json:"io_rate_packet,omitempty"`
	IoRateBytes  *uint64 `protobuf:"varint,3,opt,name=io_rate_bytes,json=ioRateBytes" json:"io_rate_bytes,omitempty"`
}

func (x *RDMATransportInfo_IORate) Reset() {
	*x = RDMATransportInfo_IORate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_chunk_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RDMATransportInfo_IORate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RDMATransportInfo_IORate) ProtoMessage() {}

func (x *RDMATransportInfo_IORate) ProtoReflect() protoreflect.Message {
	mi := &file_chunk_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RDMATransportInfo_IORate.ProtoReflect.Descriptor instead.
func (*RDMATransportInfo_IORate) Descriptor() ([]byte, []int) {
	return file_chunk_proto_rawDescGZIP(), []int{98, 0}
}

func (x *RDMATransportInfo_IORate) GetTotalIoBytes() uint64 {
	if x != nil && x.TotalIoBytes != nil {
		return *x.TotalIoBytes
	}
	return 0
}

func (x *RDMATransportInfo_IORate) GetIoRatePacket() uint64 {
	if x != nil && x.IoRatePacket != nil {
		return *x.IoRatePacket
	}
	return 0
}

func (x *RDMATransportInfo_IORate) GetIoRateBytes() uint64 {
	if x != nil && x.IoRateBytes != nil {
		return *x.IoRateBytes
	}
	return 0
}

var file_chunk_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*TransportInfo)(nil),
		ExtensionType: (*RDMATransportInfo)(nil),
		Field:         10001,
		Name:          "zbs.chunk.RDMATransportInfo.rdma_info",
		Tag:           "bytes,10001,opt,name=rdma_info",
		Filename:      "chunk.proto",
	},
}

// Extension fields to TransportInfo.
var (
	// optional zbs.chunk.RDMATransportInfo rdma_info = 10001;
	E_RDMATransportInfo_RdmaInfo = &file_chunk_proto_extTypes[0]
)

var File_chunk_proto protoreflect.FileDescriptor

var file_chunk_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x1a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x09, 0x63, 0x64, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x03,
	0x0a, 0x0d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x75, 0x73, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x6e,
	0x75, 0x6d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x5f, 0x69, 0x6f, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x49, 0x6f, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x66,
	0x6c, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x65, 0x72, 0x72, 0x66,
	0x6c, 0x61, 0x67, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x6c, 0x6f, 0x77,
	0x5f, 0x69, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x53, 0x6c,
	0x6f, 0x77, 0x49, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x74, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x74, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x61, 0x72, 0x6e, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x61, 0x72, 0x6e, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73,
	0x75, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xee, 0x03, 0x0a, 0x09, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6e,
	0x75, 0x6d, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x6e, 0x75, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x75,
	0x6d, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x6e, 0x75, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x75, 0x6d,
	0x5f, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x6e, 0x75, 0x6d, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x6e, 0x75, 0x6d, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x6e, 0x75, 0x6d, 0x46, 0x72, 0x65, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x5f, 0x69,
	0x6f, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b,
	0x6e, 0x75, 0x6d, 0x49, 0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x2e, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x65,
	0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x65,
	0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x73,
	0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6e, 0x75,
	0x6d, 0x53, 0x6c, 0x6f, 0x77, 0x49, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x74, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x74,
	0x55, 0x75, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x55, 0x73, 0x65, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x77, 0x61, 0x72, 0x6e, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x77, 0x61, 0x72, 0x6e, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xfb, 0x02, 0x0a, 0x10, 0x4a, 0x6f,
	0x75, 0x72, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x74, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1e, 0x0a, 0x0b,
	0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x6f, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x53, 0x6c, 0x6f, 0x77, 0x49, 0x6f, 0x12, 0x35, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x5f, 0x69, 0x6f, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x49,
	0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x61, 0x72, 0x6e, 0x66,
	0x6c, 0x61, 0x67, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x61, 0x72, 0x6e,
	0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x11, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x0d, 0x3a, 0x01, 0x30, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x18, 0x5f, 0x5f, 0x64, 0x65,
	0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73,
	0x65, 0x52, 0x14, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x41, 0x75, 0x74,
	0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0xda, 0x01, 0x0a, 0x0b, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x29, 0x0a, 0x10, 0x72,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x45,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x61, 0x78,
	0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x65, 0x71, 0x5f, 0x6e,
	0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73, 0x65, 0x71, 0x4e, 0x6f, 0x12, 0x30,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x5f, 0x69, 0x6f, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x49, 0x6f, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x22, 0x69, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x41, 0x64, 0x64, 0x72,
	0x12, 0x1a, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x0d, 0x3a, 0x01, 0x30, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x61, 0x49, 0x70, 0x12, 0x1e, 0x0a, 0x09,
	0x6d, 0x65, 0x74, 0x61, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x3a,
	0x01, 0x30, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0b,
	0x6d, 0x79, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x02, 0x28,
	0x0d, 0x3a, 0x01, 0x30, 0x52, 0x09, 0x6d, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x22,
	0xb3, 0x01, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x72, 0x69, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x08, 0x52, 0x08, 0x77, 0x72, 0x69, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x10, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x04, 0x20, 0x02, 0x28, 0x08, 0x52, 0x10, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x51, 0x75, 0x65, 0x75, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x05, 0x20, 0x02, 0x28, 0x08, 0x52, 0x06, 0x63,
	0x6c, 0x6f, 0x73, 0x65, 0x64, 0x22, 0x5b, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0xb9, 0x03, 0x0a, 0x0a, 0x5a, 0x62, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x70, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x70, 0x63, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x70, 0x63, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x70, 0x63, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x55, 0x6e, 0x69, 0x78, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x21, 0x0a, 0x0c, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x49, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x72,
	0x74, 0x62, 0x65, 0x61, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x65, 0x74,
	0x61, 0x5f, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x61,
	0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x70, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x70, 0x12, 0x32, 0x0a, 0x16, 0x73, 0x63, 0x76, 0x6d, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x70,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x63, 0x76, 0x6d, 0x4d, 0x6f, 0x64, 0x65,
	0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x70, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a,
	0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x54,
	0x0a, 0x0c, 0x5a, 0x62, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x32, 0x12, 0x44,
	0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x5a, 0x62, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x5a, 0x6b, 0x48, 0x6f, 0x73,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x7a, 0x6b,
	0x5f, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x7a, 0x6b,
	0x48, 0x6f, 0x73, 0x74, 0x73, 0x22, 0x28, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22,
	0xfd, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x64, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x75, 0x73, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0x35, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61,
	0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x6e, 0x65, 0x77, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x05, 0x6e, 0x65, 0x77, 0x49, 0x70, 0x22, 0x38, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x63, 0x76, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x65, 0x77,
	0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x65, 0x77, 0x49, 0x70,
	0x22, 0xa6, 0x01, 0x0a, 0x16, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x1b, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05,
	0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x14,
	0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73,
	0x65, 0x52, 0x12, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x0a, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6c, 0x0a, 0x15, 0x4d, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x05, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x0a, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x12, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x12, 0x1b, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x22,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20,
	0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x22, 0x4b, 0x0a, 0x11, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d,
	0x3a, 0x01, 0x31, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22,
	0x6b, 0x0a, 0x14, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x05, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73,
	0x65, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31,
	0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x13,
	0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52,
	0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x0a, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x77, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x04, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xa6, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70,
	0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2f, 0x0a, 0x07, 0x77, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a,
	0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6a,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x4f, 0x0a, 0x12, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x13, 0x53,
	0x65, 0x74, 0x55, 0x6e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x44, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x3a, 0x11, 0x44, 0x49, 0x53, 0x4b,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x52, 0x06, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x45, 0x72, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x3a,
	0x13, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x52, 0x07, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x22, 0x9b, 0x02,
	0x0a, 0x09, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x61, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x68,
	0x69, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x68,
	0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x70, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x70, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x62, 0x61, 0x63, 0x6b, 0x42, 0x70, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x73, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x69, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c,
	0x65, 0x61, 0x6e, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x22, 0xa0, 0x02, 0x0a, 0x11,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2c, 0x0a, 0x06, 0x63, 0x61, 0x63, 0x68, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x63, 0x61, 0x63, 0x68, 0x65, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x68, 0x69, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x07, 0x68, 0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6c,
	0x65, 0x61, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6c, 0x65, 0x61, 0x6e, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52,
	0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x61, 0x74, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a,
	0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x62,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x4b, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xd2, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x6a, 0x6f,
	0x75, 0x72, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x2e,
	0x0a, 0x11, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x6f, 0x12, 0x33,
	0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x66, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4a,
	0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32,
	0x12, 0x4d, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x75,
	0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x86, 0x02, 0x0a, 0x11, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x27, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x0e, 0x70, 0x61, 0x74, 0x72, 0x6f, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05,
	0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0d, 0x70, 0x61, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x0f, 0x70, 0x61, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30,
	0x52, 0x0e, 0x70, 0x61, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x24, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x6f, 0x70, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x20, 0x0a, 0x0a, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x5f, 0x62, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x09, 0x69,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x42, 0x77, 0x22, 0x77, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x12, 0x22, 0x0a,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01,
	0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x7a, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x32, 0x12, 0x57, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x0a,
	0x0b, 0x50, 0x61, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x22, 0xe8, 0x01, 0x0a, 0x11, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x44, 0x69, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x3a, 0x11,
	0x44, 0x49, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41, 0x54,
	0x48, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x12, 0x5f, 0x5f, 0x64,
	0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x44, 0x65, 0x70, 0x72, 0x65,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x3a, 0x10, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x55, 0x4d, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x41, 0x55, 0x54, 0x4f, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x70, 0x0a, 0x0b, 0x44,
	0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69,
	0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x3a, 0x11, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x42, 0x59,
	0x5f, 0x50, 0x41, 0x54, 0x48, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x22, 0x9f, 0x01,
	0x0a, 0x0e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0d, 0x52, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x68,
	0x64, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x68,
	0x64, 0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x64, 0x64, 0x5f, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x02, 0x28, 0x04, 0x52, 0x09, 0x68, 0x64, 0x64, 0x4f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x73, 0x64, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x73, 0x64, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x73, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x73, 0x64, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22,
	0x94, 0x03, 0x0a, 0x0f, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d,
	0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x02, 0x28, 0x0d, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x02, 0x28,
	0x0d, 0x52, 0x06, 0x70, 0x61, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x5f, 0x70, 0x69, 0x64, 0x18, 0x06, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x09, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x50, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x65, 0x70,
	0x6f, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x70,
	0x6f, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x65, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x67, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x0f, 0x74, 0x68, 0x69, 0x63,
	0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0e, 0x74, 0x68, 0x69, 0x63, 0x6b, 0x50,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x04, 0x70, 0x65, 0x72, 0x66,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x04, 0x70,
	0x65, 0x72, 0x66, 0x12, 0x31, 0x0a, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x15, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x22, 0x46, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x05,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x22,
	0x0a, 0x0e, 0x50, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x03, 0x70,
	0x69, 0x64, 0x22, 0x6f, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x12, 0x1c, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x3a, 0x04, 0x31, 0x30, 0x32, 0x34, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12,
	0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63,
	0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x17, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x65, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x2a, 0x0a, 0x14, 0x53, 0x65, 0x74,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x26, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0xee, 0x01,
	0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a,
	0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xb6,
	0x01, 0x0a, 0x10, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d,
	0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x65, 0x72, 0x72, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x61, 0x72,
	0x6e, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x61,
	0x72, 0x6e, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x64, 0x69, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x72, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73, 0x6b, 0x73, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01,
	0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x72, 0x0a,
	0x1b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73,
	0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x53, 0x0a, 0x12,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x44, 0x69, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x23, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x39, 0x0a, 0x15, 0x44, 0x61, 0x74, 0x61, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x20, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x72, 0x64, 0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x07, 0x75, 0x73, 0x65, 0x52, 0x64, 0x6d,
	0x61, 0x22, 0x3d, 0x0a, 0x0f, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x12, 0x2a, 0x0a, 0x0d, 0x61, 0x67, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c,
	0x73, 0x65, 0x52, 0x0c, 0x61, 0x67, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x22, 0xbd, 0x01, 0x0a, 0x11, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x0d, 0x76, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x0c, 0x76, 0x68, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x12, 0x28, 0x0a, 0x0c, 0x6e, 0x76, 0x6d, 0x66, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x0b, 0x6e, 0x76, 0x6d, 0x66, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x0d,
	0x69, 0x73, 0x63, 0x73, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0c, 0x69, 0x73, 0x63, 0x73,
	0x69, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0b, 0x6e, 0x66, 0x73, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x0a, 0x6e, 0x66, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x22, 0xec, 0x06, 0x0a, 0x13, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x6c, 0x73, 0x6d, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x4c, 0x53, 0x4d, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x07, 0x6c, 0x73, 0x6d, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x28, 0x0a, 0x0e, 0x6c, 0x73, 0x6d, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x3a, 0x01, 0x30, 0x52, 0x0d, 0x6c, 0x73,
	0x6d, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x32, 0x0a, 0x0a, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x62, 0x77, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x07, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x42, 0x77, 0x12, 0x25, 0x0a, 0x0e, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x6f, 0x70, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x77,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x77, 0x72, 0x69, 0x74, 0x65, 0x62, 0x61, 0x63,
	0x6b, 0x42, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x73, 0x6d, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x73, 0x6d, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x15,
	0x0a, 0x06, 0x6c, 0x73, 0x6d, 0x5f, 0x62, 0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x6c, 0x73, 0x6d, 0x42, 0x77, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x72, 0x65,
	0x63, 0x6c, 0x61, 0x69, 0x6d, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x77, 0x61, 0x69,
	0x74, 0x52, 0x65, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x61, 0x69, 0x74,
	0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x77, 0x61,
	0x69, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x6d, 0x61, 0x70,
	0x5f, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x6e, 0x6d,
	0x61, 0x70, 0x49, 0x6f, 0x70, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x5f,
	0x62, 0x77, 0x18, 0x17, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x42,
	0x77, 0x12, 0x3b, 0x0a, 0x08, 0x64, 0x63, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x29, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x64, 0x63, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45,
	0x0a, 0x10, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x0f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x43, 0x61, 0x70, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3f, 0x0a, 0x0c, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x5f, 0x6f, 0x6e, 0x18, 0x32, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x4f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x6f, 0x6e, 0x18,
	0x33, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x4f, 0x6e, 0x12, 0x2a,
	0x0a, 0x11, 0x61, 0x64, 0x61, 0x70, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x5f, 0x6f, 0x6e, 0x18, 0x34, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x64, 0x61, 0x70, 0x74,
	0x69, 0x76, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x4f, 0x6e, 0x12, 0x4f, 0x0a, 0x0f, 0x61, 0x63,
	0x63, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x35, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x43,
	0x6f, 0x70, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x3a, 0x13, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x5f, 0x43,
	0x4f, 0x50, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x52, 0x0d, 0x61, 0x63,
	0x63, 0x65, 0x6c, 0x43, 0x6f, 0x70, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d,
	0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22,
	0x99, 0x01, 0x0a, 0x15, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x4d, 0x0a, 0x12, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x13, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x11, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x4e, 0x75, 0x6d, 0x22, 0x85, 0x02, 0x0a, 0x10,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x12, 0x2c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x28,
	0x0a, 0x0c, 0x61, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0b, 0x61, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6c, 0x69,
	0x76, 0x65, 0x53, 0x65, 0x63, 0x12, 0x3c, 0x0a, 0x0d, 0x61, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x61, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x0f, 0x61, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x5f, 0x69, 0x73,
	0x6f, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61,
	0x6c, 0x73, 0x65, 0x52, 0x0e, 0x61, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x49, 0x73, 0x6f, 0x6c, 0x61,
	0x74, 0x65, 0x64, 0x22, 0x57, 0x0a, 0x11, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x09, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02,
	0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x07,
	0x64, 0x72, 0x79, 0x5f, 0x72, 0x75, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x64, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x22, 0x2c, 0x0a, 0x12,
	0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x22, 0x33, 0x0a, 0x0b, 0x44, 0x69,
	0x73, 0x6b, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22,
	0x66, 0x0a, 0x11, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x64,
	0x69, 0x73, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x44,
	0x69, 0x73, 0x6b, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x92, 0x03, 0x0a, 0x12, 0x53, 0x68, 0x6f, 0x77,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x02, 0x28, 0x04, 0x52,
	0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x02, 0x28, 0x04, 0x52, 0x0a, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28,
	0x0a, 0x10, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6c, 0x6f, 0x62, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x42, 0x6c, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x62, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x62, 0x4e, 0x75, 0x6d,
	0x12, 0x33, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x64, 0x69, 0x73,
	0x6b, 0x52, 0x65, 0x66, 0x73, 0x12, 0x2e, 0x0a, 0x0f, 0x74, 0x68, 0x69, 0x63, 0x6b, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05,
	0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0e, 0x74, 0x68, 0x69, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x04, 0x70, 0x65, 0x72, 0x66, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x04, 0x70, 0x65, 0x72, 0x66,
	0x12, 0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x63, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x87, 0x01, 0x0a,
	0x14, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x21, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x3a,
	0x09, 0x32, 0x36, 0x38, 0x34, 0x33, 0x35, 0x34, 0x35, 0x36, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x11, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x74,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x79, 0x5f, 0x74, 0x73, 0x63, 0x18, 0x03, 0x20,
	0x02, 0x28, 0x04, 0x52, 0x07, 0x62, 0x75, 0x73, 0x79, 0x54, 0x73, 0x63, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x64, 0x6c, 0x65, 0x5f, 0x74, 0x73, 0x63, 0x18, 0x04, 0x20, 0x02, 0x28, 0x04, 0x52, 0x07,
	0x69, 0x64, 0x6c, 0x65, 0x54, 0x73, 0x63, 0x22, 0x5c, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x6f,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x68, 0x72,
	0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0xcb, 0x01, 0x0a, 0x18, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x09, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca, 0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c,
	0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x3a,
	0x01, 0x30, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4d,
	0x73, 0x12, 0x2b, 0x0a, 0x10, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x0e,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4d, 0x73, 0x12, 0x33,
	0x0a, 0x14, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52,
	0x12, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x4d, 0x73, 0x22, 0xd5, 0x01, 0x0a, 0x22, 0x53, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x09, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x42, 0x05, 0xca,
	0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x29,
	0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64,
	0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4d, 0x73, 0x12, 0x2b, 0x0a, 0x10, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x0e, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x4d, 0x73, 0x12, 0x33, 0x0a, 0x14, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x12, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4d, 0x73, 0x22, 0x68, 0x0a, 0x23, 0x53,
	0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x69, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6b, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a,
	0x0a, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x67, 0x0a, 0x24, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x09, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xca,
	0x49, 0x02, 0x08, 0x01, 0x52, 0x08, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x6c, 0x6c, 0x22, 0x8f, 0x02, 0x0a, 0x0f,
	0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x02, 0x28, 0x04,
	0x52, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6f,
	0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x0c,
	0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x50, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f,
	0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18,
	0x04, 0x20, 0x02, 0x28, 0x04, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79,
	0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x36, 0x0a, 0x17, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x02, 0x28, 0x04, 0x52, 0x15, 0x62, 0x61, 0x73, 0x65, 0x50, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a,
	0x12, 0x70, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x02, 0x28, 0x04, 0x52, 0x11, 0x70, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x6e, 0x6d, 0x61, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0xb4, 0x01,
	0x0a, 0x1b, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x55, 0x6e, 0x6d, 0x61, 0x70,
	0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a,
	0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x3a, 0x01,
	0x30, 0x52, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0d, 0x74, 0x65,
	0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x50,
	0x69, 0x64, 0x12, 0x2a, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f,
	0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0e,
	0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x24,
	0x0a, 0x0c, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18, 0x04,
	0x20, 0x02, 0x28, 0x04, 0x3a, 0x01, 0x30, 0x52, 0x0b, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x42, 0x69,
	0x74, 0x6d, 0x61, 0x70, 0x22, 0x42, 0x0a, 0x0b, 0x42, 0x79, 0x74, 0x65, 0x73, 0x42, 0x69, 0x74,
	0x6d, 0x61, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x6f,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c,
	0x52, 0x06, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x22, 0x8f, 0x02, 0x0a, 0x1e, 0x54, 0x65, 0x6d,
	0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x56, 0x32, 0x12, 0x23, 0x0a, 0x0d, 0x74,
	0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0d, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x50, 0x69, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x70,
	0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x02, 0x28, 0x04, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6f,
	0x72, 0x61, 0x72, 0x79, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52,
	0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x69, 0x74, 0x6d,
	0x61, 0x70, 0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x62, 0x69, 0x74,
	0x6d, 0x61, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x42, 0x69, 0x74, 0x6d,
	0x61, 0x70, 0x52, 0x0c, 0x62, 0x79, 0x74, 0x65, 0x73, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x02, 0x28,
	0x0d, 0x52, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x43, 0x0a, 0x0c, 0x53, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x73,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x69, 0x74, 0x6d, 0x61,
	0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x06, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x22,
	0xca, 0x01, 0x0a, 0x17, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x56, 0x31, 0x12, 0x23, 0x0a, 0x0d, 0x74,
	0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x0d, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x50, 0x69, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x70,
	0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x02, 0x28, 0x04, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6f,
	0x72, 0x61, 0x72, 0x79, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52,
	0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x12, 0x3e, 0x0a, 0x0e,
	0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x52, 0x0d, 0x73,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x73, 0x22, 0xd4, 0x01, 0x0a,
	0x17, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74,
	0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x56, 0x33, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70,
	0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52,
	0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x50, 0x69, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x04, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72,
	0x79, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x6d,
	0x61, 0x70, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0b, 0x75, 0x6e, 0x6d, 0x61, 0x70, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x12, 0x25, 0x0a, 0x0e,
	0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x42, 0x69, 0x74,
	0x6d, 0x61, 0x70, 0x22, 0x69, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72,
	0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61,
	0x72, 0x79, 0x50, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61,
	0x72, 0x79, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x02, 0x28, 0x04, 0x52, 0x0e,
	0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x22, 0x96,
	0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a,
	0x10, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x5a, 0x0a, 0x17, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72,
	0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x74, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x56, 0x33, 0x52, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6f,
	0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70,
	0x12, 0x31, 0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13,
	0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x0a, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x92, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x11, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x50, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x07, 0x6d, 0x61, 0x78,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x04, 0x32, 0x30, 0x34, 0x38,
	0x52, 0x06, 0x6d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31,
	0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x66, 0x0a, 0x1b,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x11, 0x74,
	0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x10, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x73, 0x22, 0x79, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f,
	0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x65, 0x6d,
	0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61,
	0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d,
	0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22,
	0x82, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x5b, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x69, 0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x50,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x69, 0x64, 0x31, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x69, 0x64,
	0x31, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x69, 0x64, 0x32, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52,
	0x04, 0x70, 0x69, 0x64, 0x32, 0x12, 0x28, 0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31,
	0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xdb, 0x01, 0x0a, 0x08, 0x53, 0x69, 0x6e, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x03, 0x6c, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6c, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x6c, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x66, 0x50, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x65, 0x72, 0x66, 0x45, 0x70, 0x6f, 0x63, 0x68,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e,
	0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x6f,
	0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x69, 0x6e, 0x6b, 0x54,
	0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x8e, 0x01,
	0x0a, 0x14, 0x53, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x73,
	0x69, 0x6e, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x69,
	0x6e, 0x6b, 0x5f, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x73, 0x69, 0x6e, 0x6b, 0x49, 0x6f, 0x43,
	0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a,
	0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xda,
	0x07, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x5f, 0x73,
	0x69, 0x6e, 0x6b, 0x5f, 0x63, 0x6d, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x53, 0x69, 0x6e, 0x6b, 0x43, 0x6d, 0x64, 0x52, 0x0a, 0x63, 0x75, 0x72,
	0x53, 0x69, 0x6e, 0x6b, 0x43, 0x6d, 0x64, 0x12, 0x32, 0x0a, 0x0a, 0x73, 0x69, 0x6e, 0x6b, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x69, 0x6e, 0x6b, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x09, 0x73, 0x69, 0x6e, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x58, 0x0a, 0x11, 0x73,
	0x69, 0x6e, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x72, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x52, 0x55,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x73, 0x69, 0x6e, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x72,
	0x75, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x6b, 0x0a, 0x1b, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x72, 0x75, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x4c, 0x52, 0x55, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x18, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x72, 0x75, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x38, 0x0a, 0x18, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x69, 0x64, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x43, 0x69, 0x64, 0x73, 0x12, 0x3a, 0x0a, 0x19, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x73, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x72, 0x65,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x29, 0x0a,
	0x11, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x6e,
	0x6b, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x35, 0x0a, 0x17, 0x6d, 0x61, 0x78, 0x5f,
	0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x14, 0x6d, 0x61, 0x78, 0x53, 0x69,
	0x6e, 0x6b, 0x49, 0x6f, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x32, 0x0a, 0x15, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13,
	0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x72, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6d, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x10, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x43, 0x6d, 0x64,
	0x73, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x15, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01,
	0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x1a, 0xab, 0x01,
	0x0a, 0x0c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x52, 0x55, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x69, 0x6e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x6c, 0x65, 0x61, 0x6e, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12,
	0x2a, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x22, 0x68, 0x0a, 0x16, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x4e, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xae, 0x02, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x43, 0x61, 0x70,
	0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x49, 0x0a, 0x23, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x69, 0x6f, 0x5f,
	0x64, 0x65, 0x70, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x73, 0x61, 0x74, 0x61, 0x5f, 0x68, 0x64, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1c,
	0x6d, 0x61, 0x78, 0x43, 0x61, 0x70, 0x49, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x68, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x50, 0x65, 0x72, 0x53, 0x61, 0x74, 0x61, 0x48, 0x64, 0x64, 0x12, 0x22, 0x0a, 0x0d,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x32, 0x35, 0x36, 0x6b, 0x5f, 0x69, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x32, 0x35, 0x36, 0x6b, 0x49, 0x6f,
	0x12, 0x31, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x70,
	0x5f, 0x69, 0x6f, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x43, 0x61, 0x70, 0x49, 0x6f, 0x44, 0x65,
	0x70, 0x74, 0x68, 0x12, 0x2b, 0x0a, 0x12, 0x63, 0x61, 0x70, 0x5f, 0x69, 0x6f, 0x5f, 0x64, 0x65,
	0x70, 0x74, 0x68, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x61, 0x70, 0x49, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x68, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x12, 0x20, 0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x41, 0x70, 0x70,
	0x49, 0x6f, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xab, 0x02, 0x0a, 0x11, 0x43, 0x61, 0x70, 0x49, 0x4f,
	0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x5f, 0x69, 0x6f, 0x5f,
	0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x75, 0x72,
	0x49, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x6f, 0x5f, 0x64, 0x65,
	0x70, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x69, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2a, 0x0a,
	0x11, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x69, 0x6f, 0x5f, 0x64, 0x65, 0x70,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x64, 0x49, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x6f, 0x5f,
	0x64, 0x65, 0x70, 0x74, 0x68, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x69, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x68, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12,
	0x29, 0x0a, 0x11, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x77, 0x5f, 0x69, 0x6f,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x77, 0x49, 0x6f, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6f,
	0x70, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x69, 0x6f, 0x70, 0x73, 0x12, 0x24,
	0x0a, 0x0e, 0x61, 0x76, 0x67, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6e, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x76, 0x67, 0x4c, 0x61, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x4e, 0x73, 0x22, 0xba, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x49,
	0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x32, 0x35, 0x36, 0x6b, 0x5f,
	0x69, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x32,
	0x35, 0x36, 0x6b, 0x49, 0x6f, 0x12, 0x2f, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69,
	0x6f, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6f, 0x44, 0x65, 0x70, 0x74,
	0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x29, 0x0a, 0x11, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x77, 0x5f, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0e, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x77, 0x49, 0x6f, 0x4e, 0x75,
	0x6d, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6f, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x49, 0x6f, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x41, 0x70, 0x70, 0x49, 0x6f, 0x12, 0x32, 0x0a, 0x05, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x61, 0x70, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x22, 0x0a,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01,
	0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x70, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x49, 0x4f, 0x54, 0x68, 0x72,
	0x6f, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12,
	0x52, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x49, 0x4f,
	0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x74, 0x0a, 0x0b, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6f, 0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x04, 0x69, 0x6f, 0x70, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x70, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x62, 0x70, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x70, 0x73, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x70, 0x73,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x70, 0x73,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6d, 0x61,
	0x78, 0x42, 0x70, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xa0, 0x02, 0x0a, 0x16, 0x49, 0x4f,
	0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x61, 0x64, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x0f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x52, 0x65, 0x61, 0x64, 0x12, 0x40, 0x0a, 0x10, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72,
	0x66, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x52, 0x65, 0x61, 0x64, 0x12, 0x40, 0x0a, 0x10, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50,
	0x65, 0x72, 0x66, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x11, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0f, 0x66, 0x72, 0x6f,
	0x6d, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x57, 0x72, 0x69, 0x74, 0x65, 0x22, 0xa8, 0x01, 0x0a,
	0x13, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6f, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x77, 0x61, 0x69, 0x74, 0x69,
	0x6e, 0x67, 0x49, 0x6f, 0x4e, 0x75, 0x6d, 0x22, 0xc2, 0x0b, 0x0a, 0x16, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x41, 0x0a, 0x1e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6f, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x61, 0x74, 0x61,
	0x5f, 0x68, 0x64, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x19, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x6f, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x65, 0x72, 0x53, 0x61,
	0x74, 0x61, 0x48, 0x64, 0x64, 0x12, 0x3c, 0x0a, 0x1c, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6f, 0x5f,
	0x62, 0x75, 0x73, 0x79, 0x5f, 0x62, 0x70, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x61, 0x74,
	0x61, 0x5f, 0x68, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x61, 0x70, 0x70,
	0x49, 0x6f, 0x42, 0x75, 0x73, 0x79, 0x42, 0x70, 0x73, 0x50, 0x65, 0x72, 0x53, 0x61, 0x74, 0x61,
	0x48, 0x64, 0x64, 0x12, 0x3e, 0x0a, 0x1d, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6f, 0x5f, 0x62, 0x75,
	0x73, 0x79, 0x5f, 0x69, 0x6f, 0x70, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x61, 0x74, 0x61,
	0x5f, 0x68, 0x64, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x17, 0x61, 0x70, 0x70, 0x49,
	0x6f, 0x42, 0x75, 0x73, 0x79, 0x49, 0x6f, 0x70, 0x73, 0x50, 0x65, 0x72, 0x53, 0x61, 0x74, 0x61,
	0x48, 0x64, 0x64, 0x12, 0x4f, 0x0a, 0x25, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6d, 0x67, 0x72, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77,
	0x4d, 0x67, 0x72, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x12, 0x3a, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x66, 0x50, 0x65, 0x72, 0x66,
	0x12, 0x38, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x66,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0a,
	0x61, 0x70, 0x70, 0x43, 0x61, 0x70, 0x50, 0x65, 0x72, 0x66, 0x12, 0x44, 0x0a, 0x12, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x65, 0x72, 0x66,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x10,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x50, 0x65, 0x72, 0x66,
	0x12, 0x42, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x70,
	0x5f, 0x70, 0x65, 0x72, 0x66, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x61, 0x70,
	0x50, 0x65, 0x72, 0x66, 0x12, 0x5b, 0x0a, 0x1a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x42, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x17, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x50, 0x65, 0x72, 0x66, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x12, 0x59, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61,
	0x70, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x52, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x61,
	0x70, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x71, 0x0a, 0x24,
	0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f,
	0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x61,
	0x64, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x20, 0x72,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x50, 0x65, 0x72, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x6f, 0x0a, 0x23, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61,
	0x70, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x52,
	0x65, 0x61, 0x64, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x1f, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x70, 0x52, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x50, 0x65, 0x72, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x65, 0x0a, 0x1e, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x61, 0x70, 0x5f, 0x65, 0x63, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x61, 0x64, 0x57,
	0x72, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1a, 0x72, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x70, 0x45, 0x63, 0x50, 0x65, 0x72, 0x66,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x65, 0x0a, 0x1e, 0x73, 0x69, 0x6e, 0x6b, 0x5f,
	0x70, 0x65, 0x72, 0x66, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x5f, 0x70, 0x65, 0x72,
	0x66, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65,
	0x72, 0x66, 0x52, 0x65, 0x61, 0x64, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x1a, 0x73, 0x69, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x50, 0x65, 0x72, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x63,
	0x0a, 0x1d, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x61, 0x64, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x19, 0x73, 0x69, 0x6e, 0x6b, 0x43, 0x61,
	0x70, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x50, 0x65, 0x72, 0x66, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x59, 0x0a, 0x18, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x63, 0x61, 0x70, 0x5f,
	0x65, 0x63, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x49, 0x4f, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x61, 0x64, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x73, 0x69, 0x6e, 0x6b, 0x43, 0x61,
	0x70, 0x45, 0x63, 0x50, 0x65, 0x72, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x48,
	0x0a, 0x21, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1d, 0x6d, 0x61, 0x78, 0x52, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30,
	0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6c, 0x0a, 0x18,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x12, 0x50, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74,
	0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43, 0x0a, 0x0b, 0x52, 0x44,
	0x4d, 0x41, 0x55, 0x44, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6c, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71,
	0x70, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x71, 0x70, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x67, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x67, 0x69, 0x64, 0x22,
	0x3f, 0x0a, 0x0f, 0x52, 0x44, 0x4d, 0x41, 0x55, 0x44, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x52, 0x44,
	0x4d, 0x41, 0x55, 0x44, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65,
	0x22, 0xdb, 0x01, 0x0a, 0x0d, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x61, 0x74, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x69, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x53, 0x65, 0x6e, 0x64, 0x49, 0x66, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x63, 0x76, 0x5f, 0x69, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x76, 0x49, 0x66, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x66, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x66,
	0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x76, 0x5f,
	0x69, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x76, 0x49, 0x66, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x69, 0x64, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x70, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x12,
	0x1d, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0xf9,
	0x01, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3f, 0x0a, 0x0e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x6d, 0x70, 0x61, 0x74, 0x68, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x61, 0x74, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d, 0x70, 0x61, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x2a,
	0x09, 0x08, 0x90, 0x4e, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0x8f, 0x05, 0x0a, 0x11, 0x52,
	0x44, 0x4d, 0x41, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2f, 0x0a, 0x14, 0x6e, 0x75, 0x6d, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x77, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11,
	0x6e, 0x75, 0x6d, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x6e, 0x64, 0x57,
	0x72, 0x12, 0x2f, 0x0a, 0x14, 0x6e, 0x75, 0x6d, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x63, 0x76, 0x5f, 0x77, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x11, 0x6e, 0x75, 0x6d, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x76,
	0x57, 0x72, 0x12, 0x3f, 0x0a, 0x1d, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f,
	0x70, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x18, 0x6e, 0x75, 0x6d, 0x53, 0x65,
	0x6e, 0x64, 0x4f, 0x70, 0x73, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x12, 0x3f, 0x0a, 0x1d, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x63, 0x76, 0x5f,
	0x6f, 0x70, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x18, 0x6e, 0x75, 0x6d, 0x52,
	0x65, 0x63, 0x76, 0x4f, 0x70, 0x73, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x12, 0x40, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x52, 0x44, 0x4d, 0x41, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x4f, 0x52, 0x61, 0x74, 0x65, 0x52, 0x08, 0x73, 0x65,
	0x6e, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x52, 0x44, 0x4d, 0x41, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x4f, 0x52, 0x61, 0x74, 0x65, 0x52, 0x08,
	0x72, 0x65, 0x61, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x52, 0x44, 0x4d, 0x41, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x4f, 0x52, 0x61, 0x74,
	0x65, 0x52, 0x09, 0x77, 0x72, 0x69, 0x74, 0x65, 0x52, 0x61, 0x74, 0x65, 0x1a, 0x78, 0x0a, 0x06,
	0x49, 0x4f, 0x52, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x69, 0x6f, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6f, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e,
	0x69, 0x6f, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x69, 0x6f, 0x52, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x6f, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x69, 0x6f, 0x52, 0x61, 0x74,
	0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x32, 0x54, 0x0a, 0x09, 0x72, 0x64, 0x6d, 0x61, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x91, 0x4e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x52, 0x44, 0x4d, 0x41, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x72, 0x64, 0x6d, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa5, 0x01, 0x0a,
	0x0c, 0x44, 0x43, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a,
	0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x0d, 0x44, 0x43, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x46, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x43,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x84, 0x02,
	0x0a, 0x0c, 0x44, 0x43, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38,
	0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x67, 0x67, 0x72,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x61, 0x67,
	0x67, 0x72, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x61, 0x67, 0x67, 0x72, 0x5f, 0x61, 0x63,
	0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x61, 0x67, 0x67, 0x72,
	0x41, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x1c, 0x61, 0x67, 0x67, 0x72, 0x5f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x61, 0x67, 0x67, 0x72,
	0x4c, 0x61, 0x73, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x22, 0xa4, 0x01, 0x0a, 0x0d, 0x44, 0x43, 0x4d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x0a, 0x64, 0x63, 0x5f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x43, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x64, 0x63, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x37,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52,
	0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x0e, 0x44,
	0x43, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x47, 0x0a,
	0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x43, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x7a, 0x0a, 0x0f, 0x44, 0x61, 0x74, 0x61, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x08, 0x64, 0x63, 0x73,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x43, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x64, 0x63, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a,
	0x08, 0x64, 0x63, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x43, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x64, 0x63, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x2a, 0x58, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x2a, 0xd3, 0x01, 0x0a,
	0x0c, 0x44, 0x69, 0x73, 0x6b, 0x45, 0x72, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x17, 0x0a,
	0x13, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x5f, 0x5f, 0x44, 0x45, 0x50, 0x52,
	0x45, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x5f,
	0x48, 0x49, 0x47, 0x48, 0x5f, 0x4c, 0x41, 0x54, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x49,
	0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x4f, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x44,
	0x49, 0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x53, 0x55, 0x4d,
	0x10, 0x08, 0x12, 0x23, 0x0a, 0x1f, 0x5f, 0x5f, 0x44, 0x45, 0x50, 0x52, 0x45, 0x43, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x55, 0x4d, 0x4f, 0x55,
	0x4e, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x10, 0x12, 0x26, 0x0a, 0x22, 0x5f, 0x5f, 0x44, 0x45, 0x50,
	0x52, 0x45, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52,
	0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x20, 0x12,
	0x11, 0x0a, 0x0d, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x59, 0x4e, 0x43,
	0x10, 0x40, 0x2a, 0x27, 0x0a, 0x0d, 0x44, 0x69, 0x73, 0x6b, 0x57, 0x61, 0x72, 0x6e, 0x46, 0x6c,
	0x61, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x57, 0x41, 0x52, 0x4e,
	0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x4c, 0x41, 0x54, 0x10, 0x01, 0x2a, 0x8b, 0x01, 0x0a, 0x0d,
	0x4c, 0x53, 0x4d, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a,
	0x18, 0x4c, 0x53, 0x4d, 0x5f, 0x43, 0x41, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x41,
	0x46, 0x45, 0x5f, 0x55, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x4c,
	0x53, 0x4d, 0x5f, 0x43, 0x41, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x5f, 0x55, 0x4e, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x59, 0x10, 0x02, 0x12, 0x1d,
	0x0a, 0x19, 0x4c, 0x53, 0x4d, 0x5f, 0x43, 0x41, 0x50, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x53, 0x4f, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x1a, 0x0a,
	0x16, 0x4c, 0x53, 0x4d, 0x5f, 0x43, 0x41, 0x50, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x52, 0x45,
	0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x2a, 0x48, 0x0a, 0x0b, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x43, 0x48,
	0x45, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x43,
	0x41, 0x43, 0x48, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x13,
	0x0a, 0x0f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x2a, 0x82, 0x01, 0x0a, 0x0d, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x0c, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x41, 0x4c,
	0x5f, 0x49, 0x44, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4a, 0x4f, 0x55, 0x52, 0x4e,
	0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4a, 0x4f, 0x55,
	0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x42, 0x55, 0x53, 0x59, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x4a,
	0x4f, 0x55, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0c, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x41, 0x4c,
	0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0xe7, 0x07, 0x2a, 0x48, 0x0a, 0x12, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x14, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x41, 0x4c, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x4d,
	0x4f, 0x55, 0x4e, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x4a, 0x4f, 0x55, 0x52,
	0x4e, 0x41, 0x4c, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x2a, 0x44, 0x0a, 0x0a, 0x44, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x10, 0x0a, 0x0c, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x2a, 0x66, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x15, 0x0a,
	0x11, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x4f, 0x55, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x49, 0x53,
	0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03,
	0x2a, 0x58, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x6b, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x55, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x49, 0x53, 0x4b,
	0x5f, 0x55, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x45, 0x10,
	0x01, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x55, 0x4d, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x02, 0x2a, 0x3e, 0x0a, 0x0e, 0x44, 0x69,
	0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x44, 0x49, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x55, 0x49,
	0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x01, 0x2a, 0x80, 0x01, 0x0a, 0x0c, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x41,
	0x55, 0x52, 0x4f, 0x52, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x41,
	0x55, 0x52, 0x4f, 0x52, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x55, 0x4e,
	0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x55, 0x52, 0x4f, 0x52, 0x41,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47,
	0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x55, 0x52, 0x4f, 0x52, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x53, 0x0a,
	0x0c, 0x53, 0x69, 0x6e, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a,
	0x0c, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x53, 0x49, 0x4e, 0x4b, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x01,
	0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x49, 0x4e, 0x4b, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x02,
	0x12, 0x10, 0x0a, 0x0c, 0x44, 0x52, 0x41, 0x49, 0x4e, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x54,
	0x10, 0x03, 0x2a, 0x59, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x49, 0x58, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54,
	0x43, 0x50, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x44, 0x4d, 0x41, 0x10, 0x03, 0x2a, 0x53, 0x0a,
	0x12, 0x44, 0x61, 0x74, 0x61, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x48, 0x41, 0x4e,
	0x4e, 0x45, 0x4c, 0x5f, 0x56, 0x31, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x56, 0x32, 0x10, 0x02, 0x12, 0x13, 0x0a,
	0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x56, 0x33,
	0x10, 0x03, 0x32, 0xf8, 0x2c, 0x0a, 0x0c, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x5a, 0x62, 0x73, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a,
	0x15, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x5a, 0x62, 0x73, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x50, 0x61, 0x72, 0x74,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x4d, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f,
	0x69, 0x64, 0x12, 0x35, 0x0a, 0x0a, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x12, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4d, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x0b, 0x55, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69,
	0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x61, 0x6c, 0x12, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x39,
	0x0a, 0x0c, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x1e,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74,
	0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x0d, 0x55, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x1c, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56,
	0x6f, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x41, 0x6c, 0x6c, 0x4a,
	0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f,
	0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x3c, 0x0a,
	0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x09,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x4c,
	0x69, 0x73, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x1e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x1c, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x53,
	0x74, 0x6f, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x56, 0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12,
	0x36, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x09, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x1d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69,
	0x64, 0x1a, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4a, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x50, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64,
	0x12, 0x34, 0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x12, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x6c, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x56, 0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12,
	0x49, 0x0a, 0x0a, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x4c, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x10, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x4b, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49, 0x50, 0x12, 0x27,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49, 0x50,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f,
	0x69, 0x64, 0x12, 0x42, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x55, 0x6e, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x6e, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64,
	0x12, 0x3e, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x55, 0x6e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79,
	0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x1e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x6e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64,
	0x12, 0x34, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x12, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x40, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x55, 0x6e, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x1e, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x6e, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x16, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64,
	0x12, 0x37, 0x0a, 0x0b, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12,
	0x1d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x46, 0x0a, 0x09, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x44, 0x69, 0x73, 0x6b, 0x12, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x38, 0x0a, 0x0b, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x1e, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x76, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x48, 0x6f, 0x73,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x50, 0x12, 0x2a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x76, 0x6d, 0x4d, 0x6f,
	0x64, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x3d,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64,
	0x1a, 0x1b, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x12, 0x24, 0x0a,
	0x0c, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x09, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56,
	0x6f, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x5a, 0x6b, 0x48, 0x6f, 0x73, 0x74,
	0x73, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x1d, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x5a, 0x6b, 0x48, 0x6f,
	0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x15, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x55, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x16, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12,
	0x38, 0x0a, 0x13, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x44, 0x0a, 0x11, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73, 0x6b, 0x73, 0x12, 0x09,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x24, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x44, 0x69, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x35, 0x0a, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x12, 0x1c, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x10, 0x49, 0x73, 0x6f, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x12, 0x09, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f,
	0x69, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x53, 0x74, 0x6f, 0x70, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64,
	0x48, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56,
	0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x4c,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f,
	0x69, 0x64, 0x1a, 0x28, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x18,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x50, 0x61, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56,
	0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x2f,
	0x0a, 0x17, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x50, 0x61, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x56, 0x6f, 0x69, 0x64, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12,
	0x42, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56,
	0x6f, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x69, 0x72, 0x74, 0x79,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x4d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x72,
	0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x1c, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x72, 0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x43, 0x44, 0x50, 0x4a,
	0x6f, 0x62, 0x12, 0x15, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x44, 0x50, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x0b, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x44, 0x50, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x44, 0x50,
	0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12,
	0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x22, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c,
	0x0a, 0x1b, 0x53, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x1d,
	0x43, 0x6c, 0x65, 0x61, 0x72, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x5a, 0x0a, 0x1c, 0x4c, 0x69, 0x73,
	0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x56, 0x6f, 0x69, 0x64, 0x1a, 0x2f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x49, 0x4f, 0x4c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x2c, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x24, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x50,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x50, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x53, 0x69, 0x6e,
	0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56,
	0x6f, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x43, 0x61, 0x70, 0x49, 0x4f, 0x54,
	0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x22, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x61, 0x70, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f,
	0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x09, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70,
	0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x23, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x15, 0x53, 0x65,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74,
	0x74, 0x6c, 0x65, 0x12, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69,
	0x64, 0x12, 0x45, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f,
	0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x0c, 0x49, 0x73, 0x6f, 0x6c,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x16, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x44, 0x4d, 0x41, 0x55, 0x44,
	0x12, 0x09, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x64, 0x1a, 0x1a, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x52, 0x44, 0x4d, 0x41, 0x55, 0x44, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x44, 0x43,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a,
	0x19, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x43, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x44, 0x43, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x1a, 0x18, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x44, 0x43,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x3e, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x5a, 0x62, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x32, 0x12, 0x12,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x1a, 0x17, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x5a,
	0x62, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x32, 0x12, 0x49, 0x0a, 0x0f, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x12, 0x12,
	0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x1a, 0x22, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x45, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f,
	0x75, 0x72, 0x6e, 0x61, 0x6c, 0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x20, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x41, 0x0a,
	0x0b, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x1a, 0x1e, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32,
	0x12, 0x3f, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x56,
	0x32, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x1a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x32, 0x12, 0x3f, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x1a, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x32, 0x12, 0x45, 0x0a, 0x0d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x20, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x51, 0x0a, 0x13, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73, 0x6b, 0x73, 0x56, 0x32,
	0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x1a, 0x26, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73,
	0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x59, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x2a, 0x2e, 0x7a, 0x62,
	0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x61, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x2e, 0x2e, 0x7a, 0x62, 0x73,
	0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6f, 0x72,
	0x61, 0x72, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x47, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a,
	0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x1a, 0x21, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x32, 0x12, 0x4f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x49, 0x4f, 0x54,
	0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x56, 0x32, 0x12, 0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x25, 0x2e,
	0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70,
	0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x32, 0x12, 0x52, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x56, 0x32, 0x12,
	0x12, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x1a, 0x23, 0x2e, 0x7a, 0x62, 0x73, 0x2e, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x4f, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x1a, 0x03, 0xc0, 0x3e, 0x00, 0x42, 0x3b, 0x5a,
	0x33, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x69, 0x6f, 0x6d, 0x65,
	0x73, 0x68, 0x2f, 0x7a, 0x62, 0x73, 0x2d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x67, 0x6f,
	0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x7a, 0x62, 0x73, 0x2f, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x80, 0x01, 0x01, 0x90, 0x01, 0x01,
}

var (
	file_chunk_proto_rawDescOnce sync.Once
	file_chunk_proto_rawDescData = file_chunk_proto_rawDesc
)

func file_chunk_proto_rawDescGZIP() []byte {
	file_chunk_proto_rawDescOnce.Do(func() {
		file_chunk_proto_rawDescData = protoimpl.X.CompressGZIP(file_chunk_proto_rawDescData)
	})
	return file_chunk_proto_rawDescData
}

var file_chunk_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_chunk_proto_msgTypes = make([]protoimpl.MessageInfo, 107)
var file_chunk_proto_goTypes = []interface{}{
	(PartitionStatus)(0),                         // 0: zbs.chunk.PartitionStatus
	(DiskErrFlags)(0),                            // 1: zbs.chunk.DiskErrFlags
	(DiskWarnFlags)(0),                           // 2: zbs.chunk.DiskWarnFlags
	(LSMCapability)(0),                           // 3: zbs.chunk.LSMCapability
	(CacheStatus)(0),                             // 4: zbs.chunk.CacheStatus
	(JournalStatus)(0),                           // 5: zbs.chunk.JournalStatus
	(JournalGroupStatus)(0),                      // 6: zbs.chunk.JournalGroupStatus
	(DiskStatus)(0),                              // 7: zbs.chunk.DiskStatus
	(DiskType)(0),                                // 8: zbs.chunk.DiskType
	(DiskUmountMode)(0),                          // 9: zbs.chunk.DiskUmountMode
	(DiskNameScheme)(0),                          // 10: zbs.chunk.DiskNameScheme
	(AuroraStatus)(0),                            // 11: zbs.chunk.AuroraStatus
	(SinkTaskType)(0),                            // 12: zbs.chunk.SinkTaskType
	(TransportType)(0),                           // 13: zbs.chunk.TransportType
	(DataChannelVersion)(0),                      // 14: zbs.chunk.DataChannelVersion
	(*PartitionInfo)(nil),                        // 15: zbs.chunk.PartitionInfo
	(*CacheInfo)(nil),                            // 16: zbs.chunk.CacheInfo
	(*JournalGroupInfo)(nil),                     // 17: zbs.chunk.JournalGroupInfo
	(*JournalInfo)(nil),                          // 18: zbs.chunk.JournalInfo
	(*MetaAddr)(nil),                             // 19: zbs.chunk.MetaAddr
	(*ClientInfo)(nil),                           // 20: zbs.chunk.ClientInfo
	(*ListClientResponse)(nil),                   // 21: zbs.chunk.ListClientResponse
	(*ZbsAddress)(nil),                           // 22: zbs.chunk.ZbsAddress
	(*ZbsAddressV2)(nil),                         // 23: zbs.chunk.ZbsAddressV2
	(*GetZkHostsResponse)(nil),                   // 24: zbs.chunk.GetZkHostsResponse
	(*GetDiskInfoRequest)(nil),                   // 25: zbs.chunk.GetDiskInfoRequest
	(*GetDiskInfoResponse)(nil),                  // 26: zbs.chunk.GetDiskInfoResponse
	(*UpdateSecondaryDataIPRequest)(nil),         // 27: zbs.chunk.UpdateSecondaryDataIPRequest
	(*UpdateScvmModeHostDataIPRequest)(nil),      // 28: zbs.chunk.UpdateScvmModeHostDataIPRequest
	(*FormatPartitionRequest)(nil),               // 29: zbs.chunk.FormatPartitionRequest
	(*MountPartitionRequest)(nil),                // 30: zbs.chunk.MountPartitionRequest
	(*FormatCacheRequest)(nil),                   // 31: zbs.chunk.FormatCacheRequest
	(*MountCacheRequest)(nil),                    // 32: zbs.chunk.MountCacheRequest
	(*FormatJournalRequest)(nil),                 // 33: zbs.chunk.FormatJournalRequest
	(*MountJournalRequest)(nil),                  // 34: zbs.chunk.MountJournalRequest
	(*WorkerInfo)(nil),                           // 35: zbs.chunk.WorkerInfo
	(*ListPartitionResponse)(nil),                // 36: zbs.chunk.ListPartitionResponse
	(*ListPartitionResponseV2)(nil),              // 37: zbs.chunk.ListPartitionResponseV2
	(*SetUnhealthyRequest)(nil),                  // 38: zbs.chunk.SetUnhealthyRequest
	(*CacheStat)(nil),                            // 39: zbs.chunk.CacheStat
	(*ListCacheResponse)(nil),                    // 40: zbs.chunk.ListCacheResponse
	(*ListCacheResponseV2)(nil),                  // 41: zbs.chunk.ListCacheResponseV2
	(*ListJournalResponse)(nil),                  // 42: zbs.chunk.ListJournalResponse
	(*ListJournalResponseV2)(nil),                // 43: zbs.chunk.ListJournalResponseV2
	(*DiskInspectorStat)(nil),                    // 44: zbs.chunk.DiskInspectorStat
	(*GetDiskInspectorStatsResponse)(nil),        // 45: zbs.chunk.GetDiskInspectorStatsResponse
	(*GetDiskInspectorStatsResponseV2)(nil),      // 46: zbs.chunk.GetDiskInspectorStatsResponseV2
	(*PathRequest)(nil),                          // 47: zbs.chunk.PathRequest
	(*UmountDiskRequest)(nil),                    // 48: zbs.chunk.UmountDiskRequest
	(*DiskRequest)(nil),                          // 49: zbs.chunk.DiskRequest
	(*ChunkBlockInfo)(nil),                       // 50: zbs.chunk.ChunkBlockInfo
	(*ChunkExtentInfo)(nil),                      // 51: zbs.chunk.ChunkExtentInfo
	(*ListExtentResponse)(nil),                   // 52: zbs.chunk.ListExtentResponse
	(*PExtentRequest)(nil),                       // 53: zbs.chunk.PExtentRequest
	(*ListPExtentRequest)(nil),                   // 54: zbs.chunk.ListPExtentRequest
	(*InvalidateExtentRequest)(nil),              // 55: zbs.chunk.InvalidateExtentRequest
	(*SetVerifyModeRequest)(nil),                 // 56: zbs.chunk.SetVerifyModeRequest
	(*QueryDiskRequest)(nil),                     // 57: zbs.chunk.QueryDiskRequest
	(*QueryDiskResponse)(nil),                    // 58: zbs.chunk.QueryDiskResponse
	(*RejectedDiskInfo)(nil),                     // 59: zbs.chunk.RejectedDiskInfo
	(*ListRejectedDisksResponse)(nil),            // 60: zbs.chunk.ListRejectedDisksResponse
	(*ListRejectedDisksResponseV2)(nil),          // 61: zbs.chunk.ListRejectedDisksResponseV2
	(*AcceptDiskRequest)(nil),                    // 62: zbs.chunk.AcceptDiskRequest
	(*DataChannelServerInfo)(nil),                // 63: zbs.chunk.DataChannelServerInfo
	(*ChunkCapability)(nil),                      // 64: zbs.chunk.ChunkCapability
	(*GatewayServerInfo)(nil),                    // 65: zbs.chunk.GatewayServerInfo
	(*SummaryInfoResponse)(nil),                  // 66: zbs.chunk.SummaryInfoResponse
	(*SummaryInfoResponseV2)(nil),                // 67: zbs.chunk.SummaryInfoResponseV2
	(*ChunkServiceStat)(nil),                     // 68: zbs.chunk.ChunkServiceStat
	(*DirtyBlockRequest)(nil),                    // 69: zbs.chunk.DirtyBlockRequest
	(*DirtyBlockResponse)(nil),                   // 70: zbs.chunk.DirtyBlockResponse
	(*DiskRefInfo)(nil),                          // 71: zbs.chunk.DiskRefInfo
	(*ShowExtentRequest)(nil),                    // 72: zbs.chunk.ShowExtentRequest
	(*ShowExtentResponse)(nil),                   // 73: zbs.chunk.ShowExtentResponse
	(*PromoteExtentRequest)(nil),                 // 74: zbs.chunk.PromoteExtentRequest
	(*PollingThreadInfo)(nil),                    // 75: zbs.chunk.PollingThreadInfo
	(*GetPollingStatsResponse)(nil),              // 76: zbs.chunk.GetPollingStatsResponse
	(*VolumeIOLatencyInjection)(nil),             // 77: zbs.chunk.VolumeIOLatencyInjection
	(*SetVolumeIOLatencyInjectionRequest)(nil),   // 78: zbs.chunk.SetVolumeIOLatencyInjectionRequest
	(*SetVolumeIOLatencyInjectionResponse)(nil),  // 79: zbs.chunk.SetVolumeIOLatencyInjectionResponse
	(*ListVolumeIOLatencyInjectionResponse)(nil), // 80: zbs.chunk.ListVolumeIOLatencyInjectionResponse
	(*ClearVolumeIOLatencyInjectionRequest)(nil), // 81: zbs.chunk.ClearVolumeIOLatencyInjectionRequest
	(*TemporaryExtent)(nil),                      // 82: zbs.chunk.TemporaryExtent
	(*TemporaryUnmapBitmapSegment)(nil),          // 83: zbs.chunk.TemporaryUnmapBitmapSegment
	(*BytesBitmap)(nil),                          // 84: zbs.chunk.BytesBitmap
	(*TemporaryExtentSegmentBitmapV2)(nil),       // 85: zbs.chunk.TemporaryExtentSegmentBitmapV2
	(*SectorBitmap)(nil),                         // 86: zbs.chunk.SectorBitmap
	(*TemporaryExtentBitmapV1)(nil),              // 87: zbs.chunk.TemporaryExtentBitmapV1
	(*TemporaryExtentBitmapV3)(nil),              // 88: zbs.chunk.TemporaryExtentBitmapV3
	(*GetTemporaryExtentRequest)(nil),            // 89: zbs.chunk.GetTemporaryExtentRequest
	(*GetTemporaryExtentResponse)(nil),           // 90: zbs.chunk.GetTemporaryExtentResponse
	(*ListTemporaryExtentRequest)(nil),           // 91: zbs.chunk.ListTemporaryExtentRequest
	(*ListTemporaryExtentResponse)(nil),          // 92: zbs.chunk.ListTemporaryExtentResponse
	(*GetTemporaryExtentSummaryResponse)(nil),    // 93: zbs.chunk.GetTemporaryExtentSummaryResponse
	(*GetTemporaryExtentSummaryResponseV2)(nil),  // 94: zbs.chunk.GetTemporaryExtentSummaryResponseV2
	(*ComparePExtentRequest)(nil),                // 95: zbs.chunk.ComparePExtentRequest
	(*SinkTask)(nil),                             // 96: zbs.chunk.SinkTask
	(*SetSinkParamsRequest)(nil),                 // 97: zbs.chunk.SetSinkParamsRequest
	(*ListSinkInfoResponse)(nil),                 // 98: zbs.chunk.ListSinkInfoResponse
	(*ListSinkInfoResponseV2)(nil),               // 99: zbs.chunk.ListSinkInfoResponseV2
	(*SetCapIOThrottleRequest)(nil),              // 100: zbs.chunk.SetCapIOThrottleRequest
	(*CapIOThrottleInfo)(nil),                    // 101: zbs.chunk.CapIOThrottleInfo
	(*GetCapIOThrottleResponse)(nil),             // 102: zbs.chunk.GetCapIOThrottleResponse
	(*GetCapIOThrottleResponseV2)(nil),           // 103: zbs.chunk.GetCapIOThrottleResponseV2
	(*IOPerfStats)(nil),                          // 104: zbs.chunk.IOPerfStats
	(*IOPerfReadWriteDetails)(nil),               // 105: zbs.chunk.IOPerfReadWriteDetails
	(*ThrottleBucketStats)(nil),                  // 106: zbs.chunk.ThrottleBucketStats
	(*InternalIOThrottleInfo)(nil),               // 107: zbs.chunk.InternalIOThrottleInfo
	(*InternalIOThrottleInfoV2)(nil),             // 108: zbs.chunk.InternalIOThrottleInfoV2
	(*RDMAUDRoute)(nil),                          // 109: zbs.chunk.RDMAUDRoute
	(*RDMAUDRouteInfo)(nil),                      // 110: zbs.chunk.RDMAUDRouteInfo
	(*MultipathInfo)(nil),                        // 111: zbs.chunk.MultipathInfo
	(*TransportInfo)(nil),                        // 112: zbs.chunk.TransportInfo
	(*RDMATransportInfo)(nil),                    // 113: zbs.chunk.RDMATransportInfo
	(*DCServerInfo)(nil),                         // 114: zbs.chunk.DCServerInfo
	(*DCServerInfos)(nil),                        // 115: zbs.chunk.DCServerInfos
	(*DCClientInfo)(nil),                         // 116: zbs.chunk.DCClientInfo
	(*DCManagerInfo)(nil),                        // 117: zbs.chunk.DCManagerInfo
	(*DCManagerInfos)(nil),                       // 118: zbs.chunk.DCManagerInfos
	(*DataChannelInfo)(nil),                      // 119: zbs.chunk.DataChannelInfo
	(*ListSinkInfoResponse_BlockLRUInfo)(nil),    // 120: zbs.chunk.ListSinkInfoResponse.BlockLRUInfo
	(*RDMATransportInfo_IORate)(nil),             // 121: zbs.chunk.RDMATransportInfo.IORate
	(*zbs.LSMMeta)(nil),                          // 122: zbs.LSMMeta
	(*zbs.ChunkSpaceInfo)(nil),                   // 123: zbs.ChunkSpaceInfo
	(zbs.AccelCopyMode)(0),                       // 124: zbs.AccelCopyMode
	(*zbs.SinkCmd)(nil),                          // 125: zbs.SinkCmd
	(*zbs.Void)(nil),                             // 126: zbs.Void
	(*zbs.GetCDPJobRequest)(nil),                 // 127: zbs.GetCDPJobRequest
	(*zbs.ListCDPJobsRequest)(nil),               // 128: zbs.ListCDPJobsRequest
	(*zbs.ChunkInstance)(nil),                    // 129: zbs.ChunkInstance
	(*zbs.ListRecoverResponse)(nil),              // 130: zbs.ListRecoverResponse
	(*zbs.CDPJobInfo)(nil),                       // 131: zbs.CDPJobInfo
	(*zbs.ListCDPJobsResponse)(nil),              // 132: zbs.ListCDPJobsResponse
	(*block.CompareExtentResponse)(nil),          // 133: zbs.block.CompareExtentResponse
	(*zbs.ListRecoverResponseV2)(nil),            // 134: zbs.ListRecoverResponseV2
}
var file_chunk_proto_depIdxs = []int32{
	0,   // 0: zbs.chunk.PartitionInfo.status:type_name -> zbs.chunk.PartitionStatus
	4,   // 1: zbs.chunk.CacheInfo.status:type_name -> zbs.chunk.CacheStatus
	6,   // 2: zbs.chunk.JournalGroupInfo.status:type_name -> zbs.chunk.JournalGroupStatus
	5,   // 3: zbs.chunk.JournalInfo.status:type_name -> zbs.chunk.JournalStatus
	20,  // 4: zbs.chunk.ListClientResponse.clients:type_name -> zbs.chunk.ClientInfo
	22,  // 5: zbs.chunk.ZbsAddressV2.instances_response:type_name -> zbs.chunk.ZbsAddress
	7,   // 6: zbs.chunk.GetDiskInfoResponse.status:type_name -> zbs.chunk.DiskStatus
	8,   // 7: zbs.chunk.GetDiskInfoResponse.disk_type:type_name -> zbs.chunk.DiskType
	15,  // 8: zbs.chunk.ListPartitionResponse.partitions:type_name -> zbs.chunk.PartitionInfo
	35,  // 9: zbs.chunk.ListPartitionResponse.workers:type_name -> zbs.chunk.WorkerInfo
	36,  // 10: zbs.chunk.ListPartitionResponseV2.instances_response:type_name -> zbs.chunk.ListPartitionResponse
	10,  // 11: zbs.chunk.SetUnhealthyRequest.scheme:type_name -> zbs.chunk.DiskNameScheme
	1,   // 12: zbs.chunk.SetUnhealthyRequest.errflag:type_name -> zbs.chunk.DiskErrFlags
	16,  // 13: zbs.chunk.ListCacheResponse.caches:type_name -> zbs.chunk.CacheInfo
	39,  // 14: zbs.chunk.ListCacheResponse.cache_stat:type_name -> zbs.chunk.CacheStat
	40,  // 15: zbs.chunk.ListCacheResponseV2.instances_response:type_name -> zbs.chunk.ListCacheResponse
	18,  // 16: zbs.chunk.ListJournalResponse.journals:type_name -> zbs.chunk.JournalInfo
	17,  // 17: zbs.chunk.ListJournalResponse.groups:type_name -> zbs.chunk.JournalGroupInfo
	42,  // 18: zbs.chunk.ListJournalResponseV2.instances_response:type_name -> zbs.chunk.ListJournalResponse
	8,   // 19: zbs.chunk.DiskInspectorStat.type:type_name -> zbs.chunk.DiskType
	44,  // 20: zbs.chunk.GetDiskInspectorStatsResponse.stats:type_name -> zbs.chunk.DiskInspectorStat
	45,  // 21: zbs.chunk.GetDiskInspectorStatsResponseV2.instances_response:type_name -> zbs.chunk.GetDiskInspectorStatsResponse
	10,  // 22: zbs.chunk.UmountDiskRequest.scheme:type_name -> zbs.chunk.DiskNameScheme
	9,   // 23: zbs.chunk.UmountDiskRequest.mode:type_name -> zbs.chunk.DiskUmountMode
	10,  // 24: zbs.chunk.DiskRequest.scheme:type_name -> zbs.chunk.DiskNameScheme
	50,  // 25: zbs.chunk.ChunkExtentInfo.blocks:type_name -> zbs.chunk.ChunkBlockInfo
	51,  // 26: zbs.chunk.ListExtentResponse.infos:type_name -> zbs.chunk.ChunkExtentInfo
	8,   // 27: zbs.chunk.RejectedDiskInfo.last_mount_type:type_name -> zbs.chunk.DiskType
	59,  // 28: zbs.chunk.ListRejectedDisksResponse.rejected_disks:type_name -> zbs.chunk.RejectedDiskInfo
	60,  // 29: zbs.chunk.ListRejectedDisksResponseV2.instances_response:type_name -> zbs.chunk.ListRejectedDisksResponse
	122, // 30: zbs.chunk.SummaryInfoResponse.lsm_meta:type_name -> zbs.LSMMeta
	123, // 31: zbs.chunk.SummaryInfoResponse.space_info:type_name -> zbs.ChunkSpaceInfo
	63,  // 32: zbs.chunk.SummaryInfoResponse.dcs_info:type_name -> zbs.chunk.DataChannelServerInfo
	64,  // 33: zbs.chunk.SummaryInfoResponse.chunk_capability:type_name -> zbs.chunk.ChunkCapability
	65,  // 34: zbs.chunk.SummaryInfoResponse.gateway_info:type_name -> zbs.chunk.GatewayServerInfo
	124, // 35: zbs.chunk.SummaryInfoResponse.accel_copy_mode:type_name -> zbs.AccelCopyMode
	66,  // 36: zbs.chunk.SummaryInfoResponseV2.instances_response:type_name -> zbs.chunk.SummaryInfoResponse
	11,  // 37: zbs.chunk.ChunkServiceStat.aurora_status:type_name -> zbs.chunk.AuroraStatus
	71,  // 38: zbs.chunk.ShowExtentResponse.disk_refs:type_name -> zbs.chunk.DiskRefInfo
	75,  // 39: zbs.chunk.GetPollingStatsResponse.polling_stats:type_name -> zbs.chunk.PollingThreadInfo
	77,  // 40: zbs.chunk.SetVolumeIOLatencyInjectionResponse.injection:type_name -> zbs.chunk.VolumeIOLatencyInjection
	77,  // 41: zbs.chunk.ListVolumeIOLatencyInjectionResponse.injections:type_name -> zbs.chunk.VolumeIOLatencyInjection
	84,  // 42: zbs.chunk.TemporaryExtentSegmentBitmapV2.bytes_bitmaps:type_name -> zbs.chunk.BytesBitmap
	86,  // 43: zbs.chunk.TemporaryExtentBitmapV1.sector_bitmaps:type_name -> zbs.chunk.SectorBitmap
	82,  // 44: zbs.chunk.GetTemporaryExtentResponse.temporary_extent:type_name -> zbs.chunk.TemporaryExtent
	88,  // 45: zbs.chunk.GetTemporaryExtentResponse.temporary_extent_bitmap:type_name -> zbs.chunk.TemporaryExtentBitmapV3
	82,  // 46: zbs.chunk.ListTemporaryExtentResponse.temporary_extents:type_name -> zbs.chunk.TemporaryExtent
	93,  // 47: zbs.chunk.GetTemporaryExtentSummaryResponseV2.instances_response:type_name -> zbs.chunk.GetTemporaryExtentSummaryResponse
	12,  // 48: zbs.chunk.SinkTask.type:type_name -> zbs.chunk.SinkTaskType
	125, // 49: zbs.chunk.ListSinkInfoResponse.cur_sink_cmd:type_name -> zbs.SinkCmd
	96,  // 50: zbs.chunk.ListSinkInfoResponse.sink_tasks:type_name -> zbs.chunk.SinkTask
	120, // 51: zbs.chunk.ListSinkInfoResponse.sinkable_lru_info:type_name -> zbs.chunk.ListSinkInfoResponse.BlockLRUInfo
	120, // 52: zbs.chunk.ListSinkInfoResponse.potential_sinkable_lru_info:type_name -> zbs.chunk.ListSinkInfoResponse.BlockLRUInfo
	98,  // 53: zbs.chunk.ListSinkInfoResponseV2.instances_response:type_name -> zbs.chunk.ListSinkInfoResponse
	101, // 54: zbs.chunk.GetCapIOThrottleResponse.infos:type_name -> zbs.chunk.CapIOThrottleInfo
	102, // 55: zbs.chunk.GetCapIOThrottleResponseV2.instances_response:type_name -> zbs.chunk.GetCapIOThrottleResponse
	104, // 56: zbs.chunk.IOPerfReadWriteDetails.from_local_read:type_name -> zbs.chunk.IOPerfStats
	104, // 57: zbs.chunk.IOPerfReadWriteDetails.from_remote_read:type_name -> zbs.chunk.IOPerfStats
	104, // 58: zbs.chunk.IOPerfReadWriteDetails.from_local_write:type_name -> zbs.chunk.IOPerfStats
	104, // 59: zbs.chunk.IOPerfReadWriteDetails.from_remote_write:type_name -> zbs.chunk.IOPerfStats
	104, // 60: zbs.chunk.InternalIOThrottleInfo.app_perf_perf:type_name -> zbs.chunk.IOPerfStats
	104, // 61: zbs.chunk.InternalIOThrottleInfo.app_cap_perf:type_name -> zbs.chunk.IOPerfStats
	104, // 62: zbs.chunk.InternalIOThrottleInfo.internal_perf_perf:type_name -> zbs.chunk.IOPerfStats
	104, // 63: zbs.chunk.InternalIOThrottleInfo.internal_cap_perf:type_name -> zbs.chunk.IOPerfStats
	106, // 64: zbs.chunk.InternalIOThrottleInfo.internal_perf_bucket_stats:type_name -> zbs.chunk.ThrottleBucketStats
	106, // 65: zbs.chunk.InternalIOThrottleInfo.internal_cap_bucket_stats:type_name -> zbs.chunk.ThrottleBucketStats
	105, // 66: zbs.chunk.InternalIOThrottleInfo.reposition_perf_replica_perf_details:type_name -> zbs.chunk.IOPerfReadWriteDetails
	105, // 67: zbs.chunk.InternalIOThrottleInfo.reposition_cap_replica_perf_details:type_name -> zbs.chunk.IOPerfReadWriteDetails
	105, // 68: zbs.chunk.InternalIOThrottleInfo.reposition_cap_ec_perf_details:type_name -> zbs.chunk.IOPerfReadWriteDetails
	105, // 69: zbs.chunk.InternalIOThrottleInfo.sink_perf_replica_perf_details:type_name -> zbs.chunk.IOPerfReadWriteDetails
	105, // 70: zbs.chunk.InternalIOThrottleInfo.sink_cap_replica_perf_details:type_name -> zbs.chunk.IOPerfReadWriteDetails
	105, // 71: zbs.chunk.InternalIOThrottleInfo.sink_cap_ec_perf_details:type_name -> zbs.chunk.IOPerfReadWriteDetails
	107, // 72: zbs.chunk.InternalIOThrottleInfoV2.instances_response:type_name -> zbs.chunk.InternalIOThrottleInfo
	109, // 73: zbs.chunk.RDMAUDRouteInfo.route:type_name -> zbs.chunk.RDMAUDRoute
	13,  // 74: zbs.chunk.TransportInfo.transport_type:type_name -> zbs.chunk.TransportType
	111, // 75: zbs.chunk.TransportInfo.mpath_info:type_name -> zbs.chunk.MultipathInfo
	121, // 76: zbs.chunk.RDMATransportInfo.send_rate:type_name -> zbs.chunk.RDMATransportInfo.IORate
	121, // 77: zbs.chunk.RDMATransportInfo.read_rate:type_name -> zbs.chunk.RDMATransportInfo.IORate
	121, // 78: zbs.chunk.RDMATransportInfo.write_rate:type_name -> zbs.chunk.RDMATransportInfo.IORate
	112, // 79: zbs.chunk.DCServerInfo.transports:type_name -> zbs.chunk.TransportInfo
	14,  // 80: zbs.chunk.DCServerInfo.version:type_name -> zbs.chunk.DataChannelVersion
	114, // 81: zbs.chunk.DCServerInfos.instances_response:type_name -> zbs.chunk.DCServerInfo
	112, // 82: zbs.chunk.DCClientInfo.transports:type_name -> zbs.chunk.TransportInfo
	116, // 83: zbs.chunk.DCManagerInfo.dc_clients:type_name -> zbs.chunk.DCClientInfo
	14,  // 84: zbs.chunk.DCManagerInfo.version:type_name -> zbs.chunk.DataChannelVersion
	117, // 85: zbs.chunk.DCManagerInfos.instances_response:type_name -> zbs.chunk.DCManagerInfo
	114, // 86: zbs.chunk.DataChannelInfo.dcs_info:type_name -> zbs.chunk.DCServerInfo
	117, // 87: zbs.chunk.DataChannelInfo.dcm_info:type_name -> zbs.chunk.DCManagerInfo
	112, // 88: zbs.chunk.RDMATransportInfo.rdma_info:extendee -> zbs.chunk.TransportInfo
	113, // 89: zbs.chunk.RDMATransportInfo.rdma_info:type_name -> zbs.chunk.RDMATransportInfo
	126, // 90: zbs.chunk.ChunkService.GetZbsAddress:input_type -> zbs.Void
	29,  // 91: zbs.chunk.ChunkService.FormatPartition:input_type -> zbs.chunk.FormatPartitionRequest
	30,  // 92: zbs.chunk.ChunkService.MountPartition:input_type -> zbs.chunk.MountPartitionRequest
	48,  // 93: zbs.chunk.ChunkService.UmountPartition:input_type -> zbs.chunk.UmountDiskRequest
	32,  // 94: zbs.chunk.ChunkService.MountCache:input_type -> zbs.chunk.MountCacheRequest
	48,  // 95: zbs.chunk.ChunkService.UmountCache:input_type -> zbs.chunk.UmountDiskRequest
	33,  // 96: zbs.chunk.ChunkService.FormatJournal:input_type -> zbs.chunk.FormatJournalRequest
	34,  // 97: zbs.chunk.ChunkService.MountJournal:input_type -> zbs.chunk.MountJournalRequest
	48,  // 98: zbs.chunk.ChunkService.UmountJournal:input_type -> zbs.chunk.UmountDiskRequest
	126, // 99: zbs.chunk.ChunkService.FlushAllJournals:input_type -> zbs.Void
	126, // 100: zbs.chunk.ChunkService.ListPartition:input_type -> zbs.Void
	126, // 101: zbs.chunk.ChunkService.ListJournal:input_type -> zbs.Void
	126, // 102: zbs.chunk.ChunkService.ListCache:input_type -> zbs.Void
	126, // 103: zbs.chunk.ChunkService.StopServer:input_type -> zbs.Void
	126, // 104: zbs.chunk.ChunkService.ListClient:input_type -> zbs.Void
	126, // 105: zbs.chunk.ChunkService.ListRecover:input_type -> zbs.Void
	126, // 106: zbs.chunk.ChunkService.ListMigrate:input_type -> zbs.Void
	54,  // 107: zbs.chunk.ChunkService.ListExtent:input_type -> zbs.chunk.ListPExtentRequest
	53,  // 108: zbs.chunk.ChunkService.CheckExtent:input_type -> zbs.chunk.PExtentRequest
	49,  // 109: zbs.chunk.ChunkService.InvalidateCache:input_type -> zbs.chunk.DiskRequest
	126, // 110: zbs.chunk.ChunkService.CheckAllExtents:input_type -> zbs.Void
	72,  // 111: zbs.chunk.ChunkService.ShowExtent:input_type -> zbs.chunk.ShowExtentRequest
	74,  // 112: zbs.chunk.ChunkService.PromoteExtent:input_type -> zbs.chunk.PromoteExtentRequest
	25,  // 113: zbs.chunk.ChunkService.GetDiskInfo:input_type -> zbs.chunk.GetDiskInfoRequest
	55,  // 114: zbs.chunk.ChunkService.InvalidateExtent:input_type -> zbs.chunk.InvalidateExtentRequest
	56,  // 115: zbs.chunk.ChunkService.SetVerifyMode:input_type -> zbs.chunk.SetVerifyModeRequest
	27,  // 116: zbs.chunk.ChunkService.UpdateSecondaryDataIP:input_type -> zbs.chunk.UpdateSecondaryDataIPRequest
	38,  // 117: zbs.chunk.ChunkService.SetUnhealthyPartition:input_type -> zbs.chunk.SetUnhealthyRequest
	49,  // 118: zbs.chunk.ChunkService.SetHealthyPartition:input_type -> zbs.chunk.DiskRequest
	38,  // 119: zbs.chunk.ChunkService.SetUnhealthyCache:input_type -> zbs.chunk.SetUnhealthyRequest
	49,  // 120: zbs.chunk.ChunkService.SetHealthyCache:input_type -> zbs.chunk.DiskRequest
	38,  // 121: zbs.chunk.ChunkService.SetUnhealthyJournal:input_type -> zbs.chunk.SetUnhealthyRequest
	49,  // 122: zbs.chunk.ChunkService.SetHealthyJournal:input_type -> zbs.chunk.DiskRequest
	31,  // 123: zbs.chunk.ChunkService.FormatCache:input_type -> zbs.chunk.FormatCacheRequest
	57,  // 124: zbs.chunk.ChunkService.QueryDisk:input_type -> zbs.chunk.QueryDiskRequest
	126, // 125: zbs.chunk.ChunkService.SummaryInfo:input_type -> zbs.Void
	28,  // 126: zbs.chunk.ChunkService.UpdateScvmModeHostDataIP:input_type -> zbs.chunk.UpdateScvmModeHostDataIPRequest
	126, // 127: zbs.chunk.ChunkService.GetChunkServiceStat:input_type -> zbs.Void
	126, // 128: zbs.chunk.ChunkService.ReloadConfig:input_type -> zbs.Void
	126, // 129: zbs.chunk.ChunkService.GetZkHosts:input_type -> zbs.Void
	49,  // 130: zbs.chunk.ChunkService.CancelUmountPartition:input_type -> zbs.chunk.DiskRequest
	49,  // 131: zbs.chunk.ChunkService.CancelUmountCache:input_type -> zbs.chunk.DiskRequest
	49,  // 132: zbs.chunk.ChunkService.InvalidatePartition:input_type -> zbs.chunk.DiskRequest
	126, // 133: zbs.chunk.ChunkService.ListRejectedDisks:input_type -> zbs.Void
	62,  // 134: zbs.chunk.ChunkService.AcceptDisk:input_type -> zbs.chunk.AcceptDiskRequest
	49,  // 135: zbs.chunk.ChunkService.IsolatePartition:input_type -> zbs.chunk.DiskRequest
	126, // 136: zbs.chunk.ChunkService.StartAurora:input_type -> zbs.Void
	126, // 137: zbs.chunk.ChunkService.StopAurora:input_type -> zbs.Void
	126, // 138: zbs.chunk.ChunkService.ReloadHostName:input_type -> zbs.Void
	126, // 139: zbs.chunk.ChunkService.GetDiskInspectorStats:input_type -> zbs.Void
	126, // 140: zbs.chunk.ChunkService.StartDiskInspectorPatrol:input_type -> zbs.Void
	126, // 141: zbs.chunk.ChunkService.StopDiskInspectorPatrol:input_type -> zbs.Void
	69,  // 142: zbs.chunk.ChunkService.StartDirtyBlockTracking:input_type -> zbs.chunk.DirtyBlockRequest
	69,  // 143: zbs.chunk.ChunkService.StopDirtyBlockTracking:input_type -> zbs.chunk.DirtyBlockRequest
	69,  // 144: zbs.chunk.ChunkService.GetDirtyBlocks:input_type -> zbs.chunk.DirtyBlockRequest
	127, // 145: zbs.chunk.ChunkService.GetCDPJob:input_type -> zbs.GetCDPJobRequest
	128, // 146: zbs.chunk.ChunkService.ListCDPJobs:input_type -> zbs.ListCDPJobsRequest
	126, // 147: zbs.chunk.ChunkService.GetPollingStats:input_type -> zbs.Void
	78,  // 148: zbs.chunk.ChunkService.SetVolumeIOLatencyInjection:input_type -> zbs.chunk.SetVolumeIOLatencyInjectionRequest
	81,  // 149: zbs.chunk.ChunkService.ClearVolumeIOLatencyInjection:input_type -> zbs.chunk.ClearVolumeIOLatencyInjectionRequest
	126, // 150: zbs.chunk.ChunkService.ListVolumeIOLatencyInjection:input_type -> zbs.Void
	126, // 151: zbs.chunk.ChunkService.GetTemporaryExtentSummary:input_type -> zbs.Void
	89,  // 152: zbs.chunk.ChunkService.GetTemporaryExtent:input_type -> zbs.chunk.GetTemporaryExtentRequest
	91,  // 153: zbs.chunk.ChunkService.ListTemporaryExtent:input_type -> zbs.chunk.ListTemporaryExtentRequest
	95,  // 154: zbs.chunk.ChunkService.ComparePExtent:input_type -> zbs.chunk.ComparePExtentRequest
	126, // 155: zbs.chunk.ChunkService.ListSinkInfo:input_type -> zbs.Void
	97,  // 156: zbs.chunk.ChunkService.SetSinkParams:input_type -> zbs.chunk.SetSinkParamsRequest
	100, // 157: zbs.chunk.ChunkService.SetCapIOThrottle:input_type -> zbs.chunk.SetCapIOThrottleRequest
	126, // 158: zbs.chunk.ChunkService.GetCapIOThrottle:input_type -> zbs.Void
	107, // 159: zbs.chunk.ChunkService.SetInternalIOThrottle:input_type -> zbs.chunk.InternalIOThrottleInfo
	126, // 160: zbs.chunk.ChunkService.GetInternalIOThrottle:input_type -> zbs.Void
	49,  // 161: zbs.chunk.ChunkService.IsolateCache:input_type -> zbs.chunk.DiskRequest
	126, // 162: zbs.chunk.ChunkService.GetRouteForRDMAUD:input_type -> zbs.Void
	129, // 163: zbs.chunk.ChunkService.GetDCManagerInfo:input_type -> zbs.ChunkInstance
	129, // 164: zbs.chunk.ChunkService.GetDCServerInfo:input_type -> zbs.ChunkInstance
	129, // 165: zbs.chunk.ChunkService.GetZbsAddressV2:input_type -> zbs.ChunkInstance
	129, // 166: zbs.chunk.ChunkService.ListPartitionV2:input_type -> zbs.ChunkInstance
	129, // 167: zbs.chunk.ChunkService.ListJournalV2:input_type -> zbs.ChunkInstance
	129, // 168: zbs.chunk.ChunkService.ListCacheV2:input_type -> zbs.ChunkInstance
	129, // 169: zbs.chunk.ChunkService.ListRecoverV2:input_type -> zbs.ChunkInstance
	129, // 170: zbs.chunk.ChunkService.ListMigrateV2:input_type -> zbs.ChunkInstance
	129, // 171: zbs.chunk.ChunkService.SummaryInfoV2:input_type -> zbs.ChunkInstance
	129, // 172: zbs.chunk.ChunkService.ListRejectedDisksV2:input_type -> zbs.ChunkInstance
	129, // 173: zbs.chunk.ChunkService.GetDiskInspectorStatsV2:input_type -> zbs.ChunkInstance
	129, // 174: zbs.chunk.ChunkService.GetTemporaryExtentSummaryV2:input_type -> zbs.ChunkInstance
	129, // 175: zbs.chunk.ChunkService.ListSinkInfoV2:input_type -> zbs.ChunkInstance
	129, // 176: zbs.chunk.ChunkService.GetCapIOThrottleV2:input_type -> zbs.ChunkInstance
	129, // 177: zbs.chunk.ChunkService.GetInternalIOThrottleV2:input_type -> zbs.ChunkInstance
	22,  // 178: zbs.chunk.ChunkService.GetZbsAddress:output_type -> zbs.chunk.ZbsAddress
	126, // 179: zbs.chunk.ChunkService.FormatPartition:output_type -> zbs.Void
	126, // 180: zbs.chunk.ChunkService.MountPartition:output_type -> zbs.Void
	126, // 181: zbs.chunk.ChunkService.UmountPartition:output_type -> zbs.Void
	126, // 182: zbs.chunk.ChunkService.MountCache:output_type -> zbs.Void
	126, // 183: zbs.chunk.ChunkService.UmountCache:output_type -> zbs.Void
	126, // 184: zbs.chunk.ChunkService.FormatJournal:output_type -> zbs.Void
	126, // 185: zbs.chunk.ChunkService.MountJournal:output_type -> zbs.Void
	126, // 186: zbs.chunk.ChunkService.UmountJournal:output_type -> zbs.Void
	126, // 187: zbs.chunk.ChunkService.FlushAllJournals:output_type -> zbs.Void
	36,  // 188: zbs.chunk.ChunkService.ListPartition:output_type -> zbs.chunk.ListPartitionResponse
	42,  // 189: zbs.chunk.ChunkService.ListJournal:output_type -> zbs.chunk.ListJournalResponse
	40,  // 190: zbs.chunk.ChunkService.ListCache:output_type -> zbs.chunk.ListCacheResponse
	126, // 191: zbs.chunk.ChunkService.StopServer:output_type -> zbs.Void
	21,  // 192: zbs.chunk.ChunkService.ListClient:output_type -> zbs.chunk.ListClientResponse
	130, // 193: zbs.chunk.ChunkService.ListRecover:output_type -> zbs.ListRecoverResponse
	130, // 194: zbs.chunk.ChunkService.ListMigrate:output_type -> zbs.ListRecoverResponse
	52,  // 195: zbs.chunk.ChunkService.ListExtent:output_type -> zbs.chunk.ListExtentResponse
	126, // 196: zbs.chunk.ChunkService.CheckExtent:output_type -> zbs.Void
	126, // 197: zbs.chunk.ChunkService.InvalidateCache:output_type -> zbs.Void
	126, // 198: zbs.chunk.ChunkService.CheckAllExtents:output_type -> zbs.Void
	73,  // 199: zbs.chunk.ChunkService.ShowExtent:output_type -> zbs.chunk.ShowExtentResponse
	126, // 200: zbs.chunk.ChunkService.PromoteExtent:output_type -> zbs.Void
	26,  // 201: zbs.chunk.ChunkService.GetDiskInfo:output_type -> zbs.chunk.GetDiskInfoResponse
	126, // 202: zbs.chunk.ChunkService.InvalidateExtent:output_type -> zbs.Void
	126, // 203: zbs.chunk.ChunkService.SetVerifyMode:output_type -> zbs.Void
	126, // 204: zbs.chunk.ChunkService.UpdateSecondaryDataIP:output_type -> zbs.Void
	126, // 205: zbs.chunk.ChunkService.SetUnhealthyPartition:output_type -> zbs.Void
	126, // 206: zbs.chunk.ChunkService.SetHealthyPartition:output_type -> zbs.Void
	126, // 207: zbs.chunk.ChunkService.SetUnhealthyCache:output_type -> zbs.Void
	126, // 208: zbs.chunk.ChunkService.SetHealthyCache:output_type -> zbs.Void
	126, // 209: zbs.chunk.ChunkService.SetUnhealthyJournal:output_type -> zbs.Void
	126, // 210: zbs.chunk.ChunkService.SetHealthyJournal:output_type -> zbs.Void
	126, // 211: zbs.chunk.ChunkService.FormatCache:output_type -> zbs.Void
	58,  // 212: zbs.chunk.ChunkService.QueryDisk:output_type -> zbs.chunk.QueryDiskResponse
	66,  // 213: zbs.chunk.ChunkService.SummaryInfo:output_type -> zbs.chunk.SummaryInfoResponse
	126, // 214: zbs.chunk.ChunkService.UpdateScvmModeHostDataIP:output_type -> zbs.Void
	68,  // 215: zbs.chunk.ChunkService.GetChunkServiceStat:output_type -> zbs.chunk.ChunkServiceStat
	126, // 216: zbs.chunk.ChunkService.ReloadConfig:output_type -> zbs.Void
	24,  // 217: zbs.chunk.ChunkService.GetZkHosts:output_type -> zbs.chunk.GetZkHostsResponse
	126, // 218: zbs.chunk.ChunkService.CancelUmountPartition:output_type -> zbs.Void
	126, // 219: zbs.chunk.ChunkService.CancelUmountCache:output_type -> zbs.Void
	126, // 220: zbs.chunk.ChunkService.InvalidatePartition:output_type -> zbs.Void
	60,  // 221: zbs.chunk.ChunkService.ListRejectedDisks:output_type -> zbs.chunk.ListRejectedDisksResponse
	126, // 222: zbs.chunk.ChunkService.AcceptDisk:output_type -> zbs.Void
	126, // 223: zbs.chunk.ChunkService.IsolatePartition:output_type -> zbs.Void
	126, // 224: zbs.chunk.ChunkService.StartAurora:output_type -> zbs.Void
	126, // 225: zbs.chunk.ChunkService.StopAurora:output_type -> zbs.Void
	126, // 226: zbs.chunk.ChunkService.ReloadHostName:output_type -> zbs.Void
	45,  // 227: zbs.chunk.ChunkService.GetDiskInspectorStats:output_type -> zbs.chunk.GetDiskInspectorStatsResponse
	126, // 228: zbs.chunk.ChunkService.StartDiskInspectorPatrol:output_type -> zbs.Void
	126, // 229: zbs.chunk.ChunkService.StopDiskInspectorPatrol:output_type -> zbs.Void
	126, // 230: zbs.chunk.ChunkService.StartDirtyBlockTracking:output_type -> zbs.Void
	126, // 231: zbs.chunk.ChunkService.StopDirtyBlockTracking:output_type -> zbs.Void
	70,  // 232: zbs.chunk.ChunkService.GetDirtyBlocks:output_type -> zbs.chunk.DirtyBlockResponse
	131, // 233: zbs.chunk.ChunkService.GetCDPJob:output_type -> zbs.CDPJobInfo
	132, // 234: zbs.chunk.ChunkService.ListCDPJobs:output_type -> zbs.ListCDPJobsResponse
	76,  // 235: zbs.chunk.ChunkService.GetPollingStats:output_type -> zbs.chunk.GetPollingStatsResponse
	79,  // 236: zbs.chunk.ChunkService.SetVolumeIOLatencyInjection:output_type -> zbs.chunk.SetVolumeIOLatencyInjectionResponse
	126, // 237: zbs.chunk.ChunkService.ClearVolumeIOLatencyInjection:output_type -> zbs.Void
	80,  // 238: zbs.chunk.ChunkService.ListVolumeIOLatencyInjection:output_type -> zbs.chunk.ListVolumeIOLatencyInjectionResponse
	93,  // 239: zbs.chunk.ChunkService.GetTemporaryExtentSummary:output_type -> zbs.chunk.GetTemporaryExtentSummaryResponse
	90,  // 240: zbs.chunk.ChunkService.GetTemporaryExtent:output_type -> zbs.chunk.GetTemporaryExtentResponse
	92,  // 241: zbs.chunk.ChunkService.ListTemporaryExtent:output_type -> zbs.chunk.ListTemporaryExtentResponse
	133, // 242: zbs.chunk.ChunkService.ComparePExtent:output_type -> zbs.block.CompareExtentResponse
	98,  // 243: zbs.chunk.ChunkService.ListSinkInfo:output_type -> zbs.chunk.ListSinkInfoResponse
	126, // 244: zbs.chunk.ChunkService.SetSinkParams:output_type -> zbs.Void
	126, // 245: zbs.chunk.ChunkService.SetCapIOThrottle:output_type -> zbs.Void
	102, // 246: zbs.chunk.ChunkService.GetCapIOThrottle:output_type -> zbs.chunk.GetCapIOThrottleResponse
	126, // 247: zbs.chunk.ChunkService.SetInternalIOThrottle:output_type -> zbs.Void
	107, // 248: zbs.chunk.ChunkService.GetInternalIOThrottle:output_type -> zbs.chunk.InternalIOThrottleInfo
	126, // 249: zbs.chunk.ChunkService.IsolateCache:output_type -> zbs.Void
	110, // 250: zbs.chunk.ChunkService.GetRouteForRDMAUD:output_type -> zbs.chunk.RDMAUDRouteInfo
	118, // 251: zbs.chunk.ChunkService.GetDCManagerInfo:output_type -> zbs.chunk.DCManagerInfos
	115, // 252: zbs.chunk.ChunkService.GetDCServerInfo:output_type -> zbs.chunk.DCServerInfos
	23,  // 253: zbs.chunk.ChunkService.GetZbsAddressV2:output_type -> zbs.chunk.ZbsAddressV2
	37,  // 254: zbs.chunk.ChunkService.ListPartitionV2:output_type -> zbs.chunk.ListPartitionResponseV2
	43,  // 255: zbs.chunk.ChunkService.ListJournalV2:output_type -> zbs.chunk.ListJournalResponseV2
	41,  // 256: zbs.chunk.ChunkService.ListCacheV2:output_type -> zbs.chunk.ListCacheResponseV2
	134, // 257: zbs.chunk.ChunkService.ListRecoverV2:output_type -> zbs.ListRecoverResponseV2
	134, // 258: zbs.chunk.ChunkService.ListMigrateV2:output_type -> zbs.ListRecoverResponseV2
	67,  // 259: zbs.chunk.ChunkService.SummaryInfoV2:output_type -> zbs.chunk.SummaryInfoResponseV2
	61,  // 260: zbs.chunk.ChunkService.ListRejectedDisksV2:output_type -> zbs.chunk.ListRejectedDisksResponseV2
	46,  // 261: zbs.chunk.ChunkService.GetDiskInspectorStatsV2:output_type -> zbs.chunk.GetDiskInspectorStatsResponseV2
	94,  // 262: zbs.chunk.ChunkService.GetTemporaryExtentSummaryV2:output_type -> zbs.chunk.GetTemporaryExtentSummaryResponseV2
	99,  // 263: zbs.chunk.ChunkService.ListSinkInfoV2:output_type -> zbs.chunk.ListSinkInfoResponseV2
	103, // 264: zbs.chunk.ChunkService.GetCapIOThrottleV2:output_type -> zbs.chunk.GetCapIOThrottleResponseV2
	108, // 265: zbs.chunk.ChunkService.GetInternalIOThrottleV2:output_type -> zbs.chunk.InternalIOThrottleInfoV2
	178, // [178:266] is the sub-list for method output_type
	90,  // [90:178] is the sub-list for method input_type
	89,  // [89:90] is the sub-list for extension type_name
	88,  // [88:89] is the sub-list for extension extendee
	0,   // [0:88] is the sub-list for field type_name
}

func init() { file_chunk_proto_init() }
func file_chunk_proto_init() {
	if File_chunk_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_chunk_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartitionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CacheInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JournalGroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JournalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetaAddr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClientResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZbsAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZbsAddressV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetZkHostsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiskInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiskInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSecondaryDataIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateScvmModeHostDataIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormatPartitionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MountPartitionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormatCacheRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MountCacheRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormatJournalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MountJournalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPartitionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPartitionResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUnhealthyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CacheStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCacheResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCacheResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJournalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJournalResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiskInspectorStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiskInspectorStatsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiskInspectorStatsResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PathRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UmountDiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkBlockInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkExtentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExtentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvalidateExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetVerifyModeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDiskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RejectedDiskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRejectedDisksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRejectedDisksResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptDiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataChannelServerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkCapability); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayServerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SummaryInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SummaryInfoResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkServiceStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirtyBlockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirtyBlockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiskRefInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShowExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShowExtentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PromoteExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollingThreadInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPollingStatsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumeIOLatencyInjection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetVolumeIOLatencyInjectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetVolumeIOLatencyInjectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVolumeIOLatencyInjectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearVolumeIOLatencyInjectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemporaryExtent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemporaryUnmapBitmapSegment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BytesBitmap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemporaryExtentSegmentBitmapV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectorBitmap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemporaryExtentBitmapV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemporaryExtentBitmapV3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemporaryExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemporaryExtentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemporaryExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemporaryExtentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemporaryExtentSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemporaryExtentSummaryResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComparePExtentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SinkTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetSinkParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSinkInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSinkInfoResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCapIOThrottleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CapIOThrottleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCapIOThrottleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCapIOThrottleResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IOPerfStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IOPerfReadWriteDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThrottleBucketStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalIOThrottleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalIOThrottleInfoV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RDMAUDRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RDMAUDRouteInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultipathInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransportInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RDMATransportInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCServerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCServerInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCClientInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCManagerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCManagerInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataChannelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSinkInfoResponse_BlockLRUInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_chunk_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RDMATransportInfo_IORate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_chunk_proto_rawDesc,
			NumEnums:      15,
			NumMessages:   107,
			NumExtensions: 1,
			NumServices:   1,
		},
		GoTypes:           file_chunk_proto_goTypes,
		DependencyIndexes: file_chunk_proto_depIdxs,
		EnumInfos:         file_chunk_proto_enumTypes,
		MessageInfos:      file_chunk_proto_msgTypes,
		ExtensionInfos:    file_chunk_proto_extTypes,
	}.Build()
	File_chunk_proto = out.File
	file_chunk_proto_rawDesc = nil
	file_chunk_proto_goTypes = nil
	file_chunk_proto_depIdxs = nil
}
