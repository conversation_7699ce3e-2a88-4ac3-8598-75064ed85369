# Protocol Documentation
<a name="top"></a>

## Table of Contents

- [cdp.proto](#cdp-proto)
    - [CDPErrorDetail](#zbs-CDPErrorDetail)
    - [CDPJobInfo](#zbs-CDPJobInfo)
    - [CDPJobMetrics](#zbs-CDPJobMetrics)
    - [CDPJobStageUpdate](#zbs-CDPJobStageUpdate)
    - [CDPJobUpdate](#zbs-CDPJobUpdate)
    - [CDPJobUpdateDone](#zbs-CDPJobUpdateDone)
    - [CDPRemoteInfo](#zbs-CDPRemoteInfo)
    - [CDPVolumeID](#zbs-CDPVolumeID)
    - [CancelCDPJobRequest](#zbs-CancelCDPJobRequest)
    - [CancelCDPJobsByGroupRequest](#zbs-CancelCDPJobsByGroupRequest)
    - [CreateCDPJobRequest](#zbs-CreateCDPJobRequest)
    - [CreateCDPJobsByGroupRequest](#zbs-CreateCDPJobsByGroupRequest)
    - [CreateCDPJobsByGroupResponse](#zbs-CreateCDPJobsByGroupResponse)
    - [FinishCDPJobRequest](#zbs-FinishCDPJobRequest)
    - [FinishCDPJobsByGroupRequest](#zbs-FinishCDPJobsByGroupRequest)
    - [GetCDPJobByVolumeRequest](#zbs-GetCDPJobByVolumeRequest)
    - [GetCDPJobRequest](#zbs-GetCDPJobRequest)
    - [ListCDPJobsRequest](#zbs-ListCDPJobsRequest)
    - [ListCDPJobsResponse](#zbs-ListCDPJobsResponse)
  
    - [CDPJobStage](#zbs-CDPJobStage)
  
- [chunk.proto](#chunk-proto)
    - [AcceptDiskRequest](#zbs-chunk-AcceptDiskRequest)
    - [CacheInfo](#zbs-chunk-CacheInfo)
    - [CacheStat](#zbs-chunk-CacheStat)
    - [ChunkBlockInfo](#zbs-chunk-ChunkBlockInfo)
    - [ChunkCapability](#zbs-chunk-ChunkCapability)
    - [ChunkExtentInfo](#zbs-chunk-ChunkExtentInfo)
    - [ChunkServiceStat](#zbs-chunk-ChunkServiceStat)
    - [ClearVolumeIOLatencyInjectionRequest](#zbs-chunk-ClearVolumeIOLatencyInjectionRequest)
    - [ClientInfo](#zbs-chunk-ClientInfo)
    - [CompareExtentRequest](#zbs-chunk-CompareExtentRequest)
    - [CompareExtentResponse](#zbs-chunk-CompareExtentResponse)
    - [DCClientInfo](#zbs-chunk-DCClientInfo)
    - [DCManagerInfo](#zbs-chunk-DCManagerInfo)
    - [DCServerInfo](#zbs-chunk-DCServerInfo)
    - [DataChannelInfo](#zbs-chunk-DataChannelInfo)
    - [DataChannelServerInfo](#zbs-chunk-DataChannelServerInfo)
    - [DirtyBlockRequest](#zbs-chunk-DirtyBlockRequest)
    - [DirtyBlockResponse](#zbs-chunk-DirtyBlockResponse)
    - [DiskInspectorStat](#zbs-chunk-DiskInspectorStat)
    - [DiskRefInfo](#zbs-chunk-DiskRefInfo)
    - [DiskRequest](#zbs-chunk-DiskRequest)
    - [FormatCacheRequest](#zbs-chunk-FormatCacheRequest)
    - [FormatJournalRequest](#zbs-chunk-FormatJournalRequest)
    - [FormatPartitionRequest](#zbs-chunk-FormatPartitionRequest)
    - [GatewayServerInfo](#zbs-chunk-GatewayServerInfo)
    - [GetDiskInfoRequest](#zbs-chunk-GetDiskInfoRequest)
    - [GetDiskInfoResponse](#zbs-chunk-GetDiskInfoResponse)
    - [GetDiskInspectorStatsResponse](#zbs-chunk-GetDiskInspectorStatsResponse)
    - [GetPollingStatsResponse](#zbs-chunk-GetPollingStatsResponse)
    - [GetTemporaryExtentRequest](#zbs-chunk-GetTemporaryExtentRequest)
    - [GetTemporaryExtentResponse](#zbs-chunk-GetTemporaryExtentResponse)
    - [GetTemporaryExtentSummaryResponse](#zbs-chunk-GetTemporaryExtentSummaryResponse)
    - [GetZkHostsResponse](#zbs-chunk-GetZkHostsResponse)
    - [InvalidateExtentRequest](#zbs-chunk-InvalidateExtentRequest)
    - [JournalGroupInfo](#zbs-chunk-JournalGroupInfo)
    - [JournalInfo](#zbs-chunk-JournalInfo)
    - [ListCacheResponse](#zbs-chunk-ListCacheResponse)
    - [ListClientResponse](#zbs-chunk-ListClientResponse)
    - [ListExtentResponse](#zbs-chunk-ListExtentResponse)
    - [ListJournalResponse](#zbs-chunk-ListJournalResponse)
    - [ListPartitionResponse](#zbs-chunk-ListPartitionResponse)
    - [ListRejectedDisksResponse](#zbs-chunk-ListRejectedDisksResponse)
    - [ListTemporaryExtentRequest](#zbs-chunk-ListTemporaryExtentRequest)
    - [ListTemporaryExtentResponse](#zbs-chunk-ListTemporaryExtentResponse)
    - [ListVolumeIOLatencyInjectionResponse](#zbs-chunk-ListVolumeIOLatencyInjectionResponse)
    - [MetaAddr](#zbs-chunk-MetaAddr)
    - [MountCacheRequest](#zbs-chunk-MountCacheRequest)
    - [MountJournalRequest](#zbs-chunk-MountJournalRequest)
    - [MountPartitionRequest](#zbs-chunk-MountPartitionRequest)
    - [PExtentRequest](#zbs-chunk-PExtentRequest)
    - [PartitionInfo](#zbs-chunk-PartitionInfo)
    - [PathRequest](#zbs-chunk-PathRequest)
    - [PollingThreadInfo](#zbs-chunk-PollingThreadInfo)
    - [QueryDiskRequest](#zbs-chunk-QueryDiskRequest)
    - [QueryDiskResponse](#zbs-chunk-QueryDiskResponse)
    - [RDMATransportInfo](#zbs-chunk-RDMATransportInfo)
    - [RDMATransportInfo.IORate](#zbs-chunk-RDMATransportInfo-IORate)
    - [RejectedDiskInfo](#zbs-chunk-RejectedDiskInfo)
    - [SectorBitmap](#zbs-chunk-SectorBitmap)
    - [SetUnhealthyRequest](#zbs-chunk-SetUnhealthyRequest)
    - [SetVerifyModeRequest](#zbs-chunk-SetVerifyModeRequest)
    - [SetVolumeIOLatencyInjectionRequest](#zbs-chunk-SetVolumeIOLatencyInjectionRequest)
    - [SetVolumeIOLatencyInjectionResponse](#zbs-chunk-SetVolumeIOLatencyInjectionResponse)
    - [ShowExtentRequest](#zbs-chunk-ShowExtentRequest)
    - [ShowExtentResponse](#zbs-chunk-ShowExtentResponse)
    - [SummaryInfoResponse](#zbs-chunk-SummaryInfoResponse)
    - [TemporaryExtent](#zbs-chunk-TemporaryExtent)
    - [TemporaryExtentBitmap](#zbs-chunk-TemporaryExtentBitmap)
    - [TransportInfo](#zbs-chunk-TransportInfo)
    - [UmountDiskRequest](#zbs-chunk-UmountDiskRequest)
    - [UpdateScvmModeHostDataIPRequest](#zbs-chunk-UpdateScvmModeHostDataIPRequest)
    - [UpdateSecondaryDataIPRequest](#zbs-chunk-UpdateSecondaryDataIPRequest)
    - [VolumeIOLatencyInjection](#zbs-chunk-VolumeIOLatencyInjection)
    - [WorkerInfo](#zbs-chunk-WorkerInfo)
    - [ZbsAddress](#zbs-chunk-ZbsAddress)
  
    - [AuroraStatus](#zbs-chunk-AuroraStatus)
    - [CacheStatus](#zbs-chunk-CacheStatus)
    - [DataChannelVersion](#zbs-chunk-DataChannelVersion)
    - [DiskErrFlags](#zbs-chunk-DiskErrFlags)
    - [DiskNameScheme](#zbs-chunk-DiskNameScheme)
    - [DiskStatus](#zbs-chunk-DiskStatus)
    - [DiskType](#zbs-chunk-DiskType)
    - [DiskUmountMode](#zbs-chunk-DiskUmountMode)
    - [DiskWarnFlags](#zbs-chunk-DiskWarnFlags)
    - [JournalGroupStatus](#zbs-chunk-JournalGroupStatus)
    - [JournalStatus](#zbs-chunk-JournalStatus)
    - [LSMCapability](#zbs-chunk-LSMCapability)
    - [PartitionStatus](#zbs-chunk-PartitionStatus)
    - [TransportType](#zbs-chunk-TransportType)
  
    - [ChunkService](#zbs-chunk-ChunkService)
  
- [common.proto](#common-proto)
    - [AccessPerf](#zbs-AccessPerf)
    - [Address](#zbs-Address)
    - [Addresses](#zbs-Addresses)
    - [BlockDevicePerf](#zbs-BlockDevicePerf)
    - [Bool](#zbs-Bool)
    - [Capacity](#zbs-Capacity)
    - [Chunk](#zbs-Chunk)
    - [ChunkConnectivity](#zbs-ChunkConnectivity)
    - [ChunkId](#zbs-ChunkId)
    - [ChunkIsolateFlag](#zbs-ChunkIsolateFlag)
    - [ChunkIsolateInfo](#zbs-ChunkIsolateInfo)
    - [ChunkIsolateRecord](#zbs-ChunkIsolateRecord)
    - [ChunkPerf](#zbs-ChunkPerf)
    - [ChunkPids](#zbs-ChunkPids)
    - [ChunkSpaceInfo](#zbs-ChunkSpaceInfo)
    - [ChunkTopology](#zbs-ChunkTopology)
    - [Chunks](#zbs-Chunks)
    - [CleanChunkInfoCmd](#zbs-CleanChunkInfoCmd)
    - [CounterInfo](#zbs-CounterInfo)
    - [CpuUsage](#zbs-CpuUsage)
    - [DataChannelConnectivity](#zbs-DataChannelConnectivity)
    - [Dimension](#zbs-Dimension)
    - [DiskPerf](#zbs-DiskPerf)
    - [DiskUsage](#zbs-DiskUsage)
    - [DiskUsages](#zbs-DiskUsages)
    - [GFlagsVar](#zbs-GFlagsVar)
    - [GFlagsVarName](#zbs-GFlagsVarName)
    - [GFlagsVars](#zbs-GFlagsVars)
    - [GcCmd](#zbs-GcCmd)
    - [GetPExtentInfoCmd](#zbs-GetPExtentInfoCmd)
    - [HeapProfilerRequest](#zbs-HeapProfilerRequest)
    - [JournalPerf](#zbs-JournalPerf)
    - [LSMDBPerf](#zbs-LSMDBPerf)
    - [LSMMeta](#zbs-LSMMeta)
    - [LSMPerf](#zbs-LSMPerf)
    - [LSMVersion](#zbs-LSMVersion)
    - [Label](#zbs-Label)
    - [Labels](#zbs-Labels)
    - [Lease](#zbs-Lease)
    - [License](#zbs-License)
    - [LicenseCapabilities](#zbs-LicenseCapabilities)
    - [LicenseCertificate](#zbs-LicenseCertificate)
    - [LicenseCertificateV1](#zbs-LicenseCertificateV1)
    - [LicenseCertificateV2](#zbs-LicenseCertificateV2)
    - [LicenseRequest](#zbs-LicenseRequest)
    - [LicenseV1](#zbs-LicenseV1)
    - [LicenseV2](#zbs-LicenseV2)
    - [ListCounterRequest](#zbs-ListCounterRequest)
    - [ListCounterResponse](#zbs-ListCounterResponse)
    - [ListRecoverResponse](#zbs-ListRecoverResponse)
    - [ListThreadCacheResponse](#zbs-ListThreadCacheResponse)
    - [LocalIOPerf](#zbs-LocalIOPerf)
    - [MaintenanceCmd](#zbs-MaintenanceCmd)
    - [MemUsage](#zbs-MemUsage)
    - [NegotiationAbility](#zbs-NegotiationAbility)
    - [NetworkUsage](#zbs-NetworkUsage)
    - [NetworkUsages](#zbs-NetworkUsages)
    - [NodePerf](#zbs-NodePerf)
    - [PExtent](#zbs-PExtent)
    - [PExtentInfo](#zbs-PExtentInfo)
    - [PExtentResp](#zbs-PExtentResp)
    - [Pagination](#zbs-Pagination)
    - [Position](#zbs-Position)
    - [ProfilerRequest](#zbs-ProfilerRequest)
    - [RangeU64](#zbs-RangeU64)
    - [RecoverCmd](#zbs-RecoverCmd)
    - [RecoverFromTemporaryReplica](#zbs-RecoverFromTemporaryReplica)
    - [RecoverInfo](#zbs-RecoverInfo)
    - [RecoverPerf](#zbs-RecoverPerf)
    - [RecoverSummary](#zbs-RecoverSummary)
    - [ReplicaIOPerf](#zbs-ReplicaIOPerf)
    - [RevokeCmd](#zbs-RevokeCmd)
    - [RpcErrorDetail](#zbs-RpcErrorDetail)
    - [RpcStatus](#zbs-RpcStatus)
    - [SessionInfo](#zbs-SessionInfo)
    - [SessionInfos](#zbs-SessionInfos)
    - [SetVLOGRequest](#zbs-SetVLOGRequest)
    - [StatusRequest](#zbs-StatusRequest)
    - [StatusResponse](#zbs-StatusResponse)
    - [StoragePerf](#zbs-StoragePerf)
    - [StoragePool](#zbs-StoragePool)
    - [StoragePoolId](#zbs-StoragePoolId)
    - [StoragePools](#zbs-StoragePools)
    - [StorageSpace](#zbs-StorageSpace)
    - [TemporaryReplica](#zbs-TemporaryReplica)
    - [ThreadCacheInfo](#zbs-ThreadCacheInfo)
    - [TimeSpec](#zbs-TimeSpec)
    - [TopoObj](#zbs-TopoObj)
    - [TopoObjId](#zbs-TopoObjId)
    - [TopoObjs](#zbs-TopoObjs)
    - [TowerLicense](#zbs-TowerLicense)
    - [TowerLicenseCertificate](#zbs-TowerLicenseCertificate)
    - [UID](#zbs-UID)
    - [VExtent](#zbs-VExtent)
    - [VExtentLease](#zbs-VExtentLease)
    - [Void](#zbs-Void)
  
    - [ChunkHealthyStatus](#zbs-ChunkHealthyStatus)
    - [ChunkState](#zbs-ChunkState)
    - [ChunkStatus](#zbs-ChunkStatus)
    - [DataChannelStatus](#zbs-DataChannelStatus)
    - [LSMState](#zbs-LSMState)
    - [LicenseType](#zbs-LicenseType)
    - [MigrateMode](#zbs-MigrateMode)
    - [PExtentStatus](#zbs-PExtentStatus)
    - [PricingType](#zbs-PricingType)
    - [RecoverMode](#zbs-RecoverMode)
    - [RecoverState](#zbs-RecoverState)
    - [SoftwareEdition](#zbs-SoftwareEdition)
    - [TopoType](#zbs-TopoType)
  
    - [File-level Extensions](#common-proto-extensions)
  
    - [CommonService](#zbs-CommonService)
    - [SystemManagementService](#zbs-SystemManagementService)
  
- [descriptor.proto](#descriptor-proto)
    - [DescriptorProto](#google-protobuf-DescriptorProto)
    - [DescriptorProto.ExtensionRange](#google-protobuf-DescriptorProto-ExtensionRange)
    - [EnumDescriptorProto](#google-protobuf-EnumDescriptorProto)
    - [EnumOptions](#google-protobuf-EnumOptions)
    - [EnumValueDescriptorProto](#google-protobuf-EnumValueDescriptorProto)
    - [EnumValueOptions](#google-protobuf-EnumValueOptions)
    - [FieldDescriptorProto](#google-protobuf-FieldDescriptorProto)
    - [FieldOptions](#google-protobuf-FieldOptions)
    - [FileDescriptorProto](#google-protobuf-FileDescriptorProto)
    - [FileDescriptorSet](#google-protobuf-FileDescriptorSet)
    - [FileOptions](#google-protobuf-FileOptions)
    - [MessageOptions](#google-protobuf-MessageOptions)
    - [MethodDescriptorProto](#google-protobuf-MethodDescriptorProto)
    - [MethodOptions](#google-protobuf-MethodOptions)
    - [ServiceDescriptorProto](#google-protobuf-ServiceDescriptorProto)
    - [ServiceOptions](#google-protobuf-ServiceOptions)
    - [SourceCodeInfo](#google-protobuf-SourceCodeInfo)
    - [SourceCodeInfo.Location](#google-protobuf-SourceCodeInfo-Location)
    - [UninterpretedOption](#google-protobuf-UninterpretedOption)
    - [UninterpretedOption.NamePart](#google-protobuf-UninterpretedOption-NamePart)
  
    - [FieldDescriptorProto.Label](#google-protobuf-FieldDescriptorProto-Label)
    - [FieldDescriptorProto.Type](#google-protobuf-FieldDescriptorProto-Type)
    - [FieldOptions.CType](#google-protobuf-FieldOptions-CType)
    - [FileOptions.OptimizeMode](#google-protobuf-FileOptions-OptimizeMode)
  
- [error.proto](#error-proto)
    - [ErrorCode](#zbs-ErrorCode)
    - [UserCode](#zbs-UserCode)
  
- [meta.proto](#meta-proto)
    - [AbilityState](#zbs-meta-AbilityState)
    - [AccessDataReportRequest](#zbs-meta-AccessDataReportRequest)
    - [AccessKeepAliveRequest](#zbs-meta-AccessKeepAliveRequest)
    - [AccessKeepAliveResponse](#zbs-meta-AccessKeepAliveResponse)
    - [AccessPoint](#zbs-meta-AccessPoint)
    - [AcquireNVMFIOPermissionRequest](#zbs-meta-AcquireNVMFIOPermissionRequest)
    - [AddChunkToStoragePoolRequest](#zbs-meta-AddChunkToStoragePoolRequest)
    - [AddLunAllowedInitiatorsRequest](#zbs-meta-AddLunAllowedInitiatorsRequest)
    - [AddReplicaRequest](#zbs-meta-AddReplicaRequest)
    - [BatchedSetAttrRequest](#zbs-meta-BatchedSetAttrRequest)
    - [BatchedSetAttrResponse](#zbs-meta-BatchedSetAttrResponse)
    - [CancelChunkRemovingRequest](#zbs-meta-CancelChunkRemovingRequest)
    - [CheckLeaseRequest](#zbs-meta-CheckLeaseRequest)
    - [CheckLeaseResponse](#zbs-meta-CheckLeaseResponse)
    - [ChunkDataReportRequest](#zbs-meta-ChunkDataReportRequest)
    - [ChunkKeepAliveRequest](#zbs-meta-ChunkKeepAliveRequest)
    - [ChunkKeepAliveResponse](#zbs-meta-ChunkKeepAliveResponse)
    - [ChunkTemporaryReplicaInfo](#zbs-meta-ChunkTemporaryReplicaInfo)
    - [ChunksResponse](#zbs-meta-ChunksResponse)
    - [ClearVhostIOPermissionRequest](#zbs-meta-ClearVhostIOPermissionRequest)
    - [CloneVolumeRequest](#zbs-meta-CloneVolumeRequest)
    - [ClusterInfo](#zbs-meta-ClusterInfo)
    - [ClusterPerf](#zbs-meta-ClusterPerf)
    - [ClusterStatus](#zbs-meta-ClusterStatus)
    - [ClusterSummary](#zbs-meta-ClusterSummary)
    - [ConfigUpdate](#zbs-meta-ConfigUpdate)
    - [ConsistencyFile](#zbs-meta-ConsistencyFile)
    - [ConsistencyGroup](#zbs-meta-ConsistencyGroup)
    - [ConsistencyGroupSnapshot](#zbs-meta-ConsistencyGroupSnapshot)
    - [ConsistencyLUN](#zbs-meta-ConsistencyLUN)
    - [ConsistencyNamespace](#zbs-meta-ConsistencyNamespace)
    - [ConsistencyVolume](#zbs-meta-ConsistencyVolume)
    - [ConsistencyVolumeSnapshot](#zbs-meta-ConsistencyVolumeSnapshot)
    - [ConvertLunIntoFileRequest](#zbs-meta-ConvertLunIntoFileRequest)
    - [ConvertVolumeIntoFileRequest](#zbs-meta-ConvertVolumeIntoFileRequest)
    - [ConvertVolumeIntoLunRequest](#zbs-meta-ConvertVolumeIntoLunRequest)
    - [CowPExtentRequest](#zbs-meta-CowPExtentRequest)
    - [CreateConsistencyGroupRequest](#zbs-meta-CreateConsistencyGroupRequest)
    - [CreateConsistencyGroupSnapshotRequest](#zbs-meta-CreateConsistencyGroupSnapshotRequest)
    - [CreateHardlinkRequest](#zbs-meta-CreateHardlinkRequest)
    - [CreateISCSILunRequest](#zbs-meta-CreateISCSILunRequest)
    - [CreateISCSISnapshotRequest](#zbs-meta-CreateISCSISnapshotRequest)
    - [CreateISCSITargetRequest](#zbs-meta-CreateISCSITargetRequest)
    - [CreateInodeRequest](#zbs-meta-CreateInodeRequest)
    - [CreateNFSSnapshotRequest](#zbs-meta-CreateNFSSnapshotRequest)
    - [CreateNVMFDistNamespaceRequest](#zbs-meta-CreateNVMFDistNamespaceRequest)
    - [CreateNVMFDistSubsystemRequest](#zbs-meta-CreateNVMFDistSubsystemRequest)
    - [CreateNVMFSnapshotRequest](#zbs-meta-CreateNVMFSnapshotRequest)
    - [CreateSnapshotRequest](#zbs-meta-CreateSnapshotRequest)
    - [CreateStoragePoolRequest](#zbs-meta-CreateStoragePoolRequest)
    - [CreateTopoObjRequest](#zbs-meta-CreateTopoObjRequest)
    - [CreateVolumeRequest](#zbs-meta-CreateVolumeRequest)
    - [DbItem](#zbs-meta-DbItem)
    - [DeleteConsistencyGroupRequest](#zbs-meta-DeleteConsistencyGroupRequest)
    - [DeleteConsistencyGroupSnapshotRequest](#zbs-meta-DeleteConsistencyGroupSnapshotRequest)
    - [DeleteInodeRequest](#zbs-meta-DeleteInodeRequest)
    - [DeleteInodeResponse](#zbs-meta-DeleteInodeResponse)
    - [DistNamespaceGroupPath](#zbs-meta-DistNamespaceGroupPath)
    - [DistNamespacePath](#zbs-meta-DistNamespacePath)
    - [DropISCSIConnCmd](#zbs-meta-DropISCSIConnCmd)
    - [DumpDbRequest](#zbs-meta-DumpDbRequest)
    - [DumpDbResponse](#zbs-meta-DumpDbResponse)
    - [FindInodeRequest](#zbs-meta-FindInodeRequest)
    - [FindPExtentRequest](#zbs-meta-FindPExtentRequest)
    - [FindVolumeRequest](#zbs-meta-FindVolumeRequest)
    - [GetAccessRecordRequest](#zbs-meta-GetAccessRecordRequest)
    - [GetAccessRecordResponse](#zbs-meta-GetAccessRecordResponse)
    - [GetAllocPExtentRequest](#zbs-meta-GetAllocPExtentRequest)
    - [GetChunkConnectivitiesResponse](#zbs-meta-GetChunkConnectivitiesResponse)
    - [GetChunkIsolateInfoResponse](#zbs-meta-GetChunkIsolateInfoResponse)
    - [GetChunkIsolateInfoResponseItem](#zbs-meta-GetChunkIsolateInfoResponseItem)
    - [GetLeaseRequest](#zbs-meta-GetLeaseRequest)
    - [GetMaintenanceInfoResponse](#zbs-meta-GetMaintenanceInfoResponse)
    - [GetNVMFAccessRecordRequest](#zbs-meta-GetNVMFAccessRecordRequest)
    - [GetNVMFAccessRecordResponse](#zbs-meta-GetNVMFAccessRecordResponse)
    - [GetNVMFOptimizedAccessRequest](#zbs-meta-GetNVMFOptimizedAccessRequest)
    - [GetNVMFVolumeAccessRecordsRequest](#zbs-meta-GetNVMFVolumeAccessRecordsRequest)
    - [GetPExtentRefResponse](#zbs-meta-GetPExtentRefResponse)
    - [GetPExtentRequest](#zbs-meta-GetPExtentRequest)
    - [GetServicePortalRequest](#zbs-meta-GetServicePortalRequest)
    - [GetTemporaryReplicaRequest](#zbs-meta-GetTemporaryReplicaRequest)
    - [GetTemporaryReplicaResponse](#zbs-meta-GetTemporaryReplicaResponse)
    - [GetTemporaryReplicaSummaryResponse](#zbs-meta-GetTemporaryReplicaSummaryResponse)
    - [GetVExtentLeaseRequest](#zbs-meta-GetVExtentLeaseRequest)
    - [GetVTableResponse](#zbs-meta-GetVTableResponse)
    - [GetVhostIOPermissionResponse](#zbs-meta-GetVhostIOPermissionResponse)
    - [IOThrottleConfig](#zbs-meta-IOThrottleConfig)
    - [ISCSIAccessRecord](#zbs-meta-ISCSIAccessRecord)
    - [ISCSIAccessRecords](#zbs-meta-ISCSIAccessRecords)
    - [ISCSIConfigUpdate](#zbs-meta-ISCSIConfigUpdate)
    - [ISCSIConnection](#zbs-meta-ISCSIConnection)
    - [ISCSIConnectionRecord](#zbs-meta-ISCSIConnectionRecord)
    - [ISCSIConnections](#zbs-meta-ISCSIConnections)
    - [ISCSILun](#zbs-meta-ISCSILun)
    - [ISCSILunsResponse](#zbs-meta-ISCSILunsResponse)
    - [ISCSIServicePortal](#zbs-meta-ISCSIServicePortal)
    - [ISCSISnapshotPath](#zbs-meta-ISCSISnapshotPath)
    - [ISCSISpc2ReleaseRequest](#zbs-meta-ISCSISpc2ReleaseRequest)
    - [ISCSISpc2ReserveRequest](#zbs-meta-ISCSISpc2ReserveRequest)
    - [ISCSITarget](#zbs-meta-ISCSITarget)
    - [ISCSITargetShortInfo](#zbs-meta-ISCSITargetShortInfo)
    - [ISCSITargetsResponse](#zbs-meta-ISCSITargetsResponse)
    - [InitiatorChapInfo](#zbs-meta-InitiatorChapInfo)
    - [InodeId](#zbs-meta-InodeId)
    - [KeyRequest](#zbs-meta-KeyRequest)
    - [KeyResponse](#zbs-meta-KeyResponse)
    - [ListConsistencyGroupRequest](#zbs-meta-ListConsistencyGroupRequest)
    - [ListConsistencyGroupResponse](#zbs-meta-ListConsistencyGroupResponse)
    - [ListConsistencyGroupSnapshotRequest](#zbs-meta-ListConsistencyGroupSnapshotRequest)
    - [ListConsistencyGroupSnapshotResponse](#zbs-meta-ListConsistencyGroupSnapshotResponse)
    - [ListDbResponse](#zbs-meta-ListDbResponse)
    - [ListISCSIConnectionRequest](#zbs-meta-ListISCSIConnectionRequest)
    - [ListISCSIConnectionResponse](#zbs-meta-ListISCSIConnectionResponse)
    - [ListInodeRequest](#zbs-meta-ListInodeRequest)
    - [ListNVMFConnectionRequest](#zbs-meta-ListNVMFConnectionRequest)
    - [ListNVMFConnectionResponse](#zbs-meta-ListNVMFConnectionResponse)
    - [ListNVMFDistSubsystemsRequest](#zbs-meta-ListNVMFDistSubsystemsRequest)
    - [ListPExtentRequest](#zbs-meta-ListPExtentRequest)
    - [ListPoolRequest](#zbs-meta-ListPoolRequest)
    - [ListSnapshotRequest](#zbs-meta-ListSnapshotRequest)
    - [ListTargetPortalsRequest](#zbs-meta-ListTargetPortalsRequest)
    - [ListTargetsRequest](#zbs-meta-ListTargetsRequest)
    - [ListTemporaryReplicaRequest](#zbs-meta-ListTemporaryReplicaRequest)
    - [ListTemporaryReplicaResponse](#zbs-meta-ListTemporaryReplicaResponse)
    - [ListVolumeRequest](#zbs-meta-ListVolumeRequest)
    - [LookupInodeRequest](#zbs-meta-LookupInodeRequest)
    - [LunPath](#zbs-meta-LunPath)
    - [MetaSummary](#zbs-meta-MetaSummary)
    - [MountPoint](#zbs-meta-MountPoint)
    - [MountTable](#zbs-meta-MountTable)
    - [MoveDataAndDeleteVolumeRequest](#zbs-meta-MoveDataAndDeleteVolumeRequest)
    - [MoveFileRequest](#zbs-meta-MoveFileRequest)
    - [MoveISCSILunRequest](#zbs-meta-MoveISCSILunRequest)
    - [MoveISCSISnapshotRequest](#zbs-meta-MoveISCSISnapshotRequest)
    - [MoveNFSSnapshotRequest](#zbs-meta-MoveNFSSnapshotRequest)
    - [MoveSnapshotRequest](#zbs-meta-MoveSnapshotRequest)
    - [MoveVolumeRequest](#zbs-meta-MoveVolumeRequest)
    - [NFSAttr](#zbs-meta-NFSAttr)
    - [NFSInode](#zbs-meta-NFSInode)
    - [NFSInodes](#zbs-meta-NFSInodes)
    - [NFSSnapshotPath](#zbs-meta-NFSSnapshotPath)
    - [NVMFAccessRecord](#zbs-meta-NVMFAccessRecord)
    - [NVMFAccessRecords](#zbs-meta-NVMFAccessRecords)
    - [NVMFConfigUpdate](#zbs-meta-NVMFConfigUpdate)
    - [NVMFConnection](#zbs-meta-NVMFConnection)
    - [NVMFConnectionRecord](#zbs-meta-NVMFConnectionRecord)
    - [NVMFConnections](#zbs-meta-NVMFConnections)
    - [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace)
    - [NVMFDistNamespaceGroup](#zbs-meta-NVMFDistNamespaceGroup)
    - [NVMFDistNamespaceGroupsResponse](#zbs-meta-NVMFDistNamespaceGroupsResponse)
    - [NVMFDistNamespacesResponse](#zbs-meta-NVMFDistNamespacesResponse)
    - [NVMFDistSubsystem](#zbs-meta-NVMFDistSubsystem)
    - [NVMFDistSubsystemsResponse](#zbs-meta-NVMFDistSubsystemsResponse)
    - [NVMFNamespaceAllowedHost](#zbs-meta-NVMFNamespaceAllowedHost)
    - [NVMFOptimizedPath](#zbs-meta-NVMFOptimizedPath)
    - [NVMFOptimizedPaths](#zbs-meta-NVMFOptimizedPaths)
    - [NVMFSnapshotPath](#zbs-meta-NVMFSnapshotPath)
    - [NVMFTarget](#zbs-meta-NVMFTarget)
    - [NVMFTargetsResponse](#zbs-meta-NVMFTargetsResponse)
    - [NVMFVolumeAccessRecords](#zbs-meta-NVMFVolumeAccessRecords)
    - [NVMFVolumeReset](#zbs-meta-NVMFVolumeReset)
    - [NegotiatedConfig](#zbs-meta-NegotiatedConfig)
    - [PExtentRef](#zbs-meta-PExtentRef)
    - [PExtentsResponse](#zbs-meta-PExtentsResponse)
    - [PRInfo](#zbs-meta-PRInfo)
    - [PRReg](#zbs-meta-PRReg)
    - [PRRequest](#zbs-meta-PRRequest)
    - [PRResponse](#zbs-meta-PRResponse)
    - [Pool](#zbs-meta-Pool)
    - [PoolPath](#zbs-meta-PoolPath)
    - [PoolsResponse](#zbs-meta-PoolsResponse)
    - [RebalanceAccessPointStatus](#zbs-meta-RebalanceAccessPointStatus)
    - [RecoverCmds](#zbs-meta-RecoverCmds)
    - [RecoverModeInfo](#zbs-meta-RecoverModeInfo)
    - [RecoverParameter](#zbs-meta-RecoverParameter)
    - [RefreshChildExtentLocationRequest](#zbs-meta-RefreshChildExtentLocationRequest)
    - [ReleaseBadLeaseRequest](#zbs-meta-ReleaseBadLeaseRequest)
    - [ReleaseLeaseRequest](#zbs-meta-ReleaseLeaseRequest)
    - [RemoveChunkFromStoragePoolRequest](#zbs-meta-RemoveChunkFromStoragePoolRequest)
    - [RemoveLunAllowedInitiatorsRequest](#zbs-meta-RemoveLunAllowedInitiatorsRequest)
    - [RemoveReplicaRequest](#zbs-meta-RemoveReplicaRequest)
    - [RenameInodeRequest](#zbs-meta-RenameInodeRequest)
    - [RenameInodeResponse](#zbs-meta-RenameInodeResponse)
    - [ReplaceReplicaRequest](#zbs-meta-ReplaceReplicaRequest)
    - [ReportVolumeAccessRequest](#zbs-meta-ReportVolumeAccessRequest)
    - [RerouteStatus](#zbs-meta-RerouteStatus)
    - [ReserveVolumeSpaceRequest](#zbs-meta-ReserveVolumeSpaceRequest)
    - [ResetLunPrRequest](#zbs-meta-ResetLunPrRequest)
    - [ResizeVolumeRequest](#zbs-meta-ResizeVolumeRequest)
    - [RevokeClientCmd](#zbs-meta-RevokeClientCmd)
    - [RollbackConsistencyGroupSnapshotRequest](#zbs-meta-RollbackConsistencyGroupSnapshotRequest)
    - [RollbackNFSSnapshotRequest](#zbs-meta-RollbackNFSSnapshotRequest)
    - [SAttr3](#zbs-meta-SAttr3)
    - [SerializedMetaInfo](#zbs-meta-SerializedMetaInfo)
    - [SessionMigrateLimit](#zbs-meta-SessionMigrateLimit)
    - [SessionRecoverLimit](#zbs-meta-SessionRecoverLimit)
    - [SetAccessRecordRequest](#zbs-meta-SetAccessRecordRequest)
    - [SetAccessRecordResponse](#zbs-meta-SetAccessRecordResponse)
    - [SetAttrRequest](#zbs-meta-SetAttrRequest)
    - [SetChunkIsolatePolicyRequest](#zbs-meta-SetChunkIsolatePolicyRequest)
    - [SetMaintenanceModeRequest](#zbs-meta-SetMaintenanceModeRequest)
    - [SetNVMFAccessRecordRequest](#zbs-meta-SetNVMFAccessRecordRequest)
    - [SetNVMFAccessRecordResponse](#zbs-meta-SetNVMFAccessRecordResponse)
    - [SetNVMFOptimizedAccessRequest](#zbs-meta-SetNVMFOptimizedAccessRequest)
    - [SetUpgradeModeRequest](#zbs-meta-SetUpgradeModeRequest)
    - [ShowConsistencyGroupRequest](#zbs-meta-ShowConsistencyGroupRequest)
    - [ShowConsistencyGroupSnapshotRequest](#zbs-meta-ShowConsistencyGroupSnapshotRequest)
    - [ShowVolumeRequest](#zbs-meta-ShowVolumeRequest)
    - [ShowVolumeResponse](#zbs-meta-ShowVolumeResponse)
    - [ShowVolumesRequest](#zbs-meta-ShowVolumesRequest)
    - [ShowVolumesResponse](#zbs-meta-ShowVolumesResponse)
    - [SizeInfo](#zbs-meta-SizeInfo)
    - [SnapshotPath](#zbs-meta-SnapshotPath)
    - [SnapshotV1](#zbs-meta-SnapshotV1)
    - [SnapshotsResponse](#zbs-meta-SnapshotsResponse)
    - [SpecialRecoverRequest](#zbs-meta-SpecialRecoverRequest)
    - [SubsystemRequirement](#zbs-meta-SubsystemRequirement)
    - [SwitchRequest](#zbs-meta-SwitchRequest)
    - [SyncTargetsRequest](#zbs-meta-SyncTargetsRequest)
    - [TargetAndLunInfo](#zbs-meta-TargetAndLunInfo)
    - [TargetChapInfo](#zbs-meta-TargetChapInfo)
    - [TargetPortals](#zbs-meta-TargetPortals)
    - [TargetRequirement](#zbs-meta-TargetRequirement)
    - [TruncateRequest](#zbs-meta-TruncateRequest)
    - [UpdateClusterInfoRequest](#zbs-meta-UpdateClusterInfoRequest)
    - [UpdateConsistencyGroupRequest](#zbs-meta-UpdateConsistencyGroupRequest)
    - [UpdateConsistencyGroupSnapshotRequest](#zbs-meta-UpdateConsistencyGroupSnapshotRequest)
    - [UpdateEpochRequest](#zbs-meta-UpdateEpochRequest)
    - [UpdateEpochResponse](#zbs-meta-UpdateEpochResponse)
    - [UpdateISCSILunRequest](#zbs-meta-UpdateISCSILunRequest)
    - [UpdateISCSISnapshotRequest](#zbs-meta-UpdateISCSISnapshotRequest)
    - [UpdateISCSITargetRequest](#zbs-meta-UpdateISCSITargetRequest)
    - [UpdateNFSFileRequest](#zbs-meta-UpdateNFSFileRequest)
    - [UpdateNFSSnapshotRequest](#zbs-meta-UpdateNFSSnapshotRequest)
    - [UpdateNVMFDistNamespaceGroupRequest](#zbs-meta-UpdateNVMFDistNamespaceGroupRequest)
    - [UpdateNVMFDistNamespaceRequest](#zbs-meta-UpdateNVMFDistNamespaceRequest)
    - [UpdateNVMFDistSubsystemRequest](#zbs-meta-UpdateNVMFDistSubsystemRequest)
    - [UpdateNVMFSnapshotRequest](#zbs-meta-UpdateNVMFSnapshotRequest)
    - [UpdatePoolRequest](#zbs-meta-UpdatePoolRequest)
    - [UpdateSnapshotRequest](#zbs-meta-UpdateSnapshotRequest)
    - [UpdateStoragePoolRequest](#zbs-meta-UpdateStoragePoolRequest)
    - [UpdateTopoObjRequest](#zbs-meta-UpdateTopoObjRequest)
    - [UpdateVolumeRequest](#zbs-meta-UpdateVolumeRequest)
    - [VersionInfo](#zbs-meta-VersionInfo)
    - [VhostConfigUpdate](#zbs-meta-VhostConfigUpdate)
    - [VhostIOPermissionEntry](#zbs-meta-VhostIOPermissionEntry)
    - [VhostIOPermissionRecord](#zbs-meta-VhostIOPermissionRecord)
    - [VhostIOPermissionRequest](#zbs-meta-VhostIOPermissionRequest)
    - [Volume](#zbs-meta-Volume)
    - [VolumeId](#zbs-meta-VolumeId)
    - [VolumeInfo](#zbs-meta-VolumeInfo)
    - [VolumePath](#zbs-meta-VolumePath)
    - [VolumeSizeResponse](#zbs-meta-VolumeSizeResponse)
    - [VolumesResponse](#zbs-meta-VolumesResponse)
    - [ZoneSummary](#zbs-meta-ZoneSummary)
  
    - [ClusterStatus.ClusterStatusFlags](#zbs-meta-ClusterStatus-ClusterStatusFlags)
    - [ConfigUpdateType](#zbs-meta-ConfigUpdateType)
    - [ConsistencyVolumeType](#zbs-meta-ConsistencyVolumeType)
    - [CreateHow](#zbs-meta-CreateHow)
    - [FindVolumeType](#zbs-meta-FindVolumeType)
    - [ISCSIConfigOption](#zbs-meta-ISCSIConfigOption)
    - [ISCSIType](#zbs-meta-ISCSIType)
    - [MetaStatus](#zbs-meta-MetaStatus)
    - [NFSType](#zbs-meta-NFSType)
    - [NVMFAccessPolicy](#zbs-meta-NVMFAccessPolicy)
    - [NVMFConfigOption](#zbs-meta-NVMFConfigOption)
    - [NVMFTransportType](#zbs-meta-NVMFTransportType)
    - [PExtentStatus](#zbs-meta-PExtentStatus)
    - [VolumeStatus](#zbs-meta-VolumeStatus)
    - [time_how](#zbs-meta-time_how)
  
    - [CDPService](#zbs-meta-CDPService)
    - [ChunkService](#zbs-meta-ChunkService)
    - [ISCSIService](#zbs-meta-ISCSIService)
    - [MetaService](#zbs-meta-MetaService)
    - [NFSService](#zbs-meta-NFSService)
    - [NVMFService](#zbs-meta-NVMFService)
    - [StatusService](#zbs-meta-StatusService)
  
- [perf_rpc.proto](#perf_rpc-proto)
    - [DisableProbeVolumesRequest](#zbs-DisableProbeVolumesRequest)
    - [ProbeVolumesRequest](#zbs-ProbeVolumesRequest)
    - [UIOPerf](#zbs-UIOPerf)
    - [VolumePerf](#zbs-VolumePerf)
    - [VolumePerfRequest](#zbs-VolumePerfRequest)
    - [VolumesPerf](#zbs-VolumesPerf)
    - [VolumesPerfRequest](#zbs-VolumesPerfRequest)
  
    - [ChunkPerfService](#zbs-ChunkPerfService)
    - [VolumePerfService](#zbs-VolumePerfService)
  
- [session.proto](#session-proto)
    - [AcquireNFSLoginPermissionRequest](#zbs-consensus-AcquireNFSLoginPermissionRequest)
    - [AddItemRequest](#zbs-consensus-AddItemRequest)
    - [CreateSessionRequest](#zbs-consensus-CreateSessionRequest)
    - [DataReportRequest](#zbs-consensus-DataReportRequest)
    - [DataReportResponse](#zbs-consensus-DataReportResponse)
    - [Item](#zbs-consensus-Item)
    - [KeepAliveRequest](#zbs-consensus-KeepAliveRequest)
    - [KeepAliveResponse](#zbs-consensus-KeepAliveResponse)
    - [ListSessionRequest](#zbs-consensus-ListSessionRequest)
    - [RefreshLocalNFSClientIPsRequest](#zbs-consensus-RefreshLocalNFSClientIPsRequest)
    - [RemoveItemRequest](#zbs-consensus-RemoveItemRequest)
    - [Session](#zbs-consensus-Session)
    - [SessionEpoch](#zbs-consensus-SessionEpoch)
    - [Sessions](#zbs-consensus-Sessions)
  
    - [DataReportService](#zbs-consensus-DataReportService)
    - [SessionService](#zbs-consensus-SessionService)
  
- [task.proto](#task-proto)
    - [CopyVolumeTask](#zbs-task-CopyVolumeTask)
    - [GatewayEndpoint](#zbs-task-GatewayEndpoint)
    - [ListTaskByDateRequest](#zbs-task-ListTaskByDateRequest)
    - [ListTaskByStatusRequest](#zbs-task-ListTaskByStatusRequest)
    - [RsyncTask](#zbs-task-RsyncTask)
    - [RunTime](#zbs-task-RunTime)
    - [SetBpsMaxRequest](#zbs-task-SetBpsMaxRequest)
    - [SetRunTimeRequest](#zbs-task-SetRunTimeRequest)
    - [SyncVolumeTask](#zbs-task-SyncVolumeTask)
    - [Task](#zbs-task-Task)
    - [TaskId](#zbs-task-TaskId)
    - [TaskProgress](#zbs-task-TaskProgress)
    - [Tasks](#zbs-task-Tasks)
    - [VirtualIP](#zbs-task-VirtualIP)
    - [VirtualIPs](#zbs-task-VirtualIPs)
  
    - [CopyVolumeTaskState](#zbs-task-CopyVolumeTaskState)
    - [TaskState](#zbs-task-TaskState)
  
    - [CopyVolumeService](#zbs-task-CopyVolumeService)
    - [RsyncService](#zbs-task-RsyncService)
    - [StatusService](#zbs-task-StatusService)
    - [VIPService](#zbs-task-VIPService)
  
- [zbs_metrics.proto](#zbs_metrics-proto)
    - [Bucket](#zbs-metric-proto-Bucket)
    - [Counter](#zbs-metric-proto-Counter)
    - [Gauge](#zbs-metric-proto-Gauge)
    - [Histogram](#zbs-metric-proto-Histogram)
    - [LabelPair](#zbs-metric-proto-LabelPair)
    - [Metric](#zbs-metric-proto-Metric)
    - [MetricFamily](#zbs-metric-proto-MetricFamily)
    - [Quantile](#zbs-metric-proto-Quantile)
    - [Summary](#zbs-metric-proto-Summary)
    - [Untyped](#zbs-metric-proto-Untyped)
  
    - [MetricType](#zbs-metric-proto-MetricType)
  
- [Scalar Value Types](#scalar-value-types)



<a name="cdp-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## cdp.proto



<a name="zbs-CDPErrorDetail"></a>

### CDPErrorDetail



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| error_code | [ErrorCode](#zbs-ErrorCode) | optional |  Default: EOK |
| error_message | [string](#string) | optional |  |






<a name="zbs-CDPJobInfo"></a>

### CDPJobInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| local | [CDPVolumeID](#zbs-CDPVolumeID) | required |  |
| remote | [CDPRemoteInfo](#zbs-CDPRemoteInfo) | required |  |
| group | [bytes](#bytes) | optional |  |
| cid | [uint32](#uint32) | optional |  |
| stage | [CDPJobStage](#zbs-CDPJobStage) | optional |  |
| session_id | [bytes](#bytes) | optional |  |
| error | [CDPErrorDetail](#zbs-CDPErrorDetail) | optional |  |
| skip_fc_write_zero | [bool](#bool) | optional |  |






<a name="zbs-CDPJobMetrics"></a>

### CDPJobMetrics



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| total_fc_blocks | [uint64](#uint64) | optional |  |
| remain_fc_blocks | [uint64](#uint64) | optional |  |
| copied_fc_blocks | [uint64](#uint64) | optional |  |
| total_dirty_blocks | [uint64](#uint64) | optional |  |
| remain_dirty_blocks | [uint64](#uint64) | optional |  |
| copied_dirty_blocks | [uint64](#uint64) | optional |  |
| block_queue_depth | [uint64](#uint64) | optional |  |
| stage_duration | [uint64](#uint64) | optional |  |
| total_inflight | [uint32](#uint32) | optional |  |
| total_iops | [float](#float) | optional |  |
| total_iop30s | [float](#float) | optional |  |
| total_speed_bps | [float](#float) | optional |  |
| total_avg_size_bytes | [float](#float) | optional |  |
| total_avg_latency_ns | [float](#float) | optional |  |
| block_inflight | [uint32](#uint32) | optional |  |
| block_iops | [float](#float) | optional |  |
| block_iop30s | [float](#float) | optional |  |
| block_speed_bps | [float](#float) | optional |  |
| block_avg_size_bytes | [float](#float) | optional |  |
| block_avg_latency_ns | [float](#float) | optional |  |
| user_inflight | [uint32](#uint32) | optional |  |
| user_iops | [float](#float) | optional |  |
| user_iop30s | [float](#float) | optional |  |
| user_speed_bps | [float](#float) | optional |  |
| user_avg_size_bytes | [float](#float) | optional |  |
| user_avg_latency_ns | [float](#float) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| metrics | CDPJobMetrics | CDPJobInfo | 10001 |  |




<a name="zbs-CDPJobStageUpdate"></a>

### CDPJobStageUpdate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| stage | [CDPJobStage](#zbs-CDPJobStage) | required |  |
| err | [CDPErrorDetail](#zbs-CDPErrorDetail) | optional |  |






<a name="zbs-CDPJobUpdate"></a>

### CDPJobUpdate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| create_reqs | [CreateCDPJobRequest](#zbs-CreateCDPJobRequest) | repeated | notify chunk manage CDP jobs |
| cancel_reqs | [CancelCDPJobRequest](#zbs-CancelCDPJobRequest) | repeated |  |
| finish_reqs | [FinishCDPJobRequest](#zbs-FinishCDPJobRequest) | repeated |  |






<a name="zbs-CDPJobUpdateDone"></a>

### CDPJobUpdateDone



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| job_update | [CDPJobStageUpdate](#zbs-CDPJobStageUpdate) | repeated |  |






<a name="zbs-CDPRemoteInfo"></a>

### CDPRemoteInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| hosts | [bytes](#bytes) | required |  |






<a name="zbs-CDPVolumeID"></a>

### CDPVolumeID



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |






<a name="zbs-CancelCDPJobRequest"></a>

### CancelCDPJobRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| sync | [bool](#bool) | optional |  |
| reply_error | [bool](#bool) | optional |  Default: false |






<a name="zbs-CancelCDPJobsByGroupRequest"></a>

### CancelCDPJobsByGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group | [bytes](#bytes) | required |  |
| sync | [bool](#bool) | optional |  |






<a name="zbs-CreateCDPJobRequest"></a>

### CreateCDPJobRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| local | [CDPVolumeID](#zbs-CDPVolumeID) | required |  |
| remote | [CDPRemoteInfo](#zbs-CDPRemoteInfo) | required |  |
| group | [bytes](#bytes) | optional |  |
| cid | [uint32](#uint32) | optional |  |
| id | [bytes](#bytes) | optional |  |
| skip_fc_write_zero | [bool](#bool) | optional |  |






<a name="zbs-CreateCDPJobsByGroupRequest"></a>

### CreateCDPJobsByGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group | [bytes](#bytes) | required |  |
| create_reqs | [CreateCDPJobRequest](#zbs-CreateCDPJobRequest) | repeated |  |






<a name="zbs-CreateCDPJobsByGroupResponse"></a>

### CreateCDPJobsByGroupResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cdp_jobs | [CDPJobInfo](#zbs-CDPJobInfo) | repeated |  |






<a name="zbs-FinishCDPJobRequest"></a>

### FinishCDPJobRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| sync | [bool](#bool) | optional |  |






<a name="zbs-FinishCDPJobsByGroupRequest"></a>

### FinishCDPJobsByGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group | [bytes](#bytes) | required |  |
| sync | [bool](#bool) | optional |  |






<a name="zbs-GetCDPJobByVolumeRequest"></a>

### GetCDPJobByVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |






<a name="zbs-GetCDPJobRequest"></a>

### GetCDPJobRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |






<a name="zbs-ListCDPJobsRequest"></a>

### ListCDPJobsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group | [bytes](#bytes) | optional |  |






<a name="zbs-ListCDPJobsResponse"></a>

### ListCDPJobsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| jobs | [CDPJobInfo](#zbs-CDPJobInfo) | repeated |  |





 


<a name="zbs-CDPJobStage"></a>

### CDPJobStage
CDP messages

| Name | Number | Description |
| ---- | ------ | ----------- |
| CDP_STAGE_INIT | 0 |  |
| CDP_STAGE_FULL_COPY | 1 |  |
| CDP_STAGE_DELTA_COPY | 2 |  |
| CDP_STAGE_MIRROR | 3 |  |
| CDP_STAGE_CANCELLING | 4 |  |
| CDP_STAGE_FINISHING | 5 |  |
| CDP_STAGE_ERROR | 6 |  |
| CDP_STAGE_CANCELLED | 7 |  |
| CDP_STAGE_DONE | 8 |  |
| CDP_STAGE_ERROR_NEED_NOTIFY | 9 |  |


 

 

 



<a name="chunk-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## chunk.proto



<a name="zbs-chunk-AcceptDiskRequest"></a>

### AcceptDiskRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [string](#string) | required |  |






<a name="zbs-chunk-CacheInfo"></a>

### CacheInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| device_id | [string](#string) | optional |  |
| uuid | [string](#string) | optional |  |
| num_blocks | [uint64](#uint64) | optional | deprecated |
| num_active | [uint64](#uint64) | optional | deprecated |
| num_inactive | [uint64](#uint64) | optional | deprecated |
| num_free | [uint64](#uint64) | optional | deprecated |
| num_io_errors | [uint64](#uint64) | optional |  |
| status | [CacheStatus](#zbs-chunk-CacheStatus) | optional |  |
| errflags | [uint32](#uint32) | optional |  |
| num_slow_io | [uint64](#uint64) | optional |  |
| part_uuid | [string](#string) | optional |  |
| num_used | [uint64](#uint64) | optional | deprecated |
| warnflags | [uint32](#uint32) | optional |  |
| total_size | [uint64](#uint64) | optional |  |
| used_size | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-CacheStat"></a>

### CacheStat



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| hit_rate | [double](#double) | optional |  |
| promotion_bps | [uint64](#uint64) | optional |  |
| writeback_bps | [uint64](#uint64) | optional |  |
| used_bytes | [uint64](#uint64) | optional |  |
| total_bytes | [uint64](#uint64) | optional |  |
| active_bytes | [uint64](#uint64) | optional |  |
| inactive_bytes | [uint64](#uint64) | optional |  |
| clean_bytes | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-ChunkBlockInfo"></a>

### ChunkBlockInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| block_no | [uint32](#uint32) | required |  |
| hdd_path | [string](#string) | required |  |
| hdd_offset | [uint64](#uint64) | required |  |
| ssd_path | [string](#string) | optional |  |
| ssd_offset | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-ChunkCapability"></a>

### ChunkCapability



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| agile_recover | [bool](#bool) | optional |  Default: false |






<a name="zbs-chunk-ChunkExtentInfo"></a>

### ChunkExtentInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| status | [uint32](#uint32) | required |  |
| num_children | [uint32](#uint32) | required |  |
| part_id | [uint32](#uint32) | required |  |
| extent_no | [uint32](#uint32) | required |  |
| origin_pid | [uint32](#uint32) | required |  |
| bitmap | [bytes](#bytes) | optional |  |
| epoch | [uint64](#uint64) | optional |  |
| origin_epoch | [uint64](#uint64) | optional |  |
| gen | [uint64](#uint64) | optional |  |
| blocks | [ChunkBlockInfo](#zbs-chunk-ChunkBlockInfo) | repeated |  |






<a name="zbs-chunk-ChunkServiceStat"></a>

### ChunkServiceStat



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| access_healthy | [bool](#bool) | required | deprecated Default: false |
| aurora_state | [bool](#bool) | required | deprecated Default: false |
| access_alive_sec | [uint64](#uint64) | optional |  Default: 0 |
| aurora_status | [AuroraStatus](#zbs-chunk-AuroraStatus) | optional |  |






<a name="zbs-chunk-ClearVolumeIOLatencyInjectionRequest"></a>

### ClearVolumeIOLatencyInjectionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | optional |  |
| clear_all | [bool](#bool) | optional |  |






<a name="zbs-chunk-ClientInfo"></a>

### ClientInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| socket | [string](#string) | required |  |
| writable | [bool](#bool) | required |  |
| handling_requests | [uint32](#uint32) | required |  |
| sender_queue_empty | [bool](#bool) | required |  |
| closed | [bool](#bool) | required |  |






<a name="zbs-chunk-CompareExtentRequest"></a>

### CompareExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid1 | [uint32](#uint32) | required |  |
| pid2 | [uint32](#uint32) | required |  |






<a name="zbs-chunk-CompareExtentResponse"></a>

### CompareExtentResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| diff_ranges | [zbs.RangeU64](#zbs-RangeU64) | repeated |  |






<a name="zbs-chunk-DCClientInfo"></a>

### DCClientInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| transport | [TransportInfo](#zbs-chunk-TransportInfo) | optional |  |
| aggr_server_response_times | [uint32](#uint32) | optional |  |
| aggr_accumulate_error_times | [uint32](#uint32) | optional |  |
| aggr_last_second_error_times | [uint32](#uint32) | optional |  |






<a name="zbs-chunk-DCManagerInfo"></a>

### DCManagerInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dc_clients | [DCClientInfo](#zbs-chunk-DCClientInfo) | repeated |  |
| version | [DataChannelVersion](#zbs-chunk-DataChannelVersion) | optional |  |






<a name="zbs-chunk-DCServerInfo"></a>

### DCServerInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| transports | [TransportInfo](#zbs-chunk-TransportInfo) | repeated |  |
| version | [DataChannelVersion](#zbs-chunk-DataChannelVersion) | optional |  |






<a name="zbs-chunk-DataChannelInfo"></a>

### DataChannelInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dcs_info | [DCServerInfo](#zbs-chunk-DCServerInfo) | optional |  |
| dcm_info | [DCManagerInfo](#zbs-chunk-DCManagerInfo) | optional |  |






<a name="zbs-chunk-DataChannelServerInfo"></a>

### DataChannelServerInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| use_rdma | [bool](#bool) | optional |  Default: false |






<a name="zbs-chunk-DirtyBlockRequest"></a>

### DirtyBlockRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| dry_run | [bool](#bool) | optional |  Default: false |






<a name="zbs-chunk-DirtyBlockResponse"></a>

### DirtyBlockResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| blocks | [uint64](#uint64) | repeated |  |






<a name="zbs-chunk-DiskInspectorStat"></a>

### DiskInspectorStat



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| uuid | [string](#string) | required |  |
| type | [DiskType](#zbs-chunk-DiskType) | required | cache, partition |
| patrol_started | [bool](#bool) | optional |  Default: false |
| patrol_progress | [uint32](#uint32) | optional |  Default: 0 |
| inspect_iops | [uint64](#uint64) | optional |  Default: 0 |
| inspect_bw | [uint64](#uint64) | optional |  Default: 0 |






<a name="zbs-chunk-DiskRefInfo"></a>

### DiskRefInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| num | [uint32](#uint32) | required |  |






<a name="zbs-chunk-DiskRequest"></a>

### DiskRequest
must be compatible with PathRequest


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| disk_name | [string](#string) | required |  |
| scheme | [DiskNameScheme](#zbs-chunk-DiskNameScheme) | optional |  Default: DISK_NAME_BY_PATH |






<a name="zbs-chunk-FormatCacheRequest"></a>

### FormatCacheRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| force | [bool](#bool) | optional | for cache, if the cache has data, force should be true. Default: false |






<a name="zbs-chunk-FormatJournalRequest"></a>

### FormatJournalRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| force | [bool](#bool) | optional | if the partition has already formatted, force re-format. Default: false |






<a name="zbs-chunk-FormatPartitionRequest"></a>

### FormatPartitionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| force | [bool](#bool) | optional | if the partition has already formatted, force re-format. Default: false |
| ignore_data_checksum | [bool](#bool) | optional |  Default: false |






<a name="zbs-chunk-GatewayServerInfo"></a>

### GatewayServerInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vhost_started | [bool](#bool) | optional |  Default: false |
| nvmf_started | [bool](#bool) | optional |  Default: false |
| iscsi_started | [bool](#bool) | optional |  Default: false |
| nfs_started | [bool](#bool) | optional |  Default: false |






<a name="zbs-chunk-GetDiskInfoRequest"></a>

### GetDiskInfoRequest
deprecated


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |






<a name="zbs-chunk-GetDiskInfoResponse"></a>

### GetDiskInfoResponse
deprecated


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| status | [DiskStatus](#zbs-chunk-DiskStatus) | required |  |
| disk_type | [DiskType](#zbs-chunk-DiskType) | optional |  |
| version | [string](#string) | optional | Defined by disk_type |
| device_id | [string](#string) | optional |  |
| uuid | [string](#string) | optional |  |
| used_size | [uint64](#uint64) | optional |  |
| total_size | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-GetDiskInspectorStatsResponse"></a>

### GetDiskInspectorStatsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| stats | [DiskInspectorStat](#zbs-chunk-DiskInspectorStat) | repeated |  |






<a name="zbs-chunk-GetPollingStatsResponse"></a>

### GetPollingStatsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| polling_stats | [PollingThreadInfo](#zbs-chunk-PollingThreadInfo) | repeated |  |






<a name="zbs-chunk-GetTemporaryExtentRequest"></a>

### GetTemporaryExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_pid | [uint32](#uint32) | required |  |
| temporary_epoch | [uint64](#uint64) | required |  |
| get_temporary_generation | [bool](#bool) | optional | may block current IO Default: false |






<a name="zbs-chunk-GetTemporaryExtentResponse"></a>

### GetTemporaryExtentResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_extent | [TemporaryExtent](#zbs-chunk-TemporaryExtent) | required |  |
| temporary_extent_bitmap | [TemporaryExtentBitmap](#zbs-chunk-TemporaryExtentBitmap) | required |  |
| temporary_generation | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-GetTemporaryExtentSummaryResponse"></a>

### GetTemporaryExtentSummaryResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_extent_num | [uint32](#uint32) | required |  |






<a name="zbs-chunk-GetZkHostsResponse"></a>

### GetZkHostsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| zk_hosts | [string](#string) | required |  |






<a name="zbs-chunk-InvalidateExtentRequest"></a>

### InvalidateExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| recursive | [bool](#bool) | required |  |
| epoch | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-JournalGroupInfo"></a>

### JournalGroupInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| device_id | [string](#string) | optional |  |
| uuid | [string](#string) | optional |  |
| part_uuid | [string](#string) | optional |  |
| errflags | [uint32](#uint32) | optional |  |
| num_slow_io | [uint64](#uint64) | optional |  |
| status | [JournalGroupStatus](#zbs-chunk-JournalGroupStatus) | optional |  |
| num_io_errors | [uint64](#uint64) | optional |  |
| warnflags | [uint32](#uint32) | optional |  |
| id | [uint32](#uint32) | optional |  Default: 0 |
| __deprecated_auto_delete | [bool](#bool) | optional | DISK_ERR_UMOUNTING is enough to represent JournalGroup need umount. Default: false |






<a name="zbs-chunk-JournalInfo"></a>

### JournalInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| reserved_entries | [uint64](#uint64) | optional |  |
| max_entries | [uint64](#uint64) | optional |  |
| seq_no | [uint64](#uint64) | optional |  |
| status | [JournalStatus](#zbs-chunk-JournalStatus) | optional |  |
| num_io_errors | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-ListCacheResponse"></a>

### ListCacheResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| caches | [CacheInfo](#zbs-chunk-CacheInfo) | repeated |  |
| hit_rate | [double](#double) | optional | deprecated |
| total_active | [uint64](#uint64) | optional | deprecated |
| total_inactive | [uint64](#uint64) | optional | deprecated |
| total_clean | [uint64](#uint64) | optional | deprecated |
| cache_stat | [CacheStat](#zbs-chunk-CacheStat) | optional |  |






<a name="zbs-chunk-ListClientResponse"></a>

### ListClientResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| clients | [ClientInfo](#zbs-chunk-ClientInfo) | repeated |  |
| total | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-ListExtentResponse"></a>

### ListExtentResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| infos | [ChunkExtentInfo](#zbs-chunk-ChunkExtentInfo) | repeated |  |






<a name="zbs-chunk-ListJournalResponse"></a>

### ListJournalResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| journals | [JournalInfo](#zbs-chunk-JournalInfo) | repeated |  |
| disabled_group_no | [uint32](#uint32) | optional | **Deprecated.**  |
| groups | [JournalGroupInfo](#zbs-chunk-JournalGroupInfo) | repeated |  |






<a name="zbs-chunk-ListPartitionResponse"></a>

### ListPartitionResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| partitions | [PartitionInfo](#zbs-chunk-PartitionInfo) | repeated |  |
| workers | [WorkerInfo](#zbs-chunk-WorkerInfo) | repeated |  |






<a name="zbs-chunk-ListRejectedDisksResponse"></a>

### ListRejectedDisksResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| rejected_disks | [RejectedDiskInfo](#zbs-chunk-RejectedDiskInfo) | repeated |  |






<a name="zbs-chunk-ListTemporaryExtentRequest"></a>

### ListTemporaryExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| start_temporary_pid | [uint32](#uint32) | required |  Default: 0 |
| max_num | [uint32](#uint32) | optional |  Default: 2048 |






<a name="zbs-chunk-ListTemporaryExtentResponse"></a>

### ListTemporaryExtentResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_extents | [TemporaryExtent](#zbs-chunk-TemporaryExtent) | repeated |  |






<a name="zbs-chunk-ListVolumeIOLatencyInjectionResponse"></a>

### ListVolumeIOLatencyInjectionResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| injections | [VolumeIOLatencyInjection](#zbs-chunk-VolumeIOLatencyInjection) | repeated |  |






<a name="zbs-chunk-MetaAddr"></a>

### MetaAddr



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| meta_ip | [uint32](#uint32) | required |  Default: 0 |
| meta_port | [uint32](#uint32) | required |  Default: 0 |
| my_chunk_id | [uint32](#uint32) | required |  Default: 0 |






<a name="zbs-chunk-MountCacheRequest"></a>

### MountCacheRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |






<a name="zbs-chunk-MountJournalRequest"></a>

### MountJournalRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |






<a name="zbs-chunk-MountPartitionRequest"></a>

### MountPartitionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| force | [bool](#bool) | optional | for mount, if the partition has data, force should be true. Default: false |






<a name="zbs-chunk-PExtentRequest"></a>

### PExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |






<a name="zbs-chunk-PartitionInfo"></a>

### PartitionInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| device_id | [string](#string) | optional |  |
| uuid | [string](#string) | optional |  |
| total_size | [uint64](#uint64) | optional | Bytes |
| used_size | [uint64](#uint64) | optional | Bytes |
| num_checksum_errors | [uint64](#uint64) | optional |  |
| num_io_errors | [uint64](#uint64) | optional |  |
| status | [PartitionStatus](#zbs-chunk-PartitionStatus) | optional |  |
| errflags | [uint32](#uint32) | optional |  |
| num_slow_io | [uint64](#uint64) | optional |  |
| part_uuid | [string](#string) | optional |  |
| warnflags | [uint32](#uint32) | optional |  |
| checksum_enable | [uint32](#uint32) | optional |  |






<a name="zbs-chunk-PathRequest"></a>

### PathRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |






<a name="zbs-chunk-PollingThreadInfo"></a>

### PollingThreadInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| tid | [int32](#int32) | required |  |
| name | [string](#string) | required |  |
| busy_tsc | [uint64](#uint64) | required |  |
| idle_tsc | [uint64](#uint64) | required |  |






<a name="zbs-chunk-QueryDiskRequest"></a>

### QueryDiskRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |






<a name="zbs-chunk-QueryDiskResponse"></a>

### QueryDiskResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| in_use | [bool](#bool) | optional |  |
| exist | [bool](#bool) | optional |  |
| type | [string](#string) | optional | cache, journal, partition |
| errflags | [uint32](#uint32) | optional |  |
| formatted | [bool](#bool) | optional |  |
| match_host | [bool](#bool) | optional |  |
| device_id | [string](#string) | optional |  |






<a name="zbs-chunk-RDMATransportInfo"></a>

### RDMATransportInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| num_handling_send_wr | [uint64](#uint64) | optional |  |
| num_handling_recv_wr | [uint64](#uint64) | optional |  |
| num_send_ops_in_last_interval | [uint64](#uint64) | optional |  |
| num_recv_ops_in_last_interval | [uint64](#uint64) | optional |  |
| send_rate | [RDMATransportInfo.IORate](#zbs-chunk-RDMATransportInfo-IORate) | optional |  |
| read_rate | [RDMATransportInfo.IORate](#zbs-chunk-RDMATransportInfo-IORate) | optional |  |
| write_rate | [RDMATransportInfo.IORate](#zbs-chunk-RDMATransportInfo-IORate) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| rdma_info | RDMATransportInfo | TransportInfo | 10001 |  |




<a name="zbs-chunk-RDMATransportInfo-IORate"></a>

### RDMATransportInfo.IORate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| total_io_bytes | [uint64](#uint64) | optional |  |
| io_rate_packet | [uint64](#uint64) | optional |  |
| io_rate_bytes | [uint64](#uint64) | optional |  |






<a name="zbs-chunk-RejectedDiskInfo"></a>

### RejectedDiskInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [string](#string) | required |  |
| device_id | [string](#string) | optional |  |
| last_mount_type | [DiskType](#zbs-chunk-DiskType) | optional |  |
| errflags | [uint32](#uint32) | optional |  |
| warnflags | [uint32](#uint32) | optional |  |






<a name="zbs-chunk-SectorBitmap"></a>

### SectorBitmap



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| sector_no | [uint32](#uint32) | required |  |
| bitmap | [bytes](#bytes) | required |  |






<a name="zbs-chunk-SetUnhealthyRequest"></a>

### SetUnhealthyRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| disk_name | [string](#string) | required |  |
| scheme | [DiskNameScheme](#zbs-chunk-DiskNameScheme) | optional |  Default: DISK_NAME_BY_PATH |
| errflag | [DiskErrFlags](#zbs-chunk-DiskErrFlags) | optional |  Default: DISK_ERR_SMART_FAIL |






<a name="zbs-chunk-SetVerifyModeRequest"></a>

### SetVerifyModeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| mode | [string](#string) | required |  |






<a name="zbs-chunk-SetVolumeIOLatencyInjectionRequest"></a>

### SetVolumeIOLatencyInjectionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| read_latency_ms | [uint64](#uint64) | optional |  Default: 0 |
| write_latency_ms | [uint64](#uint64) | optional |  Default: 0 |
| readwrite_latency_ms | [uint64](#uint64) | optional |  Default: 0 |






<a name="zbs-chunk-SetVolumeIOLatencyInjectionResponse"></a>

### SetVolumeIOLatencyInjectionResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| injection | [VolumeIOLatencyInjection](#zbs-chunk-VolumeIOLatencyInjection) | required |  |






<a name="zbs-chunk-ShowExtentRequest"></a>

### ShowExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| show_disk | [bool](#bool) | optional |  |






<a name="zbs-chunk-ShowExtentResponse"></a>

### ShowExtentResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| epoch | [uint64](#uint64) | required |  |
| generation | [uint64](#uint64) | required |  |
| status | [uint32](#uint32) | required |  |
| private_blob_num | [uint32](#uint32) | optional |  |
| shared_blob_num | [uint32](#uint32) | optional |  |
| disk_refs | [DiskRefInfo](#zbs-chunk-DiskRefInfo) | repeated |  |






<a name="zbs-chunk-SummaryInfoResponse"></a>

### SummaryInfoResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lsm_meta | [zbs.LSMMeta](#zbs-LSMMeta) | optional |  |
| lsm_capability | [fixed64](#fixed64) | optional |  Default: 0 |
| space_info | [zbs.ChunkSpaceInfo](#zbs-ChunkSpaceInfo) | optional |  |
| merge_iops | [uint64](#uint64) | optional |  |
| merge_bw | [uint64](#uint64) | optional |  |
| writeback_iops | [uint64](#uint64) | optional |  |
| writeback_bw | [uint64](#uint64) | optional |  |
| lsm_iops | [uint64](#uint64) | optional |  |
| lsm_bw | [uint64](#uint64) | optional |  |
| wait_reclaim | [uint64](#uint64) | optional |  |
| wait_merge | [uint64](#uint64) | optional |  |
| unmap_iops | [uint64](#uint64) | optional |  |
| unmap_bw | [uint64](#uint64) | optional |  |
| dcs_info | [DataChannelServerInfo](#zbs-chunk-DataChannelServerInfo) | optional |  |
| chunk_capability | [ChunkCapability](#zbs-chunk-ChunkCapability) | optional |  |
| gateway_info | [GatewayServerInfo](#zbs-chunk-GatewayServerInfo) | optional |  |






<a name="zbs-chunk-TemporaryExtent"></a>

### TemporaryExtent



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| epoch | [uint64](#uint64) | required |  |
| temporary_pid | [uint32](#uint32) | required |  |
| temporary_epoch | [uint32](#uint32) | required |  |
| base_pextent_generation | [uint64](#uint64) | required |  |
| pextent_generation | [uint64](#uint64) | required |  |






<a name="zbs-chunk-TemporaryExtentBitmap"></a>

### TemporaryExtentBitmap



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_pid | [uint32](#uint32) | required |  |
| temporary_epoch | [uint64](#uint64) | required |  |
| block_bitmap | [bytes](#bytes) | required |  |
| sector_bitmaps | [SectorBitmap](#zbs-chunk-SectorBitmap) | repeated |  |






<a name="zbs-chunk-TransportInfo"></a>

### TransportInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| local_address | [string](#string) | optional |  |
| remote_address | [string](#string) | optional |  |
| transport_type | [TransportType](#zbs-chunk-TransportType) | optional |  |
| up_time | [uint64](#uint64) | optional | in seconds |






<a name="zbs-chunk-UmountDiskRequest"></a>

### UmountDiskRequest
must be compatible with PathRequest and MountPartitionRequest


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| disk_name | [string](#string) | required |  |
| scheme | [DiskNameScheme](#zbs-chunk-DiskNameScheme) | optional |  Default: DISK_NAME_BY_PATH |
| __deprecated_force | [bool](#bool) | optional | **Deprecated.**  |
| mode | [DiskUmountMode](#zbs-chunk-DiskUmountMode) | optional |  Default: DISK_UMOUNT_AUTO |






<a name="zbs-chunk-UpdateScvmModeHostDataIPRequest"></a>

### UpdateScvmModeHostDataIPRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| new_ip | [string](#string) | required |  |






<a name="zbs-chunk-UpdateSecondaryDataIPRequest"></a>

### UpdateSecondaryDataIPRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| new_ip | [string](#string) | required |  |






<a name="zbs-chunk-VolumeIOLatencyInjection"></a>

### VolumeIOLatencyInjection



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| read_latency_ms | [uint64](#uint64) | optional |  Default: 0 |
| write_latency_ms | [uint64](#uint64) | optional |  Default: 0 |
| readwrite_latency_ms | [uint64](#uint64) | optional |  Default: 0 |






<a name="zbs-chunk-WorkerInfo"></a>

### WorkerInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| worker_id | [uint32](#uint32) | required |  |
| queue_size | [uint64](#uint64) | required |  |






<a name="zbs-chunk-ZbsAddress"></a>

### ZbsAddress



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| rpc_ip | [string](#string) | optional |  |
| rpc_port | [int32](#int32) | optional |  |
| data_ip | [string](#string) | optional |  |
| data_port | [int32](#int32) | optional |  |
| data_unix_path | [string](#string) | optional |  |
| heartbeat_ip | [string](#string) | optional |  |
| heartbeat_port | [int32](#int32) | optional |  |
| meta_ip | [string](#string) | optional |  |
| meta_port | [int32](#int32) | optional |  |
| chunk_id | [uint32](#uint32) | optional |  |
| secondary_data_ip | [string](#string) | optional |  |
| scvm_mode_host_data_ip | [string](#string) | optional |  |





 


<a name="zbs-chunk-AuroraStatus"></a>

### AuroraStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| AURORA_STATUS_INITIALIZING | 1 |  |
| AURORA_STATUS_RUNNING | 2 |  |
| AURORA_STATUS_STOPPING | 3 |  |
| AURORA_STATUS_STOPPED | 4 |  |



<a name="zbs-chunk-CacheStatus"></a>

### CacheStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| CACHE_MOUNTED | 0 |  |
| CACHE_STAGING | 1 |  |
| CACHE_MIGRATING | 2 |  |



<a name="zbs-chunk-DataChannelVersion"></a>

### DataChannelVersion


| Name | Number | Description |
| ---- | ------ | ----------- |
| DATA_CHANNEL_V1 | 1 |  |
| DATA_CHANNEL_V2 | 2 |  |
| DATA_CHANNEL_V3 | 3 |  |



<a name="zbs-chunk-DiskErrFlags"></a>

### DiskErrFlags


| Name | Number | Description |
| ---- | ------ | ----------- |
| DISK_ERR_SMART_FAIL | 1 |  |
| __DEPRECATED_DISK_ERR_HIGH_LAT | 2 |  |
| DISK_ERR_IO | 4 |  |
| DISK_ERR_CHECKSUM | 8 |  |
| __DEPRECATED_DISK_ERR_UMOUNTING | 16 |  |
| __DEPRECATED_DISK_ERR_FORCE_UMOUNT | 32 |  |
| DISK_ERR_SYNC | 64 |  |



<a name="zbs-chunk-DiskNameScheme"></a>

### DiskNameScheme


| Name | Number | Description |
| ---- | ------ | ----------- |
| DISK_NAME_BY_UUID | 0 | uuid lsm writes on super block |
| DISK_NAME_BY_PATH | 1 |  |



<a name="zbs-chunk-DiskStatus"></a>

### DiskStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| DISK_MOUNTED | 0 |  |
| DISK_AVAILABLE | 1 |  |
| DISK_STAGING | 2 |  |



<a name="zbs-chunk-DiskType"></a>

### DiskType


| Name | Number | Description |
| ---- | ------ | ----------- |
| DISK_TYPE_PARTITION | 0 |  |
| DISK_TYPE_JOURNAL | 1 |  |
| DISK_TYPE_CACHE | 2 |  |
| DISK_TYPE_UNKNOWN | 3 |  |



<a name="zbs-chunk-DiskUmountMode"></a>

### DiskUmountMode


| Name | Number | Description |
| ---- | ------ | ----------- |
| DISK_UMOUNT_AUTO | 0 |  |
| DISK_UMOUNT_MIGRATE | 1 |  |
| DISK_UMOUNT_OFFLINE | 2 |  |



<a name="zbs-chunk-DiskWarnFlags"></a>

### DiskWarnFlags


| Name | Number | Description |
| ---- | ------ | ----------- |
| DISK_WARN_HIGH_LAT | 1 |  |



<a name="zbs-chunk-JournalGroupStatus"></a>

### JournalGroupStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| JOURNALGROUP_MOUNTED | 0 |  |
| JOURNALGROUP_STAGING | 1 |  |



<a name="zbs-chunk-JournalStatus"></a>

### JournalStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| JOURNAL_IDLE | 0 |  |
| JOURNAL_FULL | 1 |  |
| JOURNAL_BUSY | 2 |  |
| JOURNAL_UMOUNTING | 3 |  |
| JOURNAL_ERROR | 4 |  |
| JOURNAL_INIT | 999 |  |



<a name="zbs-chunk-LSMCapability"></a>

### LSMCapability


| Name | Number | Description |
| ---- | ------ | ----------- |
| LSM_CAP_DISK_SAFE_UMOUNT | 1 | all defined capabilities should be pow of 2 |
| LSM_CAP_DISK_REJECT_UNHEALTHY | 2 |  |
| LSM_CAP_PARTITION_ISOLATE | 4 |  |
| LSM_CAP_COMPARE_EXTENT | 8 |  |



<a name="zbs-chunk-PartitionStatus"></a>

### PartitionStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| PARTITION_MOUNTED | 0 |  |
| PARTITION_STAGING | 1 |  |
| PARTITION_MIGRATING | 2 |  |



<a name="zbs-chunk-TransportType"></a>

### TransportType


| Name | Number | Description |
| ---- | ------ | ----------- |
| TRANSPORT_TYPE_UNIX | 1 |  |
| TRANSPORT_TYPE_TCP | 2 |  |
| TRANSPORT_TYPE_RDMA | 3 |  |


 

 


<a name="zbs-chunk-ChunkService"></a>

### ChunkService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| GetZbsAddress | [.zbs.Void](#zbs-Void) | [ZbsAddress](#zbs-chunk-ZbsAddress) |  |
| FormatPartition | [FormatPartitionRequest](#zbs-chunk-FormatPartitionRequest) | [.zbs.Void](#zbs-Void) |  |
| MountPartition | [MountPartitionRequest](#zbs-chunk-MountPartitionRequest) | [.zbs.Void](#zbs-Void) |  |
| UmountPartition | [UmountDiskRequest](#zbs-chunk-UmountDiskRequest) | [.zbs.Void](#zbs-Void) |  |
| MountCache | [MountCacheRequest](#zbs-chunk-MountCacheRequest) | [.zbs.Void](#zbs-Void) |  |
| UmountCache | [UmountDiskRequest](#zbs-chunk-UmountDiskRequest) | [.zbs.Void](#zbs-Void) |  |
| FormatJournal | [FormatJournalRequest](#zbs-chunk-FormatJournalRequest) | [.zbs.Void](#zbs-Void) |  |
| MountJournal | [MountJournalRequest](#zbs-chunk-MountJournalRequest) | [.zbs.Void](#zbs-Void) |  |
| UmountJournal | [UmountDiskRequest](#zbs-chunk-UmountDiskRequest) | [.zbs.Void](#zbs-Void) |  |
| FlushAllJournals | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| ListPartition | [.zbs.Void](#zbs-Void) | [ListPartitionResponse](#zbs-chunk-ListPartitionResponse) |  |
| ListJournal | [.zbs.Void](#zbs-Void) | [ListJournalResponse](#zbs-chunk-ListJournalResponse) |  |
| ListCache | [.zbs.Void](#zbs-Void) | [ListCacheResponse](#zbs-chunk-ListCacheResponse) |  |
| StopServer | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| ListClient | [.zbs.Void](#zbs-Void) | [ListClientResponse](#zbs-chunk-ListClientResponse) |  |
| ListRecover | [.zbs.Void](#zbs-Void) | [.zbs.ListRecoverResponse](#zbs-ListRecoverResponse) |  |
| ListMigrate | [.zbs.Void](#zbs-Void) | [.zbs.ListRecoverResponse](#zbs-ListRecoverResponse) | deprecated |
| ListExtent | [.zbs.Void](#zbs-Void) | [ListExtentResponse](#zbs-chunk-ListExtentResponse) |  |
| CheckExtent | [PExtentRequest](#zbs-chunk-PExtentRequest) | [.zbs.Void](#zbs-Void) |  |
| InvalidateCache | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| CheckAllExtents | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| ShowExtent | [ShowExtentRequest](#zbs-chunk-ShowExtentRequest) | [ShowExtentResponse](#zbs-chunk-ShowExtentResponse) |  |
| MergeExtent | [PExtentRequest](#zbs-chunk-PExtentRequest) | [.zbs.Void](#zbs-Void) |  |
| GetDiskInfo | [GetDiskInfoRequest](#zbs-chunk-GetDiskInfoRequest) | [GetDiskInfoResponse](#zbs-chunk-GetDiskInfoResponse) | deprecated |
| InvalidateExtent | [InvalidateExtentRequest](#zbs-chunk-InvalidateExtentRequest) | [.zbs.Void](#zbs-Void) |  |
| SetVerifyMode | [SetVerifyModeRequest](#zbs-chunk-SetVerifyModeRequest) | [.zbs.Void](#zbs-Void) |  |
| UpdateSecondaryDataIP | [UpdateSecondaryDataIPRequest](#zbs-chunk-UpdateSecondaryDataIPRequest) | [.zbs.Void](#zbs-Void) |  |
| SetUnhealthyPartition | [SetUnhealthyRequest](#zbs-chunk-SetUnhealthyRequest) | [.zbs.Void](#zbs-Void) |  |
| SetHealthyPartition | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| SetUnhealthyCache | [SetUnhealthyRequest](#zbs-chunk-SetUnhealthyRequest) | [.zbs.Void](#zbs-Void) |  |
| SetHealthyCache | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| SetUnhealthyJournal | [SetUnhealthyRequest](#zbs-chunk-SetUnhealthyRequest) | [.zbs.Void](#zbs-Void) |  |
| SetHealthyJournal | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| FormatCache | [FormatCacheRequest](#zbs-chunk-FormatCacheRequest) | [.zbs.Void](#zbs-Void) |  |
| QueryDisk | [QueryDiskRequest](#zbs-chunk-QueryDiskRequest) | [QueryDiskResponse](#zbs-chunk-QueryDiskResponse) |  |
| SummaryInfo | [.zbs.Void](#zbs-Void) | [SummaryInfoResponse](#zbs-chunk-SummaryInfoResponse) |  |
| UpdateScvmModeHostDataIP | [UpdateScvmModeHostDataIPRequest](#zbs-chunk-UpdateScvmModeHostDataIPRequest) | [.zbs.Void](#zbs-Void) |  |
| GetChunkServiceStat | [.zbs.Void](#zbs-Void) | [ChunkServiceStat](#zbs-chunk-ChunkServiceStat) |  |
| ReloadConfig | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| GetZkHosts | [.zbs.Void](#zbs-Void) | [GetZkHostsResponse](#zbs-chunk-GetZkHostsResponse) |  |
| CancelUmountPartition | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| CancelUmountCache | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| InvalidatePartition | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| ListRejectedDisks | [.zbs.Void](#zbs-Void) | [ListRejectedDisksResponse](#zbs-chunk-ListRejectedDisksResponse) |  |
| AcceptDisk | [AcceptDiskRequest](#zbs-chunk-AcceptDiskRequest) | [.zbs.Void](#zbs-Void) |  |
| IsolatePartition | [DiskRequest](#zbs-chunk-DiskRequest) | [.zbs.Void](#zbs-Void) |  |
| StartAurora | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| StopAurora | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| ReloadHostName | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| GetDiskInspectorStats | [.zbs.Void](#zbs-Void) | [GetDiskInspectorStatsResponse](#zbs-chunk-GetDiskInspectorStatsResponse) |  |
| StartDiskInspectorPatrol | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| StopDiskInspectorPatrol | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| StartDirtyBlockTracking | [DirtyBlockRequest](#zbs-chunk-DirtyBlockRequest) | [.zbs.Void](#zbs-Void) |  |
| StopDirtyBlockTracking | [DirtyBlockRequest](#zbs-chunk-DirtyBlockRequest) | [.zbs.Void](#zbs-Void) |  |
| GetDirtyBlocks | [DirtyBlockRequest](#zbs-chunk-DirtyBlockRequest) | [DirtyBlockResponse](#zbs-chunk-DirtyBlockResponse) |  |
| GetCDPJob | [.zbs.GetCDPJobRequest](#zbs-GetCDPJobRequest) | [.zbs.CDPJobInfo](#zbs-CDPJobInfo) |  |
| ListCDPJobs | [.zbs.ListCDPJobsRequest](#zbs-ListCDPJobsRequest) | [.zbs.ListCDPJobsResponse](#zbs-ListCDPJobsResponse) |  |
| GetPollingStats | [.zbs.Void](#zbs-Void) | [GetPollingStatsResponse](#zbs-chunk-GetPollingStatsResponse) |  |
| SetVolumeIOLatencyInjection | [SetVolumeIOLatencyInjectionRequest](#zbs-chunk-SetVolumeIOLatencyInjectionRequest) | [SetVolumeIOLatencyInjectionResponse](#zbs-chunk-SetVolumeIOLatencyInjectionResponse) |  |
| ClearVolumeIOLatencyInjection | [ClearVolumeIOLatencyInjectionRequest](#zbs-chunk-ClearVolumeIOLatencyInjectionRequest) | [.zbs.Void](#zbs-Void) |  |
| ListVolumeIOLatencyInjection | [.zbs.Void](#zbs-Void) | [ListVolumeIOLatencyInjectionResponse](#zbs-chunk-ListVolumeIOLatencyInjectionResponse) |  |
| GetTemporaryExtentSummary | [.zbs.Void](#zbs-Void) | [GetTemporaryExtentSummaryResponse](#zbs-chunk-GetTemporaryExtentSummaryResponse) |  |
| GetTemporaryExtent | [GetTemporaryExtentRequest](#zbs-chunk-GetTemporaryExtentRequest) | [GetTemporaryExtentResponse](#zbs-chunk-GetTemporaryExtentResponse) |  |
| ListTemporaryExtent | [ListTemporaryExtentRequest](#zbs-chunk-ListTemporaryExtentRequest) | [ListTemporaryExtentResponse](#zbs-chunk-ListTemporaryExtentResponse) |  |
| CompareExtent | [CompareExtentRequest](#zbs-chunk-CompareExtentRequest) | [CompareExtentResponse](#zbs-chunk-CompareExtentResponse) |  |

 



<a name="common-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## common.proto



<a name="zbs-AccessPerf"></a>

### AccessPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| read_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| write_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| total_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| cross_zone_read_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| cross_zone_write_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| cross_zone_total_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| hard_rate | [float](#float) | optional |  |
| retry_rate | [float](#float) | optional |  |
| timeout_rate | [float](#float) | optional |  |
| replica_io_perfs | [ReplicaIOPerf](#zbs-ReplicaIOPerf) | repeated |  |
| local_io_perf | [LocalIOPerf](#zbs-LocalIOPerf) | optional |  |






<a name="zbs-Address"></a>

### Address



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ip | [string](#string) | required |  |
| port | [int32](#int32) | required |  |






<a name="zbs-Addresses"></a>

### Addresses



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| addrs | [Address](#zbs-Address) | repeated |  |






<a name="zbs-BlockDevicePerf"></a>

### BlockDevicePerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [bytes](#bytes) | optional |  |
| total_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| max_total_latency_ns | [float](#float) | optional |  |
| read_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| max_read_latency_ns | [float](#float) | optional |  |
| write_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| max_write_latency_ns | [float](#float) | optional |  |
| sync_rate | [float](#float) | optional |  |
| sync_latency_ns | [float](#float) | optional |  |
| max_sync_latency_ns | [float](#float) | optional |  |






<a name="zbs-Bool"></a>

### Bool



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| value | [bool](#bool) | required |  |






<a name="zbs-Capacity"></a>

### Capacity



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| row | [int32](#int32) | optional |  Default: 1 |
| column | [int32](#int32) | optional |  Default: 1 |






<a name="zbs-Chunk"></a>

### Chunk



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) | optional |  |
| v1_parent_id | [uint32](#uint32) | optional | **Deprecated.** pid is the brick id for chunk, it could be zero |
| data_ip | [uint32](#uint32) | optional |  |
| data_port | [uint32](#uint32) | optional |  |
| rpc_ip | [uint32](#uint32) | optional |  |
| rpc_port | [uint32](#uint32) | optional |  |
| heartbeat_ip | [uint32](#uint32) | optional |  |
| heartbeat_port | [uint32](#uint32) | optional |  |
| registered_date | [uint64](#uint64) | optional |  |
| status | [ChunkStatus](#zbs-ChunkStatus) | optional |  |
| space_info | [ChunkSpaceInfo](#zbs-ChunkSpaceInfo) | optional |  |
| read_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| write_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| use_state | [ChunkState](#zbs-ChunkState) | optional |  Default: CHUNK_STATE_UNKNOWN |
| total_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_read_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_write_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| ipmi_ip | [string](#string) | optional |  |
| topo_id | [bytes](#bytes) | optional | topology info |
| zone_id | [bytes](#bytes) | optional | only internal use, not valid in all scenario |
| storage_pool_id | [bytes](#bytes) | optional | default has no storage pool |
| last_succeed_heartbeat | [uint64](#uint64) | optional | heartbeat info |
| node_perf | [NodePerf](#zbs-NodePerf) | optional |  |
| recover_info | [RecoverSummary](#zbs-RecoverSummary) | optional |  |
| host_name | [bytes](#bytes) | optional |  |
| lsm_version | [LSMVersion](#zbs-LSMVersion) | optional |  |
| maintenance_mode | [bool](#bool) | optional |  Default: false |
| has_config_push_ability | [bool](#bool) | optional |  Default: false |
| maintenance_mode_expire_time_s | [uint64](#uint64) | optional |  Default: 0 |
| chunk_perf | [ChunkPerf](#zbs-ChunkPerf) | optional |  |
| ability | [NegotiationAbility](#zbs-NegotiationAbility) | optional |  |
| isolate_record | [ChunkIsolateRecord](#zbs-ChunkIsolateRecord) | optional |  |






<a name="zbs-ChunkConnectivity"></a>

### ChunkConnectivity



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cid | [uint32](#uint32) | optional |  |
| dc_connectivities | [DataChannelConnectivity](#zbs-DataChannelConnectivity) | repeated |  |






<a name="zbs-ChunkId"></a>

### ChunkId
user could specify either id or ip:port to query a chunk. If both are
specified, id is preferred.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) | optional | id identify a chunk |
| rpc_ip | [uint32](#uint32) | optional | ip &#43; port |
| rpc_port | [uint32](#uint32) | optional |  |






<a name="zbs-ChunkIsolateFlag"></a>

### ChunkIsolateFlag



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| migrate_lease | [bool](#bool) | optional |  Default: false |
| migrate_nvmf_op_node | [bool](#bool) | optional |  Default: false |
| disable_nvmf_op_path | [bool](#bool) | optional |  Default: false |
| migrate_iscsi | [bool](#bool) | optional |  Default: false |
| avoid_read_slow_replica | [bool](#bool) | optional |  Default: false |
| remove_slow_replica | [bool](#bool) | optional |  Default: false |
| avoid_recover | [bool](#bool) | optional |  Default: false |






<a name="zbs-ChunkIsolateInfo"></a>

### ChunkIsolateInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| my_chunk_id | [int32](#int32) | optional |  |
| records | [ChunkIsolateRecord](#zbs-ChunkIsolateRecord) | repeated |  |






<a name="zbs-ChunkIsolateRecord"></a>

### ChunkIsolateRecord



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| chunk_id | [uint32](#uint32) | optional |  |
| need_clear | [bool](#bool) | optional |  |
| is_unbanning | [bool](#bool) | optional |  |
| event_time | [uint64](#uint64) | optional |  |
| isolate_policy | [ChunkIsolateFlag](#zbs-ChunkIsolateFlag) | optional |  |
| target_isolate_status | [ChunkIsolateFlag](#zbs-ChunkIsolateFlag) | optional |  |
| status | [ChunkHealthyStatus](#zbs-ChunkHealthyStatus) | optional |  |






<a name="zbs-ChunkPerf"></a>

### ChunkPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| access_perf | [AccessPerf](#zbs-AccessPerf) | optional |  |
| recover_perf | [RecoverPerf](#zbs-RecoverPerf) | optional |  |
| lsm_perf | [LSMPerf](#zbs-LSMPerf) | optional |  |






<a name="zbs-ChunkPids"></a>

### ChunkPids



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pids | [uint32](#uint32) | repeated |  |
| rx_pids | [uint32](#uint32) | repeated |  |
| tx_pids | [uint32](#uint32) | repeated |  |






<a name="zbs-ChunkSpaceInfo"></a>

### ChunkSpaceInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| valid_data_space | [uint64](#uint64) | optional |  Default: 0 |
| used_data_space | [uint64](#uint64) | optional |  Default: 0 |
| provisioned_data_space | [uint64](#uint64) | optional | all provisioned space, for internal use, chunk should not set this field Default: 0 |
| total_data_capacity | [uint64](#uint64) | optional |  Default: 0 |
| failure_data_space | [uint64](#uint64) | optional |  Default: 0 |
| allocated_data_space | [uint64](#uint64) | optional |  Default: 0 |
| thin_used_data_space | [uint64](#uint64) | optional |  Default: 0 |
| id | [uint32](#uint32) | optional | id of the chunk, for internal use, chunk should not set this field |
| valid_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| used_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| dirty_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| failure_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| total_cache_capacity | [uint64](#uint64) | optional |  Default: 0 |
| temporary_replica_space | [uint64](#uint64) | optional |  Default: 0 |
| num_rx_pids | [uint32](#uint32) | optional |  Default: 0 |
| num_tx_pids | [uint32](#uint32) | optional |  Default: 0 |
| num_reserved_pids | [uint32](#uint32) | optional |  Default: 0 |






<a name="zbs-ChunkTopology"></a>

### ChunkTopology



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pod_id | [bytes](#bytes) | optional |  |
| rack_id | [bytes](#bytes) | optional |  |
| brick_id | [bytes](#bytes) | optional |  |
| zone_id | [bytes](#bytes) | optional |  |






<a name="zbs-Chunks"></a>

### Chunks



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| chunks | [Chunk](#zbs-Chunk) | repeated |  |






<a name="zbs-CleanChunkInfoCmd"></a>

### CleanChunkInfoCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| chunk | [Chunk](#zbs-Chunk) | required |  |
| secondary_data_ip | [uint32](#uint32) | optional |  |






<a name="zbs-CounterInfo"></a>

### CounterInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | required |  |
| duration | [uint64](#uint64) | required | second |
| total_value | [uint64](#uint64) | required |  |
| total_time | [uint64](#uint64) | required |  |
| total_cnt | [uint64](#uint64) | required |  |
| last_value | [uint64](#uint64) | required |  |
| last_time | [uint64](#uint64) | required |  |
| last_cnt | [uint64](#uint64) | required |  |
| cur_value | [uint64](#uint64) | required |  |
| cur_time | [uint64](#uint64) | required |  |
| cur_cnt | [uint64](#uint64) | required |  |






<a name="zbs-CpuUsage"></a>

### CpuUsage



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| idle | [double](#double) | optional | pecentage [0.0, 1.0] Default: 0 |
| busy | [double](#double) | optional |  Default: 0 |
| iowait | [double](#double) | optional |  Default: 0 |






<a name="zbs-DataChannelConnectivity"></a>

### DataChannelConnectivity



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dst_cid | [uint32](#uint32) | required |  |
| status | [DataChannelStatus](#zbs-DataChannelStatus) | optional |  Default: DC_STATUS_CONNECTED |






<a name="zbs-Dimension"></a>

### Dimension



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| row | [int32](#int32) | optional |  Default: 1 |
| column | [int32](#int32) | optional |  Default: 1 |






<a name="zbs-DiskPerf"></a>

### DiskPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [string](#string) | required |  |
| perf | [StoragePerf](#zbs-StoragePerf) | optional |  |






<a name="zbs-DiskUsage"></a>

### DiskUsage



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | optional |  |
| rd_ios | [uint64](#uint64) | optional |  |
| wr_ios | [uint64](#uint64) | optional |  |
| rd_bps | [uint64](#uint64) | optional | Read Bytes/s |
| wr_bps | [uint64](#uint64) | optional | Write Bytes/s |
| avgrq_sz | [double](#double) | optional | Average request size (sector) |
| avgqu_sz | [double](#double) | optional | Average IO queue size |
| await | [double](#double) | optional | average io wait time (ms) |
| svctm | [double](#double) | optional | average io service time (ms) |
| util | [double](#double) | optional | io util [0, 1.00] |






<a name="zbs-DiskUsages"></a>

### DiskUsages



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| usages | [DiskUsage](#zbs-DiskUsage) | repeated |  |






<a name="zbs-GFlagsVar"></a>

### GFlagsVar



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [string](#string) | required |  |
| value | [string](#string) | required |  |






<a name="zbs-GFlagsVarName"></a>

### GFlagsVarName



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [string](#string) | required |  |






<a name="zbs-GFlagsVars"></a>

### GFlagsVars



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vars | [GFlagsVar](#zbs-GFlagsVar) | repeated |  |






<a name="zbs-GcCmd"></a>

### GcCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| epoch | [uint64](#uint64) | optional |  |






<a name="zbs-GetPExtentInfoCmd"></a>

### GetPExtentInfoCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pextent_range_start | [uint32](#uint32) | optional | include this extent |
| pextent_range_end | [uint32](#uint32) | optional | not including this extent |






<a name="zbs-HeapProfilerRequest"></a>

### HeapProfilerRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| prefix | [string](#string) | required |  |






<a name="zbs-JournalPerf"></a>

### JournalPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [bytes](#bytes) | optional |  |
| perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| max_latency_ns | [float](#float) | optional |  |






<a name="zbs-LSMDBPerf"></a>

### LSMDBPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| get_rate | [float](#float) | optional |  |
| get_latency_ns | [float](#float) | optional |  |
| max_get_latency_ns | [float](#float) | optional |  |
| put_rate | [float](#float) | optional |  |
| put_latency_ns | [float](#float) | optional |  |
| max_put_latency_ns | [float](#float) | optional |  |
| write_rate | [float](#float) | optional |  |
| write_latency_ns | [float](#float) | optional |  |
| max_write_latency_ns | [float](#float) | optional |  |
| delete_rate | [float](#float) | optional |  |
| delete_latency_ns | [float](#float) | optional |  |
| max_delete_latency_ns | [float](#float) | optional |  |






<a name="zbs-LSMMeta"></a>

### LSMMeta



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lsm_uuid | [bytes](#bytes) | optional |  |
| ctime | [uint64](#uint64) | optional |  |
| deprecated | [bytes](#bytes) | optional |  |
| version | [LSMVersion](#zbs-LSMVersion) | optional |  |
| lsm_state | [LSMState](#zbs-LSMState) | optional |  Default: LSM_READY |






<a name="zbs-LSMPerf"></a>

### LSMPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| read_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| read_cache_hit_rate | [float](#float) | optional |  |
| write_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| write_cache_hit_rate | [float](#float) | optional |  |
| total_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| total_cache_hit_rate | [float](#float) | optional |  |
| failed_io_rate | [float](#float) | optional |  |
| hard_io_rate | [float](#float) | optional |  |
| slow_io_rate | [float](#float) | optional |  |
| throttle_latency_ns | [float](#float) | optional |  |
| cache_promotion_rate | [float](#float) | optional |  |
| cache_promotion_bps | [float](#float) | optional |  |
| cache_writeback_rate | [float](#float) | optional |  |
| cache_writeback_bps | [float](#float) | optional |  |
| journal_reclaim_rate | [float](#float) | optional |  |
| journal_reclaim_latency_ns | [float](#float) | optional |  |
| db_perf | [LSMDBPerf](#zbs-LSMDBPerf) | optional |  |
| journal_perfs | [JournalPerf](#zbs-JournalPerf) | repeated |  |
| cache_perfs | [BlockDevicePerf](#zbs-BlockDevicePerf) | repeated |  |
| partition_perfs | [BlockDevicePerf](#zbs-BlockDevicePerf) | repeated |  |
| unmap_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |






<a name="zbs-LSMVersion"></a>

### LSMVersion



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| major_num | [uint64](#uint64) | required |  |
| minor_num | [uint64](#uint64) | required |  |
| revision_num | [uint64](#uint64) | required |  |






<a name="zbs-Label"></a>

### Label



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [bytes](#bytes) | required |  |
| value | [bytes](#bytes) | required |  |






<a name="zbs-Labels"></a>

### Labels



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| labels | [Label](#zbs-Label) | repeated |  |






<a name="zbs-Lease"></a>

### Lease



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| owner | [SessionInfo](#zbs-SessionInfo) | optional | owner info |
| pid | [uint32](#uint32) | optional | corresponding pextent info |
| location | [uint32](#uint32) | optional |  |
| origin_pid | [uint32](#uint32) | optional |  Default: 0 |
| epoch | [uint64](#uint64) | optional |  Default: 0 |
| origin_epoch | [uint64](#uint64) | optional |  Default: 0 |
| ever_exist | [bool](#bool) | optional |  Default: false |
| meta_generation | [uint64](#uint64) | optional | meta-recorded gen Default: 0 |
| expected_replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  Default: true |
| chunks | [Chunk](#zbs-Chunk) | repeated |  |
| temporary_replicas | [TemporaryReplica](#zbs-TemporaryReplica) | repeated |  |






<a name="zbs-License"></a>

### License



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| serial | [string](#string) | required | installation serial number |
| version | [string](#string) | optional |  |
| software_edition | [SoftwareEdition](#zbs-SoftwareEdition) | optional |  |
| max_chunk_num | [uint64](#uint64) | required |  |
| max_physical_data_capacity | [uint64](#uint64) | optional | bytes. 0 means no limit |
| max_physical_data_capacity_per_node | [uint64](#uint64) | optional | bytes. 0 means no limit |
| license_capabilities | [LicenseCapabilities](#zbs-LicenseCapabilities) | optional |  |
| license_type | [LicenseType](#zbs-LicenseType) | optional |  Default: TRIAL |
| subscription_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| subscription_period | [uint64](#uint64) | optional | seconds |
| vendor | [string](#string) | optional |  |
| maintenance_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| maintenance_period | [uint64](#uint64) | optional | seconds |
| sign_date | [uint64](#uint64) | required | move sign_date from index 1 to index 31 to break compatibility

seconds, UNIX TIME |
| platform | [uint32](#uint32) | optional |  |
| mode | [string](#string) | optional | license mode: ALL/SERVER_SAN/HCI |
| period | [uint64](#uint64) | required | seconds. move from index 2 to index 34 to break compatibility |
| pricing_type | [PricingType](#zbs-PricingType) | optional |  Default: PRICING_TYPE_UNKNOWN |






<a name="zbs-LicenseCapabilities"></a>

### LicenseCapabilities



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| has_metrox | [bool](#bool) | optional |  Default: false |
| has_remote_backup | [bool](#bool) | optional |  Default: false |






<a name="zbs-LicenseCertificate"></a>

### LicenseCertificate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| license | [License](#zbs-License) | required |  |
| signature_hex | [string](#string) | required |  |






<a name="zbs-LicenseCertificateV1"></a>

### LicenseCertificateV1



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| license | [LicenseV1](#zbs-LicenseV1) | required |  |
| signature_hex | [string](#string) | required |  |






<a name="zbs-LicenseCertificateV2"></a>

### LicenseCertificateV2



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| license | [LicenseV2](#zbs-LicenseV2) | required |  |
| signature_hex | [string](#string) | required |  |






<a name="zbs-LicenseRequest"></a>

### LicenseRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| certificate_hex | [string](#string) | required |  |






<a name="zbs-LicenseV1"></a>

### LicenseV1



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| sign_date | [uint64](#uint64) | required | seconds, UNIX TIME |
| period | [uint64](#uint64) | required | seconds |
| serial | [string](#string) | required | instalation serial number |
| version | [string](#string) | optional |  |
| software_edition | [SoftwareEdition](#zbs-SoftwareEdition) | optional |  |
| max_chunk_num | [uint64](#uint64) | required |  |
| max_physical_data_capacity | [uint64](#uint64) | optional | bytes. 0 means no limit |
| max_physical_data_capacity_per_node | [uint64](#uint64) | optional | bytes. 0 means no limit |
| license_capabilities | [LicenseCapabilities](#zbs-LicenseCapabilities) | optional |  |
| license_type | [LicenseType](#zbs-LicenseType) | optional |  Default: TRIAL |
| subscription_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| subscription_period | [uint64](#uint64) | optional | seconds |
| maintenance_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| maintenance_period | [uint64](#uint64) | optional | seconds |
| deprecated__product_name | [string](#string) | required | **Deprecated.** NOTE: The following optionals are deprecated, kept just for compatibility |
| max_pool_num | [uint64](#uint64) | optional | **Deprecated.**  |
| max_volume_num | [uint64](#uint64) | optional | **Deprecated.**  |
| max_snap_num | [uint64](#uint64) | optional | **Deprecated.** per volume |
| max_volume_size | [uint64](#uint64) | optional | **Deprecated.** GB |
| deprecated__max_physical_space_size | [uint64](#uint64) | optional | **Deprecated.** bytes |






<a name="zbs-LicenseV2"></a>

### LicenseV2



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| period | [uint64](#uint64) | required | seconds |
| serial | [string](#string) | required | installation serial number |
| version | [string](#string) | optional |  |
| software_edition | [SoftwareEdition](#zbs-SoftwareEdition) | optional |  |
| max_chunk_num | [uint64](#uint64) | required |  |
| max_physical_data_capacity | [uint64](#uint64) | optional | bytes. 0 means no limit |
| max_physical_data_capacity_per_node | [uint64](#uint64) | optional | bytes. 0 means no limit |
| license_capabilities | [LicenseCapabilities](#zbs-LicenseCapabilities) | optional |  |
| license_type | [LicenseType](#zbs-LicenseType) | optional |  Default: TRIAL |
| subscription_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| subscription_period | [uint64](#uint64) | optional | seconds |
| vendor | [string](#string) | optional |  |
| maintenance_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| maintenance_period | [uint64](#uint64) | optional | seconds |
| sign_date | [uint64](#uint64) | required | move sign_date from index 1 to index 31 to break compatibility

seconds, UNIX TIME |
| platform | [uint32](#uint32) | optional |  |






<a name="zbs-ListCounterRequest"></a>

### ListCounterRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pattern | [string](#string) | optional |  Default: * |






<a name="zbs-ListCounterResponse"></a>

### ListCounterResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| counters | [CounterInfo](#zbs-CounterInfo) | repeated |  |






<a name="zbs-ListRecoverResponse"></a>

### ListRecoverResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| speed | [uint64](#uint64) | required |  |
| infos | [RecoverInfo](#zbs-RecoverInfo) | repeated |  |
| recover_speed | [uint64](#uint64) | optional |  |
| migrate_speed | [uint64](#uint64) | optional |  |
| cross_zone_recover_speed | [uint64](#uint64) | optional |  |
| cross_zone_migrate_speed | [uint64](#uint64) | optional |  |
| cross_zone_speed | [uint64](#uint64) | optional |  |
| from_local_speed | [uint64](#uint64) | optional |  |
| from_local_recover_speed | [uint64](#uint64) | optional |  |
| from_local_migrate_speed | [uint64](#uint64) | optional |  |
| from_remote_speed | [uint64](#uint64) | optional |  |
| from_remote_recover_speed | [uint64](#uint64) | optional |  |
| from_remote_migrate_speed | [uint64](#uint64) | optional |  |






<a name="zbs-ListThreadCacheResponse"></a>

### ListThreadCacheResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| infos | [ThreadCacheInfo](#zbs-ThreadCacheInfo) | repeated |  |
| total_used_memory | [uint64](#uint64) | required |  |






<a name="zbs-LocalIOPerf"></a>

### LocalIOPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| total_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| read_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| write_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| throttle_latency_ns | [float](#float) | optional |  |






<a name="zbs-MaintenanceCmd"></a>

### MaintenanceCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cid | [ChunkId](#zbs-ChunkId) | required |  |






<a name="zbs-MemUsage"></a>

### MemUsage



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| total | [uint64](#uint64) | optional | KB |
| free | [uint64](#uint64) | optional |  |
| available | [uint64](#uint64) | optional |  |






<a name="zbs-NegotiationAbility"></a>

### NegotiationAbility



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| has_config_push_ability | [bool](#bool) | optional |  Default: false |
| has_thick_extent_ability | [bool](#bool) | optional |  Default: false |
| synced_thick_extent | [bool](#bool) | optional |  Default: false |
| has_unmap_ability | [bool](#bool) | optional |  Default: false |






<a name="zbs-NetworkUsage"></a>

### NetworkUsage



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | optional | Byts/s |
| rx | [uint64](#uint64) | optional |  |
| tx | [uint64](#uint64) | optional |  |






<a name="zbs-NetworkUsages"></a>

### NetworkUsages



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| usages | [NetworkUsage](#zbs-NetworkUsage) | repeated |  |






<a name="zbs-NodePerf"></a>

### NodePerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| disks | [DiskUsages](#zbs-DiskUsages) | optional |  |
| networks | [NetworkUsages](#zbs-NetworkUsages) | optional |  |
| cpu | [CpuUsage](#zbs-CpuUsage) | optional |  |
| mem | [MemUsage](#zbs-MemUsage) | optional |  |






<a name="zbs-PExtent"></a>

### PExtent



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| location | [uint32](#uint32) | optional |  Default: 0 |
| alive_location | [uint32](#uint32) | optional |  |
| expected_replica_num | [uint32](#uint32) | optional |  |
| ever_exist | [bool](#bool) | optional |  |
| garbage | [bool](#bool) | optional |  Default: false |
| origin_pid | [uint32](#uint32) | optional |  |
| epoch | [uint64](#uint64) | optional |  Default: 0 |
| origin_epoch | [uint64](#uint64) | optional |  Default: 0 |
| generation | [uint64](#uint64) | optional |  Default: 0 |
| preferred_cid | [uint32](#uint32) | optional |  Default: 0 |
| thin_provision | [bool](#bool) | optional |  Default: true |
| read_only | [bool](#bool) | optional |  Default: false |
| temporary_replicas | [TemporaryReplica](#zbs-TemporaryReplica) | repeated |  |






<a name="zbs-PExtentInfo"></a>

### PExtentInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| generation | [uint64](#uint64) | required |  |
| origin_pid | [uint32](#uint32) | optional |  |
| epoch | [uint64](#uint64) | optional |  |
| origin_epoch | [uint64](#uint64) | optional |  |
| status | [PExtentStatus](#zbs-PExtentStatus) | optional | backward compatibility; Default: PEXTENT_STATUS_ALLOCATED |
| thin_provision | [bool](#bool) | optional |  Default: true |
| used_data_space | [uint64](#uint64) | optional |  Default: 0 |






<a name="zbs-PExtentResp"></a>

### PExtentResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| location | [uint32](#uint32) | optional |  Default: 0 |
| alive_location | [uint32](#uint32) | optional |  |
| expected_replica_num | [uint32](#uint32) | optional |  |
| ever_exist | [bool](#bool) | optional |  |
| garbage | [bool](#bool) | optional |  Default: false |
| origin_pid | [uint32](#uint32) | optional |  |
| epoch | [uint64](#uint64) | optional |  Default: 0 |
| origin_epoch | [uint64](#uint64) | optional |  Default: 0 |
| generation | [uint64](#uint64) | optional |  Default: 0 |
| preferred_cid | [uint32](#uint32) | optional |  Default: 0 |
| owner_cid | [uint32](#uint32) | optional |  Default: 0 |
| thin_provision | [bool](#bool) | optional |  Default: true |
| temporary_replicas | [TemporaryReplica](#zbs-TemporaryReplica) | repeated |  |






<a name="zbs-Pagination"></a>

### Pagination



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pos | [uint64](#uint64) | optional | for pagination Default: 0 |
| num | [uint64](#uint64) | optional |  Default: 512 |






<a name="zbs-Position"></a>

### Position



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| row | [int32](#int32) | optional |  Default: 1 |
| column | [int32](#int32) | optional |  Default: 1 |






<a name="zbs-ProfilerRequest"></a>

### ProfilerRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| file_name | [string](#string) | required |  |
| thread_filter | [string](#string) | optional |  |






<a name="zbs-RangeU64"></a>

### RangeU64



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| start | [uint64](#uint64) | required |  |
| length | [uint64](#uint64) | required |  |






<a name="zbs-RecoverCmd"></a>

### RecoverCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| lease | [Lease](#zbs-Lease) | optional | lease for the pextent |
| dst_chunk | [uint32](#uint32) | required |  |
| replace_chunk | [uint32](#uint32) | optional |  |
| src_chunk | [uint32](#uint32) | optional |  |
| is_migrate | [bool](#bool) | optional |  Default: false |
| epoch | [uint64](#uint64) | optional |  |
| active_location | [uint32](#uint32) | optional | active_location when meta generation cmd, only for display now |
| recover_from_temporary_replica | [RecoverFromTemporaryReplica](#zbs-RecoverFromTemporaryReplica) | optional |  |
| agile_recover_only | [bool](#bool) | optional |  Default: false |
| start_ms | [uint64](#uint64) | optional | the following is for internal use by MetaServer |






<a name="zbs-RecoverFromTemporaryReplica"></a>

### RecoverFromTemporaryReplica



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| src_temporary_replica | [TemporaryReplica](#zbs-TemporaryReplica) | required |  |
| rollback_failed_replica | [bool](#bool) | optional |  Default: false |
| force_recover_from_temporary_replia | [bool](#bool) | optional |  Default: false |






<a name="zbs-RecoverInfo"></a>

### RecoverInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| state | [RecoverState](#zbs-RecoverState) | required |  |
| cur_block | [uint32](#uint32) | required |  |
| src_cid | [uint32](#uint32) | required |  |
| dst_cid | [uint32](#uint32) | required |  |
| is_migrate | [bool](#bool) | required |  |
| silence_ms | [uint64](#uint64) | required |  |
| replace_cid | [uint32](#uint32) | optional |  Default: 0 |
| epoch | [uint64](#uint64) | optional |  |






<a name="zbs-RecoverPerf"></a>

### RecoverPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| recover_migrate_speed | [uint64](#uint64) | optional |  |
| recover_speed | [uint64](#uint64) | optional |  |
| migrate_speed | [uint64](#uint64) | optional |  |
| cross_zone_recover_speed | [uint64](#uint64) | optional |  |
| cross_zone_migrate_speed | [uint64](#uint64) | optional |  |
| cross_zone_recover_migrate_speed | [uint64](#uint64) | optional |  |






<a name="zbs-RecoverSummary"></a>

### RecoverSummary



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pending_migrates_bytes | [uint64](#uint64) | optional |  |
| pending_recovers_bytes | [uint64](#uint64) | optional |  |
| recover_speed | [uint64](#uint64) | optional | **Deprecated.** bps |
| migrate_speed | [uint64](#uint64) | optional | **Deprecated.** bps |
| recover_migrate_speed | [uint64](#uint64) | optional | **Deprecated.** bps |
| cross_zone_recover_speed | [uint64](#uint64) | optional | **Deprecated.** bps |
| cross_zone_migrate_speed | [uint64](#uint64) | optional | **Deprecated.** bps |
| cross_zone_recover_migrate_speed | [uint64](#uint64) | optional | **Deprecated.** bps |






<a name="zbs-ReplicaIOPerf"></a>

### ReplicaIOPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| from_cid | [uint32](#uint32) | required |  |
| to_cid | [uint32](#uint32) | required |  |
| total_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| read_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |
| write_perf | [StoragePerf](#zbs-StoragePerf) | optional |  |






<a name="zbs-RevokeCmd"></a>

### RevokeCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vtable_id | [bytes](#bytes) | required |  |
| pid | [uint32](#uint32) | optional |  |






<a name="zbs-RpcErrorDetail"></a>

### RpcErrorDetail



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| error_code | [ErrorCode](#zbs-ErrorCode) | optional |  |
| error_message | [string](#string) | optional |  |






<a name="zbs-RpcStatus"></a>

### RpcStatus



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ok | [bool](#bool) | required |  Default: true |
| errors | [RpcErrorDetail](#zbs-RpcErrorDetail) | repeated |  |
| user_code | [UserCode](#zbs-UserCode) | optional |  |






<a name="zbs-SessionInfo"></a>

### SessionInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| uuid | [bytes](#bytes) | required |  |
| ip | [bytes](#bytes) | required |  |
| num_ip | [uint32](#uint32) | optional | num representation of ip |
| port | [uint32](#uint32) | required |  |
| cid | [uint32](#uint32) | optional | chunk cid if session is a chunk session |
| local_cid | [uint32](#uint32) | optional | closes chunk to this session (e.g. local chunk) |
| secondary_data_ip | [bytes](#bytes) | optional |  |
| zone | [bytes](#bytes) | optional |  |
| scvm_mode_host_data_ip | [bytes](#bytes) | optional |  |
| alive_sec | [uint64](#uint64) | optional |  |
| machine_uuid | [string](#string) | optional |  |






<a name="zbs-SessionInfos"></a>

### SessionInfos



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| infos | [SessionInfo](#zbs-SessionInfo) | repeated |  |






<a name="zbs-SetVLOGRequest"></a>

### SetVLOGRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| module_pattern | [string](#string) | optional |  |
| log_level | [int32](#int32) | required |  |






<a name="zbs-StatusRequest"></a>

### StatusRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enable | [bool](#bool) | required |  |






<a name="zbs-StatusResponse"></a>

### StatusResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enabled | [bool](#bool) | required |  |






<a name="zbs-StoragePerf"></a>

### StoragePerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| iops | [float](#float) | optional | io per second Default: 0 |
| bandwidth | [float](#float) | optional | Bytes per second Default: 0 |
| latency | [float](#float) | optional | nano seconds Default: 0 |
| avg_request_size | [float](#float) | optional | Bytes Default: 0 |
| cache_hit_rate | [float](#float) | optional | **Deprecated.** 0.0~1.0 |






<a name="zbs-StoragePool"></a>

### StoragePool



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | optional |  |
| name | [bytes](#bytes) | optional |  |
| chunks | [Chunk](#zbs-Chunk) | repeated | chunks in the storage pool |
| space | [StorageSpace](#zbs-StorageSpace) | optional |  |
| total_nodes | [uint32](#uint32) | optional |  Default: 0 |
| healthy_nodes | [uint32](#uint32) | optional |  Default: 0 |
| connecting_nodes | [uint32](#uint32) | optional | nodes are being connecting Default: 0 |
| warning_nodes | [uint32](#uint32) | optional |  Default: 0 |
| error_nodes | [uint32](#uint32) | optional |  Default: 0 |
| removing_nodes | [uint32](#uint32) | optional | nodes are being removed Default: 0 |
| total_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** perf info |
| read_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| write_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_read_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_write_perf | [StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| num_ongoing_recovers | [uint32](#uint32) | optional | recover and migrate info Default: 0 |
| num_pending_recovers | [uint32](#uint32) | optional |  Default: 0 |
| num_ongoing_migrates | [uint32](#uint32) | optional |  Default: 0 |
| num_pending_migrates | [uint32](#uint32) | optional |  Default: 0 |
| num_pending_recycles | [uint32](#uint32) | optional |  Default: 0 |
| recover_info | [RecoverSummary](#zbs-RecoverSummary) | optional |  |
| chunk_perf | [ChunkPerf](#zbs-ChunkPerf) | optional |  |






<a name="zbs-StoragePoolId"></a>

### StoragePoolId



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |






<a name="zbs-StoragePools"></a>

### StoragePools



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| storage_pools | [StoragePool](#zbs-StoragePool) | repeated |  |






<a name="zbs-StorageSpace"></a>

### StorageSpace



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| valid_data_space | [uint64](#uint64) | optional |  |
| provisioned_data_space | [uint64](#uint64) | optional |  |
| used_data_space | [uint64](#uint64) | optional |  |
| total_data_capacity | [uint64](#uint64) | optional |  |
| failure_data_space | [uint64](#uint64) | optional |  |
| valid_cache_space | [uint64](#uint64) | optional |  |
| used_cache_space | [uint64](#uint64) | optional |  |
| dirty_cache_space | [uint64](#uint64) | optional |  |
| failure_cache_space | [uint64](#uint64) | optional |  |
| total_cache_capacity | [uint64](#uint64) | optional |  |
| temporary_replica_space | [uint64](#uint64) | optional |  |






<a name="zbs-TemporaryReplica"></a>

### TemporaryReplica



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_pid | [uint32](#uint32) | required |  |
| temporary_epoch | [uint64](#uint64) | required |  |
| failed_cid | [uint32](#uint32) | required |  |
| create_at_generation | [uint64](#uint64) | required |  |
| location | [uint32](#uint32) | optional |  Default: 0 |
| ever_exist | [bool](#bool) | optional |  Default: false |
| meta_generation | [uint64](#uint64) | optional |  Default: 0 |
| pid | [uint32](#uint32) | optional |  |
| epoch | [uint64](#uint64) | optional |  |






<a name="zbs-ThreadCacheInfo"></a>

### ThreadCacheInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| thread_id | [int64](#int64) | required |  |
| thread_name | [string](#string) | required |  |
| cached_size | [uint64](#uint64) | required |  |
| used_size | [int64](#int64) | required |  |






<a name="zbs-TimeSpec"></a>

### TimeSpec



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| seconds | [int64](#int64) | required |  |
| nseconds | [int64](#int64) | required |  |






<a name="zbs-TopoObj"></a>

### TopoObj



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| type | [TopoType](#zbs-TopoType) | optional |  Default: NODE |
| id | [bytes](#bytes) | optional |  |
| parent_id | [bytes](#bytes) | optional | if no parent is set, the default is the root object called &#34;topo&#34; Default: topo |
| name | [bytes](#bytes) | optional |  |
| create_time | [uint64](#uint64) | optional |  |
| desc | [bytes](#bytes) | optional |  |
| position | [Position](#zbs-Position) | optional |  |
| capacity | [Capacity](#zbs-Capacity) | optional |  |
| dimension | [Dimension](#zbs-Dimension) | optional |  |






<a name="zbs-TopoObjId"></a>

### TopoObjId



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |






<a name="zbs-TopoObjs"></a>

### TopoObjs



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| objs | [TopoObj](#zbs-TopoObj) | repeated |  |






<a name="zbs-TowerLicense"></a>

### TowerLicense



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| version | [uint32](#uint32) | required |  |
| serial | [string](#string) | required |  |
| software_edition | [SoftwareEdition](#zbs-SoftwareEdition) | required |  |
| license_type | [LicenseType](#zbs-LicenseType) | optional |  Default: TRIAL |
| max_node_num | [uint64](#uint64) | required |  |
| max_cluster_num | [uint64](#uint64) | required |  |
| sign_date | [uint64](#uint64) | required | seconds, UNIX TIME |
| period | [uint64](#uint64) | required | seconds |
| maintenance_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| maintenance_period | [uint64](#uint64) | optional | seconds |
| subscription_start_date | [uint64](#uint64) | optional | seconds, UNIX TIME |
| subscription_period | [uint64](#uint64) | optional | seconds |






<a name="zbs-TowerLicenseCertificate"></a>

### TowerLicenseCertificate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| license | [TowerLicense](#zbs-TowerLicense) | required |  |
| signature_hex | [string](#string) | required |  |






<a name="zbs-UID"></a>

### UID



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| low_bits | [uint64](#uint64) | required |  |
| high_bits | [uint64](#uint64) | required |  |






<a name="zbs-VExtent"></a>

### VExtent



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vextent_id | [uint32](#uint32) | required | vextent table, max size: 64KB |
| location | [uint32](#uint32) | required | pextent locations; |
| alive_location | [uint32](#uint32) | required |  |






<a name="zbs-VExtentLease"></a>

### VExtentLease



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lease | [Lease](#zbs-Lease) | required |  |
| cow | [bool](#bool) | optional |  |






<a name="zbs-Void"></a>

### Void






 


<a name="zbs-ChunkHealthyStatus"></a>

### ChunkHealthyStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| CHUNK_HEALTHY_STATUS_NORMAL | 1 |  |
| CHUNK_HEALTHY_STATUS_FAIL_SLOW_ISOLATING | 2 |  |
| CHUNK_HEALTHY_STATUS_FAIL_SLOW_NOT_ISOLATE | 3 |  |
| CHUNK_HEALTHY_STATUS_FAIL_SLOW_ISOLATED | 4 |  |



<a name="zbs-ChunkState"></a>

### ChunkState


| Name | Number | Description |
| ---- | ------ | ----------- |
| CHUNK_STATE_UNKNOWN | 0 |  |
| CHUNK_STATE_IDLE | 1 |  |
| CHUNK_STATE_IN_USE | 2 |  |
| CHUNK_STATE_REMOVING | 3 |  |



<a name="zbs-ChunkStatus"></a>

### ChunkStatus
TODO(wenhao): add more states: CLEANING, CLEANED

| Name | Number | Description |
| ---- | ------ | ----------- |
| CHUNK_STATUS_INITIALIZING | 1 |  |
| CHUNK_STATUS_CONNECTED_HEALTHY | 2 |  |
| CHUNK_STATUS_CONNECTED_ERROR | 3 |  |
| CHUNK_STATUS_CONNECTED_WARNING | 4 |  |
| CHUNK_STATUS_CONNECTING | 5 |  |
| CHUNK_STATUS_SESSION_EXPIRED | 6 |  |



<a name="zbs-DataChannelStatus"></a>

### DataChannelStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| DC_STATUS_CONNECTED | 1 |  |
| DC_STATUS_DISCONNECTED | 2 |  |



<a name="zbs-LSMState"></a>

### LSMState


| Name | Number | Description |
| ---- | ------ | ----------- |
| LSM_READY | 0 |  |
| LSM_UPGRADING | 1 |  |
| LSM_ROLLBACKING | 2 |  |



<a name="zbs-LicenseType"></a>

### LicenseType


| Name | Number | Description |
| ---- | ------ | ----------- |
| TRIAL | 1 |  |
| PERPETUAL | 2 |  |
| SUBSCRIPTION | 3 |  |



<a name="zbs-MigrateMode"></a>

### MigrateMode


| Name | Number | Description |
| ---- | ------ | ----------- |
| MIGRATE_AUTO | 0 |  |
| MIGRATE_STATIC | 1 |  |
| MIGRATE_INVALID | 2 |  |



<a name="zbs-PExtentStatus"></a>

### PExtentStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| PEXTENT_STATUS_INIT | 99 | this status should only appear when object is allocated |
| PEXTENT_STATUS_INVALID | 0 | processing invalidation |
| PEXTENT_STATUS_ALLOCATED | 1 | status OK |
| PEXTENT_STATUS_RECOVERING | 2 | data moving in |
| PEXTENT_STATUS_OFFLINE | 3 | data lost due to device offline |
| PEXTENT_STATUS_CORRUPT | 4 | checksum error occurred |
| PEXTENT_STATUS_IOERROR | 5 | io error occurred |
| PEXTENT_STATUS_UMOUNTING | 6 | umounting and waiting recovery/migration |



<a name="zbs-PricingType"></a>

### PricingType


| Name | Number | Description |
| ---- | ------ | ----------- |
| PRICING_TYPE_UNKNOWN | 1 |  |
| CPU_SLOT_NUM | 2 |  |
| VM_NUM | 3 |  |



<a name="zbs-RecoverMode"></a>

### RecoverMode


| Name | Number | Description |
| ---- | ------ | ----------- |
| RECOVER_AUTO | 0 |  |
| RECOVER_STATIC | 1 |  |
| RECOVER_INVALID | 2 |  |



<a name="zbs-RecoverState"></a>

### RecoverState


| Name | Number | Description |
| ---- | ------ | ----------- |
| INIT | 0 |  |
| START | 1 |  |
| READ | 2 |  |
| WRITE | 3 |  |
| END | 4 |  |



<a name="zbs-SoftwareEdition"></a>

### SoftwareEdition


| Name | Number | Description |
| ---- | ------ | ----------- |
| STANDARD | 1 |  |
| ENTERPRISE | 2 |  |
| ENTERPRISE_EISOO | 3 |  |
| ESSENTIAL | 4 |  |
| COMMUNITY | 5 |  |
| EXPRESS | 6 |  |



<a name="zbs-TopoType"></a>

### TopoType


| Name | Number | Description |
| ---- | ------ | ----------- |
| NODE | 0 |  |
| BRICK | 1 |  |
| RACK | 2 |  |
| POD | 3 |  |
| CLUSTER | 4 |  |
| ZONE | 5 |  |
| ALL_TOPO_TYPE | 99 |  |


 


<a name="common-proto-extensions"></a>

### File-level Extensions
| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| rpc_service_id | uint32 | .google.protobuf.ServiceOptions | 1000 |  |

 


<a name="zbs-CommonService"></a>

### CommonService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| ListCounter | [ListCounterRequest](#zbs-ListCounterRequest) | [ListCounterResponse](#zbs-ListCounterResponse) |  |
| StartProfiler | [ProfilerRequest](#zbs-ProfilerRequest) | [Void](#zbs-Void) |  |
| StopProfiler | [Void](#zbs-Void) | [Void](#zbs-Void) |  |
| StartHeapProfiler | [HeapProfilerRequest](#zbs-HeapProfilerRequest) | [Void](#zbs-Void) |  |
| StopHeapProfiler | [Void](#zbs-Void) | [Void](#zbs-Void) |  |
| ListThreadCache | [Void](#zbs-Void) | [ListThreadCacheResponse](#zbs-ListThreadCacheResponse) |  |
| SetVLOG | [SetVLOGRequest](#zbs-SetVLOGRequest) | [Void](#zbs-Void) |  |
| GetGFlagsVar | [GFlagsVarName](#zbs-GFlagsVarName) | [GFlagsVar](#zbs-GFlagsVar) |  |
| SetGFlagsVar | [GFlagsVar](#zbs-GFlagsVar) | [Void](#zbs-Void) |  |
| ListGFlagsVar | [Void](#zbs-Void) | [GFlagsVars](#zbs-GFlagsVars) |  |


<a name="zbs-SystemManagementService"></a>

### SystemManagementService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| Enable | [StatusRequest](#zbs-StatusRequest) | [RpcStatus](#zbs-RpcStatus) |  |
| Show | [Void](#zbs-Void) | [StatusResponse](#zbs-StatusResponse) |  |

 



<a name="descriptor-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## descriptor.proto



<a name="google-protobuf-DescriptorProto"></a>

### DescriptorProto
Describes a message type.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| field | [FieldDescriptorProto](#google-protobuf-FieldDescriptorProto) | repeated |  |
| extension | [FieldDescriptorProto](#google-protobuf-FieldDescriptorProto) | repeated |  |
| nested_type | [DescriptorProto](#google-protobuf-DescriptorProto) | repeated |  |
| enum_type | [EnumDescriptorProto](#google-protobuf-EnumDescriptorProto) | repeated |  |
| extension_range | [DescriptorProto.ExtensionRange](#google-protobuf-DescriptorProto-ExtensionRange) | repeated |  |
| options | [MessageOptions](#google-protobuf-MessageOptions) | optional |  |






<a name="google-protobuf-DescriptorProto-ExtensionRange"></a>

### DescriptorProto.ExtensionRange



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| start | [int32](#int32) | optional |  |
| end | [int32](#int32) | optional |  |






<a name="google-protobuf-EnumDescriptorProto"></a>

### EnumDescriptorProto
Describes an enum type.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| value | [EnumValueDescriptorProto](#google-protobuf-EnumValueDescriptorProto) | repeated |  |
| options | [EnumOptions](#google-protobuf-EnumOptions) | optional |  |






<a name="google-protobuf-EnumOptions"></a>

### EnumOptions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| allow_alias | [bool](#bool) | optional | Set this option to false to disallow mapping different tag names to a same value. Default: true |
| uninterpreted_option | [UninterpretedOption](#google-protobuf-UninterpretedOption) | repeated | The parser stores options it doesn&#39;t recognize here. See above. |






<a name="google-protobuf-EnumValueDescriptorProto"></a>

### EnumValueDescriptorProto
Describes a value within an enum.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| number | [int32](#int32) | optional |  |
| options | [EnumValueOptions](#google-protobuf-EnumValueOptions) | optional |  |






<a name="google-protobuf-EnumValueOptions"></a>

### EnumValueOptions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| uninterpreted_option | [UninterpretedOption](#google-protobuf-UninterpretedOption) | repeated | The parser stores options it doesn&#39;t recognize here. See above. |






<a name="google-protobuf-FieldDescriptorProto"></a>

### FieldDescriptorProto
Describes a field within a message.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| number | [int32](#int32) | optional |  |
| label | [FieldDescriptorProto.Label](#google-protobuf-FieldDescriptorProto-Label) | optional |  |
| type | [FieldDescriptorProto.Type](#google-protobuf-FieldDescriptorProto-Type) | optional | If type_name is set, this need not be set. If both this and type_name are set, this must be either TYPE_ENUM or TYPE_MESSAGE. |
| type_name | [string](#string) | optional | For message and enum types, this is the name of the type. If the name starts with a &#39;.&#39;, it is fully-qualified. Otherwise, C&#43;&#43;-like scoping rules are used to find the type (i.e. first the nested types within this message are searched, then within the parent, on up to the root namespace). |
| extendee | [string](#string) | optional | For extensions, this is the name of the type being extended. It is resolved in the same manner as type_name. |
| default_value | [string](#string) | optional | For numeric types, contains the original text representation of the value. For booleans, &#34;true&#34; or &#34;false&#34;. For strings, contains the default text contents (not escaped in any way). For bytes, contains the C escaped value. All bytes &gt;= 128 are escaped. TODO(kenton): Base-64 encode? |
| options | [FieldOptions](#google-protobuf-FieldOptions) | optional |  |






<a name="google-protobuf-FieldOptions"></a>

### FieldOptions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ctype | [FieldOptions.CType](#google-protobuf-FieldOptions-CType) | optional | The ctype option instructs the C&#43;&#43; code generator to use a different representation of the field than it normally would. See the specific options below. This option is not yet implemented in the open source release -- sorry, we&#39;ll try to include it in a future version! Default: STRING |
| packed | [bool](#bool) | optional | The packed option can be enabled for repeated primitive fields to enable a more efficient representation on the wire. Rather than repeatedly writing the tag and type for each element, the entire array is encoded as a single length-delimited blob. |
| lazy | [bool](#bool) | optional | Should this field be parsed lazily? Lazy applies only to message-type fields. It means that when the outer message is initially parsed, the inner message&#39;s contents will not be parsed but instead stored in encoded form. The inner message will actually be parsed when it is first accessed.

This is only a hint. Implementations are free to choose whether to use eager or lazy parsing regardless of the value of this option. However, setting this option true suggests that the protocol author believes that using lazy parsing on this field is worth the additional bookkeeping overhead typically needed to implement it.

This option does not affect the public interface of any generated code; all method signatures remain the same. Furthermore, thread-safety of the interface is not affected by this option; const methods remain safe to call from multiple threads concurrently, while non-const methods continue to require exclusive access.

Note that implementations may choose not to check required fields within a lazy sub-message. That is, calling IsInitialized() on the outher message may return true even if the inner message has missing required fields. This is necessary because otherwise the inner message would have to be parsed in order to perform the check, defeating the purpose of lazy parsing. An implementation which chooses not to check required fields must be consistent about it. That is, for any particular sub-message, the implementation must either *always* check its required fields, or *never* check its required fields, regardless of whether or not the message has been parsed. Default: false |
| deprecated | [bool](#bool) | optional | Is this field deprecated? Depending on the target platform, this can emit Deprecated annotations for accessors, or it will be completely ignored; in the very least, this is a formalization for deprecating fields. Default: false |
| experimental_map_key | [string](#string) | optional | EXPERIMENTAL. DO NOT USE. For &#34;map&#34; fields, the name of the field in the enclosed type that is the key for this map. For example, suppose we have: message Item { required string name = 1; required string value = 2; } message Config { repeated Item items = 1 [experimental_map_key=&#34;name&#34;]; } In this situation, the map key for Item will be set to &#34;name&#34;. TODO: Fully-implement this, then remove the &#34;experimental_&#34; prefix. |
| weak | [bool](#bool) | optional | For Google-internal migration only. Do not use. Default: false |
| uninterpreted_option | [UninterpretedOption](#google-protobuf-UninterpretedOption) | repeated | The parser stores options it doesn&#39;t recognize here. See above. |






<a name="google-protobuf-FileDescriptorProto"></a>

### FileDescriptorProto
Describes a complete .proto file.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional | file name, relative to root of source tree |
| package | [string](#string) | optional | e.g. &#34;foo&#34;, &#34;foo.bar&#34;, etc. |
| dependency | [string](#string) | repeated | Names of files imported by this file. |
| public_dependency | [int32](#int32) | repeated | Indexes of the public imported files in the dependency list above. |
| weak_dependency | [int32](#int32) | repeated | Indexes of the weak imported files in the dependency list. For Google-internal migration only. Do not use. |
| message_type | [DescriptorProto](#google-protobuf-DescriptorProto) | repeated | All top-level definitions in this file. |
| enum_type | [EnumDescriptorProto](#google-protobuf-EnumDescriptorProto) | repeated |  |
| service | [ServiceDescriptorProto](#google-protobuf-ServiceDescriptorProto) | repeated |  |
| extension | [FieldDescriptorProto](#google-protobuf-FieldDescriptorProto) | repeated |  |
| options | [FileOptions](#google-protobuf-FileOptions) | optional |  |
| source_code_info | [SourceCodeInfo](#google-protobuf-SourceCodeInfo) | optional | This field contains optional information about the original source code. You may safely remove this entire field whithout harming runtime functionality of the descriptors -- the information is needed only by development tools. |






<a name="google-protobuf-FileDescriptorSet"></a>

### FileDescriptorSet
The protocol compiler can output a FileDescriptorSet containing the .proto
files it parses.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| file | [FileDescriptorProto](#google-protobuf-FileDescriptorProto) | repeated |  |






<a name="google-protobuf-FileOptions"></a>

### FileOptions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| java_package | [string](#string) | optional | Sets the Java package where classes generated from this .proto will be placed. By default, the proto package is used, but this is often inappropriate because proto packages do not normally start with backwards domain names. |
| java_outer_classname | [string](#string) | optional | If set, all the classes from the .proto file are wrapped in a single outer class with the given name. This applies to both Proto1 (equivalent to the old &#34;--one_java_file&#34; option) and Proto2 (where a .proto always translates to a single class, but you may want to explicitly choose the class name). |
| java_multiple_files | [bool](#bool) | optional | If set true, then the Java code generator will generate a separate .java file for each top-level message, enum, and service defined in the .proto file. Thus, these types will *not* be nested inside the outer class named by java_outer_classname. However, the outer class will still be generated to contain the file&#39;s getDescriptor() method as well as any top-level extensions defined in the file. Default: false |
| java_generate_equals_and_hash | [bool](#bool) | optional | If set true, then the Java code generator will generate equals() and hashCode() methods for all messages defined in the .proto file. This is purely a speed optimization, as the AbstractMessage base class includes reflection-based implementations of these methods. Default: false |
| optimize_for | [FileOptions.OptimizeMode](#google-protobuf-FileOptions-OptimizeMode) | optional |  Default: SPEED |
| go_package | [string](#string) | optional | Sets the Go package where structs generated from this .proto will be placed. There is no default. |
| cc_generic_services | [bool](#bool) | optional | Should generic services be generated in each language? &#34;Generic&#34; services are not specific to any particular RPC system. They are generated by the main code generators in each language (without additional plugins). Generic services were the only kind of service generation supported by early versions of proto2.

Generic services are now considered deprecated in favor of using plugins that generate code specific to your particular RPC system. Therefore, these default to false. Old code which depends on generic services should explicitly set them to true. Default: false |
| java_generic_services | [bool](#bool) | optional |  Default: false |
| py_generic_services | [bool](#bool) | optional |  Default: false |
| uninterpreted_option | [UninterpretedOption](#google-protobuf-UninterpretedOption) | repeated | The parser stores options it doesn&#39;t recognize here. See above. |






<a name="google-protobuf-MessageOptions"></a>

### MessageOptions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| message_set_wire_format | [bool](#bool) | optional | Set true to use the old proto1 MessageSet wire format for extensions. This is provided for backwards-compatibility with the MessageSet wire format. You should not use this for any other reason: It&#39;s less efficient, has fewer features, and is more complicated.

The message must be defined exactly as follows: message Foo { option message_set_wire_format = true; extensions 4 to max; } Note that the message cannot have any defined fields; MessageSets only have extensions.

All extensions of your type must be singular messages; e.g. they cannot be int32s, enums, or repeated messages.

Because this is an option, the above two restrictions are not enforced by the protocol compiler. Default: false |
| no_standard_descriptor_accessor | [bool](#bool) | optional | Disables the generation of the standard &#34;descriptor()&#34; accessor, which can conflict with a field of the same name. This is meant to make migration from proto1 easier; new code should avoid fields named &#34;descriptor&#34;. Default: false |
| uninterpreted_option | [UninterpretedOption](#google-protobuf-UninterpretedOption) | repeated | The parser stores options it doesn&#39;t recognize here. See above. |






<a name="google-protobuf-MethodDescriptorProto"></a>

### MethodDescriptorProto
Describes a method of a service.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| input_type | [string](#string) | optional | Input and output type names. These are resolved in the same way as FieldDescriptorProto.type_name, but must refer to a message type. |
| output_type | [string](#string) | optional |  |
| options | [MethodOptions](#google-protobuf-MethodOptions) | optional |  |






<a name="google-protobuf-MethodOptions"></a>

### MethodOptions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| uninterpreted_option | [UninterpretedOption](#google-protobuf-UninterpretedOption) | repeated | The parser stores options it doesn&#39;t recognize here. See above. |






<a name="google-protobuf-ServiceDescriptorProto"></a>

### ServiceDescriptorProto
Describes a service.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| method | [MethodDescriptorProto](#google-protobuf-MethodDescriptorProto) | repeated |  |
| options | [ServiceOptions](#google-protobuf-ServiceOptions) | optional |  |






<a name="google-protobuf-ServiceOptions"></a>

### ServiceOptions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| uninterpreted_option | [UninterpretedOption](#google-protobuf-UninterpretedOption) | repeated | The parser stores options it doesn&#39;t recognize here. See above. |






<a name="google-protobuf-SourceCodeInfo"></a>

### SourceCodeInfo
Encapsulates information about the original source file from which a
FileDescriptorProto was generated.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| location | [SourceCodeInfo.Location](#google-protobuf-SourceCodeInfo-Location) | repeated | A Location identifies a piece of source code in a .proto file which corresponds to a particular definition. This information is intended to be useful to IDEs, code indexers, documentation generators, and similar tools.

For example, say we have a file like: message Foo { optional string foo = 1; } Let&#39;s look at just the field definition: optional string foo = 1; ^ ^^ ^^ ^ ^^^ a bc de f ghi We have the following locations: span path represents [a,i) [ 4, 0, 2, 0 ] The whole field definition. [a,b) [ 4, 0, 2, 0, 4 ] The label (optional). [c,d) [ 4, 0, 2, 0, 5 ] The type (string). [e,f) [ 4, 0, 2, 0, 1 ] The name (foo). [g,h) [ 4, 0, 2, 0, 3 ] The number (1).

Notes: - A location may refer to a repeated field itself (i.e. not to any particular index within it). This is used whenever a set of elements are logically enclosed in a single code segment. For example, an entire extend block (possibly containing multiple extension definitions) will have an outer location whose path refers to the &#34;extensions&#34; repeated field without an index. - Multiple locations may have the same path. This happens when a single logical declaration is spread out across multiple places. The most obvious example is the &#34;extend&#34; block again -- there may be multiple extend blocks in the same scope, each of which will have the same path. - A location&#39;s span is not always a subset of its parent&#39;s span. For example, the &#34;extendee&#34; of an extension declaration appears at the beginning of the &#34;extend&#34; block and is shared by all extensions within the block. - Just because a location&#39;s span is a subset of some other location&#39;s span does not mean that it is a descendent. For example, a &#34;group&#34; defines both a type and a field in a single declaration. Thus, the locations corresponding to the type and field and their components will overlap. - Code which tries to interpret locations should probably be designed to ignore those that it doesn&#39;t understand, as more types of locations could be recorded in the future. |






<a name="google-protobuf-SourceCodeInfo-Location"></a>

### SourceCodeInfo.Location



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [int32](#int32) | repeated | Identifies which part of the FileDescriptorProto was defined at this location.

Each element is a field number or an index. They form a path from the root FileDescriptorProto to the place where the definition. For example, this path: [ 4, 3, 2, 7, 1 ] refers to: file.message_type(3) // 4, 3 .field(7) // 2, 7 .name() // 1 This is because FileDescriptorProto.message_type has field number 4: repeated DescriptorProto message_type = 4; and DescriptorProto.field has field number 2: repeated FieldDescriptorProto field = 2; and FieldDescriptorProto.name has field number 1: optional string name = 1;

Thus, the above path gives the location of a field name. If we removed the last element: [ 4, 3, 2, 7 ] this path refers to the whole field declaration (from the beginning of the label to the terminating semicolon). |
| span | [int32](#int32) | repeated | Always has exactly three or four elements: start line, start column, end line (optional, otherwise assumed same as start line), end column. These are packed into a single field for efficiency. Note that line and column numbers are zero-based -- typically you will want to add 1 to each before displaying to a user. |
| leading_comments | [string](#string) | optional | If this SourceCodeInfo represents a complete declaration, these are any comments appearing before and after the declaration which appear to be attached to the declaration.

A series of line comments appearing on consecutive lines, with no other tokens appearing on those lines, will be treated as a single comment.

Only the comment content is provided; comment markers (e.g. //) are stripped out. For block comments, leading whitespace and an asterisk will be stripped from the beginning of each line other than the first. Newlines are included in the output.

Examples:

 optional int32 foo = 1; // Comment attached to foo. // Comment attached to bar. optional int32 bar = 2;

 optional string baz = 3; // Comment attached to baz. // Another line attached to baz.

 // Comment attached to qux. // // Another line attached to qux. optional double qux = 4;

 optional string corge = 5; /* Block comment attached * to corge. Leading asterisks * will be removed. */ /* Block comment attached to * grault. */ optional int32 grault = 6; |
| trailing_comments | [string](#string) | optional |  |






<a name="google-protobuf-UninterpretedOption"></a>

### UninterpretedOption
A message representing a option the parser does not recognize. This only
appears in options protos created by the compiler::Parser class.
DescriptorPool resolves these when building Descriptor objects. Therefore,
options protos in descriptor objects (e.g. returned by Descriptor::options(),
or produced by Descriptor::CopyTo()) will never have UninterpretedOptions
in them.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [UninterpretedOption.NamePart](#google-protobuf-UninterpretedOption-NamePart) | repeated |  |
| identifier_value | [string](#string) | optional | The value of the uninterpreted option, in whatever type the tokenizer identified it as during parsing. Exactly one of these should be set. |
| positive_int_value | [uint64](#uint64) | optional |  |
| negative_int_value | [int64](#int64) | optional |  |
| double_value | [double](#double) | optional |  |
| string_value | [bytes](#bytes) | optional |  |
| aggregate_value | [string](#string) | optional |  |






<a name="google-protobuf-UninterpretedOption-NamePart"></a>

### UninterpretedOption.NamePart
The name of the uninterpreted option.  Each string represents a segment in
a dot-separated name.  is_extension is true iff a segment represents an
extension (denoted with parentheses in options specs in .proto files).
E.g.,{ [&#34;foo&#34;, false], [&#34;bar.baz&#34;, true], [&#34;qux&#34;, false] } represents
&#34;foo.(bar.baz).qux&#34;.


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name_part | [string](#string) | required |  |
| is_extension | [bool](#bool) | required |  |





 


<a name="google-protobuf-FieldDescriptorProto-Label"></a>

### FieldDescriptorProto.Label


| Name | Number | Description |
| ---- | ------ | ----------- |
| LABEL_OPTIONAL | 1 | 0 is reserved for errors |
| LABEL_REQUIRED | 2 |  |
| LABEL_REPEATED | 3 | TODO(sanjay): Should we add LABEL_MAP? |



<a name="google-protobuf-FieldDescriptorProto-Type"></a>

### FieldDescriptorProto.Type


| Name | Number | Description |
| ---- | ------ | ----------- |
| TYPE_DOUBLE | 1 | 0 is reserved for errors. Order is weird for historical reasons. |
| TYPE_FLOAT | 2 |  |
| TYPE_INT64 | 3 | Not ZigZag encoded. Negative numbers take 10 bytes. Use TYPE_SINT64 if negative values are likely. |
| TYPE_UINT64 | 4 |  |
| TYPE_INT32 | 5 | Not ZigZag encoded. Negative numbers take 10 bytes. Use TYPE_SINT32 if negative values are likely. |
| TYPE_FIXED64 | 6 |  |
| TYPE_FIXED32 | 7 |  |
| TYPE_BOOL | 8 |  |
| TYPE_STRING | 9 |  |
| TYPE_GROUP | 10 | Tag-delimited aggregate. |
| TYPE_MESSAGE | 11 | Length-delimited aggregate. |
| TYPE_BYTES | 12 | New in version 2. |
| TYPE_UINT32 | 13 |  |
| TYPE_ENUM | 14 |  |
| TYPE_SFIXED32 | 15 |  |
| TYPE_SFIXED64 | 16 |  |
| TYPE_SINT32 | 17 | Uses ZigZag encoding. |
| TYPE_SINT64 | 18 | Uses ZigZag encoding. |



<a name="google-protobuf-FieldOptions-CType"></a>

### FieldOptions.CType


| Name | Number | Description |
| ---- | ------ | ----------- |
| STRING | 0 | Default mode. |
| CORD | 1 |  |
| STRING_PIECE | 2 |  |



<a name="google-protobuf-FileOptions-OptimizeMode"></a>

### FileOptions.OptimizeMode
Generated classes can be optimized for speed or code size.

| Name | Number | Description |
| ---- | ------ | ----------- |
| SPEED | 1 | Generate complete code for parsing, serialization, |
| CODE_SIZE | 2 | etc.

Use ReflectionOps to implement these methods. |
| LITE_RUNTIME | 3 | Generate code using MessageLite and the lite runtime. |


 

 

 



<a name="error-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## error.proto


 


<a name="zbs-ErrorCode"></a>

### ErrorCode


| Name | Number | Description |
| ---- | ------ | ----------- |
| EOK | 0 |  |
| SYSEPERM | 1 | Operation not permitted |
| SYSENOENT | 2 | No such file or directory |
| SYSESRCH | 3 | No such process |
| SYSEINTR | 4 | Interrupted system call |
| SYSEIO | 5 | I/O error |
| SYSENXIO | 6 | No such device or address |
| SYSE2BIG | 7 | Argument list too long |
| SYSENOEXEC | 8 | Exec format error |
| SYSEBADF | 9 | Bad file number |
| SYSECHILD | 10 | No child processes |
| SYSEAGAIN | 11 | Try again |
| SYSENOMEM | 12 | Out of memory |
| SYSEACCES | 13 | Permission denied |
| SYSEFAULT | 14 | Bad address |
| SYSENOTBLK | 15 | Block device required |
| SYSEBUSY | 16 | Device or resource busy |
| SYSEEXIST | 17 | File exists |
| SYSEXDEV | 18 | Cross-device link |
| SYSENODEV | 19 | No such device |
| SYSENOTDIR | 20 | Not a directory |
| SYSEISDIR | 21 | Is a directory |
| SYSEINVAL | 22 | Invalid argument |
| SYSENFILE | 23 | File table overflow |
| SYSEMFILE | 24 | Too many open files |
| SYSENOTTY | 25 | Not a typewriter |
| SYSETXTBSY | 26 | Text file busy |
| SYSEFBIG | 27 | File too large |
| SYSENOSPC | 28 | No space left on device |
| SYSESPIPE | 29 | Illegal seek |
| SYSEROFS | 30 | Read-only file system |
| SYSEMLINK | 31 | Too many links |
| SYSEPIPE | 32 | Broken pipe |
| SYSEDOM | 33 | Math argument out of domain of func |
| SYSERANGE | 34 | Math result not representable |
| SYSEDEADLK | 35 | Resource deadlock would occur |
| SYSENAMETOOLONG | 36 | File name too long |
| SYSENOLCK | 37 | No record locks available |
| SYSENOSYS | 38 | Function not implemented |
| SYSENOTEMPTY | 39 | Directory not empty |
| SYSELOOP | 40 | Too many symbolic links encountered |
| SYSENOMSG | 42 | No message of desired type |
| SYSEIDRM | 43 | Identifier removed |
| SYSECHRNG | 44 | Channel number out of range |
| SYSEL2NSYNC | 45 | Level 2; not synchronized |
| SYSEL3HLT | 46 | Level 3; halted |
| SYSEL3RST | 47 | Level 3; reset |
| SYSELNRNG | 48 | Link number out of range |
| SYSEUNATCH | 49 | Protocol driver not attached |
| SYSENOCSI | 50 | No CSI structure available |
| SYSEL2HLT | 51 | Level 2; halted |
| SYSEBADE | 52 | Invalid exchange |
| SYSEBADR | 53 | Invalid request descriptor |
| SYSEXFULL | 54 | Exchange full |
| SYSENOANO | 55 | No anode |
| SYSEBADRQC | 56 | Invalid request code |
| SYSEBADSLT | 57 | Invalid slot |
| SYSEBFONT | 59 | Bad font file format |
| SYSENOSTR | 60 | Device not a stream |
| SYSENODATA | 61 | No data available |
| SYSETIME | 62 | Timer expired |
| SYSENOSR | 63 | Out of streams resources |
| SYSENONET | 64 | Machine is not on the network |
| SYSENOPKG | 65 | Package not installed |
| SYSEREMOTE | 66 | Object is remote |
| SYSENOLINK | 67 | Link has been severed |
| SYSEADV | 68 | Advertise error |
| SYSESRMNT | 69 | Srmount error |
| SYSECOMM | 70 | Communication error on send |
| SYSEPROTO | 71 | Protocol error |
| SYSEMULTIHOP | 72 | Multihop attempted |
| SYSEDOTDOT | 73 | RFS specific error |
| SYSEBADMSG | 74 | Not a data message |
| SYSEOVERFLOW | 75 | Value too large for defined data type |
| SYSENOTUNIQ | 76 | Name not unique on network |
| SYSEBADFD | 77 | File descriptor in bad state |
| SYSEREMCHG | 78 | Remote address changed |
| SYSELIBACC | 79 | Can not access a needed shared library |
| SYSELIBBAD | 80 | Accessing a corrupted shared library |
| SYSELIBSCN | 81 | .lib section in a.out corrupted |
| SYSELIBMAX | 82 | Attempting to link in too many shared libraries |
| SYSELIBEXEC | 83 | Cannot exec a shared library directly |
| SYSEILSEQ | 84 | Illegal byte sequence |
| SYSERESTART | 85 | Interrupted system call should be restarted |
| SYSESTRPIPE | 86 | Streams pipe error |
| SYSEUSERS | 87 | Too many users |
| SYSENOTSOCK | 88 | Socket operation on non-socket |
| SYSEDESTADDRREQ | 89 | Destination address required |
| SYSEMSGSIZE | 90 | Message too long |
| SYSEPROTOTYPE | 91 | Protocol wrong type for socket |
| SYSENOPROTOOPT | 92 | Protocol not available |
| SYSEPROTONOSUPPORT | 93 | Protocol not supported |
| SYSESOCKTNOSUPPORT | 94 | Socket type not supported |
| SYSEOPNOTSUPP | 95 | Operation not supported on transport endpoint |
| SYSEPFNOSUPPORT | 96 | Protocol family not supported |
| SYSEAFNOSUPPORT | 97 | Address family not supported by protocol |
| SYSEADDRINUSE | 98 | Address already in use |
| SYSEADDRNOTAVAIL | 99 | Cannot assign requested address |
| SYSENETDOWN | 100 | Network is down |
| SYSENETUNREACH | 101 | Network is unreachable |
| SYSENETRESET | 102 | Network dropped connection because of reset |
| SYSECONNABORTED | 103 | Software caused connection abort |
| SYSECONNRESET | 104 | Connection reset by peer |
| SYSENOBUFS | 105 | No buffer space available |
| SYSEISCONN | 106 | Transport endpoint is already connected |
| SYSENOTCONN | 107 | Transport endpoint is not connected |
| SYSESHUTDOWN | 108 | Cannot send after transport endpoint shutdown |
| SYSETOOMANYREFS | 109 | Too many references: cannot splice |
| SYSETIMEDOUT | 110 | Connection timed out |
| SYSECONNREFUSED | 111 | Connection refused |
| SYSEHOSTDOWN | 112 | Host is down |
| SYSEHOSTUNREACH | 113 | No route to host |
| SYSEALREADY | 114 | Operation already in progress |
| SYSEINPROGRESS | 115 | Operation now in progress |
| SYSESTALE | 116 | Stale NFS file handle |
| SYSEUCLEAN | 117 | Structure needs cleaning |
| SYSENOTNAM | 118 | Not a XENIX named type file |
| SYSENAVAIL | 119 | No XENIX semaphores available |
| SYSEISNAM | 120 | Is a named type file |
| SYSEREMOTEIO | 121 | Remote I/O error |
| SYSEDQUOT | 122 | Quota exceeded |
| SYSENOMEDIUM | 123 | No medium found |
| SYSEMEDIUMTYPE | 124 | Wrong medium type |
| SYSECANCELED | 125 | Operation Canceled |
| SYSENOKEY | 126 | Required key not available |
| SYSEKEYEXPIRED | 127 | Key has expired |
| SYSEKEYREVOKED | 128 | Key has been revoked |
| SYSEKEYREJECTED | 129 | Key was rejected by service |
| SYSEOWNERDEAD | 130 | Owner died |
| SYSENOTRECOVERABLE | 131 | State not recoverable |
| SYSERFKILL | 132 | Operation not possible due to RF-kill |
| SYSEHWPOISON | 133 | Memory page has hardware error |
| SYSEUNKNOWN | 999 |  |
| EUNKNOWN | 100001 | ==== common |
| EBadArgument | 100002 |  |
| ELevelDb | 100003 |  |
| EMongoDb | 100004 | No longer used |
| EBoot | 100005 |  |
| EAgain | 100006 |  |
| EMaxLimit | 100007 |  |
| EOther | 100009 |  |
| EProto | 100010 |  |
| EAllocSpace | 100011 |  |
| ETimedOut | 100012 |  |
| EShutDown | 100013 |  |
| ENoSpace | 100014 |  |
| EProfiler | 100015 |  |
| EKilled | 100016 |  |
| ECGroup | 100017 |  |
| ENIOError | 100019 |  |
| ETimerFd | 100020 |  |
| EInvalidPath | 100021 |  |
| ENotSupport | 100022 |  |
| EAlreadyStarted | 100023 |  |
| EInvalidArgument | 100025 |  |
| EMock | 100026 |  |
| EAsyncEventQueue | 100027 |  |
| EBadHexFormat | 100028 |  |
| EBadLicense | 100029 |  |
| EBadKeyFile | 100030 |  |
| EBadSign | 100031 |  |
| EBlkDev | 100032 |  |
| EPythonException | 100033 |  |
| ELicenseNotPermitted | 100034 |  |
| ELicenseExpired | 100035 |  |
| ENotDir | 100036 |  |
| ENotEmpty | 100037 |  |
| EIsDir | 100038 |  |
| ENameTooLong | 100039 |  |
| ENameEmpty | 100040 |  |
| EFCNTL | 100041 |  |
| EHeapProfiler | 100042 |  |
| EOnlyOneChunkRemoving | 100043 |  |
| ENotImplemented | 100044 |  |
| EChunkNotFound | 100045 |  |
| EDBCorrupt | 100046 |  |
| EDBIOError | 100047 |  |
| ENoMemory | 100048 |  |
| ESPDK | 100049 |  |
| EQueueFull | 100050 |  |
| ESystemModeNotPermitted | 100051 |  |
| EBadHeaderFormat | 100101 |  |
| EUnknownSeviceId | 100102 |  |
| EUnknownMethodId | 100103 |  |
| EBadMessageFormat | 100104 |  |
| ETooLargeMessage | 100105 |  |
| EUnknowMessageId | 100151 |  |
| ERpcClientClosed | 100152 |  |
| EDuplicateMessageId | 100161 |  |
| EAsyncServer | 100170 |  |
| EDbNotOpen | 100201 |  |
| EConfigConflict | 100202 |  |
| ESocket | 100300 | ==== socket |
| ESocketConnect | 100301 |  |
| ESocketBind | 100302 |  |
| ESocketListen | 100303 |  |
| ESocketAccept | 100304 |  |
| ESocketSelect | 100305 |  |
| ESocketClosed | 100306 |  |
| ESocketEOF | 100307 |  |
| ESocketPoll | 100308 |  |
| ESocketShutdown | 100309 |  |
| ESocketDisconnect | 100310 |  |
| EEpoll | 100320 |  |
| EEpollCtl | 100321 |  |
| EEpollTimerFd | 100322 |  |
| EEpollWait | 100323 |  |
| EEpollAlreadyExsist | 100324 |  |
| EEpollNotActive | 100325 |  |
| EEpollTooMuchYield | 100326 |  |
| EProtoAsyncClient | 100330 |  |
| EBadRequest | 100400 |  |
| EForbidden | 100403 |  |
| ENotFound | 100404 |  |
| EMethodNotAllowed | 100405 |  |
| EDuplicate | 100409 |  |
| EConnectError | 100420 |  |
| EChunksLessThanReplicas | 100421 |  |
| EChunkConnectUnavailable | 100422 |  |
| EChunksNotEnoughFreeSpace | 100423 |  |
| EInternalServerError | 100500 |  |
| EServiceUnavailable | 100503 |  |
| EMongoException | 100602 | No longer used |
| EMongoError | 100603 | No longer used |
| EMongoConnect | 100604 | No longer used |
| EZKConnectError | 100701 |  |
| EZKNoNode | 100702 |  |
| EZKError | 100703 |  |
| EZKStopped | 100704 |  |
| EZKSessionExpired | 100705 |  |
| EZKNodeExists | 100706 |  |
| EZKAPIError | 100707 |  |
| EZKInvalidCallback | 100708 |  |
| EBadNodeAddress | 100710 |  |
| EBadClusterStatus | 100711 |  |
| EZKAlreadyRegistered | 100712 |  |
| EInvalidDb | 100713 |  |
| EInvalidDbOp | 100714 |  |
| EZKCommit | 100715 |  |
| ENotInDbCluster | 100716 |  |
| ETooManyPendingJournals | 100717 |  |
| EBadElectionPathFound | 100718 |  |
| ENotLeader | 100719 |  |
| EDbReplay | 100720 |  |
| EDbClusterCommit | 100721 |  |
| EZKNotEmpty | 100722 |  |
| EBadZNodeVersion | 100723 |  |
| EIncompatibleZkJournal | 100724 |  |
| ESessionExpired | 100750 |  |
| EBadSessionEpoch | 100751 |  |
| ESessionReconnecting | 100752 |  |
| EZkMarshallingError | 100753 |  |
| EZkSystemError | 100754 |  |
| EOpen | 100901 |  |
| EIOVCountTooBig | 100903 |  |
| EDiskEOF | 100904 |  |
| EPathExist | 100905 |  |
| EPathNotFound | 100906 |  |
| EPathsRangeError | 100907 |  |
| EFillZero | 100908 |  |
| ECAWMiscompare | 100909 |  |
| ETooFewReplica | 100910 |  |
| EInvaildAccessPoint | 100911 |  |
| EDuringSpecialRecover | 100912 |  |
| EUnsupportedType | 100999 |  |
| EMetaCorrupt | 1001 |  |
| EVolumeBroken | 1003 |  |
| ERecover | 1004 |  |
| ESnapshotNotHealthy | 1007 |  |
| EDumpMeta | 1008 |  |
| ENoCmdOwner | 1009 |  |
| ETooManyReplica | 1010 |  |
| EVolumeShrinked | 1011 |  |
| ELastReplica | 1012 |  |
| EModVerfMismatch | 1013 |  |
| EGuardCheck | 1014 |  |
| EUpgrade | 1015 |  |
| ERevokeLeaseFail | 1016 |  |
| EHasStoragePool | 1017 |  |
| EVolumeEOF | 1018 |  |
| ENoHeathyChunk | 1019 |  |
| EUpgradeTimeout | 1020 |  |
| EMetaRemoveSlowReplica | 1021 |  |
| ECNoVolume | 2001 |  |
| ECUnknowOpCode | 2004 |  |
| ECAllReplicaFail | 2005 |  |
| ECRejectRecover | 2006 |  |
| ECAllocExtent | 2009 |  |
| ECReadOnly | 2010 |  |
| ECBadLocationInfo | 2011 |  |
| ECSyncGeneration | 2013 |  |
| ECGenerationNotMatch | 2014 |  |
| ECRebalance | 2015 |  |
| ECBadExtentStatus | 2016 |  |
| EChunkDataChannelServer | 2020 |  |
| EPartitionWorker | 2021 |  |
| EOriginExtentBroken | 2022 |  |
| ENotOwner | 2023 |  |
| ENotAlloc | 2024 |  |
| ENoNeedRecover | 2025 |  |
| EMetaDisconnect | 2030 |  |
| EMetaAddReplica | 2031 |  |
| EMetaRemoveReplica | 2032 |  |
| EMetaReplaceReplica | 2033 |  |
| ELeaseExpired | 2034 |  |
| ENodeMonitorInit | 2035 |  |
| ENoStatInfo | 2036 |  |
| ELocalIOFull | 2037 |  |
| ELSMCanceled | 2038 |  |
| ELSMIOSlow | 2039 |  |
| ELSMInit | 3001 |  |
| EInvalidedChecksumType | 3006 |  |
| EChecksum | 3007 |  |
| EPartitionType | 3008 |  |
| EInvalidPartitionType | 3009 |  |
| EInvalidExtentStatus | 3010 |  |
| EIOQueueGet | 3011 |  |
| EIOQueuePut | 3012 |  |
| ENotFoundExtent | 3013 |  |
| EInvalidBIOCB | 3014 |  |
| EInvalidUIOCB | 3015 |  |
| EReadInvalid | 3016 |  |
| ENotEnoughPartitionSpace | 3017 |  |
| EThreadError | 3018 |  |
| EIOCTL | 3019 |  |
| EBadDevice | 3020 |  |
| EMount | 3021 |  |
| EFormat | 3022 |  |
| EExist | 3023 |  |
| EJournalBoundary | 3024 |  |
| EAllocateMem | 3025 |  |
| EReadSuperBlock | 3026 |  |
| EAllJournalsFull | 3027 |  |
| EReplayJournals | 3028 |  |
| EUmount | 3029 |  |
| ELoadJournalEntry | 3030 |  |
| EWriteSuperBlock | 3031 |  |
| EInvalidDeviceSize | 3032 |  |
| EJournal | 3033 |  |
| EPartition | 3034 |  |
| EMMap | 3035 |  |
| EMUnmap | 3036 |  |
| ENoJournal | 3037 |  |
| EUnknowCacheVersion | 3038 |  |
| EAlreadyInCheck | 3039 |  |
| EJournalBusy | 3040 |  |
| EPartitionWorkerBusy | 3041 |  |
| EBadExtentEpoch | 3042 |  |
| ENotFoundOrigin | 3043 |  |
| ENoAvailableDevID | 3044 |  |
| ELSMBusy | 3045 |  |
| EPromotion | 3046 |  |
| EWriteback | 3047 |  |
| EDeviceStatus | 3048 |  |
| EExtentEOF | 3049 |  |
| ENotReady | 5001 | ==== libzbs |
| EStopped | 5002 |  |
| EBadHandle | 5003 |  |
| EIOError | 5004 |  |
| EDataChannelManager | 5005 |  |
| EIOThrottle | 5010 |  |
| ECancelled | 5011 |  |
| EResetVolume | 5012 |  |
| EAbortTask | 5013 |  |
| EDirtyBlockTrackError | 5014 |  |
| EVmNotMigrate | 7001 | ==== elf |
| EConnectLibvirtFail | 7002 |  |
| EMetricWrongDate | 8001 | ==== metric |
| EInitiatorReservationConflict | 9001 | ==== iscsi |
| EInitiatorInvalidURL | 9002 |  |
| EInitiatorConnectFail | 9003 |  |
| EInitiatorTimeOut | 9004 |  |
| EInitiatorLogoutFail | 9005 |  |
| EInitiatorDisconnectFail | 9006 |  |
| EInitiatorDiscoveryFail | 9007 |  |
| EInitiatorLoginFail | 9008 |  |
| EInitiatorPollError | 9009 |  |
| EInitiatorServiceError | 9010 |  |
| EInitiatorContextCreate | 9011 | deprecated |
| EInitiatorAlreadyLoggedIn | 9012 |  |
| EInitiatorReportLun | 9013 |  |
| EInitiatorUnmarshall | 9014 |  |
| EInitiatorResetLun | 9015 |  |
| EInitiatorWarmReset | 9016 |  |
| EInitiatorColdReset | 9017 |  |
| EInitiatorInquiry | 9018 |  |
| ERedirectorTargetProbe | 9050 | ==== iscsi redirector |
| ERedirectorService | 9051 |  |
| ERedirectorConnection | 9052 |  |
| ESunRpc | 10001 | ==== sunrpc |
| ERpcBind | 10002 |  |
| ECreateXprt | 10003 |  |
| EWrongRunner | 10101 | ==== task |
| ERunnerInterrupt | 10102 |  |
| ETaskCanceled | 10103 |  |
| EInvalidOperation | 10104 |  |
| ETaskPaused | 10105 |  |
| ENetLink | 10201 | ==== Network |
| EInterFace | 10202 |  |
| EBadAddress | 10203 |  |
| EARPBroadCastFailed | 10204 |  |
| ERDMACreateEventChannel | 10301 | ==== rdma, unstable yet |
| ERDMACreateID | 10302 |  |
| ERDMABind | 10303 |  |
| ERDMAListen | 10304 |  |
| ERDMAAccept | 10305 |  |
| ERDMAReject | 10306 |  |
| ERDMAConnect | 10307 |  |
| ERDMADisconnect | 10308 |  |
| ERDMACreateQP | 10309 |  |
| ERDMAEventMissingID | 10310 |  |
| ERDMAEventMissingVerbs | 10311 |  |
| ERDMARegisterMessages | 10320 |  |
| ERDMANotMemory | 10321 |  |
| ERDMAQueryDevice | 10330 |  |
| ERDMAPostRecv | 10331 |  |
| ERDMAPostSend | 10332 |  |
| ERDMAPostRead | 10333 |  |
| ERDMAPostWrite | 10334 |  |
| ERDMADeviceRemoved | 10335 |  |
| EInvalidReString | 10401 | ==== regular expression |
| EReNotMatch | 10402 |  |
| EReInternalError | 10403 |  |
| ECompress | 10501 | ==== compression |
| EDecompress | 10502 |  |



<a name="zbs-UserCode"></a>

### UserCode


| Name | Number | Description |
| ---- | ------ | ----------- |
| UOK | 0 |  |
| UAllocFail | 1 |  |
| UNoEnoughSpace | 2 |  |
| UNameTooLong | 1001 | meta user code common |
| UNameEmpty | 1002 |  |
| UDescriptionTooLong | 1003 |  |
| UBadReplicaNum | 1004 |  |
| USpaceMaxLimit | 1005 |  |
| UPExtentNotFound | 1006 |  |
| UNameEncoding | 1007 |  |
| UPoolMaxLimit | 1100 | meta pool related |
| UPoolDuplicate | 1101 |  |
| UPoolPropertyCorrupt | 1102 |  |
| UPoolNotEmpty | 1103 |  |
| UPoolNotFound | 1104 |  |
| UPoolIdChanged | 1105 |  |
| UPoolCreatedDateChanged | 1106 |  |
| UDefaultPoolCannotBeDeleted | 1107 |  |
| UVolumeDuplicate | 1200 | meta volume related |
| UVolumeBadSize | 1201 |  |
| UVolumePropertyCorrupt | 1202 |  |
| UVolumeOpened | 1203 |  |
| UVolumeShrinked | 1204 |  |
| UVolumeNotFound | 1205 |  |
| UVolumeMaxLimit | 1206 |  |
| UVolumeUpdateNotAllowedWhenOpened | 1207 |  |
| UVolumePoolIdChangeNotAllowed | 1208 |  |
| UVolumeIdChangeNotAllowed | 1209 |  |
| UVolumeCreatedDateChangeNotAllowed | 1210 |  |
| UVolumeStatusChangeNotAllowed | 1211 |  |
| UVolumeOwnerChangeNotAllowed | 1212 |  |
| UVolumeThinProvisionChangeNotAllowed | 1213 |  |
| UVolumeResizeError | 1214 |  |
| UVolumeHasSnapshot | 1215 |  |
| UVolumeNotOnline | 1216 |  |
| UVolumeStripeNumTooBig | 1217 |  |
| UVolumeStripeSizeTooBig | 1218 |  |
| UVolumeStripeSizeTooSmall | 1219 |  |
| UVolumeStripeSizeNotPowerOfTwo | 1220 |  |
| UVolumeStripeSizeNotAlignToVolumeSize | 1221 |  |
| UChunkMaxLimit | 1300 | meta chunk related |
| UChunkInvalidRpcIP | 1301 |  |
| UChunkInvalidRpcPort | 1302 |  |
| UChunkInvalidDataIP | 1303 |  |
| UChunkInvalidDataPort | 1304 |  |
| UChunkInvalidHeartbeatIP | 1305 |  |
| UChunkInvalidHeartbeatPort | 1306 |  |
| UChunkRegistered | 1307 |  |
| UChunkNotRegistered | 1308 |  |
| UChunkStillOwnVolume | 1309 |  |
| UChunkNoSlotInGroup | 1310 |  |
| UZoneMaxLimit | 1311 |  |
| UPodMaxLimit | 1312 |  |
| URackMaxLimit | 1313 |  |
| UBrickMaxLimit | 1314 |  |
| UZoneNotEmpty | 1315 |  |
| UPodNotEmpty | 1316 |  |
| URackNotEmpty | 1317 |  |
| UBrickNotEmpty | 1318 |  |
| UZoneDuplicate | 1319 |  |
| UPodDuplicate | 1320 |  |
| URackDuplicate | 1321 |  |
| UBrickDuplicate | 1322 |  |
| URackNotFound | 1323 |  |
| UBrickNotFound | 1324 |  |
| UZoneNotFound | 1325 |  |
| UPodNotFound | 1326 |  |
| UOnlyOneChunkRemoving | 1327 |  |
| USnapshotNotFound | 1401 | meta snapshot related |
| USnapshotPropertyCorrupt | 1402 |  |
| USnapshotDuplicate | 1403 |  |
| USnapshotNotHealthy | 1404 |  |
| UInodeMaxLimit | 1501 | nfs related |
| UNoParentId | 1502 |  |
| UParentNotFound | 1503 |  |
| UDirNotEmpty | 1504 |  |
| UAlreadyExists | 1505 |  |
| UNotDir | 1506 |  |
| UExportMaxLimit | 1507 |  |
| UPoolExportAttributeChanged | 1508 |  |
| UNotFile | 1509 |  |
| UInodeNotFound | 1510 |  |
| UNotFormatted | 1601 | chunk related |
| UJournalNotEmpty | 1602 |  |
| UIQNNameMissing | 1701 | iscsi related |
| ULUNIDMissing | 1702 |  |
| ULUNIDDuplicate | 1703 |  |
| ULUNMaxLimit | 1704 |  |
| UTargetNotFound | 1705 |  |
| ULUNNotFound | 1706 |  |
| UIQNNameTooLong | 1707 |  |
| UEmptyLunPath | 1708 |  |
| UISCSIInvalidIQNDate | 1709 |  |
| UISCSIInvalidIQNNamingAuth | 1710 |  |
| UISCSIInvalidStorageName | 1711 |  |
| UISCSIInvalidName | 1712 |  |
| UISCSIInvalidInitiatorChapNameLen | 1713 |  |
| UISCSIInvalidInitiatorChapName | 1714 |  |
| UISCSIInvalidInitiatorChapSecLen | 1715 |  |
| UISCSIInvalidInitiatorChapSec | 1716 |  |
| UISCSIInvalidTargetChapNameLen | 1717 |  |
| UISCSIInvalidTargetChapName | 1718 |  |
| UISCSIInvalidTargetChapSecLen | 1719 |  |
| UISCSIInvalidTargetChapSec | 1720 |  |
| UISCSIInvalidIQNPrefix | 1721 |  |
| UISCSIInvalidIQNRegExpr | 1722 |  |
| UISCSIWhitelistEntryTooShort | 1733 |  |
| UISCSILUNSizeShrink | 1734 |  |
| UNVMFDistSubsystemNotFound | 1801 | nvmf related |
| UNVMFWhitelistEntryTooShort | 1802 |  |
| UNVMFInvalidNQNPrefix | 1803 |  |
| UNVMFInvalidNQNDate | 1804 |  |
| UNVMFInvalidNQNNamingAuth | 1805 |  |
| UNVMFInvalidStorageName | 1806 |  |
| UNVMFNQNNameTooLong | 1807 |  |
| UNVMFInvalidName | 1808 |  |
| UNVMFNamespaceMaxLimit | 1809 |  |
| UNVMFNamespaceIDDuplicate | 1810 |  |
| UNVMFDistNamespacePathEmpty | 1811 |  |
| UNVMFDistNamespaceSizeShrink | 1812 |  |
| UNVMFDistNamespaceIDMissing | 1813 |  |
| UNVMFDistNamespaceNotFound | 1814 |  |
| UNVMFDistNamespaceGroupNotFound | 1815 |  |
| UCDPBadJobStage | 1901 | CDP related |
| UUserAuthenticationFail | 2001 | User Account related |
| UUserLoginFail | 2002 |  |
| UUserLogoutFail | 2003 |  |
| UUserWrongOldPassword | 2004 |  |
| UUserChangePasswordFail | 2005 |  |
| UUserCreateFail | 2006 |  |
| UUserDeleteFail | 2007 |  |
| UUserAddRoleFail | 2008 |  |
| UUserDeleteRoleFail | 2009 |  |
| UUserNotAdmin | 2010 |  |
| UEmailDuplicate | 2011 |  |
| UUserNotFound | 2012 |  |
| ULoadOperationsFail | 2101 | Operation logs related |
| UVolumeDeleteFail | 2201 | Volume related |
| UVolumeCreateFail | 2202 |  |
| UVolumeUpdateFail | 2203 |  |
| UVolumeDetailFail | 2204 |  |
| UVolumeCloneFail | 2205 |  |
| UVolumeUploadFail | 2206 |  |
| UPoolVolumeListFail | 2301 | Pool related |
| UPoolDetailFail | 2302 |  |
| UPoolListFail | 2303 |  |
| UPoolNameFail | 2304 |  |
| UPoolUpdateFail | 2305 |  |
| UPoolDeleteFail | 2306 |  |
| UPoolCreateFail | 2307 |  |
| UPoolNotInWhitelist | 2308 |  |
| UNodeInfoFail | 2401 | Node related |
| UNodeListFail | 2403 |  |
| UNodeDetailFail | 2404 |  |
| UNodeDeleteFail | 2405 |  |
| UNodeAddFail | 2406 |  |
| USnapListFail | 2501 | snap related |
| USnapCreateFail | 2502 |  |
| USnapRollbackFail | 2503 |  |
| USnapDeleteFail | 2504 |  |
| USnapUpdateFail | 2505 |  |
| USnapAddSameTask | 2506 |  |
| UVMInstanceSummaryFail | 2601 | Compute related |
| UComputeResourceSummaryFail | 2602 |  |
| UVMListFail | 2603 |  |
| UVMDeleteFail | 2604 |  |
| UVMDetailFail | 2605 |  |
| UVMNewFail | 2606 |  |
| UVMConfigFail | 2607 |  |
| UVMShutdownFail | 2608 |  |
| UVMStartFail | 2609 |  |
| UVMResetFail | 2610 |  |
| UVMNodeListFail | 2611 |  |
| UConnectNodeFail | 2612 |  |
| UDiskHasBeenUsed | 2613 |  |
| UVMNotMigrate | 2614 |  |
| UDiskAlreadyAttached | 2615 |  |
| UVMNotFound | 2616 |  |
| UVMCannotMigrateToSameNode | 2617 |  |
| UVMNotAllowedRebuild | 2618 |  |
| UVMMigrateFailed | 2619 |  |
| URequestTimeOut | 2620 |  |
| UVMNameUsed | 2621 |  |
| UVMSetMemFailed | 2622 |  |
| UVlanTagInvalidValue | 2623 |  |
| UVlanExist | 2624 |  |
| UVlanChangeDefault | 2625 |  |
| UVlanNotExist | 2626 |  |
| UDeleteNotStopVM | 2627 |  |
| UVMNotRunning | 2629 |  |
| UVMRebuildFailed | 2630 |  |
| UVMTokenCreateFail | 2632 |  |
| UDiskNotAttached | 2633 |  |
| UMaxDeviceLimit | 2634 |  |
| UAccessDenied | 2635 |  |
| UNodeIpNotSpecified | 2636 |  |
| UBootPathNotSpecified | 2637 |  |
| UNameNotSpecified | 2638 |  |
| UVCPUNotSpecified | 2639 |  |
| UMemoryNotSpecified | 2640 |  |
| UBadRequest | 2641 |  |
| UDestIpNotSpecified | 2642 |  |
| UKeyNotFound | 2643 |  |
| UMissingParameter | 2644 |  |
| UConnectLibvirtFail | 2645 |  |
| UConnectMongoFail | 2646 | No longer used |
| ULicenseNotPermitted | 2701 | license |
| ULicenseExpired | 2702 |  |
| ULicenseMaxNodeNumber | 2703 |  |
| ULicenseBadFormat | 2704 |  |
| ULicenseSerialNotMatch | 2705 |  |
| ULicenseDowngradeSoftwareEdition | 2706 |  |
| ULicenseChangeLicenseType | 2707 |  |
| ULicenseMetroX | 2708 |  |
| ULicenseCapacityLimit | 2709 |  |
| ULicenseNVMFDistSubsystem | 2710 |  |
| UGetMetricFail | 2801 | metric |
| UIPPatternEmpty | 2901 | ippattern |
| UIPPatternInvalid | 2902 |  |
| UIOThrottleLimitConflict | 3001 | io throttle config |
| UIOThrottleIOPSLimitNotSpecfied | 3002 |  |
| UIOThrottleInvalidRateLimit | 3003 |  |
| UIOThrottleInvalidBurstLength | 3004 |  |
| UIOThrottleBurstRateNotSpecified | 3005 |  |
| UIOThrottleAverageNotSpecified | 3006 |  |
| UIOThrottleAverageTooLarge | 3007 |  |
| UReNullPtr | 4001 | regular expression |
| UReBadOption | 4002 |  |
| UReBadMagic | 4003 |  |
| UReNoMemory | 4004 |  |
| UReUnknown | 4005 |  |
| UOtherError | 9999 |  |


 

 

 



<a name="meta-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## meta.proto



<a name="zbs-meta-AbilityState"></a>

### AbilityState



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| upgrade_thin_provision | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-AccessDataReportRequest"></a>

### AccessDataReportRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| total_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| recover_migrate_speed | [uint64](#uint64) | optional | **Deprecated.**  |
| recover_speed | [uint64](#uint64) | optional | **Deprecated.**  |
| migrate_speed | [uint64](#uint64) | optional | **Deprecated.**  |
| cross_zone_recover_speed | [uint64](#uint64) | optional | **Deprecated.**  |
| cross_zone_migrate_speed | [uint64](#uint64) | optional | **Deprecated.**  |
| cross_zone_recover_migrate_speed | [uint64](#uint64) | optional | **Deprecated.**  |
| iscsi_conns | [ISCSIConnections](#zbs-meta-ISCSIConnections) | optional |  |
| nvmf_conns | [NVMFConnections](#zbs-meta-NVMFConnections) | optional |  |
| access_perf | [zbs.AccessPerf](#zbs-AccessPerf) | optional |  |
| recover_perf | [zbs.RecoverPerf](#zbs-RecoverPerf) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| request | AccessDataReportRequest | .zbs.consensus.DataReportRequest | 10001 |  |




<a name="zbs-meta-AccessKeepAliveRequest"></a>

### AccessKeepAliveRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** moved to AccessDataReportRequest |
| write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** moved to AccessDataReportRequest |
| total_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** moved to AccessDataReportRequest |
| cross_zone_read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** moved to AccessDataReportRequest |
| cross_zone_write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** moved to AccessDataReportRequest |
| recover_migrate_speed | [uint64](#uint64) | optional | **Deprecated.** moved to AccessDataReportRequest |
| in_recover_pids | [uint32](#uint32) | repeated | **Deprecated.**  |
| recover_speed | [uint64](#uint64) | optional | **Deprecated.** moved to AccessDataReportRequest |
| migrate_speed | [uint64](#uint64) | optional | **Deprecated.** moved to AccessDataReportRequest |
| cross_zone_recover_speed | [uint64](#uint64) | optional | **Deprecated.** moved to AccessDataReportRequest |
| cross_zone_migrate_speed | [uint64](#uint64) | optional | **Deprecated.** moved to AccessDataReportRequest |
| cross_zone_recover_migrate_speed | [uint64](#uint64) | optional | **Deprecated.** moved to AccessDataReportRequest |
| recover_limit | [uint64](#uint64) | optional |  |
| migrate_limit | [uint64](#uint64) | optional |  |
| iscsi_conns | [ISCSIConnections](#zbs-meta-ISCSIConnections) | optional |  |
| need_refresh_iscsi_config | [bool](#bool) | optional |  |
| need_refresh_nvmf_config | [bool](#bool) | optional |  |
| max_recover_limit | [uint64](#uint64) | optional |  |
| max_migrate_limit | [uint64](#uint64) | optional |  |
| enable_iscsi_config_push | [bool](#bool) | optional |  |
| nvmf_conns | [NVMFConnections](#zbs-meta-NVMFConnections) | optional |  |
| access_perf | [zbs.AccessPerf](#zbs-AccessPerf) | optional |  |
| recover_perf | [zbs.RecoverPerf](#zbs-RecoverPerf) | optional |  |
| nvmf_rdma_enabled | [bool](#bool) | optional |  |
| nvmf_tcp_enabled | [bool](#bool) | optional |  |
| cdp_job_updated | [zbs.CDPJobUpdateDone](#zbs-CDPJobUpdateDone) | optional |  |
| chunk_connectivity | [zbs.ChunkConnectivity](#zbs-ChunkConnectivity) | optional |  |
| real_isolate_status | [zbs.ChunkIsolateFlag](#zbs-ChunkIsolateFlag) | optional |  |
| enable_unmap | [bool](#bool) | optional |  |
| enable_thick_extent | [bool](#bool) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| request | AccessKeepAliveRequest | .zbs.consensus.KeepAliveRequest | 10001 |  |




<a name="zbs-meta-AccessKeepAliveResponse"></a>

### AccessKeepAliveResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| recover_cmd | [zbs.RecoverCmd](#zbs-RecoverCmd) | repeated |  |
| revoke_cmd | [zbs.RevokeCmd](#zbs-RevokeCmd) | repeated |  |
| revoke_client_cmd | [RevokeClientCmd](#zbs-meta-RevokeClientCmd) | optional |  |
| volume_need_update | [bytes](#bytes) | repeated |  |
| recover_mode | [zbs.RecoverMode](#zbs-RecoverMode) | optional |  |
| migrate_mode | [zbs.MigrateMode](#zbs-MigrateMode) | optional |  |
| static_recover_limit | [uint64](#uint64) | optional |  |
| static_migrate_limit | [uint64](#uint64) | optional |  |
| luns_need_update | [PRResponse](#zbs-meta-PRResponse) | repeated |  |
| conn_need_drop | [DropISCSIConnCmd](#zbs-meta-DropISCSIConnCmd) | optional |  |
| config_updates | [ConfigUpdate](#zbs-meta-ConfigUpdate) | repeated |  |
| all_iscsi_config | [ISCSITarget](#zbs-meta-ISCSITarget) | repeated |  |
| all_nvmf_config | [NVMFDistSubsystem](#zbs-meta-NVMFDistSubsystem) | repeated |  |
| negotiated_config | [NegotiatedConfig](#zbs-meta-NegotiatedConfig) | optional |  |
| nvmf_target_id | [uint32](#uint32) | optional |  |
| nvmf_volume_reset | [NVMFVolumeReset](#zbs-meta-NVMFVolumeReset) | optional |  |
| cdp_updates | [zbs.CDPJobUpdate](#zbs-CDPJobUpdate) | optional |  |
| maintenance_cmd | [zbs.MaintenanceCmd](#zbs-MaintenanceCmd) | repeated |  |
| isolate_info | [zbs.ChunkIsolateInfo](#zbs-ChunkIsolateInfo) | optional |  |
| clean_chunk_info_cmd | [zbs.CleanChunkInfoCmd](#zbs-CleanChunkInfoCmd) | repeated |  |
| special_recover_cmd | [zbs.RecoverCmd](#zbs-RecoverCmd) | repeated |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| response | AccessKeepAliveResponse | .zbs.consensus.KeepAliveResponse | 10001 |  |




<a name="zbs-meta-AccessPoint"></a>

### AccessPoint



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-AcquireNVMFIOPermissionRequest"></a>

### AcquireNVMFIOPermissionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| host_nqn | [string](#string) | required |  |
| cid | [uint32](#uint32) | required |  |






<a name="zbs-meta-AddChunkToStoragePoolRequest"></a>

### AddChunkToStoragePoolRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| storage_pool_id | [bytes](#bytes) | required |  |
| chunk_id | [uint32](#uint32) | required |  |






<a name="zbs-meta-AddLunAllowedInitiatorsRequest"></a>

### AddLunAllowedInitiatorsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| new_allowed_initiators | [bytes](#bytes) | required |  |






<a name="zbs-meta-AddReplicaRequest"></a>

### AddReplicaRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session | [bytes](#bytes) | required | the chunk sending this request |
| pid | [uint32](#uint32) | required |  |
| replica_chunk | [uint32](#uint32) | required | replica to be added |
| epoch | [uint64](#uint64) | optional |  |






<a name="zbs-meta-BatchedSetAttrRequest"></a>

### BatchedSetAttrRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| requests | [SetAttrRequest](#zbs-meta-SetAttrRequest) | repeated |  |






<a name="zbs-meta-BatchedSetAttrResponse"></a>

### BatchedSetAttrResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| success_ids | [bytes](#bytes) | repeated |  |
| removed_ids | [bytes](#bytes) | repeated |  |
| failed_ids | [bytes](#bytes) | repeated |  |






<a name="zbs-meta-CancelChunkRemovingRequest"></a>

### CancelChunkRemovingRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| chunk_id | [uint32](#uint32) | required |  |






<a name="zbs-meta-CheckLeaseRequest"></a>

### CheckLeaseRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |






<a name="zbs-meta-CheckLeaseResponse"></a>

### CheckLeaseResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| allocated | [bool](#bool) | required |  |






<a name="zbs-meta-ChunkDataReportRequest"></a>

### ChunkDataReportRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pextents_info | [zbs.PExtentInfo](#zbs-PExtentInfo) | repeated |  |
| read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** chunk local read perf |
| write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** chunk local write perf |
| total_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** chunk local total perf |
| node_perf | [zbs.NodePerf](#zbs-NodePerf) | optional | **Deprecated.**  |
| lsm_version | [zbs.LSMVersion](#zbs-LSMVersion) | optional |  |
| lsm_perf | [zbs.LSMPerf](#zbs-LSMPerf) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| request | ChunkDataReportRequest | .zbs.consensus.DataReportRequest | 10002 |  |




<a name="zbs-meta-ChunkKeepAliveRequest"></a>

### ChunkKeepAliveRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| status | [zbs.ChunkStatus](#zbs-ChunkStatus) | optional | moved to ChunkDataReportRequest Default: CHUNK_STATUS_CONNECTED_HEALTHY |
| space_info | [zbs.ChunkSpaceInfo](#zbs-ChunkSpaceInfo) | optional |  |
| pextents_info | [zbs.PExtentInfo](#zbs-PExtentInfo) | repeated | moved to ChunkDataReportRequest |
| read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** chunk local read perf, moved to ChunkDataReportRequest |
| write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** chunk local write perf, moved to ChunkDataReportRequest |
| total_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.** chunk local total perf, moved to ChunkDataReportRequest |
| node_perf | [zbs.NodePerf](#zbs-NodePerf) | optional | **Deprecated.** moved to ChunkDataReportRequest |
| host_name | [bytes](#bytes) | optional |  |
| lsm_version | [zbs.LSMVersion](#zbs-LSMVersion) | optional |  |
| lsm_perf | [zbs.LSMPerf](#zbs-LSMPerf) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| request | ChunkKeepAliveRequest | .zbs.consensus.KeepAliveRequest | 10002 |  |




<a name="zbs-meta-ChunkKeepAliveResponse"></a>

### ChunkKeepAliveResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| gc_cmd | [zbs.GcCmd](#zbs-GcCmd) | repeated |  |
| thick_pids | [uint32](#uint32) | repeated |  |
| reserve_space | [uint64](#uint64) | optional |  |
| thin_pids | [uint32](#uint32) | repeated |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| response | ChunkKeepAliveResponse | .zbs.consensus.KeepAliveResponse | 10002 |  |




<a name="zbs-meta-ChunkTemporaryReplicaInfo"></a>

### ChunkTemporaryReplicaInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) | required |  |
| temporary_replica_space | [uint64](#uint64) | required |  |






<a name="zbs-meta-ChunksResponse"></a>

### ChunksResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| chunks | [zbs.Chunk](#zbs-Chunk) | repeated |  |






<a name="zbs-meta-ClearVhostIOPermissionRequest"></a>

### ClearVhostIOPermissionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| reset_to_allow_all | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-CloneVolumeRequest"></a>

### CloneVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot | [SnapshotPath](#zbs-meta-SnapshotPath) | required |  |
| volume_request | [CreateVolumeRequest](#zbs-meta-CreateVolumeRequest) | required |  |






<a name="zbs-meta-ClusterInfo"></a>

### ClusterInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| uuid | [bytes](#bytes) | optional |  |
| name | [bytes](#bytes) | optional |  |
| desc | [bytes](#bytes) | optional |  |
| is_stretched | [bool](#bool) | optional |  Default: false |
| preferred_zone_id | [bytes](#bytes) | optional |  |
| zk_uuid_recorded | [bool](#bool) | optional |  Default: false |
| negotiated_config | [NegotiatedConfig](#zbs-meta-NegotiatedConfig) | optional |  |






<a name="zbs-meta-ClusterPerf"></a>

### ClusterPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| iops | [float](#float) | optional | deprecated soon

io per second |
| bandwidth | [float](#float) | optional | Bytes per second |
| latency | [float](#float) | optional | nano seconds |
| total | [zbs.StoragePerf](#zbs-StoragePerf) | optional |  |
| read | [zbs.StoragePerf](#zbs-StoragePerf) | optional |  |
| write | [zbs.StoragePerf](#zbs-StoragePerf) | optional |  |
| cross_zone_read | [zbs.StoragePerf](#zbs-StoragePerf) | optional |  |
| cross_zone_write | [zbs.StoragePerf](#zbs-StoragePerf) | optional |  |
| chunk_perf | [zbs.ChunkPerf](#zbs-ChunkPerf) | optional |  |






<a name="zbs-meta-ClusterStatus"></a>

### ClusterStatus



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| flags | [uint64](#uint64) | optional |  |






<a name="zbs-meta-ClusterSummary"></a>

### ClusterSummary



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| valid_data_space | [uint64](#uint64) | optional |  Default: 0 |
| provisioned_data_space | [uint64](#uint64) | optional |  Default: 0 |
| used_data_space | [uint64](#uint64) | optional |  Default: 0 |
| total_data_capacity | [uint64](#uint64) | optional |  Default: 0 |
| failure_data_space | [uint64](#uint64) | optional |  Default: 0 |
| valid_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| used_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| dirty_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| failure_cache_space | [uint64](#uint64) | optional |  Default: 0 |
| total_cache_capacity | [uint64](#uint64) | optional |  Default: 0 |
| total_nodes | [uint32](#uint32) | optional |  Default: 0 |
| healthy_nodes | [uint32](#uint32) | optional |  Default: 0 |
| connecting_nodes | [uint32](#uint32) | optional | nodes are being connecting Default: 0 |
| warning_nodes | [uint32](#uint32) | optional |  Default: 0 |
| error_nodes | [uint32](#uint32) | optional |  Default: 0 |
| removing_nodes | [uint32](#uint32) | optional | nodes are being removed Default: 0 |
| idle_nodes | [uint32](#uint32) | optional | nodes idle Default: 0 |
| in_use_nodes | [uint32](#uint32) | optional | nodes in used Default: 0 |
| leader | [string](#string) | optional | meta leader known by this node. |
| alive_meta_hosts | [zbs.Address](#zbs-Address) | repeated |  |
| cluster_info | [ClusterInfo](#zbs-meta-ClusterInfo) | optional |  |
| migrate_enabled | [bool](#bool) | optional |  |
| recover_enabled | [bool](#bool) | optional |  |
| upgrade_mode_duration | [uint64](#uint64) | optional | second |
| alloced_pids | [uint32](#uint32) | optional |  |
| serial | [string](#string) | optional |  |
| recover_migrate_speed | [uint64](#uint64) | optional | **Deprecated.**  Default: 0 |
| num_ongoing_recovers | [uint32](#uint32) | optional | **Deprecated.**  Default: 0 |
| num_pending_recovers | [uint32](#uint32) | optional | **Deprecated.**  Default: 0 |
| num_ongoing_migrates | [uint32](#uint32) | optional | **Deprecated.**  Default: 0 |
| num_pending_migrates | [uint32](#uint32) | optional | **Deprecated.**  Default: 0 |
| num_pending_recycles | [uint32](#uint32) | optional | **Deprecated.**  Default: 0 |
| recover_speed | [uint64](#uint64) | optional | **Deprecated.**  Default: 0 |
| migrate_speed | [uint64](#uint64) | optional | **Deprecated.**  Default: 0 |
| recover_info | [zbs.RecoverSummary](#zbs-RecoverSummary) | optional |  |
| storage_pools | [zbs.StoragePool](#zbs-StoragePool) | repeated |  |
| unallocated_chunks | [zbs.Chunk](#zbs-Chunk) | repeated |  |
| space_info | [zbs.StorageSpace](#zbs-StorageSpace) | optional |  |
| zone_infos | [ZoneSummary](#zbs-meta-ZoneSummary) | repeated |  |
| cluster_perf | [zbs.ChunkPerf](#zbs-ChunkPerf) | optional |  |






<a name="zbs-meta-ConfigUpdate"></a>

### ConfigUpdate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| update_type | [ConfigUpdateType](#zbs-meta-ConfigUpdateType) | required |  |






<a name="zbs-meta-ConsistencyFile"></a>

### ConsistencyFile



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inode_id | [bytes](#bytes) | required |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| volume | ConsistencyFile | ConsistencyVolume | 10001 |  |




<a name="zbs-meta-ConsistencyGroup"></a>

### ConsistencyGroup



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | optional |  |
| description | [bytes](#bytes) | optional |  |
| volumes | [ConsistencyVolume](#zbs-meta-ConsistencyVolume) | repeated |  |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| logical_size_bytes | [int64](#int64) | optional |  Default: -1 |
| physical_size_bytes | [int64](#int64) | optional |  Default: -1 |






<a name="zbs-meta-ConsistencyGroupSnapshot"></a>

### ConsistencyGroupSnapshot



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| group_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | optional |  |
| description | [bytes](#bytes) | optional |  |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| snapshots | [ConsistencyVolumeSnapshot](#zbs-meta-ConsistencyVolumeSnapshot) | repeated |  |
| logical_size_bytes | [int64](#int64) | optional |  Default: -1 |
| physical_size_bytes | [int64](#int64) | optional |  Default: -1 |






<a name="zbs-meta-ConsistencyLUN"></a>

### ConsistencyLUN



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_id | [uint32](#uint32) | required |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| volume | ConsistencyLUN | ConsistencyVolume | 10002 |  |




<a name="zbs-meta-ConsistencyNamespace"></a>

### ConsistencyNamespace



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_id | [uint32](#uint32) | required |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| volume | ConsistencyNamespace | ConsistencyVolume | 10003 |  |




<a name="zbs-meta-ConsistencyVolume"></a>

### ConsistencyVolume



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| pool_id | [bytes](#bytes) | required |  |
| type | [ConsistencyVolumeType](#zbs-meta-ConsistencyVolumeType) | required |  |






<a name="zbs-meta-ConsistencyVolumeSnapshot"></a>

### ConsistencyVolumeSnapshot



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| snapshot_id | [bytes](#bytes) | required |  |
| snapshot_pool_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-ConvertLunIntoFileRequest"></a>

### ConvertLunIntoFileRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| parent_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | required |  |
| sattr3 | [SAttr3](#zbs-meta-SAttr3) | optional |  |






<a name="zbs-meta-ConvertVolumeIntoFileRequest"></a>

### ConvertVolumeIntoFileRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_path | [VolumePath](#zbs-meta-VolumePath) | required |  |
| parent_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | required |  |
| sattr3 | [SAttr3](#zbs-meta-SAttr3) | optional |  |






<a name="zbs-meta-ConvertVolumeIntoLunRequest"></a>

### ConvertVolumeIntoLunRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| volume_path | [VolumePath](#zbs-meta-VolumePath) | required |  |
| type | [ISCSIType](#zbs-meta-ISCSIType) | optional |  Default: SCSI_TYPE_DISK |
| description | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| iops | [uint64](#uint64) | optional | **Deprecated.**  |
| iops_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| bps | [uint64](#uint64) | optional | **Deprecated.**  |
| bps_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| stripe_num | [uint32](#uint32) | optional | stripe num |
| stripe_size | [uint32](#uint32) | optional |  |






<a name="zbs-meta-CowPExtentRequest"></a>

### CowPExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session | [bytes](#bytes) | required |  |
| volume_id | [bytes](#bytes) | required |  |
| vextent_no | [uint32](#uint32) | required |  |






<a name="zbs-meta-CreateConsistencyGroupRequest"></a>

### CreateConsistencyGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | required |  |
| description | [bytes](#bytes) | optional |  |
| volumes | [ConsistencyVolume](#zbs-meta-ConsistencyVolume) | repeated |  |






<a name="zbs-meta-CreateConsistencyGroupSnapshotRequest"></a>

### CreateConsistencyGroupSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | optional |  |
| description | [bytes](#bytes) | optional |  |






<a name="zbs-meta-CreateHardlinkRequest"></a>

### CreateHardlinkRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| parent_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | required |  |
| target_id | [bytes](#bytes) | required |  |
| xid | [uint32](#uint32) | optional | creation verifier |






<a name="zbs-meta-CreateISCSILunRequest"></a>

### CreateISCSILunRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| size | [uint64](#uint64) | optional |  |
| type | [ISCSIType](#zbs-meta-ISCSIType) | optional |  Default: SCSI_TYPE_DISK |
| description | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| iops | [uint64](#uint64) | optional | **Deprecated.**  |
| iops_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| bps | [uint64](#uint64) | optional | **Deprecated.**  |
| bps_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| src_lun_path | [LunPath](#zbs-meta-LunPath) | optional | clone from another lun |
| src_snapshot_id | [bytes](#bytes) | optional | clone from another snapshot |
| allowed_initiators | [bytes](#bytes) | optional |  |
| single_access | [bool](#bool) | optional |  Default: false |
| stripe_num | [uint32](#uint32) | optional | stripe num |
| stripe_size | [uint32](#uint32) | optional |  |
| inode_path | [bytes](#bytes) | optional | create from nfs inode |
| preferred_cid | [uint32](#uint32) | optional |  Default: 0 |
| target_requirement | [TargetRequirement](#zbs-meta-TargetRequirement) | optional |  |
| queue_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-CreateISCSISnapshotRequest"></a>

### CreateISCSISnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| snapshot_name | [bytes](#bytes) | required |  |
| snapshot_desc | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-CreateISCSITargetRequest"></a>

### CreateISCSITargetRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | required |  |
| storage_pool_id | [bytes](#bytes) | optional | if not specified, the system storage pool is used |
| driver_name | [bytes](#bytes) | optional | &#34;iscsi&#34; or &#34;iser&#34; Default: iscsi |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| iqn_date | [bytes](#bytes) | optional | attributes for iSCSI Qualified Name (IQN) |
| iqn_naming_auth | [bytes](#bytes) | optional |  |
| external_use | [bool](#bool) | optional |  Default: false |
| description | [bytes](#bytes) | optional | 256B |
| whitelist | [bytes](#bytes) | optional |  Default: */* |
| target_chap | [TargetChapInfo](#zbs-meta-TargetChapInfo) | optional |  |
| initiator_chap | [InitiatorChapInfo](#zbs-meta-InitiatorChapInfo) | repeated |  |
| iqn_whitelist | [bytes](#bytes) | repeated | **Deprecated.**  |
| iqn_whitelist_v2 | [bytes](#bytes) | optional |  Default: */* |
| adaptive_iqn_whitelist | [bool](#bool) | optional |  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| stripe_num | [uint32](#uint32) | optional |  |
| stripe_size | [uint32](#uint32) | optional |  |
| labels | [zbs.Labels](#zbs-Labels) | optional |  |






<a name="zbs-meta-CreateInodeRequest"></a>

### CreateInodeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| parent_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | required |  |
| type | [NFSType](#zbs-meta-NFSType) | required |  |
| how | [CreateHow](#zbs-meta-CreateHow) | optional |  Default: UNCHECKED |
| sattr3 | [SAttr3](#zbs-meta-SAttr3) | optional | used for GUARD and UNCHECKED |
| createverf3 | [bytes](#bytes) | optional | create verifier |
| xid | [uint32](#uint32) | optional | internal creation verifier |
| src_inode_id | [bytes](#bytes) | optional | clone from another inode |
| src_snapshot_id | [bytes](#bytes) | optional | clone from a snapshot |
| preallocate | [bool](#bool) | optional |  |
| symlink_path | [bytes](#bytes) | optional |  |






<a name="zbs-meta-CreateNFSSnapshotRequest"></a>

### CreateNFSSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inode_id | [bytes](#bytes) | required |  |
| snapshot_name | [bytes](#bytes) | required |  |
| snapshot_desc | [bytes](#bytes) | optional |  |






<a name="zbs-meta-CreateNVMFDistNamespaceRequest"></a>

### CreateNVMFDistNamespaceRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_path | [DistNamespacePath](#zbs-meta-DistNamespacePath) | required |  |
| size | [uint64](#uint64) | optional |  |
| description | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| group_id | [bytes](#bytes) | optional | namespace group id |
| src_ns_path | [DistNamespacePath](#zbs-meta-DistNamespacePath) | optional | clone from another ns |
| src_snapshot_id | [bytes](#bytes) | optional | clone from another snapshot |
| stripe_num | [uint32](#uint32) | optional | stripe num |
| stripe_size | [uint32](#uint32) | optional |  |
| is_shared | [bool](#bool) | optional | shared namespace, only valid for INHERIT_POLICY Default: false |
| nqn_whitelist | [bytes](#bytes) | optional |  |
| single_access | [bool](#bool) | optional |  |
| alloc_ns | [bool](#bool) | optional | for test purpose Default: true |
| subsystem_requirement | [SubsystemRequirement](#zbs-meta-SubsystemRequirement) | optional |  |






<a name="zbs-meta-CreateNVMFDistSubsystemRequest"></a>

### CreateNVMFDistSubsystemRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | required |  |
| storage_pool_id | [bytes](#bytes) | optional | if not specified, the system storage pool is used |
| policy | [NVMFAccessPolicy](#zbs-meta-NVMFAccessPolicy) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| nqn_date | [bytes](#bytes) | optional | attributes for NVMe Qualified Name (NQN) |
| nqn_naming_auth | [bytes](#bytes) | optional |  |
| description | [bytes](#bytes) | optional | 256B |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| stripe_num | [uint32](#uint32) | optional |  |
| stripe_size | [uint32](#uint32) | optional |  |
| nqn_whitelist | [bytes](#bytes) | optional |  Default: */* |
| ipv4_whitelist | [bytes](#bytes) | optional |  Default: */* |
| labels | [zbs.Labels](#zbs-Labels) | optional |  |
| attributes | [zbs.Labels](#zbs-Labels) | optional |  |






<a name="zbs-meta-CreateNVMFSnapshotRequest"></a>

### CreateNVMFSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_path | [DistNamespacePath](#zbs-meta-DistNamespacePath) | required |  |
| snapshot_name | [bytes](#bytes) | required |  |
| snapshot_desc | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-CreateSnapshotRequest"></a>

### CreateSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [SnapshotPath](#zbs-meta-SnapshotPath) | required |  |
| description | [bytes](#bytes) | optional | description for this |
| nfs_meta | [NFSAttr](#zbs-meta-NFSAttr) | optional | snapshot

right now nfs inode and volume are |






<a name="zbs-meta-CreateStoragePoolRequest"></a>

### CreateStoragePoolRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | optional |  |
| chunk_ids | [uint32](#uint32) | repeated | the chunks to be included in the storage pool |






<a name="zbs-meta-CreateTopoObjRequest"></a>

### CreateTopoObjRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| type | [zbs.TopoType](#zbs-TopoType) | required |  |
| parent_id | [bytes](#bytes) | optional |  Default: topo |
| name | [bytes](#bytes) | optional | name of the rack, not unique |
| desc | [bytes](#bytes) | optional |  |
| position | [zbs.Position](#zbs-Position) | optional |  |
| capacity | [zbs.Capacity](#zbs-Capacity) | optional |  |
| dimension | [zbs.Dimension](#zbs-Dimension) | optional |  |






<a name="zbs-meta-CreateVolumeRequest"></a>

### CreateVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| volume | [Volume](#zbs-meta-Volume) | required |  |
| preferred_cid | [uint32](#uint32) | optional |  Default: 0 |






<a name="zbs-meta-DbItem"></a>

### DbItem



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [bytes](#bytes) | required |  |
| value | [bytes](#bytes) | required |  |






<a name="zbs-meta-DeleteConsistencyGroupRequest"></a>

### DeleteConsistencyGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| remain_volume_snapshot | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-DeleteConsistencyGroupSnapshotRequest"></a>

### DeleteConsistencyGroupSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_id | [bytes](#bytes) | required |  |
| remain_volume_snapshot | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-DeleteInodeRequest"></a>

### DeleteInodeRequest
Delete an inode or a hardlink


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| parent_id | [bytes](#bytes) | required |  |
| recursive | [bool](#bool) | optional |  Default: false |
| name | [bytes](#bytes) | required |  |






<a name="zbs-meta-DeleteInodeResponse"></a>

### DeleteInodeResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inode_id | [bytes](#bytes) | optional | inode_id that has been deleted |






<a name="zbs-meta-DistNamespaceGroupPath"></a>

### DistNamespaceGroupPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| group_id | [bytes](#bytes) | optional |  |
| group_name | [bytes](#bytes) | optional |  |






<a name="zbs-meta-DistNamespacePath"></a>

### DistNamespacePath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| ns_id | [uint32](#uint32) | optional |  Default: 0 |
| ns_name | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |
| volume_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-DropISCSIConnCmd"></a>

### DropISCSIConnCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| conn_client_addrs | [bytes](#bytes) | repeated | ipv4:port |






<a name="zbs-meta-DumpDbRequest"></a>

### DumpDbRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| db_name | [string](#string) | required |  |
| pagination | [zbs.Pagination](#zbs-Pagination) | required |  |






<a name="zbs-meta-DumpDbResponse"></a>

### DumpDbResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| op_seq | [int64](#int64) | required |  |
| items | [DbItem](#zbs-meta-DbItem) | repeated |  |






<a name="zbs-meta-FindInodeRequest"></a>

### FindInodeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [bytes](#bytes) | required | e.g. /export/dir1/dir2/file1 |






<a name="zbs-meta-FindPExtentRequest"></a>

### FindPExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pextent_status | [PExtentStatus](#zbs-meta-PExtentStatus) | required |  |






<a name="zbs-meta-FindVolumeRequest"></a>

### FindVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| type | [FindVolumeType](#zbs-meta-FindVolumeType) | optional |  |






<a name="zbs-meta-GetAccessRecordRequest"></a>

### GetAccessRecordRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| target_id | [string](#string) | required |  |






<a name="zbs-meta-GetAccessRecordResponse"></a>

### GetAccessRecordResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| record | [ISCSIAccessRecords](#zbs-meta-ISCSIAccessRecords) | optional |  |






<a name="zbs-meta-GetAllocPExtentRequest"></a>

### GetAllocPExtentRequest
deprecated


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| chunk_id | [uint32](#uint32) | required |  |
| pid | [uint32](#uint32) | required |  |
| alloc | [bool](#bool) | required |  |






<a name="zbs-meta-GetChunkConnectivitiesResponse"></a>

### GetChunkConnectivitiesResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| chunk_conns | [zbs.ChunkConnectivity](#zbs-ChunkConnectivity) | repeated |  |






<a name="zbs-meta-GetChunkIsolateInfoResponse"></a>

### GetChunkIsolateInfoResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| items | [GetChunkIsolateInfoResponseItem](#zbs-meta-GetChunkIsolateInfoResponseItem) | repeated |  |






<a name="zbs-meta-GetChunkIsolateInfoResponseItem"></a>

### GetChunkIsolateInfoResponseItem



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| record | [zbs.ChunkIsolateRecord](#zbs-ChunkIsolateRecord) | optional |  |
| chunk_real_status | [zbs.ChunkIsolateFlag](#zbs-ChunkIsolateFlag) | optional |  |






<a name="zbs-meta-GetLeaseRequest"></a>

### GetLeaseRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| preferred_session | [bytes](#bytes) | optional |  |






<a name="zbs-meta-GetMaintenanceInfoResponse"></a>

### GetMaintenanceInfoResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cid | [uint32](#uint32) | required |  |
| expire_duration_s | [uint32](#uint32) | required |  |






<a name="zbs-meta-GetNVMFAccessRecordRequest"></a>

### GetNVMFAccessRecordRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| subsystem_id | [string](#string) | required |  |






<a name="zbs-meta-GetNVMFAccessRecordResponse"></a>

### GetNVMFAccessRecordResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| record | [NVMFAccessRecords](#zbs-meta-NVMFAccessRecords) | optional |  |






<a name="zbs-meta-GetNVMFOptimizedAccessRequest"></a>

### GetNVMFOptimizedAccessRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| subsystem_name | [string](#string) | required |  |
| ns_id | [uint32](#uint32) | required |  |






<a name="zbs-meta-GetNVMFVolumeAccessRecordsRequest"></a>

### GetNVMFVolumeAccessRecordsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-GetPExtentRefResponse"></a>

### GetPExtentRefResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| refs | [PExtentRef](#zbs-meta-PExtentRef) | repeated |  |






<a name="zbs-meta-GetPExtentRequest"></a>

### GetPExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |






<a name="zbs-meta-GetServicePortalRequest"></a>

### GetServicePortalRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| initiator | [string](#string) | required |  |
| target_name | [string](#string) | required |  |
| initiator_ip | [string](#string) | required |  |
| conn_server_ip | [string](#string) | required | which address was redirector get login request |
| local_access_ip | [string](#string) | required | data ip address in redirectord node who send request |






<a name="zbs-meta-GetTemporaryReplicaRequest"></a>

### GetTemporaryReplicaRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_pid | [uint32](#uint32) | required |  |
| temporary_epoch | [uint64](#uint64) | required |  |






<a name="zbs-meta-GetTemporaryReplicaResponse"></a>

### GetTemporaryReplicaResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_replica | [zbs.TemporaryReplica](#zbs-TemporaryReplica) | required |  |






<a name="zbs-meta-GetTemporaryReplicaSummaryResponse"></a>

### GetTemporaryReplicaSummaryResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_replica_space | [uint64](#uint64) | required |  Default: 0 |
| chunk_infos | [ChunkTemporaryReplicaInfo](#zbs-meta-ChunkTemporaryReplicaInfo) | repeated |  |






<a name="zbs-meta-GetVExtentLeaseRequest"></a>

### GetVExtentLeaseRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vtable_id | [bytes](#bytes) | required |  |
| vextent_no | [uint32](#uint32) | required |  |
| preferred_session | [bytes](#bytes) | optional |  |
| is_write | [bool](#bool) | optional | if it is a write request Default: false |
| resize | [bool](#bool) | optional | if resize as needed Default: false |
| preferred_cid | [uint32](#uint32) | optional |  |
| no_alloc | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-GetVTableResponse"></a>

### GetVTableResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vextents | [zbs.VExtent](#zbs-VExtent) | repeated |  |






<a name="zbs-meta-GetVhostIOPermissionResponse"></a>

### GetVhostIOPermissionResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| record | [VhostIOPermissionRecord](#zbs-meta-VhostIOPermissionRecord) | optional |  |






<a name="zbs-meta-IOThrottleConfig"></a>

### IOThrottleConfig
for the description of the leaky bucket alogorithm
refer to https://blogs.igalia.com/berto/2016/05/24/io-bursts-with-qemu-2-6/


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| iops | [uint64](#uint64) | optional |  Default: 0 |
| iops_rd | [uint64](#uint64) | optional |  Default: 0 |
| iops_wr | [uint64](#uint64) | optional |  Default: 0 |
| iops_max | [uint64](#uint64) | optional |  Default: 0 |
| iops_rd_max | [uint64](#uint64) | optional |  Default: 0 |
| iops_wr_max | [uint64](#uint64) | optional |  Default: 0 |
| iops_max_length | [uint64](#uint64) | optional |  Default: 1 |
| iops_rd_max_length | [uint64](#uint64) | optional |  Default: 1 |
| iops_wr_max_length | [uint64](#uint64) | optional |  Default: 1 |
| io_size | [uint64](#uint64) | optional | 4K Default: 4096 |
| bps | [uint64](#uint64) | optional |  Default: 0 |
| bps_rd | [uint64](#uint64) | optional |  Default: 0 |
| bps_wr | [uint64](#uint64) | optional |  Default: 0 |
| bps_max | [uint64](#uint64) | optional |  Default: 0 |
| bps_rd_max | [uint64](#uint64) | optional |  Default: 0 |
| bps_wr_max | [uint64](#uint64) | optional |  Default: 0 |
| bps_max_length | [uint64](#uint64) | optional |  Default: 1 |
| bps_rd_max_length | [uint64](#uint64) | optional |  Default: 1 |
| bps_wr_max_length | [uint64](#uint64) | optional |  Default: 1 |






<a name="zbs-meta-ISCSIAccessRecord"></a>

### ISCSIAccessRecord



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| initiator | [string](#string) | optional |  |
| cid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-ISCSIAccessRecords"></a>

### ISCSIAccessRecords



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| target_id | [bytes](#bytes) | required | target uuid |
| accesses | [ISCSIAccessRecord](#zbs-meta-ISCSIAccessRecord) | repeated | access login record for balance target &amp; external use target |






<a name="zbs-meta-ISCSIConfigUpdate"></a>

### ISCSIConfigUpdate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| target_id | [bytes](#bytes) | required |  |
| config_ver | [uint32](#uint32) | required |  |
| op | [ISCSIConfigOption](#zbs-meta-ISCSIConfigOption) | repeated |  |
| target | [ISCSITarget](#zbs-meta-ISCSITarget) | optional |  |
| lun | [ISCSILun](#zbs-meta-ISCSILun) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| update | ISCSIConfigUpdate | ConfigUpdate | 10001 |  |




<a name="zbs-meta-ISCSIConnection"></a>

### ISCSIConnection



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| initiator | [bytes](#bytes) | required |  |
| initiator_ip | [bytes](#bytes) | required |  |
| target_id | [bytes](#bytes) | required |  |
| client_port | [uint32](#uint32) | required |  |
| target_addr | [bytes](#bytes) | optional |  |
| target_name | [bytes](#bytes) | optional |  |
| survival_time | [uint64](#uint64) | optional |  |
| tr_type | [bytes](#bytes) | optional |  |






<a name="zbs-meta-ISCSIConnectionRecord"></a>

### ISCSIConnectionRecord



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| conn | [ISCSIConnection](#zbs-meta-ISCSIConnection) | required |  |
| session_id | [bytes](#bytes) | required |  |
| cid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-ISCSIConnections"></a>

### ISCSIConnections



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| conns | [ISCSIConnection](#zbs-meta-ISCSIConnection) | repeated |  |






<a name="zbs-meta-ISCSILun"></a>

### ISCSILun



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_id | [uint32](#uint32) | required | lun_id, used in scsi REPORTLUNS |
| pool_id | [bytes](#bytes) | required | same with underlying pool_id |
| volume_id | [bytes](#bytes) | required | same with underlying volume_id |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| type | [ISCSIType](#zbs-meta-ISCSIType) | optional | **Deprecated.**  Default: SCSI_TYPE_DISK |
| size | [uint64](#uint64) | optional | copied from volume |
| description | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| iops | [uint64](#uint64) | optional | **Deprecated.**  |
| iops_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| bps | [uint64](#uint64) | optional | **Deprecated.**  |
| bps_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| unique_size | [int64](#int64) | optional |  Default: -1 |
| shared_size | [int64](#int64) | optional |  Default: -1 |
| reserve_id | [uint64](#uint64) | optional | spc2 reservation Default: 0 |
| name | [bytes](#bytes) | optional | for openstack volume name |
| naa | [bytes](#bytes) | optional | for scsi id in host |
| allowed_initiators | [bytes](#bytes) | optional |  Default: */* |
| single_access | [bool](#bool) | optional |  Default: false |
| stripe_num | [uint32](#uint32) | optional | stripe num |
| stripe_size | [uint32](#uint32) | optional |  |
| pr_info | [PRInfo](#zbs-meta-PRInfo) | optional |  |
| queue_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-ISCSILunsResponse"></a>

### ISCSILunsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| luns | [ISCSILun](#zbs-meta-ISCSILun) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-ISCSIServicePortal"></a>

### ISCSIServicePortal



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| portal | [string](#string) | optional | ip:port as iscsi target access point |






<a name="zbs-meta-ISCSISnapshotPath"></a>

### ISCSISnapshotPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | optional |  |
| snapshot_name | [bytes](#bytes) | optional |  |
| snapshot_id | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-ISCSISpc2ReleaseRequest"></a>

### ISCSISpc2ReleaseRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| reserve_id | [uint64](#uint64) | required |  |
| force | [bool](#bool) | required |  |






<a name="zbs-meta-ISCSISpc2ReserveRequest"></a>

### ISCSISpc2ReserveRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| reserve_id | [uint64](#uint64) | required |  |






<a name="zbs-meta-ISCSITarget"></a>

### ISCSITarget



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required | pool_id |
| name | [bytes](#bytes) | required | same with pool_name, used in iqn_name |
| iqn_name | [bytes](#bytes) | required | iqn name rfc 3721 https://www.ietf.org/rfc/rfc3721.txt iSCSI Qualified Name (IQN) Format: The iSCSI Qualified Name is documented in RFC 3720, with further examples of names in RFC 3721. Briefly, the fields are: literal iqn (iSCSI Qualified Name) date (yyyy-mm) that the naming authority took ownership of the domain reversed domain name of the authority (e.g. org.alpinelinux, com.example, to.yp.cr) Optional &#34;:&#34; prefixing a storage target name specified by the naming authority. From the RFC: Naming String defined by Type Date Auth &#34;example.com&#34; naming authority &#43;--&#43;&#43;-----&#43; &#43;---------&#43; &#43;-----------------------------&#43; | || | | | | |

 iqn.1992-01.com.example:storage:diskarrays-sn-a8675309 iqn.1992-01.com.example iqn.1992-01.com.example:storage.tape1.sys1.xyz iqn.1992-01.com.example:storage.disk2.sys1.xyz[10]

used in iscsi INQUIRY, maxmium 223 |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| iqn_date | [bytes](#bytes) | optional | attributes for iSCSI Qualified Name (IQN) our rule for iqn_name: iqn.$iqn_date.$iqn_naming_auth:$storage_pool_name:$name |
| iqn_naming_auth | [bytes](#bytes) | optional |  |
| description | [bytes](#bytes) | optional |  |
| driver_name | [bytes](#bytes) | optional | &#34;iscsi&#34; or &#34;iser&#34; Default: iscsi |
| pool | [Pool](#zbs-meta-Pool) | optional | pool of this target |
| target_chap | [TargetChapInfo](#zbs-meta-TargetChapInfo) | optional | mutual chap |
| initiator_chap | [InitiatorChapInfo](#zbs-meta-InitiatorChapInfo) | repeated | acceptable chaps, non-zero size means CHAP is required |
| iqn_whitelist | [bytes](#bytes) | repeated | **Deprecated.**  |
| external_use | [bool](#bool) | optional |  Default: false |
| iqn_whitelist_v2 | [bytes](#bytes) | optional |  Default: */* |
| adaptive_iqn_whitelist | [bool](#bool) | optional |  Default: false |
| luns | [ISCSILun](#zbs-meta-ISCSILun) | repeated | this field is available from ListTarget, but not guaranteed for other rpc |
| config_ver | [uint32](#uint32) | optional | use for quick compare target config Default: 0 |
| labels | [zbs.Labels](#zbs-Labels) | optional |  |






<a name="zbs-meta-ISCSITargetShortInfo"></a>

### ISCSITargetShortInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required | pool_id |
| config_ver | [uint32](#uint32) | required |  |






<a name="zbs-meta-ISCSITargetsResponse"></a>

### ISCSITargetsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| targets | [ISCSITarget](#zbs-meta-ISCSITarget) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-InitiatorChapInfo"></a>

### InitiatorChapInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| iqn | [bytes](#bytes) | required |  |
| chap_name | [bytes](#bytes) | required |  |
| secret | [bytes](#bytes) | required |  |
| enable | [bool](#bool) | optional |  Default: true |






<a name="zbs-meta-InodeId"></a>

### InodeId



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |






<a name="zbs-meta-KeyRequest"></a>

### KeyRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [string](#string) | required |  |
| value | [string](#string) | optional |  |






<a name="zbs-meta-KeyResponse"></a>

### KeyResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| value | [string](#string) | optional |  |






<a name="zbs-meta-ListConsistencyGroupRequest"></a>

### ListConsistencyGroupRequest







<a name="zbs-meta-ListConsistencyGroupResponse"></a>

### ListConsistencyGroupResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| groups | [ConsistencyGroup](#zbs-meta-ConsistencyGroup) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-ListConsistencyGroupSnapshotRequest"></a>

### ListConsistencyGroupSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-ListConsistencyGroupSnapshotResponse"></a>

### ListConsistencyGroupSnapshotResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshots | [ConsistencyGroupSnapshot](#zbs-meta-ConsistencyGroupSnapshot) | repeated |  |






<a name="zbs-meta-ListDbResponse"></a>

### ListDbResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| version | [VersionInfo](#zbs-meta-VersionInfo) | optional |  |
| pool_name | [bytes](#bytes) | required |  |
| db_name | [string](#string) | repeated |  |






<a name="zbs-meta-ListISCSIConnectionRequest"></a>

### ListISCSIConnectionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-ListISCSIConnectionResponse"></a>

### ListISCSIConnectionResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| records | [ISCSIConnectionRecord](#zbs-meta-ISCSIConnectionRecord) | repeated |  |






<a name="zbs-meta-ListInodeRequest"></a>

### ListInodeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| pagination | [zbs.Pagination](#zbs-Pagination) | optional |  |






<a name="zbs-meta-ListNVMFConnectionRequest"></a>

### ListNVMFConnectionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-ListNVMFConnectionResponse"></a>

### ListNVMFConnectionResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| records | [NVMFConnectionRecord](#zbs-meta-NVMFConnectionRecord) | repeated |  |






<a name="zbs-meta-ListNVMFDistSubsystemsRequest"></a>

### ListNVMFDistSubsystemsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| show_ns | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-ListPExtentRequest"></a>

### ListPExtentRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid_start | [uint32](#uint32) | required |  |
| pid_end | [uint32](#uint32) | required |  |
| show_non_exist | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-ListPoolRequest"></a>

### ListPoolRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pagination | [zbs.Pagination](#zbs-Pagination) | optional |  |
| user | [bytes](#bytes) | optional | user id. Not set or empty means root user. |






<a name="zbs-meta-ListSnapshotRequest"></a>

### ListSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_path | [VolumePath](#zbs-meta-VolumePath) | optional |  |
| pagination | [zbs.Pagination](#zbs-Pagination) | optional |  |






<a name="zbs-meta-ListTargetPortalsRequest"></a>

### ListTargetPortalsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| iqn_name | [bytes](#bytes) | required |  |
| source_ip | [uint32](#uint32) | optional | report portals for given source IP |






<a name="zbs-meta-ListTargetsRequest"></a>

### ListTargetsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| show_lun | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-ListTemporaryReplicaRequest"></a>

### ListTemporaryReplicaRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| start_temporary_pid | [uint32](#uint32) | optional |  Default: 0 |
| max_num | [uint32](#uint32) | optional |  Default: 2048 |






<a name="zbs-meta-ListTemporaryReplicaResponse"></a>

### ListTemporaryReplicaResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| temporary_replicas | [zbs.TemporaryReplica](#zbs-TemporaryReplica) | repeated |  |






<a name="zbs-meta-ListVolumeRequest"></a>

### ListVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| pagination | [zbs.Pagination](#zbs-Pagination) | optional |  |






<a name="zbs-meta-LookupInodeRequest"></a>

### LookupInodeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dir_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | required |  |






<a name="zbs-meta-LunPath"></a>

### LunPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| lun_id | [uint32](#uint32) | optional |  Default: 0 |
| lun_name | [bytes](#bytes) | optional |  |
| lun_uuid | [bytes](#bytes) | optional | pool_path/lun_id/lun_name are ignored |
| storage_pool_id | [bytes](#bytes) | optional | with lun_uuid only |
| secondary_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-MetaSummary"></a>

### MetaSummary



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| meta_status | [MetaStatus](#zbs-meta-MetaStatus) | optional |  |
| is_leader | [bool](#bool) | optional |  |
| leader | [string](#string) | optional |  |
| alive_meta_hosts | [zbs.Address](#zbs-Address) | repeated |  |






<a name="zbs-meta-MountPoint"></a>

### MountPoint



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| client | [bytes](#bytes) | required |  |
| dir | [bytes](#bytes) | required |  |






<a name="zbs-meta-MountTable"></a>

### MountTable



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| entries | [MountPoint](#zbs-meta-MountPoint) | repeated |  |






<a name="zbs-meta-MoveDataAndDeleteVolumeRequest"></a>

### MoveDataAndDeleteVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| src_volume | [VolumePath](#zbs-meta-VolumePath) | required |  |
| dst_volume | [VolumePath](#zbs-meta-VolumePath) | required |  |






<a name="zbs-meta-MoveFileRequest"></a>

### MoveFileRequest
move a file inode between two exports


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| parent_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | required |  |
| src_inode_path | [bytes](#bytes) | required |  |






<a name="zbs-meta-MoveISCSILunRequest"></a>

### MoveISCSILunRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| src_lun_path | [LunPath](#zbs-meta-LunPath) | required |  |






<a name="zbs-meta-MoveISCSISnapshotRequest"></a>

### MoveISCSISnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_path | [ISCSISnapshotPath](#zbs-meta-ISCSISnapshotPath) | required |  |
| dst_target | [PoolPath](#zbs-meta-PoolPath) | required |  |






<a name="zbs-meta-MoveNFSSnapshotRequest"></a>

### MoveNFSSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_path | [NFSSnapshotPath](#zbs-meta-NFSSnapshotPath) | required |  |
| dst_export_name | [bytes](#bytes) | required |  |






<a name="zbs-meta-MoveSnapshotRequest"></a>

### MoveSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [SnapshotPath](#zbs-meta-SnapshotPath) | required |  |
| dst_pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |






<a name="zbs-meta-MoveVolumeRequest"></a>

### MoveVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_path | [VolumePath](#zbs-meta-VolumePath) | required |  |
| dst_pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |






<a name="zbs-meta-NFSAttr"></a>

### NFSAttr



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| type | [NFSType](#zbs-meta-NFSType) | optional |  |
| mode | [uint32](#uint32) | optional |  Default: 432 |
| uid | [uint32](#uint32) | optional |  Default: 0 |
| gid | [uint32](#uint32) | optional |  Default: 0 |
| size | [uint64](#uint64) | optional |  |
| ctime | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| mtime | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| atime | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| nlink | [uint32](#uint32) | optional |  Default: 1 |






<a name="zbs-meta-NFSInode"></a>

### NFSInode



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id_v1 | [uint32](#uint32) | optional | **Deprecated.**  |
| parent_id_v1 | [uint32](#uint32) | optional | **Deprecated.**  |
| id | [bytes](#bytes) | optional |  |
| parent_id | [bytes](#bytes) | optional |  |
| name | [bytes](#bytes) | optional |  |
| pool_id_v1 | [uint32](#uint32) | optional | **Deprecated.**  |
| pool_id | [bytes](#bytes) | optional | the pool id corresponding to the export |
| attr | [NFSAttr](#zbs-meta-NFSAttr) | optional |  |
| preallocate | [bool](#bool) | optional | **Deprecated.**  |
| volume_id_v1 | [uint32](#uint32) | optional | **Deprecated.**  Default: 0 |
| volume_id | [bytes](#bytes) | optional | volume id in zbs. used when it is a file |
| unique_size | [int64](#int64) | optional | used size. used when it is a file Default: -1 |
| shared_size | [int64](#int64) | optional |  Default: -1 |
| createverf3 | [bytes](#bytes) | optional | to store EXCLUSIVE create verifier |
| xid | [uint32](#uint32) | optional | to store xid as a creation verifier |
| symlink_path | [bytes](#bytes) | optional | note there is a low possibility that two requests from different clients have the same xid. However since a) possibility is very low b) hard to differentiate the case where nfs server was reset and request retried, so just take xid here

used for symlink path |






<a name="zbs-meta-NFSInodes"></a>

### NFSInodes



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inodes | [NFSInode](#zbs-meta-NFSInode) | repeated |  |






<a name="zbs-meta-NFSSnapshotPath"></a>

### NFSSnapshotPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inode_id | [bytes](#bytes) | optional |  |
| snapshot_name | [bytes](#bytes) | optional |  |
| export_name | [bytes](#bytes) | optional | for list operation only |
| snapshot_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-NVMFAccessRecord"></a>

### NVMFAccessRecord



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| host_nqn | [string](#string) | optional |  |
| cid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-NVMFAccessRecords"></a>

### NVMFAccessRecords



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| subsystem_id | [bytes](#bytes) | required | subsystem uuid |
| accesses | [NVMFAccessRecord](#zbs-meta-NVMFAccessRecord) | repeated | access login record for balance subsystem |






<a name="zbs-meta-NVMFConfigUpdate"></a>

### NVMFConfigUpdate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| subsystem_id | [bytes](#bytes) | required |  |
| config_ver | [uint32](#uint32) | required |  |
| op | [NVMFConfigOption](#zbs-meta-NVMFConfigOption) | repeated |  |
| subsystem | [NVMFDistSubsystem](#zbs-meta-NVMFDistSubsystem) | optional |  |
| ns | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| update | NVMFConfigUpdate | ConfigUpdate | 10002 |  |




<a name="zbs-meta-NVMFConnection"></a>

### NVMFConnection



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| host_nqn | [bytes](#bytes) | required | TODO(jiewei): remove unused fileds |
| host_ip | [bytes](#bytes) | required |  |
| subsystem_id | [bytes](#bytes) | required |  |
| client_port | [uint32](#uint32) | required |  |
| subsystem_addr | [bytes](#bytes) | optional |  |
| subsystem_name | [bytes](#bytes) | optional |  |
| survival_time | [uint64](#uint64) | optional |  |
| tr_type | [bytes](#bytes) | optional |  |






<a name="zbs-meta-NVMFConnectionRecord"></a>

### NVMFConnectionRecord



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| conn | [NVMFConnection](#zbs-meta-NVMFConnection) | required |  |
| session_id | [bytes](#bytes) | required |  |
| cid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-NVMFConnections"></a>

### NVMFConnections



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| conns | [NVMFConnection](#zbs-meta-NVMFConnection) | repeated |  |






<a name="zbs-meta-NVMFDistNamespace"></a>

### NVMFDistNamespace



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_id | [uint32](#uint32) | required |  |
| pool_id | [bytes](#bytes) | required | same with underlying pool_id |
| volume_id | [bytes](#bytes) | required | same with underlying volume_id |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| size | [uint64](#uint64) | optional | copied from volume |
| description | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| unique_size | [int64](#int64) | optional |  Default: -1 |
| shared_size | [int64](#int64) | optional |  Default: -1 |
| name | [bytes](#bytes) | optional |  |
| group_id | [bytes](#bytes) | optional | namespace group id |
| current_location | [uint32](#uint32) | optional | deprecated Default: 0 |
| expected_location | [uint32](#uint32) | optional | deprecated Default: 0 |
| optimized_paths | [NVMFOptimizedPath](#zbs-meta-NVMFOptimizedPath) | repeated |  |
| preferred_cid | [uint32](#uint32) | optional |  Default: 0 |
| stripe_num | [uint32](#uint32) | optional | stripe num |
| stripe_size | [uint32](#uint32) | optional |  |
| is_shared | [bool](#bool) | optional | shared namespace, only valid for INHERIT_POLICY Default: false |
| nqn_whitelist | [bytes](#bytes) | optional |  |
| single_access | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-NVMFDistNamespaceGroup"></a>

### NVMFDistNamespaceGroup



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_id | [bytes](#bytes) | required |  |
| pool_id | [bytes](#bytes) | required |  |
| name | [bytes](#bytes) | required |  |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |






<a name="zbs-meta-NVMFDistNamespaceGroupsResponse"></a>

### NVMFDistNamespaceGroupsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_groups | [NVMFDistNamespaceGroup](#zbs-meta-NVMFDistNamespaceGroup) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-NVMFDistNamespacesResponse"></a>

### NVMFDistNamespacesResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| namespaces | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-NVMFDistSubsystem"></a>

### NVMFDistSubsystem



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required | pool_id |
| name | [bytes](#bytes) | required | same with pool_name, used in nqn_name |
| nqn_name | [bytes](#bytes) | required | maximum 223 |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| nqn_date | [bytes](#bytes) | optional | attributes for NVMe Qualified Name (NQN) our rule for nqn_name: nqn.$nqn_date.$nqn_naming_auth:$storage_pool_name:$name |
| nqn_naming_auth | [bytes](#bytes) | optional |  |
| description | [bytes](#bytes) | optional |  |
| pool | [Pool](#zbs-meta-Pool) | optional | pool of this subsystem |
| policy | [NVMFAccessPolicy](#zbs-meta-NVMFAccessPolicy) | optional |  Default: INHERIT_POLICY |
| namespaces | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) | repeated |  |
| config_ver | [uint32](#uint32) | optional | use for quick compare subsystem config Default: 0 |
| nqn_whitelist | [bytes](#bytes) | optional |  Default: */* |
| ipv4_whitelist | [bytes](#bytes) | optional |  Default: */* |
| labels | [zbs.Labels](#zbs-Labels) | optional |  |
| attributes | [zbs.Labels](#zbs-Labels) | optional |  |






<a name="zbs-meta-NVMFDistSubsystemsResponse"></a>

### NVMFDistSubsystemsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dist_subsystems | [NVMFDistSubsystem](#zbs-meta-NVMFDistSubsystem) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-NVMFNamespaceAllowedHost"></a>

### NVMFNamespaceAllowedHost



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_path | [DistNamespacePath](#zbs-meta-DistNamespacePath) | required |  |
| host_nqn | [bytes](#bytes) | required |  |






<a name="zbs-meta-NVMFOptimizedPath"></a>

### NVMFOptimizedPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| host_nqn | [string](#string) | required |  |
| optimized_cid | [uint32](#uint32) | required |  |
| expected_optimized_cid | [uint32](#uint32) | optional |  Default: 0 |
| current_location | [uint32](#uint32) | optional |  Default: 0 |
| expected_location | [uint32](#uint32) | optional |  Default: 0 |






<a name="zbs-meta-NVMFOptimizedPaths"></a>

### NVMFOptimizedPaths



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| paths | [NVMFOptimizedPath](#zbs-meta-NVMFOptimizedPath) | repeated |  |






<a name="zbs-meta-NVMFSnapshotPath"></a>

### NVMFSnapshotPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_path | [DistNamespacePath](#zbs-meta-DistNamespacePath) | optional |  |
| snapshot_name | [bytes](#bytes) | optional |  |
| snapshot_id | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-NVMFTarget"></a>

### NVMFTarget



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| address | [bytes](#bytes) | required |  |
| port | [int32](#int32) | required |  Default: 4420 |
| tr_type | [NVMFTransportType](#zbs-meta-NVMFTransportType) | required |  Default: NVMF_TR_TYPE_RDMA |






<a name="zbs-meta-NVMFTargetsResponse"></a>

### NVMFTargetsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| targets | [NVMFTarget](#zbs-meta-NVMFTarget) | repeated |  |






<a name="zbs-meta-NVMFVolumeAccessRecords"></a>

### NVMFVolumeAccessRecords



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| accesses | [NVMFAccessRecord](#zbs-meta-NVMFAccessRecord) | repeated |  |






<a name="zbs-meta-NVMFVolumeReset"></a>

### NVMFVolumeReset



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| host_nqn | [string](#string) | required |  |
| nvmf_volumes | [bytes](#bytes) | repeated |  |






<a name="zbs-meta-NegotiatedConfig"></a>

### NegotiatedConfig



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enable_config_push | [bool](#bool) | optional |  Default: false |
| enable_data_report_channel | [bool](#bool) | optional |  Default: false |
| enable_thick_extent | [bool](#bool) | optional |  Default: false |
| enable_unmap | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-PExtentRef"></a>

### PExtentRef



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_name | [bytes](#bytes) | required |  |
| volume_name | [bytes](#bytes) | required |  |
| snapshot_name | [bytes](#bytes) | optional |  |
| volume_id | [bytes](#bytes) | optional |  |
| volume_is_snapshot | [bool](#bool) | optional |  Default: false |
| volume_snapshot_pool_id | [bytes](#bytes) | optional |  |
| offset | [int64](#int64) | optional | pextent index offset in volume Default: -1 |






<a name="zbs-meta-PExtentsResponse"></a>

### PExtentsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pextents | [zbs.PExtent](#zbs-PExtent) | repeated |  |






<a name="zbs-meta-PRInfo"></a>

### PRInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| gen | [uint32](#uint32) | optional | scsi pr generation Default: 0 |
| regs | [PRReg](#zbs-meta-PRReg) | repeated | registrants per lun |
| pr_holder | [bytes](#bytes) | optional | scsi-3 reservation |
| scsi2_pr_holder | [bytes](#bytes) | optional | scsi-2 reservation |






<a name="zbs-meta-PRReg"></a>

### PRReg



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [uint64](#uint64) | optional | scsi specification defined the key |
| pr_scope | [uint32](#uint32) | optional | only need 8bit |
| pr_type | [uint32](#uint32) | optional | only need 8bit |
| it_nexus | [bytes](#bytes) | optional | this is a session link identifier |






<a name="zbs-meta-PRRequest"></a>

### PRRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required | lun identifier |
| old_pr_info | [PRInfo](#zbs-meta-PRInfo) | required | the current pr information |
| new_pr_info | [PRInfo](#zbs-meta-PRInfo) | required | the next pr information |






<a name="zbs-meta-PRResponse"></a>

### PRResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required | lun identifier |
| meta_pr_info | [PRInfo](#zbs-meta-PRInfo) | optional | the record of meta pr information |






<a name="zbs-meta-Pool"></a>

### Pool



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | required | &lt; 255B |
| id_v1 | [uint32](#uint32) | optional | **Deprecated.**  |
| id | [bytes](#bytes) | optional |  |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| storage_pool_id | [bytes](#bytes) | optional | if not specified, the system storage pool is used |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| description | [bytes](#bytes) | optional | 256B |
| nfs_export | [bool](#bool) | optional | **Deprecated.** deprecated: (v1 needed it) Default: false |
| mod_verf | [uint32](#uint32) | optional | mod_verf is a guard against unintended modification/removal of a pool, if set any future requestis attempting to alter the pool needs to provide a match verification number |
| whitelist | [bytes](#bytes) | optional | ip v4: ************* ip/net-mask: ************/************* ip/mask: ************/24 */* Default: */* |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional | copied to every volume |
| stripe_num | [uint32](#uint32) | optional | copied to every volume Default: 1 |
| stripe_size | [uint32](#uint32) | optional | 256K Default: 262144 |






<a name="zbs-meta-PoolPath"></a>

### PoolPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_name | [bytes](#bytes) | optional | either pool_id or pool_name is specified if pool_id is specified, pool_name is ignored |
| pool_id | [bytes](#bytes) | optional |  |
| mod_verf | [uint32](#uint32) | optional |  |






<a name="zbs-meta-PoolsResponse"></a>

### PoolsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pools | [Pool](#zbs-meta-Pool) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-RebalanceAccessPointStatus"></a>

### RebalanceAccessPointStatus



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enable_rebalance | [bool](#bool) | optional |  |






<a name="zbs-meta-RecoverCmds"></a>

### RecoverCmds



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cmds | [zbs.RecoverCmd](#zbs-RecoverCmd) | repeated |  |






<a name="zbs-meta-RecoverModeInfo"></a>

### RecoverModeInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| recover_mode | [zbs.RecoverMode](#zbs-RecoverMode) | required |  Default: RECOVER_INVALID |
| static_recover_limit | [uint64](#uint64) | optional |  |
| session_recover_limits | [SessionRecoverLimit](#zbs-meta-SessionRecoverLimit) | repeated |  |
| enable_recover | [bool](#bool) | optional |  |
| migrate_mode | [zbs.MigrateMode](#zbs-MigrateMode) | required |  Default: MIGRATE_INVALID |
| static_migrate_limit | [uint64](#uint64) | optional |  |
| session_migrate_limits | [SessionMigrateLimit](#zbs-meta-SessionMigrateLimit) | repeated |  |
| enable_migrate | [bool](#bool) | optional |  |
| cluster_max_recover_limit | [uint64](#uint64) | optional |  |
| cluster_max_migrate_limit | [uint64](#uint64) | optional |  |






<a name="zbs-meta-RecoverParameter"></a>

### RecoverParameter



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| migrate_scan_interval_s | [uint64](#uint64) | required |  |
| recover_scan_interval_s | [uint64](#uint64) | required |  |
| recover_trigger_inteval_s | [uint64](#uint64) | required |  |
| max_migrate_cmds | [uint64](#uint64) | required |  |
| max_recover_cmds | [uint64](#uint64) | required |  |
| max_migrate_cmds_per_chunk | [uint64](#uint64) | required |  |
| max_recover_cmds_per_chunk | [uint64](#uint64) | required |  |






<a name="zbs-meta-RefreshChildExtentLocationRequest"></a>

### RefreshChildExtentLocationRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vtable_id | [bytes](#bytes) | required |  |
| vextent_no | [uint32](#uint32) | required |  |
| session_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-ReleaseBadLeaseRequest"></a>

### ReleaseBadLeaseRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| my_session_id | [bytes](#bytes) | required |  |
| replica_cids | [uint32](#uint32) | repeated |  |






<a name="zbs-meta-ReleaseLeaseRequest"></a>

### ReleaseLeaseRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_id | [bytes](#bytes) | required |  |
| pids | [uint32](#uint32) | repeated |  |






<a name="zbs-meta-RemoveChunkFromStoragePoolRequest"></a>

### RemoveChunkFromStoragePoolRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| storage_pool_id | [bytes](#bytes) | required |  |
| chunk_id | [uint32](#uint32) | required |  |
| skip_capacity_check | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-RemoveLunAllowedInitiatorsRequest"></a>

### RemoveLunAllowedInitiatorsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| initiators | [bytes](#bytes) | required |  |






<a name="zbs-meta-RemoveReplicaRequest"></a>

### RemoveReplicaRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session | [bytes](#bytes) | required | the chunk sending this request |
| pid | [uint32](#uint32) | required |  |
| replicas | [uint32](#uint32) | repeated |  |
| generation | [uint64](#uint64) | optional | replica gen: if set, the meta recorded gen will be updated to this value |
| epoch | [uint64](#uint64) | optional |  |
| alloc_temporary_replica | [bool](#bool) | optional |  Default: false |
| remove_temporary_replicas | [zbs.TemporaryReplica](#zbs-TemporaryReplica) | repeated |  |
| removing_slow | [bool](#bool) | optional | do not remove replica if no healthy chunk to recover extent. Default: false |






<a name="zbs-meta-RenameInodeRequest"></a>

### RenameInodeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| from_parent_id | [bytes](#bytes) | required |  |
| from_name | [bytes](#bytes) | required |  |
| to_parent_id | [bytes](#bytes) | required |  |
| to_name | [bytes](#bytes) | required |  |
| xid | [uint32](#uint32) | optional | operation verifier |






<a name="zbs-meta-RenameInodeResponse"></a>

### RenameInodeResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inode | [NFSInode](#zbs-meta-NFSInode) | required |  |
| deleted_inode_id | [bytes](#bytes) | optional | inode_id that has been deleted if any |






<a name="zbs-meta-ReplaceReplicaRequest"></a>

### ReplaceReplicaRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session | [bytes](#bytes) | required |  |
| pid | [uint32](#uint32) | required |  |
| src_chunk | [uint32](#uint32) | required |  |
| dst_chunk | [uint32](#uint32) | required |  |
| epoch | [uint64](#uint64) | optional |  |
| reset_location_to_dst | [bool](#bool) | optional |  Default: false |
| reset_generation | [uint64](#uint64) | optional |  |






<a name="zbs-meta-ReportVolumeAccessRequest"></a>

### ReportVolumeAccessRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_ids | [bytes](#bytes) | repeated |  |
| cid | [uint32](#uint32) | optional |  |
| force | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-RerouteStatus"></a>

### RerouteStatus



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enable_reroute | [bool](#bool) | optional |  |
| enable_secondary_data_channel | [bool](#bool) | optional | whether secondary data channel could provide data service when data channel was broken. |






<a name="zbs-meta-ReserveVolumeSpaceRequest"></a>

### ReserveVolumeSpaceRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-ResetLunPrRequest"></a>

### ResetLunPrRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |






<a name="zbs-meta-ResizeVolumeRequest"></a>

### ResizeVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [VolumePath](#zbs-meta-VolumePath) | required |  |
| size | [uint64](#uint64) | required |  |






<a name="zbs-meta-RevokeClientCmd"></a>

### RevokeClientCmd



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| client_ips | [bytes](#bytes) | repeated |  |






<a name="zbs-meta-RollbackConsistencyGroupSnapshotRequest"></a>

### RollbackConsistencyGroupSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-RollbackNFSSnapshotRequest"></a>

### RollbackNFSSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inode_id | [bytes](#bytes) | required |  |
| snapshot_name | [bytes](#bytes) | optional |  |
| snapshot_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-SAttr3"></a>

### SAttr3



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| mode | [uint32](#uint32) | optional |  |
| uid | [uint32](#uint32) | optional |  |
| gid | [uint32](#uint32) | optional |  |
| size | [uint64](#uint64) | optional |  |
| atime_how | [time_how](#zbs-meta-time_how) | optional |  Default: DONT_CHANGE |
| atime | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| mtime_how | [time_how](#zbs-meta-time_how) | optional |  Default: DONT_CHANGE |
| mtime | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| ctime_how | [time_how](#zbs-meta-time_how) | optional |  Default: DONT_CHANGE |
| ctime | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |






<a name="zbs-meta-SerializedMetaInfo"></a>

### SerializedMetaInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| meta_status | [MetaStatus](#zbs-meta-MetaStatus) | optional |  |






<a name="zbs-meta-SessionMigrateLimit"></a>

### SessionMigrateLimit



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_id | [bytes](#bytes) | required |  |
| ip | [bytes](#bytes) | required |  |
| current_migrate_limit | [uint64](#uint64) | required | MB/s |
| current_max_migrate_limit | [uint64](#uint64) | optional | MB/s |






<a name="zbs-meta-SessionRecoverLimit"></a>

### SessionRecoverLimit



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_id | [bytes](#bytes) | required |  |
| ip | [bytes](#bytes) | required |  |
| current_recover_limit | [uint64](#uint64) | required | MB/s |
| current_max_recover_limit | [uint64](#uint64) | optional | MB/s |






<a name="zbs-meta-SetAccessRecordRequest"></a>

### SetAccessRecordRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| target_id | [string](#string) | required |  |
| initiator | [string](#string) | required |  |
| cid | [uint32](#uint32) | required |  |






<a name="zbs-meta-SetAccessRecordResponse"></a>

### SetAccessRecordResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| target_id | [string](#string) | optional |  |
| initiator | [string](#string) | optional |  |
| cid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-SetAttrRequest"></a>

### SetAttrRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| sattr3 | [SAttr3](#zbs-meta-SAttr3) | required |  |
| guard | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| xid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-SetChunkIsolatePolicyRequest"></a>

### SetChunkIsolatePolicyRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [zbs.ChunkId](#zbs-ChunkId) | optional |  |
| flag | [zbs.ChunkIsolateFlag](#zbs-ChunkIsolateFlag) | optional |  |
| name | [string](#string) | optional |  |






<a name="zbs-meta-SetMaintenanceModeRequest"></a>

### SetMaintenanceModeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [zbs.ChunkId](#zbs-ChunkId) | required |  |
| mode | [bool](#bool) | required |  |
| expire_duration_s | [uint32](#uint32) | optional | second |






<a name="zbs-meta-SetNVMFAccessRecordRequest"></a>

### SetNVMFAccessRecordRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| subsystem_id | [string](#string) | required |  |
| host_nqn | [string](#string) | required |  |
| cid | [uint32](#uint32) | required |  |






<a name="zbs-meta-SetNVMFAccessRecordResponse"></a>

### SetNVMFAccessRecordResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| subsystem_id | [string](#string) | required |  |
| host_nqn | [string](#string) | required |  |
| cid | [uint32](#uint32) | optional |  |






<a name="zbs-meta-SetNVMFOptimizedAccessRequest"></a>

### SetNVMFOptimizedAccessRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| host_nqns | [string](#string) | repeated |  |
| subsystem_name | [string](#string) | required |  |
| ns_id | [uint32](#uint32) | required |  |






<a name="zbs-meta-SetUpgradeModeRequest"></a>

### SetUpgradeModeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| duration | [uint64](#uint64) | required | second |






<a name="zbs-meta-ShowConsistencyGroupRequest"></a>

### ShowConsistencyGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |






<a name="zbs-meta-ShowConsistencyGroupSnapshotRequest"></a>

### ShowConsistencyGroupSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-ShowVolumeRequest"></a>

### ShowVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | optional |  |
| volume_name | [bytes](#bytes) | optional |  |
| volume_id | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |
| show_pextent | [bool](#bool) | optional | should be set to false in most scenarios when you only want volume basic info, use ture only for forward compatible Default: true |






<a name="zbs-meta-ShowVolumeResponse"></a>

### ShowVolumeResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume | [Volume](#zbs-meta-Volume) | optional |  |
| pextents | [zbs.PExtentResp](#zbs-PExtentResp) | repeated |  |






<a name="zbs-meta-ShowVolumesRequest"></a>

### ShowVolumesRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volumes | [VolumePath](#zbs-meta-VolumePath) | repeated |  |
| show_pextent | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-ShowVolumesResponse"></a>

### ShowVolumesResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volumes | [VolumeInfo](#zbs-meta-VolumeInfo) | repeated |  |






<a name="zbs-meta-SizeInfo"></a>

### SizeInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| size | [uint64](#uint64) | optional |  |
| volume_size | [uint64](#uint64) | optional |  Default: 0 |






<a name="zbs-meta-SnapshotPath"></a>

### SnapshotPath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_path | [VolumePath](#zbs-meta-VolumePath) | optional |  |
| snapshot_name | [bytes](#bytes) | optional |  |
| snapshot_id | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-SnapshotV1"></a>

### SnapshotV1
deprecated v1 snapshot


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | required | &lt; 64B |
| size | [uint64](#uint64) | required | B |
| diff_size | [uint64](#uint64) | required | B |
| volume_id | [uint32](#uint32) | optional |  |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| description | [bytes](#bytes) | optional | 256B |
| pextents | [uint32](#uint32) | repeated | pextents in the snapshot |
| nfs_meta | [NFSAttr](#zbs-meta-NFSAttr) | optional | snapshot for nfs volatile meta info |






<a name="zbs-meta-SnapshotsResponse"></a>

### SnapshotsResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshots | [Volume](#zbs-meta-Volume) | repeated |  |






<a name="zbs-meta-SpecialRecoverRequest"></a>

### SpecialRecoverRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pid | [uint32](#uint32) | required |  |
| epoch | [uint64](#uint64) | required |  |
| temporary_pid | [uint32](#uint32) | required |  |
| temporary_epoch | [uint64](#uint64) | required |  |
| rollback_failed_replica | [bool](#bool) | optional |  |
| force_recover_from_temporary_replica | [bool](#bool) | optional |  |






<a name="zbs-meta-SubsystemRequirement"></a>

### SubsystemRequirement



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| labels | [zbs.Labels](#zbs-Labels) | optional |  |
| attributes | [zbs.Labels](#zbs-Labels) | optional |  |
| access_policy | [NVMFAccessPolicy](#zbs-meta-NVMFAccessPolicy) | optional |  Default: INHERIT_POLICY |
| replica_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-SwitchRequest"></a>

### SwitchRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enable | [bool](#bool) | required |  |






<a name="zbs-meta-SyncTargetsRequest"></a>

### SyncTargetsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| targets | [ISCSITargetShortInfo](#zbs-meta-ISCSITargetShortInfo) | repeated |  |






<a name="zbs-meta-TargetAndLunInfo"></a>

### TargetAndLunInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| target | [ISCSITarget](#zbs-meta-ISCSITarget) | required |  |
| lun | [ISCSILun](#zbs-meta-ISCSILun) | required |  |






<a name="zbs-meta-TargetChapInfo"></a>

### TargetChapInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| secret | [bytes](#bytes) | required |  |
| chap_name | [bytes](#bytes) | required |  |
| enable | [bool](#bool) | optional |  Default: true |






<a name="zbs-meta-TargetPortals"></a>

### TargetPortals



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| portal | [bytes](#bytes) | repeated | &#34;IP:Port,TPGT&#34; |






<a name="zbs-meta-TargetRequirement"></a>

### TargetRequirement



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| external_use | [bool](#bool) | optional |  |
| labels | [zbs.Labels](#zbs-Labels) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| adaptive_iqn_whitelist | [bool](#bool) | optional |  |






<a name="zbs-meta-TruncateRequest"></a>

### TruncateRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| size | [uint64](#uint64) | required |  |
| increase_only | [bool](#bool) | optional | only change the file size when it is increasing Default: false |






<a name="zbs-meta-UpdateClusterInfoRequest"></a>

### UpdateClusterInfoRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| new_name | [bytes](#bytes) | optional |  |
| new_desc | [bytes](#bytes) | optional |  |
| is_stretched | [bool](#bool) | optional |  |
| preferred_zone_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-UpdateConsistencyGroupRequest"></a>

### UpdateConsistencyGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| new_name | [bytes](#bytes) | optional |  |
| new_description | [bytes](#bytes) | optional |  |
| add_volumes | [ConsistencyVolume](#zbs-meta-ConsistencyVolume) | repeated |  |
| remove_volumes | [ConsistencyVolume](#zbs-meta-ConsistencyVolume) | repeated |  |
| remain_volume_snapshot | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-UpdateConsistencyGroupSnapshotRequest"></a>

### UpdateConsistencyGroupSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_id | [bytes](#bytes) | required |  |
| new_name | [bytes](#bytes) | optional |  |
| new_description | [bytes](#bytes) | optional |  |






<a name="zbs-meta-UpdateEpochRequest"></a>

### UpdateEpochRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enable | [bool](#bool) | required |  Default: false |
| reset | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-UpdateEpochResponse"></a>

### UpdateEpochResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| enable | [bool](#bool) | required |  Default: false |
| epoch | [uint64](#uint64) | required |  |






<a name="zbs-meta-UpdateISCSILunRequest"></a>

### UpdateISCSILunRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lun_path | [LunPath](#zbs-meta-LunPath) | required |  |
| size | [uint64](#uint64) | optional |  |
| description | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| iops | [uint64](#uint64) | optional | **Deprecated.**  |
| iops_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| bps | [uint64](#uint64) | optional | **Deprecated.**  |
| bps_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| new_name | [bytes](#bytes) | optional |  |
| new_alloc_even | [bool](#bool) | optional |  |
| new_allowed_initiators | [bytes](#bytes) | optional |  |
| skip_all_zero_first_write | [bool](#bool) | optional |  Default: false |
| prefer_cid | [uint32](#uint32) | optional |  Default: 0 |
| single_access | [bool](#bool) | optional |  Default: false |
| queue_num | [uint32](#uint32) | optional |  |
| read_only | [bool](#bool) | optional |  |






<a name="zbs-meta-UpdateISCSISnapshotRequest"></a>

### UpdateISCSISnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_path | [ISCSISnapshotPath](#zbs-meta-ISCSISnapshotPath) | required |  |
| new_name | [bytes](#bytes) | optional |  |
| new_description | [bytes](#bytes) | optional |  |
| new_alloc_even | [bool](#bool) | optional |  |






<a name="zbs-meta-UpdateISCSITargetRequest"></a>

### UpdateISCSITargetRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| name | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| iqn_date | [bytes](#bytes) | optional | attributes for iSCSI Qualified Name (IQN) |
| iqn_naming_auth | [bytes](#bytes) | optional |  |
| external_use | [bool](#bool) | optional |  Default: false |
| description | [bytes](#bytes) | optional | 256B |
| whitelist | [bytes](#bytes) | optional |  |
| chap_name | [bytes](#bytes) | optional | target chap |
| secret | [bytes](#bytes) | optional |  |
| enable_target_chap | [bool](#bool) | optional |  |
| remove_target_chap | [bool](#bool) | optional |  Default: false |
| remove_initiator_chap | [bytes](#bytes) | repeated | initiator chaps, processing seq: remove, update then create |
| update_initiator_chap | [InitiatorChapInfo](#zbs-meta-InitiatorChapInfo) | repeated |  |
| create_initiator_chap | [InitiatorChapInfo](#zbs-meta-InitiatorChapInfo) | repeated |  |
| iqn_whitelist | [bytes](#bytes) | repeated | **Deprecated.** prefix is either of: 1. &#34;iqn.&#34; for an iqn 2. &#34;reg.&#34; for a regular expression |
| remove_iqn_whitelist | [bool](#bool) | optional | **Deprecated.**  |
| iqn_whitelist_v2 | [bytes](#bytes) | optional |  |
| adaptive_iqn_whitelist | [bool](#bool) | optional |  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| stripe_num | [uint32](#uint32) | optional |  |
| stripe_size | [uint32](#uint32) | optional |  |






<a name="zbs-meta-UpdateNFSFileRequest"></a>

### UpdateNFSFileRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| inode_id | [bytes](#bytes) | required |  |
| skip_all_zero_first_write | [bool](#bool) | optional |  Default: false |
| alloc_even | [bool](#bool) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| read_only | [bool](#bool) | optional |  |






<a name="zbs-meta-UpdateNFSSnapshotRequest"></a>

### UpdateNFSSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_path | [NFSSnapshotPath](#zbs-meta-NFSSnapshotPath) | required |  |
| new_name | [bytes](#bytes) | optional |  |
| new_description | [bytes](#bytes) | optional |  |
| new_alloc_even | [bool](#bool) | optional |  |






<a name="zbs-meta-UpdateNVMFDistNamespaceGroupRequest"></a>

### UpdateNVMFDistNamespaceGroupRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_path | [DistNamespaceGroupPath](#zbs-meta-DistNamespaceGroupPath) | required |  |
| new_name | [bytes](#bytes) | optional |  |






<a name="zbs-meta-UpdateNVMFDistNamespaceRequest"></a>

### UpdateNVMFDistNamespaceRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ns_path | [DistNamespacePath](#zbs-meta-DistNamespacePath) | required |  |
| size | [uint64](#uint64) | optional |  |
| description | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| new_name | [bytes](#bytes) | optional |  |
| new_alloc_even | [bool](#bool) | optional |  |
| skip_all_zero_first_write | [bool](#bool) | optional |  Default: false |
| expected_location | [uint32](#uint32) | optional |  |
| optimized_paths | [NVMFOptimizedPath](#zbs-meta-NVMFOptimizedPath) | repeated |  |
| nqn_whitelist | [bytes](#bytes) | optional |  |
| single_access | [bool](#bool) | optional |  |
| read_only | [bool](#bool) | optional |  |






<a name="zbs-meta-UpdateNVMFDistSubsystemRequest"></a>

### UpdateNVMFDistSubsystemRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| description | [bytes](#bytes) | optional | 256B |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| stripe_num | [uint32](#uint32) | optional |  |
| stripe_size | [uint32](#uint32) | optional |  |
| nqn_whitelist | [bytes](#bytes) | optional |  |
| ipv4_whitelist | [bytes](#bytes) | optional |  |






<a name="zbs-meta-UpdateNVMFSnapshotRequest"></a>

### UpdateNVMFSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| snapshot_path | [NVMFSnapshotPath](#zbs-meta-NVMFSnapshotPath) | required |  |
| new_name | [bytes](#bytes) | optional |  |
| new_description | [bytes](#bytes) | optional |  |
| new_alloc_even | [bool](#bool) | optional |  |






<a name="zbs-meta-UpdatePoolRequest"></a>

### UpdatePoolRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [PoolPath](#zbs-meta-PoolPath) | required |  |
| name | [bytes](#bytes) | optional |  |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| description | [bytes](#bytes) | optional | 256B |
| user | [bytes](#bytes) | optional |  |
| mod_verf | [uint32](#uint32) | optional |  |
| whitelist | [bytes](#bytes) | optional |  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| stripe_num | [uint32](#uint32) | optional |  |
| stripe_size | [uint32](#uint32) | optional |  |






<a name="zbs-meta-UpdateSnapshotRequest"></a>

### UpdateSnapshotRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [SnapshotPath](#zbs-meta-SnapshotPath) | required |  |
| new_name | [bytes](#bytes) | optional |  |
| new_description | [bytes](#bytes) | optional |  |
| new_alloc_even | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-UpdateStoragePoolRequest"></a>

### UpdateStoragePoolRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| new_name | [bytes](#bytes) | optional |  |






<a name="zbs-meta-UpdateTopoObjRequest"></a>

### UpdateTopoObjRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |
| new_parent_id | [bytes](#bytes) | optional |  |
| new_name | [bytes](#bytes) | optional | new name |
| new_desc | [bytes](#bytes) | optional |  |
| new_position | [zbs.Position](#zbs-Position) | optional |  |
| new_capacity | [zbs.Capacity](#zbs-Capacity) | optional |  |
| new_dimension | [zbs.Dimension](#zbs-Dimension) | optional |  |






<a name="zbs-meta-UpdateVolumeRequest"></a>

### UpdateVolumeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [VolumePath](#zbs-meta-VolumePath) | required |  |
| name | [bytes](#bytes) | optional | &lt; 255B |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| description | [bytes](#bytes) | optional | 256B |
| iops | [uint64](#uint64) | optional | **Deprecated.**  |
| iops_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| bps | [uint64](#uint64) | optional | **Deprecated.**  |
| bps_burst | [uint64](#uint64) | optional | **Deprecated.**  |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| alloc_even | [bool](#bool) | optional |  |
| skip_all_zero_first_write | [bool](#bool) | optional |  Default: false |
| prefer_cid | [uint32](#uint32) | optional |  Default: 0 |
| read_only | [bool](#bool) | optional |  |






<a name="zbs-meta-VersionInfo"></a>

### VersionInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| major_num | [uint64](#uint64) | optional |  Default: 1 |
| minor_num | [uint64](#uint64) | optional |  Default: 0 |
| meta_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-VhostConfigUpdate"></a>

### VhostConfigUpdate



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| record | [VhostIOPermissionRecord](#zbs-meta-VhostIOPermissionRecord) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| update | VhostConfigUpdate | ConfigUpdate | 10003 |  |




<a name="zbs-meta-VhostIOPermissionEntry"></a>

### VhostIOPermissionEntry



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vm_uuid | [string](#string) | required |  |
| machine_uuid | [string](#string) | required |  |






<a name="zbs-meta-VhostIOPermissionRecord"></a>

### VhostIOPermissionRecord



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| single_access | [bool](#bool) | optional |  Default: false |
| entries | [VhostIOPermissionEntry](#zbs-meta-VhostIOPermissionEntry) | repeated |  |






<a name="zbs-meta-VhostIOPermissionRequest"></a>

### VhostIOPermissionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |
| vm_uuid | [string](#string) | required |  |
| machine_uuid | [string](#string) | required |  |
| override | [bool](#bool) | optional |  Default: false |
| single_access | [bool](#bool) | optional |  Default: false |






<a name="zbs-meta-Volume"></a>

### Volume



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [bytes](#bytes) | optional | &lt; 255B |
| size | [uint64](#uint64) | optional | Bytes |
| id_v1 | [uint32](#uint32) | optional | **Deprecated.**  |
| parent_id_v1 | [uint32](#uint32) | optional | **Deprecated.**  |
| created_time | [zbs.TimeSpec](#zbs-TimeSpec) | optional |  |
| id | [bytes](#bytes) | optional |  |
| parent_id | [bytes](#bytes) | optional | parent id |
| origin_id | [bytes](#bytes) | optional | where this volume is from: either a snapshot or a source volume (when clone from a volume) |
| replica_num | [uint32](#uint32) | optional |  |
| thin_provision | [bool](#bool) | optional |  |
| read_only | [bool](#bool) | optional | read only volume could be opened by multiple readers and can only be open for read Default: false |
| status | [VolumeStatus](#zbs-meta-VolumeStatus) | optional |  Default: VOLUME_ONLINE |
| description | [bytes](#bytes) | optional | 256B |
| iops | [uint64](#uint64) | optional | **Deprecated.**  Default: 0 |
| iops_burst | [uint64](#uint64) | optional | **Deprecated.**  Default: 0 |
| bps | [uint64](#uint64) | optional | **Deprecated.**  Default: 0 |
| bps_burst | [uint64](#uint64) | optional | **Deprecated.**  Default: 0 |
| throttling | [IOThrottleConfig](#zbs-meta-IOThrottleConfig) | optional |  |
| stripe_num | [uint32](#uint32) | optional |  Default: 1 |
| stripe_size | [uint32](#uint32) | optional | 256K Default: 262144 |
| is_snapshot | [bool](#bool) | optional | used by snapshot or cloned volume Default: false |
| snapshot_pool_id | [bytes](#bytes) | optional | snapshot&#39;s pool id |
| diff_size | [uint64](#uint64) | optional | diff size when cloning or snapshotting. Note this value is only created when the volume is created. So it doesn&#39;t reflects the diff (unique) size of the system later. Default: 0 |
| nfs_meta | [NFSAttr](#zbs-meta-NFSAttr) | optional |  |
| unique_size | [int64](#int64) | optional | unique size means data size only used by this volume unique_size == -1 means it has never be scaned Default: -1 |
| shared_size | [int64](#int64) | optional | shared size means data size shared with other volume shared_size == -1 means it has never be scaned Default: -1 |
| access_points | [AccessPoint](#zbs-meta-AccessPoint) | repeated |  |
| alloc_even | [bool](#bool) | optional |  Default: false |
| clone_count | [uint64](#uint64) | optional | how many time has volume be cloned Default: 0 |
| skip_all_zero_first_write | [bool](#bool) | optional |  Default: false |
| secondary_id | [bytes](#bytes) | optional |  |
| prefer_cid | [uint32](#uint32) | optional |  Default: 0 |
| vextent_id | [uint32](#uint32) | repeated |  |
| file | [bool](#bool) | optional |  Default: false |
| alloced_virtual_bytes | [uint64](#uint64) | optional | allocated virtual size |
| alloced_physical_bytes | [uint64](#uint64) | optional | allocated physical size = virtual size x replica num - missing replicas size |






<a name="zbs-meta-VolumeId"></a>

### VolumeId



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |






<a name="zbs-meta-VolumeInfo"></a>

### VolumeInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| path | [VolumePath](#zbs-meta-VolumePath) | required |  |
| exist | [bool](#bool) | optional |  |
| volume | [Volume](#zbs-meta-Volume) | optional |  |
| pextents | [zbs.PExtentResp](#zbs-PExtentResp) | repeated |  |






<a name="zbs-meta-VolumePath"></a>

### VolumePath



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pool_path | [PoolPath](#zbs-meta-PoolPath) | optional |  |
| volume_name | [bytes](#bytes) | optional |  |
| volume_id | [bytes](#bytes) | optional |  |
| secondary_id | [bytes](#bytes) | optional |  |






<a name="zbs-meta-VolumeSizeResponse"></a>

### VolumeSizeResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| size | [uint64](#uint64) | required |  |






<a name="zbs-meta-VolumesResponse"></a>

### VolumesResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volumes | [Volume](#zbs-meta-Volume) | repeated |  |
| total_num | [uint32](#uint32) | optional |  |






<a name="zbs-meta-ZoneSummary"></a>

### ZoneSummary



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | optional |  |
| space_info | [zbs.StorageSpace](#zbs-StorageSpace) | optional |  |
| read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| total_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_read_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| cross_zone_write_perf | [zbs.StoragePerf](#zbs-StoragePerf) | optional | **Deprecated.**  |
| recover_info | [zbs.RecoverSummary](#zbs-RecoverSummary) | optional |  |
| name | [bytes](#bytes) | optional |  |
| chunk_perf | [zbs.ChunkPerf](#zbs-ChunkPerf) | optional |  |





 


<a name="zbs-meta-ClusterStatus-ClusterStatusFlags"></a>

### ClusterStatus.ClusterStatusFlags


| Name | Number | Description |
| ---- | ------ | ----------- |
| CLUSTER_HEALTHY | 1 |  |
| CLUSTER_LICENSE_EXPIRED | 2 |  |
| CLUSTER_SPACE_FULL | 4 |  |
| CLUSTER_NO_ALIVE_CHUNK | 8 |  |



<a name="zbs-meta-ConfigUpdateType"></a>

### ConfigUpdateType


| Name | Number | Description |
| ---- | ------ | ----------- |
| UNKNOWN_TYPE | 0 |  |
| ISCSI | 1 |  |
| NVMF | 2 |  |
| VHOST | 3 |  |



<a name="zbs-meta-ConsistencyVolumeType"></a>

### ConsistencyVolumeType


| Name | Number | Description |
| ---- | ------ | ----------- |
| CONSISTENCY_NONE | 0 |  |
| CONSISTENCY_VOLUME | 1 |  |
| CONSISTENCY_LUN | 2 |  |
| CONSISTENCY_FILE | 3 |  |
| CONSISTENCY_NAMESPACE | 4 |  |



<a name="zbs-meta-CreateHow"></a>

### CreateHow


| Name | Number | Description |
| ---- | ------ | ----------- |
| UNCHECKED | 0 |  |
| GUARDED | 1 |  |
| EXCLUSIVE | 2 |  |



<a name="zbs-meta-FindVolumeType"></a>

### FindVolumeType


| Name | Number | Description |
| ---- | ------ | ----------- |
| VOLUME_EVEN | 0 |  |



<a name="zbs-meta-ISCSIConfigOption"></a>

### ISCSIConfigOption


| Name | Number | Description |
| ---- | ------ | ----------- |
| UNKNOWN | 0 |  |
| TARGET_ADD | 1 |  |
| TARGET_REMOVE | 2 |  |
| TARGET_WHITELIST | 3 |  |
| TARGET_IQN_WHITELIST_V2 | 4 |  |
| TARGET_CHAP | 5 |  |
| TARGET_INITIATOR_CHAP | 6 |  |
| TARGET_NAME | 7 |  |
| LUN_ADD | 21 |  |
| LUN_REMOVE | 22 |  |
| LUN_SIZE | 23 |  |
| LUN_ALLOWED_INITIATORS | 24 |  |
| LUN_PR_INFO | 25 |  |
| LUN_SNAPSHOT_ROLLBACK | 26 |  |



<a name="zbs-meta-ISCSIType"></a>

### ISCSIType
notice: the value of the type cannot be changed.

| Name | Number | Description |
| ---- | ------ | ----------- |
| SCSI_TYPE_DISK | 0 |  |
| SCSI_TYPE_TAPE | 1 |  |
| SCSI_TYPE_PRINTER | 2 |  |
| SCSI_TYPE_PROCESSOR | 3 |  |
| SCSI_TYPE_WORM | 4 |  |
| SCSI_TYPE_MMC | 5 |  |
| SCSI_TYPE_SCANNER | 6 |  |
| SCSI_TYPE_MOD | 7 |  |
| SCSI_TYPE_MEDIUM_CHANGER | 8 |  |
| SCSI_TYPE_COMM | 9 |  |
| SCSI_TYPE_RAID | 12 |  |
| SCSI_TYPE_ENCLOSURE | 13 |  |
| SCSI_TYPE_RBC | 14 |  |
| SCSI_TYPE_OSD | 15 |  |
| SCSI_TYPE_NO_LUN | 127 |  |
| SCSI_TYPE_PT | 255 | 0xff |



<a name="zbs-meta-MetaStatus"></a>

### MetaStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| META_BOOTING | 1 |  |
| META_RUNNING | 2 |  |
| META_STOPPED | 3 |  |
| META_CONNECTING_CHUNK | 10 |  |
| META_SCANNING_VOLUME | 11 | deprecated |



<a name="zbs-meta-NFSType"></a>

### NFSType
notice: the value of the type cannot be changed.

| Name | Number | Description |
| ---- | ------ | ----------- |
| NONE | 0 |  |
| FILE | 1 |  |
| DIR | 2 |  |
| BLK | 3 | not used |
| CHR | 4 | not used |
| LNK | 5 |  |
| SOCK | 6 | not used |
| FIFO | 7 | not used |



<a name="zbs-meta-NVMFAccessPolicy"></a>

### NVMFAccessPolicy


| Name | Number | Description |
| ---- | ------ | ----------- |
| INHERIT_POLICY | 1 |  |
| BALANCE_POLICY | 2 |  |



<a name="zbs-meta-NVMFConfigOption"></a>

### NVMFConfigOption


| Name | Number | Description |
| ---- | ------ | ----------- |
| NVMF_UNKNOWN | 0 |  |
| DIST_SUBSYSTEM_ADD | 1 |  |
| DIST_SUBSYSTEM_REMOVE | 2 |  |
| DIST_SUBSYSTEM_NQN_WHITELIST | 3 |  |
| DIST_SUBSYSTEM_IPV4_WHITELIST | 4 |  |
| DIST_NAMESPACE_ADD | 21 |  |
| DIST_NAMESPACE_REMOVE | 22 |  |
| DIST_NAMESPACE_UPDATE_CURRENT_LOC | 23 | deprecated |
| DIST_NAMESPACE_UPDATE_ANA_STATE | 24 | deprecated |
| DIST_NAMESPACE_UPDATE_SIZE | 25 |  |
| DIST_NAMESPACE_NQN_WHITELIST | 26 |  |



<a name="zbs-meta-NVMFTransportType"></a>

### NVMFTransportType


| Name | Number | Description |
| ---- | ------ | ----------- |
| NVMF_TR_TYPE_RDMA | 0 |  |
| NVMF_TR_TYPE_TCP | 1 |  |



<a name="zbs-meta-PExtentStatus"></a>

### PExtentStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| PEXTENT_HEALTHY | 0 |  |
| PEXTENT_DEAD | 1 |  |
| PEXTENT_BROKEN | 2 |  |
| PEXTENT_NEED_RECOVER | 3 |  |
| PEXTENT_GARBAGE | 4 |  |
| PEXTENT_MAY_RECOVER | 5 |  |



<a name="zbs-meta-VolumeStatus"></a>

### VolumeStatus


| Name | Number | Description |
| ---- | ------ | ----------- |
| VOLUME_ONLINE | 0 | the volume is online/ok. |
| VOLUME_OFFLINE | 1 | the volume is not available for read or write |
| VOLUME_BROKEN | 2 | the volume is broken |



<a name="zbs-meta-time_how"></a>

### time_how


| Name | Number | Description |
| ---- | ------ | ----------- |
| DONT_CHANGE | 0 |  |
| SET_TO_SERVER_TIME | 1 |  |
| SET_TO_CLIENT_TIME | 2 |  |


 

 


<a name="zbs-meta-CDPService"></a>

### CDPService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CreateJob | [.zbs.CreateCDPJobRequest](#zbs-CreateCDPJobRequest) | [.zbs.CDPJobInfo](#zbs-CDPJobInfo) | CDP job management |
| GetJob | [.zbs.GetCDPJobRequest](#zbs-GetCDPJobRequest) | [.zbs.CDPJobInfo](#zbs-CDPJobInfo) |  |
| GetJobByVolume | [.zbs.GetCDPJobByVolumeRequest](#zbs-GetCDPJobByVolumeRequest) | [.zbs.CDPJobInfo](#zbs-CDPJobInfo) |  |
| FinishJob | [.zbs.FinishCDPJobRequest](#zbs-FinishCDPJobRequest) | [.zbs.Void](#zbs-Void) |  |
| CancelJob | [.zbs.CancelCDPJobRequest](#zbs-CancelCDPJobRequest) | [.zbs.Void](#zbs-Void) |  |
| ListJobs | [.zbs.ListCDPJobsRequest](#zbs-ListCDPJobsRequest) | [.zbs.ListCDPJobsResponse](#zbs-ListCDPJobsResponse) |  |
| CreateJobsByGroup | [.zbs.CreateCDPJobsByGroupRequest](#zbs-CreateCDPJobsByGroupRequest) | [.zbs.CreateCDPJobsByGroupResponse](#zbs-CreateCDPJobsByGroupResponse) |  |
| FinishJobsByGroup | [.zbs.FinishCDPJobsByGroupRequest](#zbs-FinishCDPJobsByGroupRequest) | [.zbs.Void](#zbs-Void) |  |
| CancelJobsByGroup | [.zbs.CancelCDPJobsByGroupRequest](#zbs-CancelCDPJobsByGroupRequest) | [.zbs.Void](#zbs-Void) |  |


<a name="zbs-meta-ChunkService"></a>

### ChunkService
chunk service

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CreateStoragePool | [CreateStoragePoolRequest](#zbs-meta-CreateStoragePoolRequest) | [.zbs.StoragePool](#zbs-StoragePool) | storage pool // |
| DeleteStoragePool | [.zbs.StoragePoolId](#zbs-StoragePoolId) | [.zbs.StoragePool](#zbs-StoragePool) |  |
| UpdateStoragePool | [UpdateStoragePoolRequest](#zbs-meta-UpdateStoragePoolRequest) | [.zbs.StoragePool](#zbs-StoragePool) |  |
| ListStoragePool | [.zbs.Void](#zbs-Void) | [.zbs.StoragePools](#zbs-StoragePools) |  |
| ShowStoragePool | [.zbs.StoragePoolId](#zbs-StoragePoolId) | [.zbs.StoragePool](#zbs-StoragePool) |  |
| AddChunkToStoragePool | [AddChunkToStoragePoolRequest](#zbs-meta-AddChunkToStoragePoolRequest) | [.zbs.StoragePool](#zbs-StoragePool) |  |
| RemoveChunkFromStoragePool | [RemoveChunkFromStoragePoolRequest](#zbs-meta-RemoveChunkFromStoragePoolRequest) | [.zbs.StoragePool](#zbs-StoragePool) |  |
| CreateTopoObj | [CreateTopoObjRequest](#zbs-meta-CreateTopoObjRequest) | [.zbs.TopoObj](#zbs-TopoObj) | topo // |
| DeleteTopoObj | [.zbs.TopoObjId](#zbs-TopoObjId) | [.zbs.TopoObj](#zbs-TopoObj) |  |
| UpdateTopoObj | [UpdateTopoObjRequest](#zbs-meta-UpdateTopoObjRequest) | [.zbs.TopoObj](#zbs-TopoObj) |  |
| ListTopoObj | [.zbs.TopoObjId](#zbs-TopoObjId) | [.zbs.TopoObjs](#zbs-TopoObjs) |  |
| ShowTopoObj | [.zbs.TopoObjId](#zbs-TopoObjId) | [.zbs.TopoObj](#zbs-TopoObj) |  |
| RegisterChunk | [.zbs.Chunk](#zbs-Chunk) | [.zbs.Chunk](#zbs-Chunk) | chunk // |
| ListChunk | [.zbs.Void](#zbs-Void) | [.zbs.Chunks](#zbs-Chunks) |  |
| ShowChunk | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.Chunk](#zbs-Chunk) |  |
| RemoveChunk | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.Void](#zbs-Void) |  |
| GetChunkTopology | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.ChunkTopology](#zbs-ChunkTopology) |  |
| LeaveService | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.Void](#zbs-Void) | chunk notify the meta it is leaving (out of service)

deprecated |
| ListPid | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.ChunkPids](#zbs-ChunkPids) | show chunk pids and space info |
| SetMaintenanceMode | [SetMaintenanceModeRequest](#zbs-meta-SetMaintenanceModeRequest) | [.zbs.Chunk](#zbs-Chunk) | Maintenance |
| CancelChunkRemoving | [CancelChunkRemovingRequest](#zbs-meta-CancelChunkRemovingRequest) | [.zbs.Void](#zbs-Void) | cancel chunk removing // |
| GetMaintenanceInfo | [.zbs.Void](#zbs-Void) | [GetMaintenanceInfoResponse](#zbs-meta-GetMaintenanceInfoResponse) |  |
| BanChunk | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.Void](#zbs-Void) | isolate // |
| UnbanChunk | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.Void](#zbs-Void) |  |
| SetChunkIsolatePolicy | [SetChunkIsolatePolicyRequest](#zbs-meta-SetChunkIsolatePolicyRequest) | [.zbs.Void](#zbs-Void) |  |
| GetChunkIsolatePolicy | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.ChunkIsolateFlag](#zbs-ChunkIsolateFlag) |  |
| GetChunkIsolateInfo | [.zbs.Void](#zbs-Void) | [GetChunkIsolateInfoResponse](#zbs-meta-GetChunkIsolateInfoResponse) |  |


<a name="zbs-meta-ISCSIService"></a>

### ISCSIService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CreateTarget | [CreateISCSITargetRequest](#zbs-meta-CreateISCSITargetRequest) | [ISCSITarget](#zbs-meta-ISCSITarget) | target management |
| UpdateTarget | [UpdateISCSITargetRequest](#zbs-meta-UpdateISCSITargetRequest) | [ISCSITarget](#zbs-meta-ISCSITarget) |  |
| DeleteTarget | [PoolPath](#zbs-meta-PoolPath) | [.zbs.Void](#zbs-Void) |  |
| ListTarget | [.zbs.Void](#zbs-Void) | [ISCSITargetsResponse](#zbs-meta-ISCSITargetsResponse) |  |
| GetTarget | [PoolPath](#zbs-meta-PoolPath) | [ISCSITarget](#zbs-meta-ISCSITarget) |  |
| CreateLun | [CreateISCSILunRequest](#zbs-meta-CreateISCSILunRequest) | [ISCSILun](#zbs-meta-ISCSILun) | lun management |
| UpdateLun | [UpdateISCSILunRequest](#zbs-meta-UpdateISCSILunRequest) | [ISCSILun](#zbs-meta-ISCSILun) |  |
| DeleteLun | [LunPath](#zbs-meta-LunPath) | [.zbs.Void](#zbs-Void) |  |
| ListLun | [PoolPath](#zbs-meta-PoolPath) | [ISCSILunsResponse](#zbs-meta-ISCSILunsResponse) |  |
| GetLun | [LunPath](#zbs-meta-LunPath) | [ISCSILun](#zbs-meta-ISCSILun) |  |
| MoveLun | [MoveISCSILunRequest](#zbs-meta-MoveISCSILunRequest) | [ISCSILun](#zbs-meta-ISCSILun) |  |
| CreateSnapshot | [CreateISCSISnapshotRequest](#zbs-meta-CreateISCSISnapshotRequest) | [Volume](#zbs-meta-Volume) | snapshot management |
| ListSnapshot | [LunPath](#zbs-meta-LunPath) | [SnapshotsResponse](#zbs-meta-SnapshotsResponse) |  |
| UpdateSnapshot | [UpdateISCSISnapshotRequest](#zbs-meta-UpdateISCSISnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| DeleteSnapshot | [ISCSISnapshotPath](#zbs-meta-ISCSISnapshotPath) | [.zbs.Void](#zbs-Void) |  |
| RollbackSnapshot | [ISCSISnapshotPath](#zbs-meta-ISCSISnapshotPath) | [.zbs.Void](#zbs-Void) |  |
| ShowSnapshot | [ISCSISnapshotPath](#zbs-meta-ISCSISnapshotPath) | [Volume](#zbs-meta-Volume) |  |
| MoveSnapshot | [MoveISCSISnapshotRequest](#zbs-meta-MoveISCSISnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| Spc2Reserve | [ISCSISpc2ReserveRequest](#zbs-meta-ISCSISpc2ReserveRequest) | [.zbs.Void](#zbs-Void) | SPC2 reserve/release |
| Spc2Release | [ISCSISpc2ReleaseRequest](#zbs-meta-ISCSISpc2ReleaseRequest) | [.zbs.Void](#zbs-Void) |  |
| ConvertVolumeIntoLun | [ConvertVolumeIntoLunRequest](#zbs-meta-ConvertVolumeIntoLunRequest) | [ISCSILun](#zbs-meta-ISCSILun) | storage object convert |
| GetTargetAndLunInfo | [LunPath](#zbs-meta-LunPath) | [TargetAndLunInfo](#zbs-meta-TargetAndLunInfo) | get target/lun info by lun uuid |
| ListTargetPortals | [ListTargetPortalsRequest](#zbs-meta-ListTargetPortalsRequest) | [TargetPortals](#zbs-meta-TargetPortals) | List target portals with its iqn name

deprecated |
| ListTargets | [ListTargetsRequest](#zbs-meta-ListTargetsRequest) | [ISCSITargetsResponse](#zbs-meta-ISCSITargetsResponse) |  |
| SyncTargets | [SyncTargetsRequest](#zbs-meta-SyncTargetsRequest) | [ISCSITargetsResponse](#zbs-meta-ISCSITargetsResponse) |  |
| AddLunAllowedInitiators | [AddLunAllowedInitiatorsRequest](#zbs-meta-AddLunAllowedInitiatorsRequest) | [ISCSILun](#zbs-meta-ISCSILun) | for LUN masking |
| RemoveLunAllowedInitiators | [RemoveLunAllowedInitiatorsRequest](#zbs-meta-RemoveLunAllowedInitiatorsRequest) | [ISCSILun](#zbs-meta-ISCSILun) |  |
| SyncPRInfoXchg | [PRRequest](#zbs-meta-PRRequest) | [PRResponse](#zbs-meta-PRResponse) |  |
| ResetLunPr | [ResetLunPrRequest](#zbs-meta-ResetLunPrRequest) | [.zbs.Void](#zbs-Void) |  |
| GetServicePortal | [GetServicePortalRequest](#zbs-meta-GetServicePortalRequest) | [ISCSIServicePortal](#zbs-meta-ISCSIServicePortal) | for iscsi access balance |
| GetAccessRecord | [GetAccessRecordRequest](#zbs-meta-GetAccessRecordRequest) | [GetAccessRecordResponse](#zbs-meta-GetAccessRecordResponse) |  |
| SetAccessRecord | [SetAccessRecordRequest](#zbs-meta-SetAccessRecordRequest) | [SetAccessRecordResponse](#zbs-meta-SetAccessRecordResponse) |  |


<a name="zbs-meta-MetaService"></a>

### MetaService
metadata service

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CreatePool | [Pool](#zbs-meta-Pool) | [Pool](#zbs-meta-Pool) | create a new pool |
| ListPool | [ListPoolRequest](#zbs-meta-ListPoolRequest) | [PoolsResponse](#zbs-meta-PoolsResponse) | list pools |
| DeletePool | [PoolPath](#zbs-meta-PoolPath) | [.zbs.Void](#zbs-Void) | delete pool |
| ShowPool | [PoolPath](#zbs-meta-PoolPath) | [Pool](#zbs-meta-Pool) | show the pool spec |
| UpdatePool | [UpdatePoolRequest](#zbs-meta-UpdatePoolRequest) | [Pool](#zbs-meta-Pool) | update the pool spec |
| CreateVolume | [CreateVolumeRequest](#zbs-meta-CreateVolumeRequest) | [Volume](#zbs-meta-Volume) | create a new volume |
| ListVolume | [ListVolumeRequest](#zbs-meta-ListVolumeRequest) | [VolumesResponse](#zbs-meta-VolumesResponse) | list volumes |
| ShowVolume | [ShowVolumeRequest](#zbs-meta-ShowVolumeRequest) | [ShowVolumeResponse](#zbs-meta-ShowVolumeResponse) | show the spec of volume |
| DeleteVolume | [VolumePath](#zbs-meta-VolumePath) | [.zbs.Void](#zbs-Void) | delete a volume |
| UpdateVolume | [UpdateVolumeRequest](#zbs-meta-UpdateVolumeRequest) | [Volume](#zbs-meta-Volume) | update a volume |
| ResizeVolume | [ResizeVolumeRequest](#zbs-meta-ResizeVolumeRequest) | [Volume](#zbs-meta-Volume) |  |
| GetVolumeSize | [VolumeId](#zbs-meta-VolumeId) | [VolumeSizeResponse](#zbs-meta-VolumeSizeResponse) | get the volume size: a quicker way to get only the volume size property |
| GetVTable | [VolumePath](#zbs-meta-VolumePath) | [GetVTableResponse](#zbs-meta-GetVTableResponse) |  |
| MoveVolume | [MoveVolumeRequest](#zbs-meta-MoveVolumeRequest) | [Volume](#zbs-meta-Volume) | move a volume |
| CreateSnapshot | [CreateSnapshotRequest](#zbs-meta-CreateSnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| ShowSnapshot | [SnapshotPath](#zbs-meta-SnapshotPath) | [Volume](#zbs-meta-Volume) |  |
| UpdateSnapshot | [UpdateSnapshotRequest](#zbs-meta-UpdateSnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| DeleteSnapshot | [SnapshotPath](#zbs-meta-SnapshotPath) | [.zbs.Void](#zbs-Void) | delete a snapshot |
| MoveSnapshot | [MoveSnapshotRequest](#zbs-meta-MoveSnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| ListSnapshot | [ListSnapshotRequest](#zbs-meta-ListSnapshotRequest) | [SnapshotsResponse](#zbs-meta-SnapshotsResponse) | list all snapshots |
| CloneVolume | [CloneVolumeRequest](#zbs-meta-CloneVolumeRequest) | [Volume](#zbs-meta-Volume) | clone a volume from a snapshot |
| RollbackVolume | [SnapshotPath](#zbs-meta-SnapshotPath) | [.zbs.Void](#zbs-Void) | rollback a volume to a snapshot |
| GetPExtent | [GetPExtentRequest](#zbs-meta-GetPExtentRequest) | [.zbs.PExtent](#zbs-PExtent) | get extent location |
| CowPExtent | [CowPExtentRequest](#zbs-meta-CowPExtentRequest) | [.zbs.VExtentLease](#zbs-VExtentLease) | cow pextent, deprecated |
| GetAllocPExtent | [GetAllocPExtentRequest](#zbs-meta-GetAllocPExtentRequest) | [.zbs.PExtent](#zbs-PExtent) | Get pextent locations, if not exist, alloc replicas. |
| RemoveReplica | [RemoveReplicaRequest](#zbs-meta-RemoveReplicaRequest) | [.zbs.PExtent](#zbs-PExtent) | remove a replica of a pextent: the function only return error when 1. the client is not owner, 2. the updated pextent is not persisted into the db. The client should check the PExtent&#39;s location to update its cache. |
| AddReplica | [AddReplicaRequest](#zbs-meta-AddReplicaRequest) | [.zbs.PExtent](#zbs-PExtent) | add a replica to a pextent |
| ReplaceReplica | [ReplaceReplicaRequest](#zbs-meta-ReplaceReplicaRequest) | [.zbs.PExtent](#zbs-PExtent) | replace replica |
| ListPExtent | [ListPExtentRequest](#zbs-meta-ListPExtentRequest) | [PExtentsResponse](#zbs-meta-PExtentsResponse) |  |
| GetPExtentRef | [GetPExtentRequest](#zbs-meta-GetPExtentRequest) | [GetPExtentRefResponse](#zbs-meta-GetPExtentRefResponse) |  |
| FindPExtent | [FindPExtentRequest](#zbs-meta-FindPExtentRequest) | [PExtentsResponse](#zbs-meta-PExtentsResponse) |  |
| ListDb | [.zbs.Void](#zbs-Void) | [ListDbResponse](#zbs-meta-ListDbResponse) |  |
| DumpDb | [DumpDbRequest](#zbs-meta-DumpDbRequest) | [DumpDbResponse](#zbs-meta-DumpDbResponse) |  |
| MigratePExtent | [.zbs.RecoverCmd](#zbs-RecoverCmd) | [.zbs.Void](#zbs-Void) | user could trigger a migrate manually

deprecated |
| ListMigrate | [.zbs.Void](#zbs-Void) | [RecoverCmds](#zbs-meta-RecoverCmds) |  |
| SwitchMigrate | [SwitchRequest](#zbs-meta-SwitchRequest) | [.zbs.Void](#zbs-Void) |  |
| RecoverPExtent | [.zbs.RecoverCmd](#zbs-RecoverCmd) | [.zbs.Void](#zbs-Void) | deprecated |
| ListRecover | [.zbs.Void](#zbs-Void) | [RecoverCmds](#zbs-meta-RecoverCmds) |  |
| SwitchRecover | [SwitchRequest](#zbs-meta-SwitchRequest) | [.zbs.Void](#zbs-Void) |  |
| StopServer | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| UpdateLicense | [.zbs.LicenseRequest](#zbs-LicenseRequest) | [.zbs.Void](#zbs-Void) |  |
| ShowLicense | [.zbs.Void](#zbs-Void) | [.zbs.License](#zbs-License) |  |
| ParseLicense | [.zbs.LicenseRequest](#zbs-LicenseRequest) | [.zbs.License](#zbs-License) |  |
| GetVExtentLease | [GetVExtentLeaseRequest](#zbs-meta-GetVExtentLeaseRequest) | [.zbs.VExtentLease](#zbs-VExtentLease) |  |
| GetLease | [GetLeaseRequest](#zbs-meta-GetLeaseRequest) | [.zbs.Lease](#zbs-Lease) |  |
| UpdateEpoch | [UpdateEpochRequest](#zbs-meta-UpdateEpochRequest) | [UpdateEpochResponse](#zbs-meta-UpdateEpochResponse) | pextent epoch

deprecated |
| SetPExtentExistence | [.zbs.PExtent](#zbs-PExtent) | [.zbs.Void](#zbs-Void) |  |
| MoveDataAndDeleteVolume | [MoveDataAndDeleteVolumeRequest](#zbs-meta-MoveDataAndDeleteVolumeRequest) | [Volume](#zbs-meta-Volume) |  |
| ListAccessSession | [.zbs.Void](#zbs-Void) | [.zbs.SessionInfos](#zbs-SessionInfos) |  |
| ReleaseLease | [ReleaseLeaseRequest](#zbs-meta-ReleaseLeaseRequest) | [.zbs.Void](#zbs-Void) |  |
| ShowVolumes | [ShowVolumesRequest](#zbs-meta-ShowVolumesRequest) | [ShowVolumesResponse](#zbs-meta-ShowVolumesResponse) |  |
| UpdateClusterInfo | [UpdateClusterInfoRequest](#zbs-meta-UpdateClusterInfoRequest) | [.zbs.Void](#zbs-Void) |  |
| ShowClusterInfo | [.zbs.Void](#zbs-Void) | [ClusterInfo](#zbs-meta-ClusterInfo) |  |
| UpdateRerouteStatus | [RerouteStatus](#zbs-meta-RerouteStatus) | [.zbs.Void](#zbs-Void) |  |
| ShowRerouteStatus | [.zbs.Void](#zbs-Void) | [RerouteStatus](#zbs-meta-RerouteStatus) |  |
| ReportVolumeAccess | [ReportVolumeAccessRequest](#zbs-meta-ReportVolumeAccessRequest) | [.zbs.Void](#zbs-Void) |  |
| ReserveVolumeSpace | [ReserveVolumeSpaceRequest](#zbs-meta-ReserveVolumeSpaceRequest) | [Volume](#zbs-meta-Volume) |  |
| RefreshChildExtentLocation | [RefreshChildExtentLocationRequest](#zbs-meta-RefreshChildExtentLocationRequest) | [.zbs.VExtentLease](#zbs-VExtentLease) |  |
| SetUpgradeMode | [SetUpgradeModeRequest](#zbs-meta-SetUpgradeModeRequest) | [.zbs.Void](#zbs-Void) |  |
| GetRecoverModeInfo | [.zbs.Void](#zbs-Void) | [RecoverModeInfo](#zbs-meta-RecoverModeInfo) |  |
| SetRecoverModeInfo | [RecoverModeInfo](#zbs-meta-RecoverModeInfo) | [.zbs.Void](#zbs-Void) |  |
| EnableScanRecoverImmediate | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| EnableScanMigrateImmediate | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| FindVolume | [FindVolumeRequest](#zbs-meta-FindVolumeRequest) | [VolumesResponse](#zbs-meta-VolumesResponse) | Find volumes that fits some attributes |
| ListISCSIConnection | [ListISCSIConnectionRequest](#zbs-meta-ListISCSIConnectionRequest) | [ListISCSIConnectionResponse](#zbs-meta-ListISCSIConnectionResponse) |  |
| CheckLease | [CheckLeaseRequest](#zbs-meta-CheckLeaseRequest) | [CheckLeaseResponse](#zbs-meta-CheckLeaseResponse) |  |
| UpdateRebalanceAccessPointStatus | [RebalanceAccessPointStatus](#zbs-meta-RebalanceAccessPointStatus) | [.zbs.Void](#zbs-Void) |  |
| ShowRebalanceAccessPointStatus | [.zbs.Void](#zbs-Void) | [RebalanceAccessPointStatus](#zbs-meta-RebalanceAccessPointStatus) |  |
| CreateConsistencyGroup | [CreateConsistencyGroupRequest](#zbs-meta-CreateConsistencyGroupRequest) | [ConsistencyGroup](#zbs-meta-ConsistencyGroup) |  |
| ListConsistencyGroup | [ListConsistencyGroupRequest](#zbs-meta-ListConsistencyGroupRequest) | [ListConsistencyGroupResponse](#zbs-meta-ListConsistencyGroupResponse) |  |
| ShowConsistencyGroup | [ShowConsistencyGroupRequest](#zbs-meta-ShowConsistencyGroupRequest) | [ConsistencyGroup](#zbs-meta-ConsistencyGroup) |  |
| DeleteConsistencyGroup | [DeleteConsistencyGroupRequest](#zbs-meta-DeleteConsistencyGroupRequest) | [.zbs.Void](#zbs-Void) |  |
| UpdateConsistencyGroup | [UpdateConsistencyGroupRequest](#zbs-meta-UpdateConsistencyGroupRequest) | [ConsistencyGroup](#zbs-meta-ConsistencyGroup) |  |
| CreateConsistencyGroupSnapshot | [CreateConsistencyGroupSnapshotRequest](#zbs-meta-CreateConsistencyGroupSnapshotRequest) | [ConsistencyGroupSnapshot](#zbs-meta-ConsistencyGroupSnapshot) |  |
| ListConsistencyGroupSnapshot | [ListConsistencyGroupSnapshotRequest](#zbs-meta-ListConsistencyGroupSnapshotRequest) | [ListConsistencyGroupSnapshotResponse](#zbs-meta-ListConsistencyGroupSnapshotResponse) |  |
| ShowConsistencyGroupSnapshot | [ShowConsistencyGroupSnapshotRequest](#zbs-meta-ShowConsistencyGroupSnapshotRequest) | [ConsistencyGroupSnapshot](#zbs-meta-ConsistencyGroupSnapshot) |  |
| UpdateConsistencyGroupSnapshot | [UpdateConsistencyGroupSnapshotRequest](#zbs-meta-UpdateConsistencyGroupSnapshotRequest) | [ConsistencyGroupSnapshot](#zbs-meta-ConsistencyGroupSnapshot) |  |
| RollbackConsistencyGroupSnapshot | [RollbackConsistencyGroupSnapshotRequest](#zbs-meta-RollbackConsistencyGroupSnapshotRequest) | [.zbs.Void](#zbs-Void) |  |
| DeleteConsistencyGroupSnapshot | [DeleteConsistencyGroupSnapshotRequest](#zbs-meta-DeleteConsistencyGroupSnapshotRequest) | [.zbs.Void](#zbs-Void) |  |
| ListNVMFConnection | [ListNVMFConnectionRequest](#zbs-meta-ListNVMFConnectionRequest) | [ListNVMFConnectionResponse](#zbs-meta-ListNVMFConnectionResponse) |  |
| ShowRecoverParameter | [.zbs.Void](#zbs-Void) | [RecoverParameter](#zbs-meta-RecoverParameter) |  |
| AuthorizeVhostIOPermission | [VhostIOPermissionRequest](#zbs-meta-VhostIOPermissionRequest) | [.zbs.Void](#zbs-Void) | Vhost IO permission |
| RevokeVhostIOPermission | [VhostIOPermissionRequest](#zbs-meta-VhostIOPermissionRequest) | [.zbs.Void](#zbs-Void) |  |
| GetVhostIOPermission | [VhostIOPermissionRequest](#zbs-meta-VhostIOPermissionRequest) | [GetVhostIOPermissionResponse](#zbs-meta-GetVhostIOPermissionResponse) |  |
| ClearVhostIOPermission | [ClearVhostIOPermissionRequest](#zbs-meta-ClearVhostIOPermissionRequest) | [.zbs.Void](#zbs-Void) |  |
| SetVhostIOPermissionSingleAccess | [VhostIOPermissionRequest](#zbs-meta-VhostIOPermissionRequest) | [.zbs.Void](#zbs-Void) |  |
| ReleaseBadLease | [ReleaseBadLeaseRequest](#zbs-meta-ReleaseBadLeaseRequest) | [.zbs.Void](#zbs-Void) |  |
| SpecialRecover | [SpecialRecoverRequest](#zbs-meta-SpecialRecoverRequest) | [.zbs.Void](#zbs-Void) |  |
| ListTemporaryReplica | [ListTemporaryReplicaRequest](#zbs-meta-ListTemporaryReplicaRequest) | [ListTemporaryReplicaResponse](#zbs-meta-ListTemporaryReplicaResponse) |  |
| GetTemporaryReplica | [GetTemporaryReplicaRequest](#zbs-meta-GetTemporaryReplicaRequest) | [GetTemporaryReplicaResponse](#zbs-meta-GetTemporaryReplicaResponse) |  |
| GetTemporaryReplicaSummary | [.zbs.Void](#zbs-Void) | [GetTemporaryReplicaSummaryResponse](#zbs-meta-GetTemporaryReplicaSummaryResponse) |  |


<a name="zbs-meta-NFSService"></a>

### NFSService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CreateExport | [Pool](#zbs-meta-Pool) | [Pool](#zbs-meta-Pool) |  |
| UpdateExport | [UpdatePoolRequest](#zbs-meta-UpdatePoolRequest) | [Pool](#zbs-meta-Pool) |  |
| DeleteExport | [PoolPath](#zbs-meta-PoolPath) | [.zbs.Void](#zbs-Void) |  |
| ListExport | [.zbs.Void](#zbs-Void) | [PoolsResponse](#zbs-meta-PoolsResponse) |  |
| CreateInode | [CreateInodeRequest](#zbs-meta-CreateInodeRequest) | [NFSInode](#zbs-meta-NFSInode) |  |
| DeleteInode | [DeleteInodeRequest](#zbs-meta-DeleteInodeRequest) | [DeleteInodeResponse](#zbs-meta-DeleteInodeResponse) |  |
| RenameInode | [RenameInodeRequest](#zbs-meta-RenameInodeRequest) | [RenameInodeResponse](#zbs-meta-RenameInodeResponse) |  |
| SetAttr | [SetAttrRequest](#zbs-meta-SetAttrRequest) | [NFSInode](#zbs-meta-NFSInode) | nfs3 set attr request |
| GetAttr | [InodeId](#zbs-meta-InodeId) | [NFSAttr](#zbs-meta-NFSAttr) | nfs3 get attr request: getattr is a frequently accessed interface |
| ShowInode | [InodeId](#zbs-meta-InodeId) | [NFSInode](#zbs-meta-NFSInode) |  |
| LookupInode | [LookupInodeRequest](#zbs-meta-LookupInodeRequest) | [NFSInode](#zbs-meta-NFSInode) |  |
| FindInode | [FindInodeRequest](#zbs-meta-FindInodeRequest) | [NFSInode](#zbs-meta-NFSInode) |  |
| ListInode | [ListInodeRequest](#zbs-meta-ListInodeRequest) | [NFSInodes](#zbs-meta-NFSInodes) |  |
| TruncateFile | [TruncateRequest](#zbs-meta-TruncateRequest) | [NFSInode](#zbs-meta-NFSInode) |  |
| GetSizeInfo | [InodeId](#zbs-meta-InodeId) | [SizeInfo](#zbs-meta-SizeInfo) |  |
| Mount | [MountPoint](#zbs-meta-MountPoint) | [.zbs.Void](#zbs-Void) | mount table |
| Umount | [MountPoint](#zbs-meta-MountPoint) | [.zbs.Void](#zbs-Void) |  |
| UmountAll | [.zbs.Void](#zbs-Void) | [.zbs.Void](#zbs-Void) |  |
| ShowMount | [.zbs.Void](#zbs-Void) | [MountTable](#zbs-meta-MountTable) |  |
| CreateSnapshot | [CreateNFSSnapshotRequest](#zbs-meta-CreateNFSSnapshotRequest) | [Volume](#zbs-meta-Volume) | snapshot management |
| ListSnapshot | [NFSSnapshotPath](#zbs-meta-NFSSnapshotPath) | [SnapshotsResponse](#zbs-meta-SnapshotsResponse) |  |
| UpdateSnapshot | [UpdateNFSSnapshotRequest](#zbs-meta-UpdateNFSSnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| DeleteSnapshot | [NFSSnapshotPath](#zbs-meta-NFSSnapshotPath) | [.zbs.Void](#zbs-Void) |  |
| RollbackSnapshot | [RollbackNFSSnapshotRequest](#zbs-meta-RollbackNFSSnapshotRequest) | [.zbs.Void](#zbs-Void) |  |
| ShowSnapshot | [NFSSnapshotPath](#zbs-meta-NFSSnapshotPath) | [Volume](#zbs-meta-Volume) |  |
| MoveSnapshot | [MoveNFSSnapshotRequest](#zbs-meta-MoveNFSSnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| CreateHardlink | [CreateHardlinkRequest](#zbs-meta-CreateHardlinkRequest) | [NFSInode](#zbs-meta-NFSInode) | hardlink |
| MoveFile | [MoveFileRequest](#zbs-meta-MoveFileRequest) | [NFSInode](#zbs-meta-NFSInode) | move a file between two exports |
| BatchedSetAttr | [BatchedSetAttrRequest](#zbs-meta-BatchedSetAttrRequest) | [BatchedSetAttrResponse](#zbs-meta-BatchedSetAttrResponse) | batched set attr requests for syncing attr cache, doesn&#39;t handle guard and size |
| ConvertLunIntoFile | [ConvertLunIntoFileRequest](#zbs-meta-ConvertLunIntoFileRequest) | [NFSInode](#zbs-meta-NFSInode) | storage object convert |
| ConvertVolumeIntoFile | [ConvertVolumeIntoFileRequest](#zbs-meta-ConvertVolumeIntoFileRequest) | [NFSInode](#zbs-meta-NFSInode) |  |
| UpdateFile | [UpdateNFSFileRequest](#zbs-meta-UpdateNFSFileRequest) | [.zbs.Void](#zbs-Void) |  |


<a name="zbs-meta-NVMFService"></a>

### NVMFService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CreateDistSubsystem | [CreateNVMFDistSubsystemRequest](#zbs-meta-CreateNVMFDistSubsystemRequest) | [NVMFDistSubsystem](#zbs-meta-NVMFDistSubsystem) | DistSubsystem management |
| UpdateDistSubsystem | [UpdateNVMFDistSubsystemRequest](#zbs-meta-UpdateNVMFDistSubsystemRequest) | [NVMFDistSubsystem](#zbs-meta-NVMFDistSubsystem) |  |
| GetDistSubsystem | [PoolPath](#zbs-meta-PoolPath) | [NVMFDistSubsystem](#zbs-meta-NVMFDistSubsystem) |  |
| DeleteDistSubsystem | [PoolPath](#zbs-meta-PoolPath) | [.zbs.Void](#zbs-Void) |  |
| ListDistSubsystems | [ListNVMFDistSubsystemsRequest](#zbs-meta-ListNVMFDistSubsystemsRequest) | [NVMFDistSubsystemsResponse](#zbs-meta-NVMFDistSubsystemsResponse) |  |
| CreateDistNamespace | [CreateNVMFDistNamespaceRequest](#zbs-meta-CreateNVMFDistNamespaceRequest) | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) | DistNamespace management |
| UpdateDistNamespace | [UpdateNVMFDistNamespaceRequest](#zbs-meta-UpdateNVMFDistNamespaceRequest) | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) |  |
| DeleteDistNamespace | [DistNamespacePath](#zbs-meta-DistNamespacePath) | [.zbs.Void](#zbs-Void) |  |
| ListDistNamespace | [PoolPath](#zbs-meta-PoolPath) | [NVMFDistNamespacesResponse](#zbs-meta-NVMFDistNamespacesResponse) |  |
| ListDistNamespaceByGroupPath | [DistNamespaceGroupPath](#zbs-meta-DistNamespaceGroupPath) | [NVMFDistNamespacesResponse](#zbs-meta-NVMFDistNamespacesResponse) |  |
| GetDistNamespace | [DistNamespacePath](#zbs-meta-DistNamespacePath) | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) |  |
| CreateDistNamespaceGroup | [DistNamespaceGroupPath](#zbs-meta-DistNamespaceGroupPath) | [NVMFDistNamespaceGroup](#zbs-meta-NVMFDistNamespaceGroup) | DistNamespaceGroup management |
| UpdateDistNamespaceGroup | [UpdateNVMFDistNamespaceGroupRequest](#zbs-meta-UpdateNVMFDistNamespaceGroupRequest) | [NVMFDistNamespaceGroup](#zbs-meta-NVMFDistNamespaceGroup) |  |
| DeleteDistNamespaceGroup | [DistNamespaceGroupPath](#zbs-meta-DistNamespaceGroupPath) | [.zbs.Void](#zbs-Void) |  |
| ListDistNamespaceGroup | [PoolPath](#zbs-meta-PoolPath) | [NVMFDistNamespaceGroupsResponse](#zbs-meta-NVMFDistNamespaceGroupsResponse) |  |
| GetDistNamespaceGroup | [DistNamespaceGroupPath](#zbs-meta-DistNamespaceGroupPath) | [NVMFDistNamespaceGroup](#zbs-meta-NVMFDistNamespaceGroup) |  |
| CreateSnapshot | [CreateNVMFSnapshotRequest](#zbs-meta-CreateNVMFSnapshotRequest) | [Volume](#zbs-meta-Volume) | snapshot management |
| ListSnapshot | [DistNamespacePath](#zbs-meta-DistNamespacePath) | [SnapshotsResponse](#zbs-meta-SnapshotsResponse) |  |
| UpdateSnapshot | [UpdateNVMFSnapshotRequest](#zbs-meta-UpdateNVMFSnapshotRequest) | [Volume](#zbs-meta-Volume) |  |
| DeleteSnapshot | [NVMFSnapshotPath](#zbs-meta-NVMFSnapshotPath) | [.zbs.Void](#zbs-Void) |  |
| RollbackSnapshot | [NVMFSnapshotPath](#zbs-meta-NVMFSnapshotPath) | [.zbs.Void](#zbs-Void) |  |
| ShowSnapshot | [NVMFSnapshotPath](#zbs-meta-NVMFSnapshotPath) | [Volume](#zbs-meta-Volume) |  |
| GetNVMFOptimizedAccess | [GetNVMFOptimizedAccessRequest](#zbs-meta-GetNVMFOptimizedAccessRequest) | [NVMFOptimizedPaths](#zbs-meta-NVMFOptimizedPaths) |  |
| SetNVMFOptimizedAccess | [SetNVMFOptimizedAccessRequest](#zbs-meta-SetNVMFOptimizedAccessRequest) | [NVMFOptimizedPaths](#zbs-meta-NVMFOptimizedPaths) |  |
| GetAccessRecord | [GetNVMFAccessRecordRequest](#zbs-meta-GetNVMFAccessRecordRequest) | [GetNVMFAccessRecordResponse](#zbs-meta-GetNVMFAccessRecordResponse) |  |
| SetAccessRecord | [SetNVMFAccessRecordRequest](#zbs-meta-SetNVMFAccessRecordRequest) | [SetNVMFAccessRecordResponse](#zbs-meta-SetNVMFAccessRecordResponse) |  |
| AcquireNVMFIOPermission | [AcquireNVMFIOPermissionRequest](#zbs-meta-AcquireNVMFIOPermissionRequest) | [.zbs.Void](#zbs-Void) |  |
| ListTargets | [.zbs.Void](#zbs-Void) | [NVMFTargetsResponse](#zbs-meta-NVMFTargetsResponse) |  |
| AddDistNamespaceAllowedHost | [NVMFNamespaceAllowedHost](#zbs-meta-NVMFNamespaceAllowedHost) | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) |  |
| RemoveDistNamespaceAllowedHost | [NVMFNamespaceAllowedHost](#zbs-meta-NVMFNamespaceAllowedHost) | [NVMFDistNamespace](#zbs-meta-NVMFDistNamespace) |  |
| GetNVMFVolumeAccessRecords | [GetNVMFVolumeAccessRecordsRequest](#zbs-meta-GetNVMFVolumeAccessRecordsRequest) | [NVMFVolumeAccessRecords](#zbs-meta-NVMFVolumeAccessRecords) |  |


<a name="zbs-meta-StatusService"></a>

### StatusService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| GetClusterSummary | [.zbs.Void](#zbs-Void) | [ClusterSummary](#zbs-meta-ClusterSummary) | Get the summary of the cluster |
| GetMetaSummary | [.zbs.Void](#zbs-Void) | [MetaSummary](#zbs-meta-MetaSummary) | Get ths summary of this meta server |
| GetClusterPerf | [.zbs.Void](#zbs-Void) | [ClusterPerf](#zbs-meta-ClusterPerf) | get cluster perf |
| ShowClusterStatus | [.zbs.Void](#zbs-Void) | [ClusterStatus](#zbs-meta-ClusterStatus) |  |
| GetChunkConnectivities | [.zbs.Void](#zbs-Void) | [GetChunkConnectivitiesResponse](#zbs-meta-GetChunkConnectivitiesResponse) | get data channel connectivity for chunks |
| ShowChunkConnectivity | [.zbs.ChunkId](#zbs-ChunkId) | [.zbs.ChunkConnectivity](#zbs-ChunkConnectivity) |  |

 



<a name="perf_rpc-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## perf_rpc.proto



<a name="zbs-DisableProbeVolumesRequest"></a>

### DisableProbeVolumesRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id_list | [bytes](#bytes) | repeated |  |
| disable_all | [bool](#bool) | optional |  Default: false |






<a name="zbs-ProbeVolumesRequest"></a>

### ProbeVolumesRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id_list | [bytes](#bytes) | repeated |  |
| latency_buckets | [double](#double) | repeated |  |
| size_buckets | [double](#double) | repeated |  |






<a name="zbs-UIOPerf"></a>

### UIOPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| readwrite_iops | [float](#float) | optional |  |
| read_iops | [float](#float) | optional |  |
| write_iops | [float](#float) | optional |  |
| readwrite_speed_bps | [float](#float) | optional |  |
| read_speed_bps | [float](#float) | optional |  |
| write_speed_bps | [float](#float) | optional |  |
| readwrite_latency | [float](#float) | optional |  |
| read_latency | [float](#float) | optional |  |
| write_latency | [float](#float) | optional |  |
| local_readwrite_iops | [float](#float) | optional |  |
| local_read_iops | [float](#float) | optional |  |
| local_write_iops | [float](#float) | optional |  |
| local_readwrite_speed_bps | [float](#float) | optional |  |
| local_read_speed_bps | [float](#float) | optional |  |
| local_write_speed_bps | [float](#float) | optional |  |
| local_readwrite_latency | [float](#float) | optional |  |
| local_read_latency | [float](#float) | optional |  |
| local_write_latency | [float](#float) | optional |  |
| retry_readwrite_iops | [float](#float) | optional |  |
| retry_read_iops | [float](#float) | optional |  |
| retry_write_iops | [float](#float) | optional |  |
| failed_readwrite_iops | [float](#float) | optional |  |
| failed_write_iops | [float](#float) | optional |  |
| failed_read_iops | [float](#float) | optional |  |
| retry_queue_readwrite_size | [uint64](#uint64) | optional |  |
| retry_queue_read_size | [uint64](#uint64) | optional |  |
| retry_queue_write_size | [uint64](#uint64) | optional |  |
| active_volumes_size | [uint64](#uint64) | optional |  |
| active_extents_size | [uint64](#uint64) | optional |  |
| waiting_queue_size | [uint64](#uint64) | optional |  |






<a name="zbs-VolumePerf"></a>

### VolumePerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | optional |  |
| read_iops | [float](#float) | optional | io per second |
| read_bandwidth | [float](#float) | optional | bytes per second |
| read_latency | [float](#float) | optional | ns |
| write_iops | [float](#float) | optional | io per second |
| write_bandwidth | [float](#float) | optional | bytes per second |
| write_latency | [float](#float) | optional | ns |
| total_iops | [float](#float) | optional | io per second |
| total_bandwidth | [float](#float) | optional | bytes per second |
| total_latency | [float](#float) | optional | ns |
| read_avg_request_size | [float](#float) | optional | Bytes |
| write_avg_request_size | [float](#float) | optional | Bytes |
| total_avg_request_size | [float](#float) | optional | Bytes |
| total_iop30s | [float](#float) | optional |  |
| iodepth | [float](#float) | optional |  |
| ioctx_local_readwrite_iops | [float](#float) | optional | volume io perf after split |
| ioctx_local_readwrite_speed_bps | [float](#float) | optional |  |
| ioctx_local_readwrite_latency | [float](#float) | optional |  |
| ioctx_local_read_iops | [float](#float) | optional |  |
| ioctx_local_read_speed_bps | [float](#float) | optional |  |
| ioctx_local_read_latency | [float](#float) | optional |  |
| ioctx_local_write_iops | [float](#float) | optional |  |
| ioctx_local_write_speed_bps | [float](#float) | optional |  |
| ioctx_local_write_latency | [float](#float) | optional |  |
| ioctx_readwrite_iops | [float](#float) | optional |  |
| ioctx_readwrite_speed_bps | [float](#float) | optional |  |
| ioctx_readwrite_latency | [float](#float) | optional |  |
| ioctx_read_iops | [float](#float) | optional |  |
| ioctx_read_speed_bps | [float](#float) | optional |  |
| ioctx_read_latency | [float](#float) | optional |  |
| ioctx_write_iops | [float](#float) | optional |  |
| ioctx_write_speed_bps | [float](#float) | optional |  |
| ioctx_write_latency | [float](#float) | optional |  |
| readwrite_size_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| read_size_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| write_size_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| readwrite_logical_offset_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| read_logical_offset_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| write_logical_offset_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| readwrite_physical_offset_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| read_physical_offset_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| write_physical_offset_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| readwrite_latency_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| read_latency_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |
| write_latency_range_bucket | [metric.proto.Histogram](#zbs-metric-proto-Histogram) | optional |  |






<a name="zbs-VolumePerfRequest"></a>

### VolumePerfRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | required |  |






<a name="zbs-VolumesPerf"></a>

### VolumesPerf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| perf_list | [VolumePerf](#zbs-VolumePerf) | repeated |  |






<a name="zbs-VolumesPerfRequest"></a>

### VolumesPerfRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id_list | [bytes](#bytes) | repeated |  |





 

 

 


<a name="zbs-ChunkPerfService"></a>

### ChunkPerfService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| GetUIOPerf | [Void](#zbs-Void) | [UIOPerf](#zbs-UIOPerf) |  |
| GetAccessPerf | [Void](#zbs-Void) | [AccessPerf](#zbs-AccessPerf) |  |
| GetLSMPerf | [Void](#zbs-Void) | [LSMPerf](#zbs-LSMPerf) |  |


<a name="zbs-VolumePerfService"></a>

### VolumePerfService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| GetVolumePerf | [VolumePerfRequest](#zbs-VolumePerfRequest) | [VolumePerf](#zbs-VolumePerf) |  |
| GetVolumesPerf | [VolumesPerfRequest](#zbs-VolumesPerfRequest) | [VolumesPerf](#zbs-VolumesPerf) |  |
| GetAllVolumesPerf | [Void](#zbs-Void) | [VolumesPerf](#zbs-VolumesPerf) |  |
| ProbeVolumes | [ProbeVolumesRequest](#zbs-ProbeVolumesRequest) | [Void](#zbs-Void) |  |
| DisableProbeVolumes | [DisableProbeVolumesRequest](#zbs-DisableProbeVolumesRequest) | [Void](#zbs-Void) |  |

 



<a name="session-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## session.proto



<a name="zbs-consensus-AcquireNFSLoginPermissionRequest"></a>

### AcquireNFSLoginPermissionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | required |  |
| client_ip | [bytes](#bytes) | required |  |






<a name="zbs-consensus-AddItemRequest"></a>

### AddItemRequest
add an item that should be unique among the session group


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | required |  |
| group | [bytes](#bytes) | required |  |
| unique | [bool](#bool) | optional |  Default: false |
| item | [Item](#zbs-consensus-Item) | required | new session related items set |






<a name="zbs-consensus-CreateSessionRequest"></a>

### CreateSessionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group | [bytes](#bytes) | required |  |
| items | [Item](#zbs-consensus-Item) | repeated | set the initial key-value pairs so that the master could get the information when the session is created. |






<a name="zbs-consensus-DataReportRequest"></a>

### DataReportRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | required |  |
| cluster_uuid | [bytes](#bytes) | optional |  |






<a name="zbs-consensus-DataReportResponse"></a>

### DataReportResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ec | [zbs.ErrorCode](#zbs-ErrorCode) | optional |  Default: EOK |






<a name="zbs-consensus-Item"></a>

### Item



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [bytes](#bytes) | required |  |
| value | [bytes](#bytes) | required |  |






<a name="zbs-consensus-KeepAliveRequest"></a>

### KeepAliveRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | required |  |
| items | [Item](#zbs-consensus-Item) | repeated |  |
| cluster_uuid | [bytes](#bytes) | optional |  |
| aware_time_cost | [bool](#bool) | optional |  Default: false |






<a name="zbs-consensus-KeepAliveResponse"></a>

### KeepAliveResponse



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | optional |  |
| ec | [zbs.ErrorCode](#zbs-ErrorCode) | optional |  Default: EOK |
| lease_interval_ns | [int64](#int64) | optional | new lease interval |
| cluster_uuid | [bytes](#bytes) | optional |  |
| aware_time_cost | [bool](#bool) | optional |  Default: false |






<a name="zbs-consensus-ListSessionRequest"></a>

### ListSessionRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group | [bytes](#bytes) | required |  |






<a name="zbs-consensus-RefreshLocalNFSClientIPsRequest"></a>

### RefreshLocalNFSClientIPsRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | required |  |
| client_ips | [bytes](#bytes) | required | use &#39;,&#39; as split like *********,127.0.0.1 |






<a name="zbs-consensus-RemoveItemRequest"></a>

### RemoveItemRequest
remove an item from the session


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | required |  |
| item | [Item](#zbs-consensus-Item) | required | new session related items set |






<a name="zbs-consensus-Session"></a>

### Session



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| session_epoch | [SessionEpoch](#zbs-consensus-SessionEpoch) | required |  |
| group | [bytes](#bytes) | required |  |
| lease_expire_ns | [int64](#int64) | optional |  |
| items | [Item](#zbs-consensus-Item) | repeated | session releated items |






<a name="zbs-consensus-SessionEpoch"></a>

### SessionEpoch



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| uuid | [bytes](#bytes) | optional |  |
| epoch | [uint64](#uint64) | optional | session epoch used to figure out out-of-order requests |






<a name="zbs-consensus-Sessions"></a>

### Sessions



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| sessions | [Session](#zbs-consensus-Session) | repeated |  |





 

 

 


<a name="zbs-consensus-DataReportService"></a>

### DataReportService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| DataReport | [DataReportRequest](#zbs-consensus-DataReportRequest) | [DataReportResponse](#zbs-consensus-DataReportResponse) |  |


<a name="zbs-consensus-SessionService"></a>

### SessionService
session master implement session service

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CreateSession | [CreateSessionRequest](#zbs-consensus-CreateSessionRequest) | [KeepAliveResponse](#zbs-consensus-KeepAliveResponse) | session |
| LeaveSession | [SessionEpoch](#zbs-consensus-SessionEpoch) | [.zbs.Void](#zbs-Void) | logout itself |
| ListSession | [ListSessionRequest](#zbs-consensus-ListSessionRequest) | [Sessions](#zbs-consensus-Sessions) |  |
| KeepAlive | [KeepAliveRequest](#zbs-consensus-KeepAliveRequest) | [KeepAliveResponse](#zbs-consensus-KeepAliveResponse) |  |
| AddItem | [AddItemRequest](#zbs-consensus-AddItemRequest) | [Session](#zbs-consensus-Session) |  |
| RemoveItem | [RemoveItemRequest](#zbs-consensus-RemoveItemRequest) | [Session](#zbs-consensus-Session) |  |
| RefreshLocalNFSClientIPs | [RefreshLocalNFSClientIPsRequest](#zbs-consensus-RefreshLocalNFSClientIPsRequest) | [Session](#zbs-consensus-Session) |  |
| ShowSession | [SessionEpoch](#zbs-consensus-SessionEpoch) | [Session](#zbs-consensus-Session) |  |
| AcquireNFSLoginPermission | [AcquireNFSLoginPermissionRequest](#zbs-consensus-AcquireNFSLoginPermissionRequest) | [.zbs.Void](#zbs-Void) |  |

 



<a name="task-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## task.proto



<a name="zbs-task-CopyVolumeTask"></a>

### CopyVolumeTask
copy volume can be use for deep copy


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| src_volume_id | [bytes](#bytes) | required | field for task continue |
| dst_volume_id | [bytes](#bytes) | required |  |
| src_hosts | [bytes](#bytes) | required |  |
| dst_hosts | [bytes](#bytes) | required |  |
| cur_extent | [uint64](#uint64) | optional |  |
| cur_state | [CopyVolumeTaskState](#zbs-task-CopyVolumeTaskState) | optional |  Default: CV_INIT |
| skip_zero | [bool](#bool) | optional |  Default: true |
| use_compression | [bool](#bool) | optional |  Default: false |
| preferred_cid | [uint32](#uint32) | optional |  Default: 0 |
| volume_size_bytes | [uint64](#uint64) | optional | The following are auto generated/updated for metrics src volume size |
| synced_volume_bytes | [uint64](#uint64) | optional | currently synced volume bytes |
| transferred_volume_bytes | [uint64](#uint64) | optional | actual transfer size, actual_transfer_size / synced_volume_bytes is the dedup ratio |
| net_speed_Bps | [uint64](#uint64) | optional | current transferred speed over the network (Bps) |
| sync_speed_Bps | [uint64](#uint64) | optional | current sync speed (Bps) |
| base_volume_id | [bytes](#bytes) | optional |  |
| io_depth | [uint32](#uint32) | optional | The following are newly-added fields for task Default: 4 |
| bps_max | [uint64](#uint64) | optional |  Default: 0 |
| runtime | [RunTime](#zbs-task-RunTime) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| task | CopyVolumeTask | Task | 20005 |  |




<a name="zbs-task-GatewayEndpoint"></a>

### GatewayEndpoint



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ip | [string](#string) | required |  |
| rpc_port | [int32](#int32) | required |  |
| data_port | [int32](#int32) | required |  |






<a name="zbs-task-ListTaskByDateRequest"></a>

### ListTaskByDateRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| start_ms_since_epoch | [uint64](#uint64) | optional | list tasks created with in the date range specified by the time since epoch. |
| end_ms_since_epoch | [uint64](#uint64) | optional |  |






<a name="zbs-task-ListTaskByStatusRequest"></a>

### ListTaskByStatusRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| finished | [bool](#bool) | optional |  Default: false |






<a name="zbs-task-RsyncTask"></a>

### RsyncTask



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| hosts | [string](#string) | optional |  |
| remote_hosts | [string](#string) | optional |  |
| use_compression | [bool](#bool) | optional |  |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| task | RsyncTask | Task | 20004 |  |




<a name="zbs-task-RunTime"></a>

### RunTime



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| start_hour | [uint32](#uint32) | required |  Default: 0 |
| start_min | [uint32](#uint32) | required |  Default: 0 |
| end_hour | [uint32](#uint32) | required |  Default: 23 |
| end_min | [uint32](#uint32) | required |  Default: 59 |






<a name="zbs-task-SetBpsMaxRequest"></a>

### SetBpsMaxRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| task_id | [TaskId](#zbs-task-TaskId) | required |  |
| bps_max | [uint64](#uint64) | required |  |






<a name="zbs-task-SetRunTimeRequest"></a>

### SetRunTimeRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| task_id | [TaskId](#zbs-task-TaskId) | required |  |
| runtime | [RunTime](#zbs-task-RunTime) | required |  |






<a name="zbs-task-SyncVolumeTask"></a>

### SyncVolumeTask



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| volume_id | [bytes](#bytes) | optional |  |
| base_volume_id | [bytes](#bytes) | optional |  |
| remote_volume_id | [bytes](#bytes) | optional |  |
| preferred_cid | [uint32](#uint32) | optional | preferred chunk id Default: 0 |
| volume_size_bytes | [uint64](#uint64) | optional | The following are auto generated/updated for metrics src volume size |
| synced_volume_bytes | [uint64](#uint64) | optional | currently synced volume bytes |
| transferred_volume_bytes | [uint64](#uint64) | optional | actual transfer size, actual_transfer_size / synced_volume_bytes is the dedup ratio |
| net_speed_Bps | [uint64](#uint64) | optional | current transferred speed over the network (Bps) |
| sync_speed_Bps | [uint64](#uint64) | optional | current sync speed (Bps) |




| Extension | Type | Base | Number | Description |
| --------- | ---- | ---- | ------ | ----------- |
| task | SyncVolumeTask | RsyncTask | 60001 |  |




<a name="zbs-task-Task"></a>

### Task



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | optional |  |
| state | [TaskState](#zbs-task-TaskState) | optional |  Default: NONE |
| exit_status | [zbs.RpcStatus](#zbs-RpcStatus) | optional |  |
| specified_runner_addr | [zbs.Address](#zbs-Address) | optional | specify which runner to use. Use it only you really want to do so. |
| name | [bytes](#bytes) | optional | the name of the task |
| progress | [TaskProgress](#zbs-task-TaskProgress) | optional | the task runner can update progress here |
| runner_addr | [zbs.Address](#zbs-Address) | optional | runner running this task |
| created_ms | [uint64](#uint64) | optional | time since epoch |
| finished_ms | [uint64](#uint64) | optional | time since epoch |
| max_schedule_times | [uint64](#uint64) | optional | max retry times to schedule Default: 10 |
| schedule_times | [uint64](#uint64) | optional |  Default: 0 |
| last_failed_ms | [uint64](#uint64) | optional |  Default: 0 |
| service_id | [int32](#int32) | optional | for internal use

service id Default: 0 |
| method_id | [int32](#int32) | optional | method id Default: 0 |
| expect_state | [TaskState](#zbs-task-TaskState) | optional |  Default: NONE |






<a name="zbs-task-TaskId"></a>

### TaskId



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [bytes](#bytes) | required |  |






<a name="zbs-task-TaskProgress"></a>

### TaskProgress



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| percentage | [float](#float) | optional | progress if valid Default: 0 |
| message | [bytes](#bytes) | optional | message of the current status |






<a name="zbs-task-Tasks"></a>

### Tasks



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| tasks | [Task](#zbs-task-Task) | repeated |  |






<a name="zbs-task-VirtualIP"></a>

### VirtualIP



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| service_name | [string](#string) | required |  |
| ip | [string](#string) | required |  |






<a name="zbs-task-VirtualIPs"></a>

### VirtualIPs



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| vips | [VirtualIP](#zbs-task-VirtualIP) | repeated |  |





 


<a name="zbs-task-CopyVolumeTaskState"></a>

### CopyVolumeTaskState


| Name | Number | Description |
| ---- | ------ | ----------- |
| CV_INIT | 0 |  |
| CV_COPY_EXTENT | 1 |  |
| CV_COPY_EXTENT_DONE | 2 |  |
| CV_CANCELED | 3 |  |



<a name="zbs-task-TaskState"></a>

### TaskState


| Name | Number | Description |
| ---- | ------ | ----------- |
| NONE | 0 |  |
| CREATED | 1 |  |
| IN_PROGRESS | 2 |  |
| SUCCEED | 3 |  |
| FAILED | 4 |  |
| CANCELED | 5 |  |
| PAUSED | 6 |  |


 

 


<a name="zbs-task-CopyVolumeService"></a>

### CopyVolumeService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| CopyVolume | [Task](#zbs-task-Task) | [Task](#zbs-task-Task) |  |


<a name="zbs-task-RsyncService"></a>

### RsyncService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| SyncVolume | [Task](#zbs-task-Task) | [Task](#zbs-task-Task) |  |


<a name="zbs-task-StatusService"></a>

### StatusService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| ListRunner | [.zbs.Void](#zbs-Void) | [.zbs.Addresses](#zbs-Addresses) |  |
| ListTaskByDate | [ListTaskByDateRequest](#zbs-task-ListTaskByDateRequest) | [Tasks](#zbs-task-Tasks) |  |
| ListTaskByStatus | [ListTaskByStatusRequest](#zbs-task-ListTaskByStatusRequest) | [Tasks](#zbs-task-Tasks) |  |
| ShowTask | [TaskId](#zbs-task-TaskId) | [Task](#zbs-task-Task) |  |
| CancelTask | [TaskId](#zbs-task-TaskId) | [Task](#zbs-task-Task) |  |
| PauseTask | [TaskId](#zbs-task-TaskId) | [Task](#zbs-task-Task) |  |
| ResumeTask | [TaskId](#zbs-task-TaskId) | [Task](#zbs-task-Task) |  |
| SetRunTime | [SetRunTimeRequest](#zbs-task-SetRunTimeRequest) | [Task](#zbs-task-Task) |  |
| SetBpsMax | [SetBpsMaxRequest](#zbs-task-SetBpsMaxRequest) | [Task](#zbs-task-Task) |  |


<a name="zbs-task-VIPService"></a>

### VIPService


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| SetVIP | [VirtualIP](#zbs-task-VirtualIP) | [.zbs.Void](#zbs-Void) |  |
| DeleteVIP | [VirtualIP](#zbs-task-VirtualIP) | [.zbs.Void](#zbs-Void) |  |
| ShowVIP | [.zbs.Void](#zbs-Void) | [VirtualIPs](#zbs-task-VirtualIPs) |  |

 



<a name="zbs_metrics-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## zbs_metrics.proto



<a name="zbs-metric-proto-Bucket"></a>

### Bucket



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cumulative_count | [uint64](#uint64) | optional |  |
| upper_bound | [double](#double) | optional |  |






<a name="zbs-metric-proto-Counter"></a>

### Counter



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| value | [double](#double) | optional |  |






<a name="zbs-metric-proto-Gauge"></a>

### Gauge



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| value | [double](#double) | optional |  |






<a name="zbs-metric-proto-Histogram"></a>

### Histogram



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| sample_count | [uint64](#uint64) | optional |  |
| sample_sum | [double](#double) | optional |  |
| bucket | [Bucket](#zbs-metric-proto-Bucket) | repeated |  |






<a name="zbs-metric-proto-LabelPair"></a>

### LabelPair



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| value | [string](#string) | optional |  |






<a name="zbs-metric-proto-Metric"></a>

### Metric



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| label | [LabelPair](#zbs-metric-proto-LabelPair) | repeated |  |
| gauge | [Gauge](#zbs-metric-proto-Gauge) | optional |  |
| counter | [Counter](#zbs-metric-proto-Counter) | optional |  |
| summary | [Summary](#zbs-metric-proto-Summary) | optional |  |
| untyped | [Untyped](#zbs-metric-proto-Untyped) | optional |  |
| histogram | [Histogram](#zbs-metric-proto-Histogram) | optional |  |
| timestamp_ms | [int64](#int64) | optional |  |






<a name="zbs-metric-proto-MetricFamily"></a>

### MetricFamily



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) | optional |  |
| help | [string](#string) | optional |  |
| type | [MetricType](#zbs-metric-proto-MetricType) | optional |  |
| metric | [Metric](#zbs-metric-proto-Metric) | repeated |  |






<a name="zbs-metric-proto-Quantile"></a>

### Quantile



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| quantile | [double](#double) | optional |  |
| value | [double](#double) | optional |  |






<a name="zbs-metric-proto-Summary"></a>

### Summary



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| sample_count | [uint64](#uint64) | optional |  |
| sample_sum | [double](#double) | optional |  |
| quantile | [Quantile](#zbs-metric-proto-Quantile) | repeated |  |






<a name="zbs-metric-proto-Untyped"></a>

### Untyped



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| value | [double](#double) | optional |  |





 


<a name="zbs-metric-proto-MetricType"></a>

### MetricType


| Name | Number | Description |
| ---- | ------ | ----------- |
| COUNTER | 0 |  |
| GAUGE | 1 |  |
| SUMMARY | 2 |  |
| UNTYPED | 3 |  |
| HISTOGRAM | 4 |  |


 

 

 



## Scalar Value Types

| .proto Type | Notes | C++ | Java | Python | Go | C# | PHP | Ruby |
| ----------- | ----- | --- | ---- | ------ | -- | -- | --- | ---- |
| <a name="double" /> double |  | double | double | float | float64 | double | float | Float |
| <a name="float" /> float |  | float | float | float | float32 | float | float | Float |
| <a name="int32" /> int32 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead. | int32 | int | int | int32 | int | integer | Bignum or Fixnum (as required) |
| <a name="int64" /> int64 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead. | int64 | long | int/long | int64 | long | integer/string | Bignum |
| <a name="uint32" /> uint32 | Uses variable-length encoding. | uint32 | int | int/long | uint32 | uint | integer | Bignum or Fixnum (as required) |
| <a name="uint64" /> uint64 | Uses variable-length encoding. | uint64 | long | int/long | uint64 | ulong | integer/string | Bignum or Fixnum (as required) |
| <a name="sint32" /> sint32 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s. | int32 | int | int | int32 | int | integer | Bignum or Fixnum (as required) |
| <a name="sint64" /> sint64 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s. | int64 | long | int/long | int64 | long | integer/string | Bignum |
| <a name="fixed32" /> fixed32 | Always four bytes. More efficient than uint32 if values are often greater than 2^28. | uint32 | int | int | uint32 | uint | integer | Bignum or Fixnum (as required) |
| <a name="fixed64" /> fixed64 | Always eight bytes. More efficient than uint64 if values are often greater than 2^56. | uint64 | long | int/long | uint64 | ulong | integer/string | Bignum |
| <a name="sfixed32" /> sfixed32 | Always four bytes. | int32 | int | int | int32 | int | integer | Bignum or Fixnum (as required) |
| <a name="sfixed64" /> sfixed64 | Always eight bytes. | int64 | long | int/long | int64 | long | integer/string | Bignum |
| <a name="bool" /> bool |  | bool | boolean | boolean | bool | bool | boolean | TrueClass/FalseClass |
| <a name="string" /> string | A string must always contain UTF-8 encoded or 7-bit ASCII text. | string | String | str/unicode | string | string | string | String (UTF-8) |
| <a name="bytes" /> bytes | May contain any arbitrary sequence of bytes. | string | ByteString | str | []byte | ByteString | string | String (ASCII-8BIT) |

