package main

import (
	"fmt"
	"go/build"
	"os"

	"github.com/princjef/gomarkdoc"
	"github.com/princjef/gomarkdoc/lang"
	"github.com/princjef/gomarkdoc/logger"
)

func main() {
	// Create a renderer to output data
	out, err := gomarkdoc.NewRenderer()
	if err != nil {
		// handle error
	}

	wd, err := os.Getwd()
	if err != nil {
		// handle error
	}

	buildPkg, err := build.ImportDir(wd, build.ImportComment)
	if err != nil {
		// handle error
	}

	// Create a documentation package from the build representation of our
	// package.
	log := logger.New(logger.DebugLevel)
	pkg, err := lang.NewPackageFromBuild(log, buildPkg)
	if err != nil {
		// handle error
	}

	// Write the documentation out to console.
	fmt.Println(out.Package(pkg))
}
