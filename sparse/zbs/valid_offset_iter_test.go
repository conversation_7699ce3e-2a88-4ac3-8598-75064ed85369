/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package zbs

import (
	"io"
	"testing"

	"github.com/RoaringBitmap/roaring"
	"github.com/stretchr/testify/require"
)

func TestOffsetIter(t *testing.T) {
	offsetIter := &ValidOffsetIter{
		size:            volumeSize,
		stripeNum:       stripeNum,
		stripeSize:      stripeSize,
		stripePerExtent: extentSize / stripeSize,
	}

	cases := []struct {
		name              string
		skipExtents       []int
		expectStartOffset int64
		expectEndOffset   int64
	}{
		{
			name:              "no skip extents",
			skipExtents:       nil,
			expectStartOffset: 0,
			expectEndOffset:   ioSize,
		},
		{
			name:              "skip first extent",
			skipExtents:       []int{0},
			expectStartOffset: stripeSize,
			expectEndOffset:   stripeSize * 4,
		},
		{
			name:              "skip second extent",
			skipExtents:       []int{1},
			expectStartOffset: 0,
			expectEndOffset:   stripeSize,
		},
		{
			name:              "skip third extent",
			skipExtents:       []int{2},
			expectStartOffset: 0,
			expectEndOffset:   stripeSize * 2,
		},
		{
			name:              "skip fourth extent",
			skipExtents:       []int{3},
			expectStartOffset: 0,
			expectEndOffset:   stripeSize * 3,
		},
		{
			name:              "skip first and second extents",
			skipExtents:       []int{0, 1},
			expectStartOffset: stripeSize * 2,
			expectEndOffset:   stripeSize * 4,
		},
		{
			name:              "skip first and third extents",
			skipExtents:       []int{0, 2},
			expectStartOffset: stripeSize,
			expectEndOffset:   stripeSize * 2,
		},
		{
			name:              "skip first and fourth extents",
			skipExtents:       []int{0, 3},
			expectStartOffset: stripeSize,
			expectEndOffset:   stripeSize * 3,
		},
	}

	for _, c := range cases {
		skipExtentBitmap := roaring.NewBitmap()

		for _, skipExtent := range c.skipExtents {
			skipExtentBitmap.AddInt(skipExtent)
		}

		offsetIter.skipExtentBitmap = skipExtentBitmap

		startOffset, endOffset, err := offsetIter.NextValidOffsetRange(0, ioSize)
		require.Equal(t, nil, err, c.name)
		require.Equal(t, c.expectStartOffset, startOffset, c.name)
		require.Equal(t, c.expectEndOffset, endOffset, c.name)
	}

	startOffset, endOffset, err := offsetIter.NextValidOffsetRange(volumeSize, ioSize)
	require.Equal(t, io.EOF, err, "over the volume size")
	require.Equal(t, int64(volumeSize), startOffset, "over the volume size")
	require.Equal(t, int64(volumeSize), endOffset, "over the volume size")
}
