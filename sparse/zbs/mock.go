package zbs

import (
	"context"

	"github.com/RoaringBitmap/roaring"
	"github.com/iomesh/zbs-client-go/zbs"
	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
)

const (
	volumeId   = "volume_id"
	volumeSize = 8 << 20   // 8 MB
	extentSize = 256 << 20 // 256MB
	stripeNum  = 4
	stripeSize = 256 << 10 // 256KB
	ioDepth    = 8
	ioSize     = ioDepth * stripeSize // 8 * 256KB = 2MB
)

func newMockValidOffsetIter(skipExtentBitmap *roaring.Bitmap) *ValidOffsetIter {
	var bitmap *roaring.Bitmap

	// if skipExtentBitmap is nil, no extent will be skipped,
	// init the skipExtentBitmap with an empty bitmap.
	if skipExtentBitmap == nil {
		bitmap = roaring.NewBitmap()
	} else {
		bitmap = skipExtentBitmap
	}

	return &ValidOffsetIter{
		size:             volumeSize,
		stripeNum:        stripeNum,
		stripeSize:       stripeSize,
		stripePerExtent:  extentSize / stripeSize,
		skipExtentBitmap: bitmap,
	}
}

type mockDataChannelClient struct {
	volumeData []byte
}

var _ zbs.DataChannel = &mockDataChannelClient{}

func newMockDataChannelClient(volumeSize int) *mockDataChannelClient {
	return &mockDataChannelClient{
		volumeData: make([]byte, volumeSize),
	}
}

func (dc *mockDataChannelClient) Ping(ctx context.Context) error {
	return nil
}

func (dc *mockDataChannelClient) VolumeRead(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags) error {
	copy(buf, dc.volumeData[offset:offset+uint64(length)])

	return nil
}

func (dc *mockDataChannelClient) VolumeWrite(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags, preferredCid dc.Cid) error {
	copy(dc.volumeData[offset:], buf)

	return nil
}

func (dc *mockDataChannelClient) Close() error {
	return nil
}
