package zbs

import (
	"bytes"
	"context"
	"crypto/rand"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestRead(t *testing.T) {
	// generate random data and write into mock data channel
	data := make([]byte, volumeSize)
	_, err := rand.Read(data)
	require.NoError(t, err)

	dc := newMockDataChannelClient(volumeSize)
	copy(dc.volumeData, data)

	// read data from sparse reader
	reader, err := NewReader(dc, volumeId, volumeSize, newMockValidOffsetIter(nil))
	require.NoError(t, err)

	ctx := context.TODO()
	buf := make([]byte, ioSize)

	for offset := int64(0); offset < volumeSize; offset += ioSize {
		n, returnOffset, err := reader.Read(ctx, buf, -1)
		require.NoError(t, err)
		require.Equal(t, ioSize, n)
		require.Equal(t, offset, returnOffset)
		require.True(t, bytes.Equal(buf, data[offset:offset+ioSize]))
	}

	err = reader.Close()
	require.NoError(t, err)
}
