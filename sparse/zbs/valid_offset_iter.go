package zbs

import (
	"errors"
	"io"

	"github.com/RoaringBitmap/roaring"

	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
)

type ValidOffsetIter struct {
	// skipExtentBitmap used to skip fetch some data from zbs.
	skipExtentBitmap *roaring.Bitmap
	size             uint64
	extentSize       uint64
	stripeNum        uint64
	stripeSize       uint64
	stripePerExtent  uint64
}

func NewValidOffsetIter(volume *metav1.Volume, skipExtentBitmap *roaring.Bitmap, extentSize uint64) *ValidOffsetIter {
	var bitmap *roaring.Bitmap

	// if skipExtentBitmap is nil, no extent will be skipped,
	// init the skipExtentBitmap with an empty bitmap.
	if skipExtentBitmap == nil {
		bitmap = roaring.NewBitmap()
	} else {
		bitmap = skipExtentBitmap
	}

	return &ValidOffsetIter{
		skipExtentBitmap: bitmap,
		size:             volume.GetSize(),
		extentSize:       extentSize,
		stripeNum:        uint64(volume.GetStripeNum()),
		stripeSize:       uint64(volume.GetStripeSize()),
		stripePerExtent:  extentSize / uint64(volume.GetStripeSize()),
	}
}

func (r *ValidOffsetIter) GetValidSize() uint64 {
	return r.size - r.skipExtentBitmap.GetCardinality()*r.extentSize
}

// NextValidOffset will search the offset locate in next non-empty extent
func (r *ValidOffsetIter) NextValidOffset(offset int64) (int64, error) {
	stripeBlockIndex := uint64(offset) / r.stripeSize

	for offset < int64(r.size) {
		stripeRow := stripeBlockIndex / r.stripePerExtent
		stripeCol := stripeBlockIndex % r.stripeNum
		extentIndex := (stripeRow/r.stripeNum)*r.stripeNum + stripeCol

		if !r.skipExtentBitmap.Contains(uint32(extentIndex)) {
			return offset, nil
		}

		stripeBlockIndex += 1
		offset = int64(stripeBlockIndex * r.stripeSize)
	}

	return offset, io.EOF
}

func (r *ValidOffsetIter) NextValidOffsetRange(offset int64, length uint32) (int64, int64, error) {
	startOffset, err := r.NextValidOffset(offset)
	if err != nil {
		return startOffset, startOffset, err
	}

	endOffset := startOffset + int64(r.stripeSize)

	for nextOffset, err := r.NextValidOffset(endOffset); endOffset-startOffset < int64(length); nextOffset, err = r.NextValidOffset(endOffset) {
		if endOffset != nextOffset || errors.Is(err, io.EOF) {
			break
		}

		endOffset += int64(r.stripeSize)
	}

	if endOffset > startOffset+int64(length) {
		return startOffset, startOffset + int64(length), nil
	}

	return startOffset, endOffset, nil
}
