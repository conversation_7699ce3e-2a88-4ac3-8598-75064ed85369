package zbs

import (
	"context"

	"github.com/iomesh/zbs-client-go/zbs"
	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
)

type Writer struct {
	client   zbs.DataChannel
	volumeID string
}

func NewWriter(client zbs.DataChannel, volumeID string) (*Writer, error) {
	return &Writer{
		client:   client,
		volumeID: volumeID,
	}, nil
}

// WriteAt implement SparseWriter interface
func (w *Writer) WriteAt(ctx context.Context, p []byte, offset int64) (int, error) {
	length := uint32(len(p))

	err := w.client.VolumeWrite(ctx, w.volumeID, p, length, uint64(offset), dc.NO_PROMOTION, 0)
	if err != nil {
		return 0, err
	}

	return int(length), nil
}

// Close implement SparseWriter interface
func (w *Writer) Close() error {
	return nil
}
