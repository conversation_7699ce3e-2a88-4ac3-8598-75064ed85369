package zbs

import (
	"bytes"
	"context"
	"crypto/rand"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestWrite(t *testing.T) {
	// generate random data and sparse write into mock data channel
	data := make([]byte, volumeSize)
	_, err := rand.Read(data)
	require.NoError(t, err)

	dc := newMockDataChannelClient(volumeSize)

	writer, err := NewWriter(dc, volumeId)
	require.NoError(t, err)

	ctx := context.TODO()

	for offset := int64(0); offset < volumeSize; offset += ioSize {
		n, err := writer.WriteAt(ctx, data[offset:offset+ioSize], offset)
		require.NoError(t, err)
		require.Equal(t, ioSize, n)
	}

	err = writer.Close()
	require.NoError(t, err)

	// compare written data and expected data is the same
	require.True(t, bytes.Equal(data, dc.volumeData))
}
