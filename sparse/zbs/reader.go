/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package zbs

import (
	"context"

	sparseio "github.com/iomesh/zbs-client-go/sparse/io"
	"github.com/iomesh/zbs-client-go/zbs"
	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
)

type Reader struct {
	client   zbs.DataChannel
	volumeID string
	size     uint64
	pos      int64

	offsetIter *ValidOffsetIter
}

var _ sparseio.SparseReader = &Reader{}

// NewReader create a reader wraps volume read of zbs datachannel.
func NewReader(client zbs.DataChannel, volumeID string, size uint64, offsetIter *ValidOffsetIter) (*Reader, error) {
	return &Reader{
		client:     client,
		volumeID:   volumeID,
		size:       size,
		pos:        0,
		offsetIter: offsetIter,
	}, nil
}

// Read implement sparseio.SparseReader interface
func (r *Reader) Read(ctx context.Context, p []byte, offsetLowerBound int64) (int, int64, error) {
	length := len(p)

	offsetLowerBound = max(offsetLowerBound, r.pos)

	offset, endOffset, err := r.offsetIter.NextValidOffsetRange(offsetLowerBound, uint32(length))
	if err != nil {
		return 0, 0, err
	}

	length = sparseio.TrimLength(int(endOffset-offset), offset, int64(length))

	err = r.client.VolumeRead(ctx, r.volumeID, p, uint32(length), uint64(offset), dc.NO_PROMOTION)
	if err != nil {
		return 0, 0, err
	}

	r.pos = offset + int64(length)

	return length, offset, nil
}

// Close implement sparseio.SparseReader interface
func (r *Reader) Close() error {
	return nil
}
