/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package io

import "context"

type ProcessReader struct {
	SparseReader
	byteRead  uint64
	processCh chan uint64
}

// ProcessReader does not automatically close processCh,
// it needs to be closed manually by the user.
func NewProcessReader(reader SparseReader, processCh chan uint64) *ProcessReader {
	return &ProcessReader{
		SparseReader: reader,
		byteRead:     0,
		processCh:    processCh,
	}
}

func (r *ProcessReader) Read(ctx context.Context, p []byte, offsetLowerBound int64) (n int, offset int64, err error) {
	n, offset, err = r.SparseReader.Read(ctx, p, offsetLowerBound)
	if err != nil {
		return
	}

	r.byteRead += uint64(n)
	r.processCh <- r.byteRead

	return
}

type ProcessWriter struct {
	SparseWriter
	byteWritten uint64
	processCh   chan uint64
}

// ProcessWriter does not automatically close processCh,
// it needs to be closed manually by the user.
func NewProcessWriter(writer SparseWriter, processCh chan uint64) *ProcessWriter {
	return &ProcessWriter{
		SparseWriter: writer,
		byteWritten:  0,
		processCh:    processCh,
	}
}

func (w *ProcessWriter) WriteAt(ctx context.Context, p []byte, offset int64) (n int, err error) {
	n, err = w.SparseWriter.WriteAt(ctx, p, offset)
	if err != nil {
		return
	}

	w.byteWritten += uint64(n)
	w.processCh <- w.byteWritten

	return
}
