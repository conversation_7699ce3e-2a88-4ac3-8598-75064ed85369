/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package io

import (
	"context"
	"io"

	"github.com/pkg/errors"
	"go.uber.org/multierr"

	"github.com/iomesh/zbs-client-go/sparse/leakybuf"
)

type CopyState int

const (
	CopyStatePaused  CopyState = 0
	CopyStateRunning CopyState = 1
)

const ReadWriteBufSize int = 32

type sparseCopyInfo struct {
	reader SparseReader
	writer SparseWriter

	csCh chan CopyState

	bufPool *leakybuf.BytePool

	writerCh chan *SparseEntry
	errCh    chan error
}

func newSparseCopyInfo(reader SparseReader, writer SparseWriter, batchSize int, csCh chan CopyState) *sparseCopyInfo {
	return &sparseCopyInfo{
		reader:   reader,
		writer:   writer,
		csCh:     csCh,
		bufPool:  leakybuf.NewBytePool(ReadWriteBufSize, batchSize),
		writerCh: make(chan *SparseEntry, ReadWriteBufSize),
		errCh:    make(chan error),
	}
}

func (info *sparseCopyInfo) Close() error {
	close(info.errCh)
	err := info.writer.Close()

	return multierr.Append(err, info.reader.Close())
}

func SparseCopy(ctx context.Context, reader SparseReader, writer SparseWriter, batchSize int, csCh chan CopyState) error {
	var err error
	info := newSparseCopyInfo(reader, writer, batchSize, csCh)

	defer func() {
		_ = info.Close()
	}()

	go runWriter(ctx, info)

	writerExited, err := runReader(ctx, info)
	if writerExited {
		return err
	}

	writerErr := <-info.errCh
	err = multierr.Append(err, writerErr)

	return err
}

type SparseEntry struct {
	buf       []byte
	backedBuf []byte
	offset    int64
}

func (b *SparseEntry) Reset(buf []byte, backupBuf []byte, offset int64) {
	b.buf = buf
	b.backedBuf = backupBuf
	b.offset = offset
}

func runWriter(ctx context.Context, info *sparseCopyInfo) {
	var entry *SparseEntry
	var ok bool

	// write until got notify by close writerCh
	for {
		entry, ok = <-info.writerCh
		if !ok {
			info.errCh <- nil
			return
		}

		_, err := info.writer.WriteAt(ctx, entry.buf, entry.offset)
		if err != nil {
			info.errCh <- errors.Wrapf(err, "failed to write, offset=%d", entry.offset)
			return
		}

		info.bufPool.Put(entry.backedBuf)
	}
}

func runReader(ctx context.Context, info *sparseCopyInfo) (bool, error) {
	// after Reader exits, close writerCh to notify writer that it has finished reading
	defer close(info.writerCh)

	var err error

	// read until got EOF from reader
ReadLoop:
	for {
		select {
		case <-ctx.Done():
			err = ctx.Err()
			break ReadLoop
		case err = <-info.errCh:
			return true, err
		case copyState := <-info.csCh:
			switch copyState {
			case CopyStatePaused:
			PauseLoop:
				// blocking on next event
				for {
					select {
					case <-ctx.Done():
						err = ctx.Err()
						break ReadLoop
					case err = <-info.errCh:
						return true, err
					case copyState = <-info.csCh:
						switch copyState {
						case CopyStateRunning:
							break PauseLoop
						case CopyStatePaused:
						}
					}
				}
			case CopyStateRunning:
			}
		default:
		}

		var read int
		var offset int64
		entry := &SparseEntry{
			buf:       nil,
			backedBuf: info.bufPool.Get(),
			offset:    -1,
		}

		read, offset, err = info.reader.Read(ctx, entry.backedBuf, -1)
		if err != nil {
			if errors.Is(err, io.EOF) {
				err = nil
			}
			break ReadLoop
		}

		entry.offset = offset
		entry.buf = entry.backedBuf[:read]

		select {
		case info.writerCh <- entry:
		case err = <-info.errCh:
			return true, err
		}
	}

	return false, err
}
