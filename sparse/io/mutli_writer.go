/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package io

import (
	"context"
	"sync"

	"github.com/pkg/errors"
	"go.uber.org/multierr"
)

type MultiWriter struct {
	writers []SparseWriter
}

var _ SparseWriter = &MultiWriter{}

func NewMultiWriter(writers ...SparseWriter) *MultiWriter {
	return &MultiWriter{writers: writers}
}

func (w *MultiWriter) WriteAt(ctx context.Context, p []byte, offset int64) (int, error) {
	length := len(p)
	wg := sync.WaitGroup{}

	var errs error
	errCh := make(chan error, 1)

	go func() {
		for err := range errCh {
			errs = multierr.Append(errs, err)
		}
	}()

	for idx := range w.writers {
		wg.Add(1)

		go func(idx int) {
			defer wg.Done()

			n, err := w.writers[idx].WriteAt(ctx, p, offset)
			if err != nil {
				errCh <- err
			}

			if n != length {
				errCh <- errors.Errorf("expect Writer write %d bytes, but got %d", length, n)
			}
		}(idx)
	}

	wg.Wait()
	close(errCh)

	if errs != nil {
		return 0, errs
	}

	return length, nil
}

func (w *MultiWriter) Close() error {
	var errs error

	for _, writer := range w.writers {
		errs = multierr.Append(errs, writer.Close())
	}

	return errs
}
