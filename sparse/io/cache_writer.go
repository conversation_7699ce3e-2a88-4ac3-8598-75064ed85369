/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package io

import (
	"context"
	"errors"

	"go.uber.org/multierr"
)

type CacheWriter struct {
	writer SparseWriter

	cachedData   []byte
	cacheSize    uint32
	cachedOffset int64
	expectOffset int64
	cachedLength uint32
	expectLength uint32
}

var _ SparseWriter = &CacheWriter{}

func NewCacheWriter(writer SparseWriter, cacheSize uint32) *CacheWriter {
	return &CacheWriter{
		writer: writer,

		cachedData:   make([]byte, cacheSize),
		cacheSize:    cacheSize,
		cachedLength: 0,
		cachedOffset: -1,
		expectOffset: -1,
	}
}

func (w *CacheWriter) WriteAt(ctx context.Context, p []byte, offset int64) (int, error) {
	length := len(p)

	for len(p) > 0 {
		if offset == w.expectOffset {
			n, err := w.writeToCache(ctx, p, offset)
			if err != nil {
				return length - len(p), err
			}

			p = p[n:]
			offset += int64(n)

			continue
		}

		if w.cachedLength != 0 {
			err := w.flushCache(ctx)
			if err != nil {
				return length - len(p), err
			}
		}

		// Restriction: each write should not exceed stripe block bound,
		limit := getLengthToUpperBound(offset, int64(w.cacheSize))

		// If the length of the data has reached the bound, then send directly.
		// Otherwise, it's cached and awaits the aggregation of additional data.
		if len(p) >= limit {
			n, err := w.writer.WriteAt(ctx, p[:limit], offset)
			if err != nil {
				return length - len(p), err
			}

			if n != limit {
				return length - len(p), errors.New("partial write")
			}

			p = p[limit:]
			offset += int64(limit)
		} else {
			_, err := w.writeToCache(ctx, p, offset)
			if err != nil {
				return length - len(p), err
			}

			break
		}
	}

	return length, nil
}

func (w *CacheWriter) flushCache(ctx context.Context) error {
	n, err := w.writer.WriteAt(ctx, w.cachedData[:w.cachedLength], w.cachedOffset)
	if err != nil {
		return err
	}

	if n != int(w.cachedLength) {
		return errors.New("failed to flush full cache")
	}

	w.cachedLength = 0
	w.cachedOffset = -1
	w.expectOffset = -1

	return nil
}

func (w *CacheWriter) writeToCache(ctx context.Context, data []byte, offset int64) (int, error) {
	if w.cachedOffset == -1 {
		w.cachedOffset = offset
		w.expectOffset = offset
		w.expectLength = uint32(TrimLength(int(w.cacheSize), offset, int64(w.cacheSize)))
	}

	length := TrimLength(len(data), w.cachedOffset, int64(w.cacheSize))
	n := copy(w.cachedData[w.cachedLength:], data[:length])

	w.cachedLength += uint32(n)
	w.expectOffset += int64(n)

	var err error

	if w.cachedLength == w.expectLength {
		err = w.flushCache(ctx)
	}

	return n, err
}

func (w *CacheWriter) Close() error {
	var err error

	if w.cachedLength != 0 {
		err = w.flushCache(context.TODO())
	}

	return multierr.Append(err, w.writer.Close())
}

func getLengthToUpperBound(offset int64, size int64) int {
	upperBound := (offset/size + 1) * size

	return int(upperBound - offset)
}

func TrimLength(length int, offset int64, size int64) int {
	limit := getLengthToUpperBound(offset, size)

	return min(limit, length)
}
