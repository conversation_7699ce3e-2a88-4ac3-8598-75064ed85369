/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package io

import (
	"bytes"
	"context"
	"crypto/rand"
	"io"
	"testing"
	"time"

	"github.com/iomesh/zbs-client-go/zbs/datachannel"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
)

const (
	grainSize   = 64 << 10 // 64KB
	totalLength = 100 * grainSize
)

type mockSparseReader struct {
	extent *datachannel.MockExtent
	pos    int
	skip   skipFn
	length int
}

type skipFn func(offset int) bool

func newMockSparseReader(length int, skip skipFn) (*mockSparseReader, error) {
	buf := make([]byte, length)

	_, err := rand.Read(buf)
	if err != nil {
		return nil, err
	}

	extent := &datachannel.MockExtent{}

	err = extent.Write(buf, 0)
	if err != nil {
		return nil, err
	}

	return &mockSparseReader{
		extent: extent,
		pos:    0,
		skip:   skip,
		length: length,
	}, nil
}

func (r *mockSparseReader) Read(ctx context.Context, p []byte, offsetLowerBound int64) (int, int64, error) {
	for r.skip != nil && r.skip(r.pos) && r.pos < r.length {
		r.pos += grainSize
	}

	if r.pos >= r.length {
		return 0, 0, io.EOF
	}

	offset := int64(r.pos)
	var n int

	for n = 0; n < len(p); n += grainSize {
		if (r.skip != nil && r.skip(r.pos)) || r.pos >= r.length {
			break
		}

		buf, err := r.extent.Read(grainSize, r.pos)
		if err != nil {
			return 0, 0, err
		}

		copy(p[n:], buf)

		r.pos += grainSize
	}

	return n, offset, nil
}

func (r *mockSparseReader) Close() error {
	return nil
}

type mockSparseWriter struct {
	extent *datachannel.MockExtent
}

func newMockSparseWriter() *mockSparseWriter {
	return &mockSparseWriter{
		extent: &datachannel.MockExtent{},
	}
}

func (w *mockSparseWriter) WriteAt(ctx context.Context, p []byte, offset int64) (int, error) {
	err := w.extent.Write(p, int(offset))

	return len(p), err
}

func (w *mockSparseWriter) Close() error {
	return nil
}

func TestSparseCopySuccess(t *testing.T) {
	cases := []struct {
		name  string
		limit int
	}{
		{
			"SparseCopy 1 grain once and ",
			grainSize,
		},
		{
			"SparseCopy 10 grain once and ",
			10 * grainSize,
		},
		{
			"SparseCopy 15 grain once and ",
			15 * grainSize,
		},
	}

	skipFns := []struct {
		name string
		skip skipFn
	}{
		{
			"do not skip any grain",
			func(offset int) bool {
				return false
			},
		},
		{
			"skip grains between 10 and 30",
			func(offset int) bool {
				return offset >= 10*grainSize && offset < 30*grainSize
			},
		},
		{
			"skip odd grains",
			func(offset int) bool {
				return (offset/grainSize)%2 == 0
			},
		},
		{
			"skip last grain",
			func(offset int) bool {
				return offset >= 99*grainSize
			},
		},
	}

	for i := range cases {
		for k := range skipFns {
			t.Run(cases[i].name+skipFns[k].name, func(t *testing.T) {
				reader, err := newMockSparseReader(totalLength, skipFns[k].skip)
				require.NoError(t, err)

				writer := newMockSparseWriter()
				ctx := context.TODO()

				err = SparseCopy(ctx, reader, writer, cases[i].limit, nil)
				require.NoError(t, err)

				checkResult(t, writer.extent, reader.extent, skipFns[k].skip)
			})
		}
	}

	t.Run("reader return empty buffer", func(t *testing.T) {
		reader, err := newMockSparseReader(0, nil)
		require.NoError(t, err)

		writer := newMockSparseWriter()
		ctx := context.TODO()
		err = SparseCopy(ctx, reader, writer, grainSize, nil)
		require.NoError(t, err)
	})
}

func checkResult(t *testing.T, got *datachannel.MockExtent, expected *datachannel.MockExtent, skip skipFn) {
	var gotBuf []byte
	var expectedBuf []byte
	var err error

	for offset := 0; offset < totalLength; offset += grainSize {
		if skip(offset) {
			continue
		}

		gotBuf, err = got.Read(grainSize, offset)
		require.NoError(t, err)
		expectedBuf, err = expected.Read(grainSize, offset)
		require.NoError(t, err)
		require.True(t, bytes.Equal(gotBuf, expectedBuf), "offset=%d", offset)
	}
}

type mockFailWriter struct {
	*mockSparseWriter
}

func (w *mockFailWriter) WriteAt(ctx context.Context, p []byte, offset int64) (int, error) {
	if offset == 10*grainSize {
		return 0, errors.New("mock write error")
	}

	return w.mockSparseWriter.WriteAt(ctx, p, offset)
}

func (w *mockFailWriter) Close() error {
	return nil
}

type MockFailReader struct {
	*mockSparseReader
}

func (r *MockFailReader) Read(ctx context.Context, p []byte, offsetLowerBound int64) (int, int64, error) {
	if r.mockSparseReader.pos == 10*grainSize {
		return 0, int64(r.mockSparseReader.pos), errors.New("mock read error")
	}

	return r.mockSparseReader.Read(ctx, p, offsetLowerBound)
}

func TestSparseCopyFail(t *testing.T) {
	t.Run("context canceled", func(t *testing.T) {
		reader, err := newMockSparseReader(totalLength, nil)
		require.NoError(t, err)

		writer := newMockSparseWriter()
		ctx := context.Background()
		ctx, cancel := context.WithCancel(ctx)
		cancel()

		err = SparseCopy(ctx, reader, writer, grainSize, nil)
		require.EqualError(t, err, "context canceled")
	})

	t.Run("writer return error", func(t *testing.T) {
		writer := newMockSparseWriter()
		mockFailWriter := &mockFailWriter{writer}
		reader, err := newMockSparseReader(totalLength, nil)
		require.NoError(t, err)

		ctx := context.TODO()
		err = SparseCopy(ctx, reader, mockFailWriter, grainSize, nil)
		require.EqualError(t, err, "failed to write, offset=655360: mock write error")
	})

	t.Run("reader return error", func(t *testing.T) {
		writer := newMockSparseWriter()
		reader, err := newMockSparseReader(totalLength, nil)
		require.NoError(t, err)

		mockFailReader := &MockFailReader{reader}

		ctx := context.TODO()
		err = SparseCopy(ctx, mockFailReader, writer, grainSize, nil)
		require.EqualError(t, err, "mock read error")
	})
}

func TestSparseCopyPause(t *testing.T) {
	t.Run("sparse copy pause", func(t *testing.T) {
		csCh := make(chan CopyState, 1)
		defer close(csCh)

		doneCh := make(chan struct{})
		done := false

		reader, err := newMockSparseReader(totalLength, nil)
		require.NoError(t, err)

		writer := newMockSparseWriter()

		csCh <- CopyStatePaused

		go func() {
			err = SparseCopy(context.TODO(), reader, writer, grainSize, csCh)
			require.Nil(t, err)

			close(doneCh)
		}()

		select {
		case <-doneCh:
			done = true
		case <-time.After(3 * time.Second):
		}

		require.False(t, done)

		csCh <- CopyStateRunning

		select {
		case <-doneCh:
			done = true
		case <-time.After(3 * time.Second):
		}

		require.True(t, done)
	})
}
