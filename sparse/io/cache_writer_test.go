/*
Copyright 2021 The IOMesh Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package io

import (
	"context"
	"crypto/rand"
	"testing"

	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
	"github.com/stretchr/testify/require"
)

const (
	volumeID   = "volume_id"
	volumeSize = 8 << 20 // 8 MB
	IOSize     = 2 << 20 // 2 MB
)

type mockDataChannelClient struct {
	volumeData []byte
	writeTime  int
}

func newMockDataChannelClient() *mockDataChannelClient {
	return &mockDataChannelClient{
		volumeData: make([]byte, volumeSize),
	}
}

func (dc *mockDataChannelClient) VolumeWrite(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags, preferredCid dc.Cid) error {
	dc.writeTime += 1

	copy(dc.volumeData[offset:], buf)

	return nil
}

type mockDataChannelWriter struct {
	client *mockDataChannelClient
}

func newMockDataChannelWriter(dcClient *mockDataChannelClient) *mockDataChannelWriter {
	return &mockDataChannelWriter{
		client: dcClient,
	}
}

func (w *mockDataChannelWriter) WriteAt(ctx context.Context, p []byte, offset int64) (int, error) {
	err := w.client.VolumeWrite(ctx, volumeID, p, uint32(len(p)), uint64(offset), dc.NO_PROMOTION, 0)
	if err != nil {
		return 0, err
	}

	return len(p), nil
}

func (w *mockDataChannelWriter) Close() error {
	return nil
}

func TestWriter(t *testing.T) {
	ctx := context.TODO()

	dcClient := newMockDataChannelClient()
	writer := NewCacheWriter(newMockDataChannelWriter(dcClient), IOSize)

	data := make([]byte, volumeSize)
	_, err := rand.Read(data)
	require.NoError(t, err)

	size := IOSize / 2

	// Since the write length is less than IOsize, it will be cached.
	n, err := writer.WriteAt(ctx, data[:size], 0)
	require.NoError(t, err)
	require.Equal(t, size, n)

	require.Equal(t, size, int(writer.cachedLength))
	require.Equal(t, 0, dcClient.writeTime)

	// The offset of the cache is not continuous, triggering flush.
	n, err = writer.WriteAt(ctx, data[IOSize:IOSize+size], IOSize)
	require.NoError(t, err)
	require.Equal(t, size, n)

	require.Equal(t, size, int(writer.cachedLength))
	require.Equal(t, 1, dcClient.writeTime)
	require.Equal(t, data[:size], dcClient.volumeData[:size])

	err = writer.Close()
	require.NoError(t, err)
}

func TestWriterByWriteSize(t *testing.T) {
	ctx := context.TODO()

	dcClient := newMockDataChannelClient()
	writer := NewCacheWriter(newMockDataChannelWriter(dcClient), IOSize)

	data := make([]byte, volumeSize)
	_, err := rand.Read(data)
	require.NoError(t, err)

	expectWriteTime := volumeSize / IOSize

	for _, num := range []int{1, 2, 4, 8, 16} {
		dcClient.writeTime = 0

		writeSize := volumeSize / num

		for i := 0; i < num; i++ {
			n, err := writer.WriteAt(ctx, data[i*writeSize:(i+1)*writeSize], int64(i*writeSize))
			require.NoError(t, err)
			require.Equal(t, writeSize, n)
		}

		require.Equal(t, expectWriteTime, dcClient.writeTime)
		require.Equal(t, data, dcClient.volumeData)
	}

	err = writer.Close()
	require.NoError(t, err)
}

func TestWriterByUnalignedSize(t *testing.T) {
	ctx := context.TODO()

	dcClient := newMockDataChannelClient()
	writer := NewCacheWriter(newMockDataChannelWriter(dcClient), IOSize)

	data := make([]byte, volumeSize)
	_, err := rand.Read(data)
	require.NoError(t, err)

	size := IOSize + IOSize/2

	n, err := writer.WriteAt(ctx, data[:size], 0)
	require.NoError(t, err)
	require.Equal(t, size, n)

	require.Equal(t, IOSize/2, int(writer.cachedLength))
	require.Equal(t, data[IOSize:size], writer.cachedData[:writer.cachedLength])

	n, err = writer.WriteAt(ctx, data[size:2*size], int64(size))
	require.NoError(t, err)
	require.Equal(t, size, n)

	require.Equal(t, 0, int(writer.cachedLength))
	require.Equal(t, 3, dcClient.writeTime)
	require.Equal(t, data[:2*size], dcClient.volumeData[:2*size])
}
