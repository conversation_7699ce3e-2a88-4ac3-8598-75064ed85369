FROM registry.smtx.io/library/golang:1.21-alpine-amd64
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache bash make python3 protobuf protobuf-dev
RUN go env -w GOPROXY=https://proxy.golang.com.cn,direct
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.31.0
RUN go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.3.0
WORKDIR /zbs-client-go

# Cache go dependencies in docker image
COPY go.mod go.sum ./
RUN go mod download
