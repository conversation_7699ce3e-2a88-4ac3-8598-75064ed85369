PACKAGE=$(shell head -1 go.mod | awk '{print $$2}')
PROTO_DIR := api/proto
ZBS_PROTO_DIR := build/proto/zbs
PROTO_GEN_DIR := gen/proto
SERVICE_INFO_GEN := gen/serviceinfo
DOCKER_IMAGE_PREFIX := registry.smtx.io/zbs-client-go/protoc
DOCKER_IMAGE_TAG := v1.0.1
DOCKER_IMAGE_NAME="$(DOCKER_IMAGE_PREFIX):$(DOCKER_IMAGE_TAG)"

all: docker/proto

.PHONY: generate_service_info
generate_service_info:
	cd ${ZBS_PROTO_DIR}; ./service_info_generator.py
	go fmt ${ZBS_PROTO_DIR}/service_info.go
	mkdir -p ${SERVICE_INFO_GEN}/
	# to avoid cyclic import, we mv service_info.go into ${SERVICE_INFO_GEN} from ${PROTO_GEN_DIR}
	mv ${ZBS_PROTO_DIR}/service_info.go ${SERVICE_INFO_GEN}/

.PHONY: prepare_proto
prepare_proto:
	mkdir -p ${ZBS_PROTO_DIR}
	rm -rf ${ZBS_PROTO_DIR}/*
	cp -r scripts/service_info_generator.py ${ZBS_PROTO_DIR}
	cp -r ${PROTO_DIR}/zbs/* ${ZBS_PROTO_DIR}
	# rename metrics.proto to avoid naming conflict with Prometheus's metrics.proto
	mv ${ZBS_PROTO_DIR}/metrics.proto ${ZBS_PROTO_DIR}/zbs_metrics.proto

.PHONY: proto_go_options
proto_go_options:
	$(eval file=${ZBS_PROTO_DIR}/block.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/block\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/event.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/options.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/error.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/common.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/cdp.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/encryption.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/encryption\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/perf_rpc.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs\";" ${file}
	sed -i "s/import \"metrics.proto\";/import \"zbs_metrics.proto\";/g" ${file}

	$(eval file=${ZBS_PROTO_DIR}/zbs_metrics.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "7i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/metric/proto\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/meta.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/meta\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/session.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/consensus\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/chunk.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/chunk\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/task.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/task\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/libmeta.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/libmeta\";" ${file}

	$(eval file=${ZBS_PROTO_DIR}/encryption.proto)
	sed -i "/option go_package = .*/d" ${file}
	sed -i "2i option go_package = \"${PACKAGE}/${PROTO_GEN_DIR}/zbs/encryption\";" ${file}

.PHONY: proto
proto: prepare_proto proto_go_options generate_service_info
	go install google.golang.org/protobuf/cmd/protoc-gen-go
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc

	mkdir -p ${PROTO_GEN_DIR}/zbs
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs\
		${ZBS_PROTO_DIR}/event.proto \
		${ZBS_PROTO_DIR}/options.proto \
		${ZBS_PROTO_DIR}/error.proto \
		${ZBS_PROTO_DIR}/common.proto \
		${ZBS_PROTO_DIR}/cdp.proto \
		${ZBS_PROTO_DIR}/perf_rpc.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/block
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/block \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/block \
		${ZBS_PROTO_DIR}/block.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/meta
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/meta \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/meta \
		${ZBS_PROTO_DIR}/meta.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/consensus
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/consensus \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/consensus\
		${ZBS_PROTO_DIR}/session.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/chunk
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/chunk \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/chunk\
		${ZBS_PROTO_DIR}/chunk.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/metric/proto
	protoc -I${ZBS_PROTO_DIR}\
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/metric/proto\
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/metric/proto\
		${ZBS_PROTO_DIR}/zbs_metrics.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/task
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/task \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/task\
		${ZBS_PROTO_DIR}/task.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/libmeta
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/libmeta \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/libmeta\
		${ZBS_PROTO_DIR}/libmeta.proto

	mkdir -p ${PROTO_GEN_DIR}/zbs/encryption
	protoc -I${ZBS_PROTO_DIR} \
		--go_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/encryption \
		--go-grpc_out=paths=source_relative:${PROTO_GEN_DIR}/zbs/encryption\
		${ZBS_PROTO_DIR}/encryption.proto

.PHONY: mockgen
mockgen:
	go install go.uber.org/mock/mockgen@latest
	mockgen -destination ./zbs/zrpc/mock_client.go -package zrpc google.golang.org/grpc ClientConnInterface
	mockgen -source ./zbs/datachannel.go -destination ./zbs/mock/datachannel.go

.PHONY: test
test:
	go test $$(go list ./...) -cover

.PHONY: fmt
fmt:
	goimports -w ./utils ./zbs
	gofmt -s -w -l .

.PHONY: fix
fix:
	go fix ./...

.PHONY: lint
lint:
	golangci-lint run

.PHONY: precommit
precommit: fix fmt lint

.PHONY: docker/image
docker/image:
	docker build -t $(DOCKER_IMAGE_NAME) -f ./Dockerfile .

.PHONY: docker/push
docker/push: docker/image
	docker image push $(DOCKER_IMAGE_NAME)

.PHONY: docker/proto
docker/proto:
	docker run --rm -v=$(shell pwd):/zbs-client-go $(DOCKER_IMAGE_NAME) bash -c "make proto"
