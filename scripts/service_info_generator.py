#!/usr/bin/python
# -*- coding: utf-8 -*-

import glob
import re
import operator
import json
import os
from collections import OrderedDict

package_service_ids = OrderedDict({})
package_file_proto_map = OrderedDict({})
packages_list = []
p_package = re.compile(r"package (.*);")
p_service = re.compile(r"service (.*) {")
p_service_id = re.compile(r".*rpc_service_id. = (\d*);")


def service_id(lines):
    for line in lines:
        m = p_service_id.match(line)
        if m:
            return int(m.group(1))
    return None


def service(lines):

    def service_block_end(lines):
        for line in lines:
            yield line
            if line.startswith(r"}"):
                break

    for line in lines:
        m = p_service.match(line)
        if m:
            block = service_block_end(lines)
            yield (m.group(1).strip(), block)
            for _ in block:
                pass


def packages(files):
    for file in files:
        lines = iter(open(file))
        for line in lines:
            m = p_package.match(line)
            if m:
                yield (m.group(1).strip(), lines)


def main():
    for (client, clients) in packages(glob.glob('*.proto')):
        for (svc, svcs) in service(clients):
            id = service_id(svcs)
            if id is not None:
                if client not in package_service_ids:
                    package_service_ids[client] = {}
                package_service_ids[client][svc] = id

    for (pkg, pkg_file) in packages(glob.glob('*.proto')):
        file_name = pkg_file.name.strip().replace('.proto', '')
        if pkg not in package_file_proto_map:
            if not pkg.startswith('zbs'):
                continue
            package_file_proto_map[pkg] = []
            packages_list.append('github.com/iomesh/zbs-client-go/gen/proto/{}'.format(pkg.replace('.'
                                 , '/')))
        fd_proto = '{}.File_{}_proto'.format(pkg.split('.')[-1],
                file_name)
        package_file_proto_map[pkg].append(fd_proto)

    packages_list.sort()

    with open('service_info.go', 'w') as f:
        header_template = \
            """// This file is generated by {filename}, DO NOT MODIFY!

package serviceinfo

import (
    "google.golang.org/protobuf/reflect/protoreflect"

    {pkg}
)

"""
        f.write(header_template.format(filename=os.path.basename(__file__),
                pkg='"' + '"\n"'.join(packages_list) + '"'))
        package_service_ids_template = \
            """
var PackageServiceIds = map[string]map[string]int32{value}
"""
        f.write(package_service_ids_template.format(value=re.sub(r"}",
                r",\n}", json.dumps(package_service_ids)).replace('{',
                '{\n').replace(', ', ',\n')))
        server_port_index_template = \
            """
var ServicePortOffsetTable = map[string]int{
    "zbs.meta.MetaService":   0,
    "zbs.meta.ISCSIService":  0,
    "zbs.meta.NFSService":    0,
    "zbs.meta.NVMFService":   0,
    "zbs.meta.StatusService": 1,
    "zbs.meta.ChunkService":  0,
    "zbs.meta.CDPService": 0,
    "zbs.chunk.ChunkService": 0,
    "zbs.VolumePerfService": 3,
    "zbs.ChunkPerfService": 3,
    "zbs.task.VIPService": 0,
    "zbs.block.CompareExtentService": 0,
}
"""
        f.write(server_port_index_template)

        proto_map_template_header = \
            """
var ProtoFileDescriptorMap = map[string][]protoreflect.FileDescriptor {
"""
        f.write(proto_map_template_header)
        for pkg in package_file_proto_map:
            proto_map_item_template = \
                """"{pkg}": {{ {files} }},
                """
            package_file_proto_map[pkg].sort()
            f.write(proto_map_item_template.format(pkg=pkg,
                    files=', '.join(package_file_proto_map[pkg])).replace(' ', ''))
        proto_map_template_footer = '}\n'
        f.write(proto_map_template_footer)


if __name__ == '__main__':
    main()

