module github.com/iomesh/zbs-client-go

go 1.21

require (
	github.com/RoaringBitmap/roaring v1.6.0
	github.com/emirpasic/gods v1.12.0
	github.com/go-playground/validator/v10 v10.15.5
	github.com/go-zookeeper/zk v1.0.2
	github.com/google/uuid v1.1.2
	github.com/jjeffery/stringset v1.0.2
	github.com/pkg/errors v0.9.1
	github.com/samber/lo v1.39.0
	github.com/stretchr/testify v1.8.2
	go.uber.org/mock v0.4.0
	go.uber.org/multierr v1.11.0
	google.golang.org/grpc v1.41.0
	google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.3.0
	google.golang.org/protobuf v1.31.0
	k8s.io/klog v1.0.0
)

require (
	github.com/bits-and-blooms/bitset v1.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.2 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/golang/protobuf v1.5.0 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/mschoch/smat v0.2.0 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	golang.org/x/crypto v0.7.0 // indirect
	golang.org/x/exp v0.0.0-20220303212507-bbda1eaf7a17 // indirect
	golang.org/x/net v0.8.0 // indirect
	golang.org/x/sys v0.6.0 // indirect
	golang.org/x/text v0.8.0 // indirect
	google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
