package utils

import metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"

// helper functions for constructing pointer to primitive types

func NewInt16(val int16) *int16 {
	v := val
	return &v
}

func NewUint16(val uint16) *uint16 {
	v := val
	return &v
}

func NewInt32(val int32) *int32 {
	v := val
	return &v
}

func NewUint32(val uint32) *uint32 {
	v := val
	return &v
}

func NewInt64(val int64) *int64 {
	v := val
	return &v
}

func NewUint64(val uint64) *uint64 {
	v := val
	return &v
}

func NewBool(val bool) *bool {
	v := val
	return &v
}

func NewString(val string) *string {
	v := val
	return &v
}

// helper functions for constructing new proto message

func NewPoolPathByName(name string) *metav1.PoolPath {
	return &metav1.PoolPath{
		PoolName: []byte(name),
	}
}

func NewPoolPathById(id string) *metav1.PoolPath {
	return &metav1.PoolPath{
		PoolId: []byte(id),
	}
}

func NewVolumePathByName(poolPath *metav1.PoolPath, name string) *metav1.VolumePath {
	return &metav1.VolumePath{
		PoolPath:   poolPath,
		VolumeName: []byte(name),
	}
}

func NewVolumePathById(id string) *metav1.VolumePath {
	return &metav1.VolumePath{
		VolumeId: []byte(id),
	}
}

func NewSnapshotPathByName(volumePath *metav1.VolumePath, name string) *metav1.SnapshotPath {
	return &metav1.SnapshotPath{
		VolumePath:   volumePath,
		SnapshotName: []byte(name),
	}
}

func NewSnapshotPathById(id string) *metav1.SnapshotPath {
	return &metav1.SnapshotPath{
		SnapshotId: []byte(id),
	}
}
