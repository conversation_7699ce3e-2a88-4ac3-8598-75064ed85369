package utils

import (
	"math"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
)

var (
	letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
)

func GenerateRandomVExtents() *zbs.VExtent {
	return &zbs.VExtent{
		VextentId:     &[]uint32{uint32(RandUint64(math.MaxInt64))}[0],
		Location:      &[]uint32{uint32(RandUint64(math.MaxInt64))}[0],
		AliveLocation: &[]uint32{uint32(RandUint64(math.MaxInt64))}[0],
	}
}

func GenerateRandomString(n *uint16) string {
	if n == nil {
		n = NewUint16(10)
	}

	buf := make([]rune, *n)

	for i := range buf {
		buf[i] = letters[RandIntn(len(letters))]
	}

	return string(buf)
}

func GenerateRandomStrings(count uint16, stringLength *uint16) []string {
	buf := make([]string, count)
	for i := range buf {
		buf[i] = GenerateRandomString(stringLength)
	}

	return buf
}
