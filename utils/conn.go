package utils

import (
	"net"
	"time"
)

// Conn impls net.Conn interface
type Conn struct {
	conn net.Conn
}

func FromConn(conn net.Conn) net.Conn {
	return &Conn{
		conn: conn,
	}
}

func DialTimeout(addr string, timeout time.Duration) (net.Conn, error) {
	c, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return nil, err
	}

	return &Conn{
		conn: c,
	}, nil
}

func (c *Conn) LocalAddr() net.Addr {
	return c.conn.LocalAddr()
}

func (c *Conn) RemoteAddr() net.Addr {
	return c.conn.RemoteAddr()
}

func (c *Conn) SetDeadline(t time.Time) error {
	return c.conn.SetDeadline(t)
}

func (c *Conn) SetReadDeadline(t time.Time) error {
	return c.conn.SetReadDeadline(t)
}

func (c *Conn) SetWriteDeadline(t time.Time) error {
	return c.conn.SetWriteDeadline(t)
}

// Write write all
func (c *Conn) Write(b []byte) (int, error) {
	total := len(b)
	writed := 0

	// write all
	for writed < total {
		n, err := c.conn.Write(b[writed:total])
		if err != nil {
			return writed, err
		}

		writed += n
	}

	return total, nil
}

// Read read all
func (c *Conn) Read(b []byte) (int, error) {
	total := len(b)
	read := 0

	// read all
	for read < total {
		n, err := c.conn.Read(b[read:total])
		if err != nil {
			return read, err
		}

		read += n
	}

	return total, nil
}

func (c *Conn) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}

	return nil
}
