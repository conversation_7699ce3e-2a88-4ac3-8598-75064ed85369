package zbs

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/utils"
)

func TestMetaService_Pool(t *testing.T) {
	client, server := NewMockServerMeta(t)

	defer func() { _ = server.Stop() }()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	pool1, err := client.Meta.CreatePool(ctx, "pool1", nil)
	require.Nil(t, err)

	pool2, err := client.Meta.ShowPoolByName(ctx, string(pool1.GetName()))
	require.Nil(t, err)
	require.True(t, proto.Equal(pool1, pool2))

	poolList, err := client.Meta.ListPool(ctx, nil)
	require.Nil(t, err)
	require.Equal(t, 1, len(poolList.Pools))

	pool3 := poolList.Pools[0]
	require.True(t, proto.Equal(pool1, pool3))

	err = client.Meta.DeletePoolByName(ctx, string(pool1.GetName()))
	require.Nil(t, err)

	pool4, err := client.Meta.ShowPoolById(ctx, string(pool1.GetId()))
	require.Nil(t, pool4)
	require.NotNil(t, err)
}

func TestMetaService_Volume(t *testing.T) {
	client, server := NewMockServerMeta(t)

	defer func() { _ = server.Stop() }()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	const defaultPoolName = "default"
	defaultPool := utils.NewPoolPathByName(defaultPoolName)

	// create default pool
	_, err := client.Meta.CreatePool(ctx, defaultPoolName, nil)
	require.Nil(t, err)

	// test volume APIs
	volume1, err := client.Meta.CreateVolume(ctx, defaultPool, "vol1", 1024, nil)
	require.Nil(t, err)

	volumePath1 := utils.NewVolumePathByName(defaultPool, string(volume1.GetName()))

	volumeResponse2, err := client.Meta.ShowVolume(ctx, volumePath1)
	require.Nil(t, err)

	if !proto.Equal(volume1, volumeResponse2.Volume) {
		t.Fatalf("volume1 not equal to pool2, %+v != %+v", volume1, volumeResponse2.Volume)
	}

	volumeList, err := client.Meta.ListVolume(ctx, defaultPool)
	require.Nil(t, err)

	require.Equal(t, 1, len(volumeList.GetVolumes()))
	require.True(t, proto.Equal(volumeList.Volumes[0], volume1))

	err = client.Meta.DeleteVolume(ctx, volumePath1)
	require.Nil(t, err)

	volumeResponse2, err = client.Meta.ShowVolume(ctx, volumePath1)
	require.Nil(t, volumeResponse2)
	require.NotNil(t, err)
}

func TestMetaService_Snapshot(t *testing.T) {
	client, server := NewMockServerMeta(t)

	defer func() { _ = server.Stop() }()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// create default pool
	const defaultPoolName = "default"
	defaultPool := utils.NewPoolPathByName(defaultPoolName)
	_, err := client.Meta.CreatePool(ctx, defaultPoolName, nil)
	require.Nil(t, err)

	// create default volume
	const defaultVolumeName = "vol1"
	defaultVolume, err := client.Meta.CreateVolume(ctx, defaultPool, defaultVolumeName, 1024, nil)
	require.Nil(t, err)

	// list all snapshot of default volume, expect 0 snapshot
	defaultVolumePath := utils.NewVolumePathById(string(defaultVolume.GetId()))
	rsp, err := client.Meta.ListSnapshot(ctx, defaultVolumePath, nil)
	require.Nil(t, err)
	require.Len(t, rsp.GetSnapshots(), 0)

	// create snapshot-1 on default volume
	path := utils.NewSnapshotPathByName(defaultVolumePath, "snapshot1")
	snapshot1, err := client.Meta.CreateSnapshot(ctx, path, nil)
	require.Nil(t, err)
	require.Equal(t, "snapshot1", string(snapshot1.GetName()))
	checkSnapshotClonedFromVolume(t, defaultVolume, snapshot1)

	// check snapshot-1 by ShowSnapshot func
	path = utils.NewSnapshotPathById(string(snapshot1.GetId()))
	res, err := client.Meta.ShowSnapshot(ctx, path)
	require.Nil(t, err)
	require.True(t, proto.Equal(snapshot1, res))

	// create snapshot-2 on default volume
	path = utils.NewSnapshotPathByName(defaultVolumePath, "snapshot2")
	snapshot2, err := client.Meta.CreateSnapshot(ctx, path, nil)
	require.Nil(t, err)
	require.Equal(t, "snapshot2", string(snapshot2.GetName()))
	checkSnapshotClonedFromVolume(t, defaultVolume, snapshot2)

	// list all 2 snapshots of default volume
	rsp, err = client.Meta.ListSnapshot(ctx, defaultVolumePath, nil)
	require.Nil(t, err)
	require.Len(t, rsp.GetSnapshots(), 2)

	// delete snapshot-1
	path = utils.NewSnapshotPathById(string(snapshot1.GetId()))
	err = client.Meta.DeleteSnapshot(ctx, path)
	require.Nil(t, err)

	// check delete snapshot-1 success by show
	path = utils.NewSnapshotPathByName(defaultVolumePath, "snapshot1")
	snapshot, err := client.Meta.ShowSnapshot(ctx, path)
	require.NotNil(t, err)
	require.Nil(t, snapshot)

	// check delete snapshot-1 success by list all snapshot
	rsp, err = client.Meta.ListSnapshot(ctx, defaultVolumePath, nil)
	require.Nil(t, err)
	require.Len(t, rsp.GetSnapshots(), 1)

	// change name of snapshot-2 to snapshot-3 by update snapshot
	path = utils.NewSnapshotPathById(string(snapshot2.GetId()))
	snapshot, err = client.Meta.UpdateSnapshot(ctx, path, "snapshot3", nil, nil)
	require.Nil(t, err)
	require.Equal(t, "snapshot3", string(snapshot.GetName()))

	// check update snapshot-2 success by ShowSnapshot
	res, err = client.Meta.ShowSnapshot(ctx, path)
	require.Nil(t, err)
	require.True(t, proto.Equal(snapshot, res))
}

func TestMetaService_ClusterInfo(t *testing.T) {
	client, server := NewMockServerMeta(t)

	defer func() { _ = server.Stop() }()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	_, err := client.Meta.ShowClusterInfo(ctx)
	require.Nil(t, err)
}

func checkSnapshotClonedFromVolume(t *testing.T, volume *metav1.Volume, snapshot *metav1.Volume) {
	require.NotNil(t, snapshot)
	require.Equal(t, volume.GetId(), snapshot.GetParentId())
	require.True(t, snapshot.GetIsSnapshot())
	require.Equal(t, volume.GetOriginId(), snapshot.GetOriginId())
}
