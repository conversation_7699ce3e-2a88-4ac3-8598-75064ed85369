package datachannel

import (
	"bytes"
	"fmt"
	"reflect"
	"sync"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

type timeoutHookFunc = func()

type MockService struct {
	m           map[UUID]*MockExtent // volume_id => MockExtent
	mu          sync.Mutex
	TimeoutHook timeoutHookFunc
}

func NewMockService() *MockService {
	return &MockService{
		m:           make(map[UUID]*MockExtent),
		TimeoutHook: func() {},
	}
}

type Args struct {
	Info Info
	Buf  []byte
}

type Reply struct {
	Info  Info
	Buf   []byte
	Error *zbserror.Error
}

func (r *Reply) GetError() error {
	if r.Error != nil {
		return r.Error
	}

	return nil
}

func (s *MockService) VolumeRead(args *Args, reply *Reply) error {
	reply.Info = args.Info

	s.TimeoutHook()

	info, ok := args.Info.(*VolumeInfo)
	if !ok {
		reply.Error = zbserror.NewError(zbs.ErrorCode_EBadArgument,
			fmt.Sprintf("cannot convert %s to *VolumeInfo", reflect.TypeOf(args.Info)))
		return nil
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	extent, ok := s.m[info.VolumeID]
	if !ok {
		reply.Error = zbserror.NewError(zbs.ErrorCode_ENotFound,
			fmt.Sprintf("volume [%s] do not exist", info.VolumeID.String()))
		return nil
	}

	buf, err := extent.Read(int(info.DataLen), int(info.DataOffset))
	if err != nil {
		reply.Error = zbserror.NewError(zbs.ErrorCode_ENIOError,
			fmt.Sprintf("read volume %s failed", info.VolumeID))
		return nil
	}

	// clone buf from extent here to prevent server codec read extent buf after releasing s.mu
	reply.Buf = bytes.Clone(buf)

	return nil
}

func (s *MockService) VolumeWrite(args *Args, reply *Reply) error {
	reply.Info = args.Info

	s.TimeoutHook()

	info, ok := args.Info.(*VolumeInfo)
	if !ok {
		reply.Error = zbserror.NewError(zbs.ErrorCode_EBadArgument,
			fmt.Sprintf("cannot convert %s to *VolumeInfo", reflect.TypeOf(args.Info)))
		return nil
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	extent, ok := s.m[info.VolumeID]
	if !ok {
		extent = new(MockExtent)
		extent.id = info.VolumeID
	}

	err := extent.Write(args.Buf, int(info.DataOffset))
	if err != nil {
		reply.Error = zbserror.NewError(zbs.ErrorCode_ENIOError,
			fmt.Sprintf("write volume %s failed", info.VolumeID.String()))
		return nil
	}

	s.m[info.VolumeID] = extent

	return nil
}

func (s *MockService) Ping(args *Args, reply *Reply) error {
	reply.Info = args.Info
	return nil
}
