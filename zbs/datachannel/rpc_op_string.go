// Code generated by "stringer -type=RpcOp -linecomment -output rpc_op_string.go"; DO NOT EDIT.

package datachannel

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[VextentReadOp-0]
	_ = x[VextentWriteOp-1]
	_ = x[PextentReadOp-2]
	_ = x[PextentWriteOp-3]
	_ = x[VolumeReadOp-4]
	_ = x[VolumeWriteOp-5]
	_ = x[RecoverStartOp-6]
	_ = x[RecoverEndOp-7]
	_ = x[GetGenerationOp-8]
	_ = x[PingOp-9]
	_ = x[EchoOp-10]
	_ = x[VextentCompareAndWriteOp-11]
	_ = x[VextentCompareAndWriteReadOp-12]
	_ = x[ShakeOp-13]
	_ = x[MaxOp-14]
}

const _RpcOp_name = "VextentReadVextentWritePextentReadPextentWriteVolumeReadVolumeWriteRecoverStartRecoverEndGetGenerationPingEchoVextentCompareAndWriteVextentCompareAndWriteReadShakeMaxOp"

var _RpcOp_index = [...]uint8{0, 11, 23, 34, 46, 56, 67, 79, 89, 102, 106, 110, 132, 158, 163, 168}

func (i RpcOp) String() string {
	if i >= RpcOp(len(_RpcOp_index)-1) {
		return "RpcOp(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _RpcOp_name[_RpcOp_index[i]:_RpcOp_index[i+1]]
}
