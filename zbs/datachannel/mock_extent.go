package datachannel

type MockExtent struct {
	id  UUID
	buf []byte
}

func (e *MockExtent) Write(writeBuf []byte, offset int) error {
	if e.buf == nil {
		e.buf = make([]byte, 256)
	}

	padding := offset - len(e.buf)

	if padding > 0 {
		e.buf = append(e.buf, make([]byte, padding)...)
	}

	buf := e.buf

	e.buf = append(e.buf[:offset], writeBuf...)
	end := len(writeBuf) + offset

	if len(buf) > end {
		e.buf = append(e.buf, buf[end:]...)
	}

	return nil
}

func (e *MockExtent) Read(length int, offset int) ([]byte, error) {
	// Case 1
	if offset > len(e.buf) {
		return make([]byte, length), nil
	}

	// Case 2
	if offset+length > len(e.buf) {
		buf := e.buf[offset:]
		padding := offset + length - len(e.buf)
		buf = append(buf, make([]byte, padding)...)

		return buf, nil
	}

	// Case 3
	return e.buf[offset : offset+length], nil
}
