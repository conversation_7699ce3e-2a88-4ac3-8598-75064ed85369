package datachannel

import (
	"bytes"
	"fmt"
	"io"
	"net"
	"net/rpc"
	"reflect"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
)

type serverCodec struct {
	conn   net.Conn
	header MessageHeader
	router *routerTable
}

func newServerCodec(conn net.Conn) rpc.ServerCodec {
	return &serverCodec{
		conn:   conn,
		router: newRouterTable(),
	}
}

func (sc *serverCodec) ReadRequestHeader(req *rpc.Request) error {
	err := sc.header.Unmarshal(sc.conn)
	if err != nil {
		return err
	}

	req.ServiceMethod = sc.router.GetMethodName(sc.header.Op)
	req.Seq = sc.header.MessageId

	return nil
}

func (sc *serverCodec) ReadRequestBody(body interface{}) error {
	args, ok := body.(*Args)
	if !ok {
		return fmt.Errorf("cannot convert %s to *Args", reflect.TypeOf(body))
	}

	args.Info = sc.header.Info

	if sc.header.BufLen == 0 {
		return nil
	}

	args.Buf = make([]byte, sc.header.BufLen)

	if _, err := io.ReadFull(sc.conn, args.Buf); err != nil {
		return err
	}

	return nil
}

func (sc *serverCodec) WriteResponse(res *rpc.Response, data interface{}) error {
	if res.Error != "" {
		panic(res.Error)
	}

	buf := &bytes.Buffer{}

	reply, ok := data.(*Reply)
	if !ok {
		return fmt.Errorf("cannot convert %s to *Reply", reflect.TypeOf(data))
	}

	op := sc.router.GetOp(res.ServiceMethod)

	var payload []byte
	var err error
	ec := zbs.ErrorCode_EOK

	if reply.Error != nil {
		ec = reply.Error.EC()

		payload, err = reply.Error.Marshal()
		if err != nil {
			return err
		}
	} else {
		payload = reply.Buf
	}

	header := MessageHeader{
		Header: NewHeader(op, len(payload), res.Seq),
		Info:   reply.Info,
	}

	header.SetErrorCode(ec)

	err = header.Marshal(buf)
	if err != nil {
		return err
	}

	if payload != nil {
		buf.Write(payload)
	}

	_, err = sc.conn.Write(buf.Bytes())
	if err != nil {
		return err
	}

	return nil
}

func (sc *serverCodec) Close() error {
	return sc.conn.Close()
}
