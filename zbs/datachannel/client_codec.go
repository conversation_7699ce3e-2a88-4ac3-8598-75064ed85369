package datachannel

import (
	"bytes"
	"fmt"
	"io"
	"net"
	"net/rpc"
	"reflect"

	"google.golang.org/protobuf/proto"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

type clientCodec struct {
	conn   net.Conn
	header MessageHeader
	router *routerTable
}

func newClientCodec(conn net.Conn) *clientCodec {
	return &clientCodec{
		conn:   conn,
		router: newRouterTable(),
	}
}

func (cc *clientCodec) WriteRequest(req *rpc.Request, data interface{}) error {
	buf := &bytes.Buffer{}

	args, ok := data.(Args)
	if !ok {
		return fmt.Errorf("cannot convert %s to Args", reflect.TypeOf(data))
	}

	header := MessageHeader{
		Header: NewHeader(cc.router.GetOp(req.ServiceMethod), len(args.Buf), req.Seq+1),
		Info:   args.Info,
	}

	err := header.Marshal(buf)
	if err != nil {
		return err
	}

	if args.Buf != nil {
		buf.Write(args.Buf)
	}

	_, err = cc.conn.Write(buf.Bytes())

	return err
}

func (cc *clientCodec) ReadResponseHeader(rsp *rpc.Response) error {
	err := cc.header.Unmarshal(cc.conn)
	if err != nil {
		return err
	}
	// In zbs datachannel server, req->rsp_hdr->message_id = req->req_hdr->message_id;
	// In Golang Standard Library "net/rpc", pending call is tracking with the Seq before
	// we plus it by 1 in clientCodec.WriteRequest(), so we must minus 1 to let net/rpc
	// got correct Seq.
	rsp.Seq = cc.header.MessageId - 1
	rsp.Error = ""

	return nil
}

func (cc *clientCodec) ReadResponseBody(body interface{}) error {
	reply, ok := body.(*Reply)
	if !ok {
		return fmt.Errorf("cannot convert %s to *Reply", reflect.TypeOf(body))
	}

	reply.Info = cc.header.Info

	if cc.header.BufLen == 0 {
		return nil
	}

	if cc.header.HasError() {
		return cc.readErrorResponse(reply, cc.header.BufLen)
	}

	if _, err := io.ReadFull(cc.conn, reply.Buf); err != nil {
		return err
	}

	return nil
}

func (cc *clientCodec) Close() error {
	return cc.conn.Close()
}

func (cc *clientCodec) readErrorResponse(reply *Reply, len uint32) error {
	buf := make([]byte, len)
	if _, err := cc.conn.Read(buf); err != nil {
		return err
	}

	var status zbs.RpcStatus
	if err := proto.Unmarshal(buf, &status); err != nil {
		return fmt.Errorf("%v, buf: %s", err, buf)
	}

	reply.Error = &zbserror.Error{St: &status}

	return nil
}
