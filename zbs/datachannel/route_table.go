package datachannel

type routerTable struct {
	serviceMethodMappings map[string]RpcOp
	OpMappings            map[RpcOp]string
}

func newRouterTable() *routerTable {
	table := &routerTable{}
	table.generateMappings()

	return table
}

func (r *routerTable) GetOp(name string) RpcOp {
	return r.serviceMethodMappings[name]
}

func (r *routerTable) GetMethodName(op RpcOp) string {
	return r.OpMappings[op]
}

func (r *routerTable) generateMappings() {
	allRpcOps := []RpcOp{
		VolumeReadOp,
		VolumeWriteOp,
		PingOp,
	}

	r.serviceMethodMappings = make(map[string]RpcOp)
	r.OpMappings = make(map[RpcOp]string)

	for _, op := range allRpcOps {
		name := op.MethodName()
		r.serviceMethodMappings[name] = op
		r.OpMappings[op] = name
	}
}
