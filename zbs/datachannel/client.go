package datachannel

import (
	"context"
	"net"
	"net/rpc"
	"time"

	"k8s.io/klog"

	"github.com/iomesh/zbs-client-go/utils"
)

const (
	// client reconnect limit
	retryLimit = 5
)

type Client struct {
	*rpc.Client

	rpcTimeoutMS int32 // rpc timeout in ms
}

func NewClient(addr string, config *Config) (*Client, error) {
	rpcClient, err := newRpcClient(addr)
	if err != nil {
		return nil, err
	}

	return &Client{
		Client:       rpcClient,
		rpcTimeoutMS: config.RpcTimeoutMS,
	}, nil
}

func newRpcClient(addr string) (*rpc.Client, error) {
	conn, err := dial("tcp", addr, 2*time.Second)
	if err != nil {
		return nil, err
	}

	return rpc.NewClientWithCodec(newClientCodec(conn)), nil
}

func dial(network, addr string, timeout time.Duration) (net.Conn, error) {
	var conn net.Conn
	var err error

	dialer := net.Dialer{Timeout: timeout}

	err = utils.Until(func() error {
		conn, err = dialer.Dial(network, addr)
		if err != nil {
			return err
		}

		return nil
	}, retryLimit, time.Second)

	if err != nil {
		return nil, err
	}

	return conn, nil
}

func (c *Client) Invoke(ctx context.Context, method string, args interface{}, reply interface{}) error {
	var cancel func()
	if _, ok := ctx.Deadline(); !ok {
		ctx, cancel = context.WithTimeout(ctx, time.Duration(c.rpcTimeoutMS+1000)*time.Millisecond)
		defer cancel()
	}

	call := c.Go(method, args, reply, nil)

	select {
	case <-call.Done:
		return call.Error
	case <-ctx.Done():
		klog.Warning("Invoke reaches the deadline and returns")
		return ctx.Err()
	}
}
