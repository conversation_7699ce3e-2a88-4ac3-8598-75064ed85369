//go:generate stringer -type=RpcOp -linecomment -output rpc_op_string.go
package datachannel

type RpcOp uint32

const (
	VextentReadOp                RpcOp = iota // VextentRead
	VextentWriteOp                            // VextentWrite
	PextentReadOp                             // PextentRead
	PextentWriteOp                            // PextentWrite
	VolumeReadOp                              // VolumeRead
	VolumeWriteOp                             // VolumeWrite
	RecoverStartOp                            // RecoverStart
	RecoverEndOp                              // RecoverEnd
	GetGenerationOp                           // GetGeneration
	PingOp                                    // Ping
	EchoOp                                    // Echo
	VextentCompareAndWriteOp                  // VextentCompareAndWrite
	VextentCompareAndWriteReadOp              // VextentCompareAndWriteRead
	ShakeOp                                   // Shake
	MaxOp
)

func (i RpcOp) MethodName() string {
	if i >= RpcOp(len(_RpcOp_index)-1) {
		return "DataChannel.Unknown"
	}

	return "DataChannel." + _RpcOp_name[_RpcOp_index[i]:_RpcOp_index[i+1]]
}
