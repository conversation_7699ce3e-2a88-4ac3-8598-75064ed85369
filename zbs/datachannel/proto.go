package datachannel

import (
	"encoding/binary"
	"io"
	"math"

	"github.com/google/uuid"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
)

// MessageHeader should be fixed 80 bytes
type MessageHeader struct {
	Header      // fixed 32 bytes
	Info   Info // fixed 48 bytes (with padding)
}

func (h *MessageHeader) Marshal(w io.Writer) error {
	err := binary.Write(w, binary.LittleEndian, h.<PERSON>)
	if err != nil {
		return err
	}

	return binary.Write(w, binary.LittleEndian, h.Info)
}

func (h *MessageHeader) Unmarshal(r io.Reader) error {
	if err := binary.Read(r, binary.LittleEndian, &h.Header); err != nil {
		return err
	}

	h.SwitchInfo()

	return binary.Read(r, binary.LittleEndian, h.Info)
}

func (h *MessageHeader) SwitchInfo() {
	switch h.Op {
	case PingOp:
		h.Info = &Padding{}
	case VolumeWriteOp, VolumeReadOp:
		h.Info = &VolumeInfo{}
	}
}

type Header struct {
	Version      uint32
	ErrorCode    zbs.ErrorCode
	Op           RpcOp
	BufLen       uint32
	MessageId    uint64
	MaxMessageId uint64
}

const MessageVersion1 uint32 = 0x1001

func NewHeader(op RpcOp, length int, msgId uint64) Header {
	return Header{
		Version:      MessageVersion1,
		ErrorCode:    zbs.ErrorCode_EOK,
		Op:           op,
		BufLen:       uint32(length),
		MessageId:    msgId,
		MaxMessageId: math.MaxUint64,
	}
}

func (h *Header) HasError() bool {
	return h.ErrorCode != zbs.ErrorCode_EOK
}

func (h *Header) SetErrorCode(c zbs.ErrorCode) {
	h.ErrorCode = c
}

type UUID [16]byte

func (u *UUID) String() string {
	id, _ := uuid.FromBytes(u[:])
	return id.String()
}

func ToZBSUUID(str string) (UUID, error) {
	var res UUID

	volumeUUID, err := uuid.Parse(str)
	if err != nil {
		return res, err
	}

	byts, err := volumeUUID.MarshalBinary()
	if err != nil {
		return res, err
	}

	copy(res[:], byts)

	return res, nil
}

type Flags uint32
type IOFlags = Flags

const (
	NO_PROMOTION IOFlags = 1 << iota
	DELTA_WRITE
	NO_STRIPE
	RECOVER_IO
	MIGRATE_IO
	USE_COMPRESS
	ALLOW_SLOW_IO
	PREFER_CAP = 1 << 20
)

type Pid uint32
type Cid uint8
type Opaque [48]byte

// All XXXXInfo struct should be fixed 48 bytes
// and implement Info interface
type Info interface {
	implInfo()
}

type VextentInfo struct {
	VolumeID     UUID
	VextentNo    uint32
	DataLen      uint32
	ExtentOffset uint32
	Flags        Flags
	_            [16]byte
}

type ExtentInfo struct {
	Pid          Pid
	DataLen      uint32
	ExtentOffset uint64
	Generation   uint64
	OriginPid    Pid
	Flags        Flags
	_            [16]byte
}

type GenerationInfo struct {
	Pid         Pid
	OriginPid   Pid
	Generation  uint64
	Epoch       uint64
	OriginEpoch uint64
	Flags       Flags
	_           [12]byte
}

type VolumeInfo struct {
	VolumeID     UUID
	DataLen      uint32
	_            uint32 // c memory alignment
	DataOffset   uint64
	Flags        Flags
	PreferredCid Cid
	_            [11]byte
}

func (i *VolumeInfo) implInfo() {}

type ShakeInfo struct {
	UseCompress uint32
	CprsAlgo    uint32
	_           [40]byte
}

type EchoInfo struct {
	DataLen uint32
	_       [44]byte
}

type Padding struct {
	_ [6]uint64
}

func (p *Padding) implInfo() {}
