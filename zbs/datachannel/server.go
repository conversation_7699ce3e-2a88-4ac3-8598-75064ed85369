package datachannel

import (
	"errors"
	"net"
	"net/rpc"

	"github.com/iomesh/zbs-client-go/utils"
)

type Server struct {
	RpcServer *rpc.Server

	addr        string
	listener    net.Listener
	packageName string
}

func NewServer(addr string) *Server {
	return &Server{
		RpcServer: rpc.NewServer(),

		addr:        addr,
		listener:    nil,
		packageName: "",
	}
}

func (s *Server) Run() error {
	listener, err := net.Listen("tcp", s.addr)
	if err != nil {
		return err
	}

	s.listener = listener

	go func() {
		_ = s.accept()
	}()

	return nil
}

func (s *Server) accept() error {
	for {
		c, err := s.listener.Accept()
		if err != nil {
			return err
		}

		conn := utils.FromConn(c)

		go s.RpcServer.ServeCodec(newServerCodec(conn))
	}
}

func (s *Server) Stop() error {
	var err error
	if s.listener != nil {
		err = s.listener.Close()
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *Server) Addr() (string, error) {
	if s.listener == nil {
		return "", errors.New("server has not been started")
	}

	return s.listener.Addr().String(), nil
}

func (s *Server) RegisterName(name string, rcvr interface{}) error {
	return s.RpcServer.RegisterName(name, rcvr)
}
