package zbs

// StoragePolicy represents the attribute of a storage object
type StoragePolicy struct {
	ReplicaFactor uint32
	ThinProvision bool
	StripeNum     uint32
	StripeSize    uint32
	EC            *StoragePolicyEC
}

func NewDefaultStoragePolicy() *StoragePolicy {
	return &StoragePolicy{
		ReplicaFactor: 2,
		ThinProvision: true,
		StripeNum:     4,
		StripeSize:    256 * 1024,
	}
}

type StoragePolicyEC struct {
	K uint32
	M uint32
}
