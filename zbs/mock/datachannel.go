// Code generated by MockGen. DO NOT EDIT.
// Source: ./zbs/datachannel.go
//
// Generated by this command:
//
//	mockgen -source ./zbs/datachannel.go -destination ./zbs/mock/datachannel.go
//

// Package mock_zbs is a generated GoMock package.
package mock_zbs

import (
	context "context"
	reflect "reflect"

	datachannel "github.com/iomesh/zbs-client-go/zbs/datachannel"
	gomock "go.uber.org/mock/gomock"
)

// MockDataChannel is a mock of DataChannel interface.
type MockDataChannel struct {
	ctrl     *gomock.Controller
	recorder *MockDataChannelMockRecorder
}

// MockDataChannelMockRecorder is the mock recorder for MockDataChannel.
type MockDataChannelMockRecorder struct {
	mock *MockDataChannel
}

// NewMockDataChannel creates a new mock instance.
func NewMockDataChannel(ctrl *gomock.Controller) *MockDataChannel {
	mock := &MockDataChannel{ctrl: ctrl}
	mock.recorder = &MockDataChannelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataChannel) EXPECT() *MockDataChannelMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockDataChannel) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockDataChannelMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockDataChannel)(nil).Close))
}

// Ping mocks base method.
func (m *MockDataChannel) Ping(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Ping indicates an expected call of Ping.
func (mr *MockDataChannelMockRecorder) Ping(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockDataChannel)(nil).Ping), ctx)
}

// VolumeRead mocks base method.
func (m *MockDataChannel) VolumeRead(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags datachannel.Flags) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeRead", ctx, volumeID, buf, length, offset, flags)
	ret0, _ := ret[0].(error)
	return ret0
}

// VolumeRead indicates an expected call of VolumeRead.
func (mr *MockDataChannelMockRecorder) VolumeRead(ctx, volumeID, buf, length, offset, flags any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeRead", reflect.TypeOf((*MockDataChannel)(nil).VolumeRead), ctx, volumeID, buf, length, offset, flags)
}

// VolumeWrite mocks base method.
func (m *MockDataChannel) VolumeWrite(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags datachannel.Flags, preferredCid datachannel.Cid) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeWrite", ctx, volumeID, buf, length, offset, flags, preferredCid)
	ret0, _ := ret[0].(error)
	return ret0
}

// VolumeWrite indicates an expected call of VolumeWrite.
func (mr *MockDataChannelMockRecorder) VolumeWrite(ctx, volumeID, buf, length, offset, flags, preferredCid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeWrite", reflect.TypeOf((*MockDataChannel)(nil).VolumeWrite), ctx, volumeID, buf, length, offset, flags, preferredCid)
}
