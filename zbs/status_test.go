package zbs

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
)

func TestStatusService_GetClusterSummary(t *testing.T) {
	client, server := NewMockServerStatus(t)

	defer stopMockServer(server)

	summary, err := client.Status.GetClusterSummary(context.TODO())
	require.Nil(t, err)
	require.NotNil(t, summary)
	require.NotNil(t, summary.ClusterInfo)

	assert.Equal(t, "test-meta", string(summary.ClusterInfo.Name))
	assert.Greater(t, len(string(summary.ClusterInfo.Desc)), 0)
	assert.Greater(t, len(string(summary.ClusterInfo.Uuid)), 0)
	assert.False(t, *summary.ClusterInfo.IsStretched)
	assert.Zero(t, len(summary.ClusterInfo.PreferredZoneId))
	assert.False(t, *summary.ClusterInfo.ZkUuidRecorded)
	assert.Zero(t, *summary.ValidDataSpace)
	assert.Zero(t, *summary.UsedDataSpace)
	assert.Equal(t, uint32(1), *summary.TotalNodes)
	assert.Equal(t, uint32(1), *summary.HealthyNodes)
	assert.Equal(t, uint32(1), *summary.WarningNodes)
	assert.Equal(t, uint32(0), *summary.ErrorNodes)
	assert.Equal(t, uint32(0), *summary.ConnectingNodes)
	assert.Equal(t, "1********:20131", *summary.Leader)
	assert.Zero(t, len(summary.UnallocatedChunks))
	assert.Zero(t, len(summary.StoragePools))
}

func TestStatusService_GetMetaSummary(t *testing.T) {
	client, server := NewMockServerStatus(t)

	defer func() { _ = server.Stop() }()

	summary, err := client.Status.GetMetaSummary(context.TODO())
	require.Nil(t, err)
	require.NotNil(t, summary)

	assert.True(t, *summary.IsLeader)
	assert.Equal(t, "1********:20131", *summary.Leader)
	assert.Equal(t, "1********", *summary.AliveMetaHosts[0].Ip)
	assert.Equal(t, int32(20131), *summary.AliveMetaHosts[0].Port)
	assert.Equal(t, metav1.MetaStatus_META_RUNNING, *summary.MetaStatus)
}

func TestStatusService_ShowClusterStatus(t *testing.T) {
	client, server := NewMockServerStatus(t)

	defer func() { _ = server.Stop() }()

	status, err := client.Status.ShowClusterStatus(context.TODO())
	require.Nil(t, err)
	require.NotNil(t, status)

	assert.Equal(t, uint64(metav1.ClusterStatus_CLUSTER_HEALTHY), *status.Flags, "cluster not healthy") // nolint:staticcheck
}
