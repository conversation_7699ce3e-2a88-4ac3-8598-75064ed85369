package error

import (
	"errors"
	"fmt"

	"google.golang.org/protobuf/proto"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
)

var (
	ErrUnknown  = errors.New("unknown zrpc error")
	ErrShutdown = errors.New("client is shutting down")
)

// Error wrap the zbs RpcStatus
// it contains some error messages from zbs rpc server
type Error struct {
	St *zbs.RpcStatus
}

func NewError(code zbs.ErrorCode, message string) *Error {
	st := &zbs.RpcStatus{}

	detail := &zbs.RpcErrorDetail{}
	detail.ErrorCode = &code
	detail.ErrorMessage = &message

	ok := false
	st.Ok = &ok

	st.Errors = append(st.Errors, detail)

	return &Error{
		St: st,
	}
}

// Error return human readable error message
func (e *Error) Error() string {
	if len(e.St.Errors) == 0 {
		return ""
	}

	ecPtr := e.St.Errors[0].ErrorCode
	if ecPtr == nil {
		return ""
	}

	ec := int32(*ecPtr)
	ecName, ok := zbs.ErrorCode_name[ec]

	if !ok {
		return ErrUnknown.Error()
	}

	return fmt.Sprintf("[%s] %v", ecName, *e.St.Errors[0].ErrorMessage)
}

// Marshal returns a marshaled buffer from its RpcStatus,
func (e *Error) Marshal() ([]byte, error) {
	return proto.Marshal(e.St)
}

func (e *Error) Status() *zbs.RpcStatus {
	return e.St
}

func (e *Error) EC() zbs.ErrorCode {
	if len(e.St.Errors) == 0 {
		return zbs.ErrorCode_EOK
	}

	return *e.St.Errors[0].ErrorCode
}

func (e *Error) UC() zbs.UserCode {
	return *e.St.UserCode
}
