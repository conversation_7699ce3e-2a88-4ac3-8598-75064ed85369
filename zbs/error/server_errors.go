package error

import (
	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
)

// ServerError wraps the raw zbs error,
// the Error.Error() returns human-readable error message,
// but ServerError.Error() returns machine-readable error codes
type ServerError struct {
	Err *Error
}

func New(code zbs.ErrorCode, message string) *ServerError {
	return &ServerError{
		Err: NewError(code, message),
	}
}

// Error return machine-readable error message
func (e *ServerError) Error() string {
	msg, _ := e.Err.Marshal()
	return string(msg)
}
