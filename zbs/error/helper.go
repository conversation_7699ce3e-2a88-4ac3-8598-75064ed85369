package error

import (
	"errors"

	"google.golang.org/protobuf/proto"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
)

func ParseEC(msg string) zbs.ErrorCode {
	var st zbs.RpcStatus

	err := proto.Unmarshal([]byte(msg), &st)
	if err != nil {
		return zbs.ErrorCode_EProto
	}

	if len(st.Errors) == 0 {
		return zbs.ErrorCode_EOK
	}

	return *st.Errors[0].ErrorCode
}

func toZBSError(err error) (*Error, bool) {
	var serverError *ServerError
	var zbserr *Error

	switch {
	case errors.As(err, &serverError):
		zbserr = serverError.Err
	case errors.As(err, &zbserr):
		// assign to zbserr
	default:
		return nil, false
	}

	return zbserr, true
}

func IsNotFound(err error) bool {
	zbserr, ok := toZBSError(err)
	if !ok {
		return false
	}

	return zbserr.EC() == zbs.ErrorCode_ENotFound
}

func IsUnknownServiceId(err error) bool {
	zbserr, ok := toZBSError(err)
	if !ok {
		return false
	}

	return zbserr.EC() == zbs.ErrorCode_EUnknownSeviceId
}

func IsUnknownMethodId(err error) bool {
	zbserr, ok := toZBSError(err)
	if !ok {
		return false
	}

	return zbserr.EC() == zbs.ErrorCode_EUnknownMethodId
}

func IsRetryable(err error) bool {
	zbserr, ok := toZBSError(err)
	if !ok {
		return false
	}

	return IsRetryableRPCError(zbserr)
}

func IsRetryableRPCError(err *Error) bool {
	if err == nil {
		return false
	}

	switch err.EC() {
	case zbs.ErrorCode_ENotLeader,
		zbs.ErrorCode_ETimedOut,
		zbs.ErrorCode_ENotReady:
		return true
	}

	return IsNetworkErrorCode(err.EC())
}

func IsNetworkErrorCode(ec zbs.ErrorCode) bool {
	switch ec {
	case zbs.ErrorCode_ESocket,
		zbs.ErrorCode_ESocketConnect,
		zbs.ErrorCode_ESocketEOF,
		zbs.ErrorCode_ESocketSelect,
		zbs.ErrorCode_ESocketPoll,
		zbs.ErrorCode_ESocketClosed,
		zbs.ErrorCode_EConnectError,
		zbs.ErrorCode_EChunkConnectUnavailable,
		zbs.ErrorCode_EProtoAsyncClient,
		zbs.ErrorCode_EShutDown,
		// rdma
		zbs.ErrorCode_ERDMAConnect,
		zbs.ErrorCode_ERDMAReject,
		zbs.ErrorCode_ERDMANotMemory,
		zbs.ErrorCode_ERDMARegisterMessages,
		zbs.ErrorCode_ERDMACreateEventChannel,
		zbs.ErrorCode_ERDMACreateID,
		zbs.ErrorCode_ERDMADisconnect,
		zbs.ErrorCode_ERDMACreateQP,
		zbs.ErrorCode_ERDMAEventMissingID,
		zbs.ErrorCode_ERDMAEventMissingVerbs,
		zbs.ErrorCode_ERDMAPostRecv,
		zbs.ErrorCode_ERDMAPostSend,
		zbs.ErrorCode_ERDMAPostRead,
		zbs.ErrorCode_ERDMAPostWrite:
		return true
	}

	return false
}

// When the zbs server returns the following error, it will close the connection with the client.
func IsConnectionClosedRPCError(err *Error) bool {
	if err == nil {
		return false
	}

	switch err.EC() {
	case zbs.ErrorCode_EUnknownMethodId,
		zbs.ErrorCode_EUnknownSeviceId,
		zbs.ErrorCode_ETooLargeMessage,
		zbs.ErrorCode_EBadMessageFormat:
		return true
	}

	return false
}
