package zbs

import (
	"context"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"google.golang.org/grpc"
)

type StatusService struct {
	client metav1.StatusServiceClient
}

func NewStatusService(client grpc.ClientConnInterface) *StatusService {
	return &StatusService{
		client: metav1.NewStatusServiceClient(client),
	}
}

func (s *StatusService) GetClusterSummary(ctx context.Context) (*metav1.ClusterSummary, error) {
	return s.client.GetClusterSummary(ctx, &zbs.Void{})
}

func (s *StatusService) GetMetaSummary(ctx context.Context) (*metav1.MetaSummary, error) {
	return s.client.GetMetaSummary(ctx, &zbs.Void{})
}

func (s *StatusService) GetClusterPerf(ctx context.Context) (*metav1.ClusterPerf, error) {
	return s.client.GetClusterPerf(ctx, &zbs.Void{})
}

func (s *StatusService) ShowClusterStatus(ctx context.Context) (*metav1.ClusterStatus, error) {
	return s.client.ShowClusterStatus(ctx, &zbs.Void{})
}
