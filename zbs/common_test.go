package zbs

import (
	"fmt"
	"math/rand"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"k8s.io/klog"

	"github.com/iomesh/zbs-client-go/zbs/zrpc"
)

func NewMockServerMeta(t *testing.T) (*Client, *zrpc.Server) {
	return NewMockServer(t, "zbs.meta.MetaService", zrpc.NewMetaServer())
}

func NewMockServerStatus(t *testing.T) (*Client, *zrpc.Server) {
	return NewMockServer(t, "zbs.meta.StatusService", zrpc.NewStatusServer())
}

func NewMockServerISCSI(t *testing.T) (*Client, *zrpc.Server) {
	return NewMockServer(t, "zbs.meta.ISCSIService", zrpc.NewISCSIServer())
}

func NewMockServerCDP(t *testing.T) (*Client, *zrpc.Server) {
	return NewMockServer(t, "zbs.meta.CDPService", zrpc.NewCDPServer())
}

func NewMockServer(t *testing.T, name string, rcvr interface{}) (*Client, *zrpc.Server) {
	var server *zrpc.Server
	server = zrpc.NewServer(fmt.Sprintf("127.0.0.1:0"))

	err := server.RegisterName(name, rcvr)
	require.NoErrorf(t, err, "failed to register iscsi service")

	err = server.Run()
	require.NoErrorf(t, err, "failed to create server")

	addrStr, err := server.Addr()
	require.NoError(t, err)
	klog.Infof("mock server listen at %v", addrStr)

	addr, err := server.Addr()
	require.NoError(t, err)
	client, err := newMockClient(addr)
	require.NoErrorf(t, err, "failed to new mock client")

	return client, server
}

func stopMockServer(server *zrpc.Server) {
	pool := zrpc.RpcClientPool()
	pool.Reset()

	if err := server.Stop(); err != nil {
		klog.Errorf("failed to stop mock server, %v", err)
	}
}

func TestMain(m *testing.M) {
	// init rand seed for random string
	rand.New(rand.NewSource(time.Now().UnixNano())) // #nosec
	os.Exit(m.Run())
}
