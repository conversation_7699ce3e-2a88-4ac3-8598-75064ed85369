package zbs

import (
	"context"
	"path/filepath"
	"strings"

	"google.golang.org/grpc"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
)

type NFSService struct {
	Meta *MetaService

	client meta.NFSServiceClient
}

func NewNFSService(client grpc.ClientConnInterface) *NFSService {
	return &NFSService{
		Meta:   NewMetaService(client),
		client: meta.NewNFSServiceClient(client),
	}
}

func (s *NFSService) CloneFile(
	ctx context.Context,
	parentId string,
	name string,
	srcExportName string,
	srcPath string,
	setters ...InodeOptionSetter,
) (*meta.NFSInode, error) {
	return s.CreateInode(ctx, append(setters,
		WithInodeParentId(parentId),
		WithInodeName(name),
		WithInodeSrcExportName(srcExportName),
		WithInodeSrcPath(srcPath),
		WithInodeType(meta.NFSType_FILE))...)
}

func (s *NFSService) CreateFile(ctx context.Context, parentId string, name string, size uint64, setters ...InodeOptionSetter) (*meta.NFSInode, error) {
	return s.CreateInode(ctx, append(setters, WithInodeParentId(parentId), WithInodeName(name), WithInodeType(meta.NFSType_FILE), WithInodeSize(size))...)
}

func (s *NFSService) CreateInode(ctx context.Context, setters ...InodeOptionSetter) (*meta.NFSInode, error) {
	opts := &InodeOptions{}
	for _, apply := range setters {
		apply(opts)
	}

	var srcInodeId = opts.SrcInodeId
	var srcSnapshotId = opts.SrcSnapshotId

	if len(opts.SrcExportName) != 0 {
		if len(opts.SrcPath) != 0 {
			if len(opts.SrcSnapshotName) != 0 {
				snapshot, err := s.GetSnapshotFromName(ctx, string(opts.SrcExportName), string(opts.SrcPath), string(opts.SrcSnapshotName))
				if err != nil {
					return nil, err
				}

				srcSnapshotId = snapshot.Id
			} else {
				inode, err := s.GetInodeFromPath(ctx, string(opts.SrcExportName), string(opts.SrcPath))
				if err != nil {
					return nil, err
				}

				srcInodeId = inode.Id
			}
		}
	}

	req := &meta.CreateInodeRequest{
		ParentId:      opts.ParentId,
		Name:          opts.Name,
		Type:          opts.Type,
		How:           opts.How,
		Sattr3:        opts.Sattr3,
		Createverf3:   opts.Createverf3,
		Xid:           opts.Xid,
		SrcInodeId:    srcInodeId,
		SrcSnapshotId: srcSnapshotId,
		Preallocate:   opts.Preallocate,
		SymlinkPath:   opts.SymlinkPath,
	}

	return s.client.CreateInode(ctx, req)
}

func (s *NFSService) ShowInode(ctx context.Context, inodeId string) (*meta.NFSInode, error) {
	req := &meta.InodeId{
		Id: []byte(inodeId),
	}

	return s.client.ShowInode(ctx, req)
}

func (s *NFSService) LookupInode(ctx context.Context, dirId string, name string) (*meta.NFSInode, error) {
	req := &meta.LookupInodeRequest{
		DirId: []byte(dirId),
		Name:  []byte(name),
	}

	return s.client.LookupInode(ctx, req)
}

func (s *NFSService) WalkToPath(ctx context.Context, inodeId string, path string) (*meta.NFSInode, error) {
	var inode *meta.NFSInode

	if len(path) == 0 {
		return s.ShowInode(ctx, inodeId)
	}

	// normalize the path string
	path = filepath.Clean(path)

	// ignore the leading separator "/"
	if strings.HasPrefix(path, string(filepath.Separator)) {
		path = path[1:]
	}

	pathComps := strings.Split(path, string(filepath.Separator))

	for _, pathComp := range pathComps {
		ret, err := s.LookupInode(ctx, inodeId, pathComp)
		if err != nil {
			return nil, err
		}

		inode = ret
		inodeId = string(inode.Id)
	}

	return inode, nil
}

func (s *NFSService) GetInodeFromPath(ctx context.Context, exportName string, path string) (*meta.NFSInode, error) {
	pool, err := s.Meta.ShowPoolByName(ctx, exportName)
	if err != nil {
		return nil, err
	}

	rootInodeId := pool.Id[:18]

	return s.WalkToPath(ctx, string(rootInodeId), path)
}

func (s *NFSService) GetSnapshotFromName(ctx context.Context, exportName string, path string, snapshotName string) (*meta.Volume, error) {
	inode, err := s.GetInodeFromPath(ctx, exportName, path)
	if err != nil {
		return nil, err
	}

	req := &meta.SnapshotPath{
		VolumePath: &meta.VolumePath{
			VolumeId: inode.Id,
		},
		SnapshotName: []byte(snapshotName),
	}

	return s.Meta.ShowSnapshot(ctx, req)
}
