package zbs

import (
	"strings"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
)

type LunOptionSetter func(*LunOptions)

type LunOptions struct {
	// For create
	Size               uint64
	LunPath            *meta.LunPath
	StoragePolicy      *StoragePolicy
	CloneOpts          *LunCloneOptions
	TargetRequirement  *meta.TargetRequirement
	SingleAccess       bool
	AllowedInitiators  []byte
	Prioritized        *bool
	InodePath          []byte
	ChunkInstancesBits *uint64

	// For update
	NewName              []byte
	NewSize              *uint64
	Description          []byte
	ReplicaNum           *uint32
	ThinProvision        *bool
	Throttling           *meta.IOThrottleConfig
	NewAllowedInitiators []byte
	NewSingleAccess      *bool
}

func WithLunPath(lp *meta.LunPath) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.LunPath = lp
	}
}

func WithLunStoragePolicy(sp *StoragePolicy) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.StoragePolicy = sp
	}
}

func WithLunCloneOpts(co *LunCloneOptions) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.CloneOpts = co
	}
}

func WithLunSize(size uint64) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.Size = size
	}
}

func WithTargetRequirement(tr *meta.TargetRequirement) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.TargetRequirement = tr
	}
}

func WithLunSingleAccess(singleAccess bool) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.SingleAccess = singleAccess
	}
}

func WithLunPrioritized(prioritized bool) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.Prioritized = &prioritized
	}
}

func WithLunAllowedInitiators(allowedInitiators []string) LunOptionSetter {
	return func(opts *LunOptions) {
		if allowedInitiators != nil {
			allowedInitiatorsStr := strings.Join(allowedInitiators, ",")
			opts.AllowedInitiators = []byte(allowedInitiatorsStr)
		}
	}
}

func WithLunNewName(newName string) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.NewName = []byte(newName)
	}
}

func WithLunNewSize(newSize uint64) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.NewSize = &newSize
	}
}

func WithLunDescription(description string) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.Description = []byte(description)
	}
}

func WithLunReplicaNum(rn uint32) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.ReplicaNum = &rn
	}
}

func WithLunThinProvision(tp bool) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.ThinProvision = &tp
	}
}

func WithLunThrottling(tc *meta.IOThrottleConfig) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.Throttling = tc
	}
}

func WithLunNewAllowedInitiators(newAllowedInitiators []string) LunOptionSetter {
	return func(opts *LunOptions) {
		if newAllowedInitiators != nil {
			allowedInitiatorsStr := strings.Join(newAllowedInitiators, ",")
			opts.NewAllowedInitiators = []byte(allowedInitiatorsStr)
		}
	}
}

func WithLunNewSingleAccess(singleAccess bool) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.NewSingleAccess = &singleAccess
	}
}

func WithLunInodePath(inodePath string) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.InodePath = []byte(inodePath)
	}
}

func WithLunChunkInstancesBits(bits uint64) LunOptionSetter {
	return func(opts *LunOptions) {
		opts.ChunkInstancesBits = &bits
	}
}

type NsOptionSetter func(*NsOptions)

type NsOptions struct {
	NsPath               *meta.DistNamespacePath
	Size                 *uint64
	StoragePolicy        *StoragePolicy
	CloneOpts            *NsCloneOptions
	SubsystemRequirement *meta.SubsystemRequirement
	SingleAccess         *bool
	Prioritized          *bool
	NqnWhitelist         []byte
	IsShared             *bool
	Name                 []byte
	Description          []byte
	ReplicaNum           *uint32
	ThinProvision        *bool
	Throttling           *meta.IOThrottleConfig
	NewName              []byte
	ChunkInstancesBits   *uint64
}

func WithNsPath(lp *meta.DistNamespacePath) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.NsPath = lp
	}
}

func WithNsStoragePolicy(sp *StoragePolicy) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.StoragePolicy = sp
	}
}

func WithNsCloneOpts(co *NsCloneOptions) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.CloneOpts = co
	}
}

func WithNsSize(size uint64) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.Size = &size
	}
}

func WithSubsystemRequirement(sr *meta.SubsystemRequirement) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.SubsystemRequirement = sr
	}
}

func WithNsSingleAccess(singleAccess bool) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.SingleAccess = &singleAccess
	}
}

func WithNsPrioritized(prioritized bool) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.Prioritized = &prioritized
	}
}

func WithNsShare(isShare bool) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.IsShared = &isShare
	}
}

func WithNsNqnWhitelist(nqnWhitelist []string) NsOptionSetter {
	return func(opts *NsOptions) {
		if nqnWhitelist != nil {
			NqnWhitelistStr := strings.Join(nqnWhitelist, ",")
			opts.NqnWhitelist = []byte(NqnWhitelistStr)
		}
	}
}

func WithNsDescription(description string) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.Description = []byte(description)
	}
}

func WithNsReplicaNum(rn uint32) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.ReplicaNum = &rn
	}
}

func WithNsThinProvision(tp bool) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.ThinProvision = &tp
	}
}

func WithNsThrottling(tc *meta.IOThrottleConfig) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.Throttling = tc
	}
}

func WithNsNewName(newName string) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.NewName = []byte(newName)
	}
}

func WithNsChunkInstancesBits(bits uint64) NsOptionSetter {
	return func(opts *NsOptions) {
		opts.ChunkInstancesBits = &bits
	}
}

type InodeOptionSetter func(*InodeOptions)

type InodeOptions struct {
	ParentId        []byte
	Name            []byte
	Type            *meta.NFSType
	How             *meta.CreateHow
	Sattr3          *meta.SAttr3
	Createverf3     []byte
	Xid             *uint32
	SrcPath         []byte
	SrcExportName   []byte
	SrcSnapshotName []byte
	SrcInodeId      []byte
	SrcSnapshotId   []byte
	Preallocate     *bool
	SymlinkPath     []byte
}

func WithInodeParentId(parentId string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.ParentId = []byte(parentId)
	}
}

func WithInodeName(name string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.Name = []byte(name)
	}
}

func WithInodeType(t meta.NFSType) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.Type = &t
	}
}

func WithInodeHow(h *meta.CreateHow) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.How = h
	}
}

func WithInodeSize(size uint64) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.Sattr3 = &meta.SAttr3{
			Size: &size,
		}
	}
}

func WithInodeSattr3(sattr3 *meta.SAttr3) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.Sattr3 = sattr3
	}
}

func WithInodeCreateverf3(createverf3 string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.Createverf3 = []byte(createverf3)
	}
}

func WithInodeXid(xid uint32) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.Xid = &xid
	}
}

func WithInodeSrcExportName(srcExportName string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.SrcExportName = []byte(srcExportName)
	}
}

func WithInodeSrcPath(srcPath string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.SrcPath = []byte(srcPath)
	}
}

func WithInodeSrcInodeId(srcInodeId string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.SrcInodeId = []byte(srcInodeId)
	}
}

func WithInodeSrcSnapshotId(srcSnapshotId string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.SrcSnapshotId = []byte(srcSnapshotId)
	}
}

func WithInodePreallocate(preallocate bool) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.Preallocate = &preallocate
	}
}

func WithInodeSymlinkPath(symlinkPath string) InodeOptionSetter {
	return func(opts *InodeOptions) {
		opts.SymlinkPath = []byte(symlinkPath)
	}
}
