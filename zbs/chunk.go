package zbs

import (
	"context"

	zbsv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	chunkv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/chunk"
	"google.golang.org/grpc"
)

type ChunkService struct {
	client chunkv1.ChunkServiceClient
}

func NewChunkService(client grpc.ClientConnInterface) *ChunkService {
	return &ChunkService{
		client: chunkv1.NewChunkServiceClient(client),
	}
}

func (s *ChunkService) GetZbsAddress(ctx context.Context) (*chunkv1.ZbsAddress, error) {
	return s.client.GetZbsAddress(ctx, &zbsv1.Void{})
}

func (s *ChunkService) FormatPartition(ctx context.Context, path string, force bool, ignoreDataChecksum bool) error {
	return s.FormatPartitionV2(ctx, path, force, ignoreDataChecksum, nil)
}

func (s *ChunkService) FormatPartitionV2(ctx context.Context, path string, force bool, ignoreDataChecksum bool, instanceId *uint32) error {
	req := &chunkv1.FormatPartitionRequest{
		Path:               &[]string{path}[0],
		Force:              &[]bool{force}[0],
		IgnoreDataChecksum: &[]bool{ignoreDataChecksum}[0],
		InstanceId:         instanceId,
	}

	_, err := s.client.FormatPartition(ctx, req)

	return err
}

func (s *ChunkService) MountPartition(ctx context.Context, path string, force bool) error {
	return s.MountPartitionV2(ctx, path, force, nil)
}

func (s *ChunkService) MountPartitionV2(ctx context.Context, path string, force bool, instanceId *uint32) error {
	req := &chunkv1.MountPartitionRequest{
		Path:       &[]string{path}[0],
		Force:      &[]bool{force}[0],
		InstanceId: instanceId,
	}

	_, err := s.client.MountPartition(ctx, req)

	return err
}

func (s *ChunkService) UmountPartition(ctx context.Context, path string, force bool) error {
	req := &chunkv1.UmountDiskRequest{
		DiskName:         &[]string{path}[0],
		XDeprecatedForce: &[]bool{force}[0],
	}

	_, err := s.client.UmountPartition(ctx, req)

	return err
}

func (s *ChunkService) ListPartition(ctx context.Context) (*chunkv1.ListPartitionResponse, error) {
	v := zbsv1.Void{}
	return s.client.ListPartition(ctx, &v)
}

func (s *ChunkService) MountCache(ctx context.Context, path string) error {
	return s.MountCacheV2(ctx, path, nil)
}

func (s *ChunkService) MountCacheV2(ctx context.Context, path string, instanceId *uint32) error {
	req := &chunkv1.MountCacheRequest{
		Path:       &[]string{path}[0],
		InstanceId: instanceId,
	}
	_, err := s.client.MountCache(ctx, req)

	return err
}

func (s *ChunkService) UmountCache(ctx context.Context, path string) error {
	req := &chunkv1.UmountDiskRequest{DiskName: &[]string{path}[0]}
	_, err := s.client.UmountCache(ctx, req)

	return err
}

func (s *ChunkService) FormatCache(ctx context.Context, path string, force bool) error {
	return s.FormatCacheV2(ctx, path, force, nil)
}

func (s *ChunkService) FormatCacheV2(ctx context.Context, path string, force bool, instanceId *uint32) error {
	req := &chunkv1.FormatCacheRequest{
		Path:       &[]string{path}[0],
		Force:      &[]bool{force}[0],
		InstanceId: instanceId,
	}
	_, err := s.client.FormatCache(ctx, req)

	return err
}

func (s *ChunkService) ListCache(ctx context.Context) (*chunkv1.ListCacheResponse, error) {
	v := zbsv1.Void{}
	return s.client.ListCache(ctx, &v)
}

func (s *ChunkService) InvalidateCache(ctx context.Context, path string) error {
	req := &chunkv1.DiskRequest{DiskName: &[]string{path}[0]}
	_, err := s.client.InvalidateCache(ctx, req)

	return err
}

func (s *ChunkService) FormatJournal(ctx context.Context, path string, force bool) error {
	return s.FormatJournalV2(ctx, path, force, nil)
}

func (s *ChunkService) FormatJournalV2(ctx context.Context, path string, force bool, instanceId *uint32) error {
	req := &chunkv1.FormatJournalRequest{
		Path:       &[]string{path}[0],
		Force:      &[]bool{force}[0],
		InstanceId: instanceId,
	}
	_, err := s.client.FormatJournal(ctx, req)

	return err
}

func (s *ChunkService) MountJournal(ctx context.Context, path string) error {
	return s.MountJournalV2(ctx, path, nil)
}

func (s *ChunkService) MountJournalV2(ctx context.Context, path string, instanceId *uint32) error {
	req := &chunkv1.MountJournalRequest{
		Path:       &[]string{path}[0],
		InstanceId: instanceId,
	}
	_, err := s.client.MountJournal(ctx, req)

	return err
}

func (s *ChunkService) UmountJournal(ctx context.Context, path string) error {
	req := &chunkv1.UmountDiskRequest{
		DiskName: &[]string{path}[0],
	}
	_, err := s.client.UmountJournal(ctx, req)

	return err
}

func (s *ChunkService) FlushAllJournals(ctx context.Context) error {
	v := zbsv1.Void{}
	_, err := s.client.FlushAllJournals(ctx, &v)

	return err
}

func (s *ChunkService) ListJournal(ctx context.Context) (*chunkv1.ListJournalResponse, error) {
	v := zbsv1.Void{}
	return s.client.ListJournal(ctx, &v)
}

func (s *ChunkService) ListClient(ctx context.Context) (*chunkv1.ListClientResponse, error) {
	v := zbsv1.Void{}
	return s.client.ListClient(ctx, &v)
}

func (s *ChunkService) ListRecover(ctx context.Context) (*zbsv1.ListRecoverResponse, error) {
	v := zbsv1.Void{}
	return s.client.ListRecover(ctx, &v)
}

func (s *ChunkService) ListExtent(ctx context.Context) (*chunkv1.ListExtentResponse, error) {
	v := chunkv1.ListPExtentRequest{}
	return s.client.ListExtent(ctx, &v)
}

func (s *ChunkService) CheckExtent(ctx context.Context, pid uint32) error {
	req := &chunkv1.PExtentRequest{
		Pid: &[]uint32{pid}[0],
	}
	_, err := s.client.CheckExtent(ctx, req)

	return err
}

func (s *ChunkService) CheckAllExtents(ctx context.Context) error {
	v := zbsv1.Void{}
	_, err := s.client.CheckAllExtents(ctx, &v)

	return err
}

func (s *ChunkService) ShowExtent(ctx context.Context, pid uint32) (*chunkv1.ShowExtentResponse, error) {
	return s.ShowExtentV2(ctx, pid, nil)
}

func (s *ChunkService) ShowExtentV2(ctx context.Context, pid uint32, instanceId *uint32) (*chunkv1.ShowExtentResponse, error) {
	req := &chunkv1.ShowExtentRequest{
		Pid:        &[]uint32{pid}[0],
		InstanceId: instanceId,
	}

	return s.client.ShowExtent(ctx, req)
}

func (s *ChunkService) PromoteExtent(ctx context.Context, pid uint32, offset, length uint64) error {
	return s.PromoteExtentV2(ctx, pid, offset, length, nil)
}

func (s *ChunkService) PromoteExtentV2(ctx context.Context, pid uint32, offset, length uint64, instanceId *uint32) error {
	req := &chunkv1.PromoteExtentRequest{
		Pid:        &[]uint32{pid}[0],
		Offset:     &[]uint64{offset}[0],
		Length:     &[]uint64{length}[0],
		InstanceId: instanceId,
	}
	_, err := s.client.PromoteExtent(ctx, req)

	return err
}

func (s *ChunkService) InvalidateExtent(ctx context.Context, req *chunkv1.InvalidateExtentRequest) error {
	_, err := s.client.InvalidateExtent(ctx, req)
	return err
}

func (s *ChunkService) SetVerifyMode(ctx context.Context, req *chunkv1.SetVerifyModeRequest) error {
	_, err := s.client.SetVerifyMode(ctx, req)
	return err
}

func (s *ChunkService) SetUnhealthyPartition(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error {
	_, err := s.client.SetUnhealthyPartition(ctx, req)
	return err
}

func (s *ChunkService) SetHealthyPartition(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error {
	scheme := req.GetScheme()

	dreq := &chunkv1.DiskRequest{
		DiskName: &[]string{req.GetDiskName()}[0],
		Scheme:   &scheme,
	}

	_, err := s.client.SetHealthyPartition(ctx, dreq)

	return err
}

func (s *ChunkService) SetUnhealthyCache(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error {
	_, err := s.client.SetUnhealthyCache(ctx, req)
	return err
}

func (s *ChunkService) SetHealthyCache(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error {
	scheme := req.GetScheme()

	dreq := &chunkv1.DiskRequest{
		DiskName: &[]string{req.GetDiskName()}[0],
		Scheme:   &scheme,
	}

	_, err := s.client.SetHealthyCache(ctx, dreq)

	return err
}

func (s *ChunkService) SetUnhealthyJournal(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error {
	_, err := s.client.SetUnhealthyJournal(ctx, req)
	return err
}

func (s *ChunkService) SetHealthyJournal(ctx context.Context, req *chunkv1.SetUnhealthyRequest) error {
	scheme := req.GetScheme()

	dreq := &chunkv1.DiskRequest{
		DiskName: &[]string{req.GetDiskName()}[0],
		Scheme:   &scheme,
	}

	_, err := s.client.SetHealthyJournal(ctx, dreq)

	return err
}

func (s *ChunkService) QueryDisk(ctx context.Context, path string) (*chunkv1.QueryDiskResponse, error) {
	req := &chunkv1.QueryDiskRequest{Path: &[]string{path}[0]}
	return s.client.QueryDisk(ctx, req)
}

func (s *ChunkService) SummaryInfo(ctx context.Context) (*chunkv1.SummaryInfoResponse, error) {
	v := zbsv1.Void{}
	return s.client.SummaryInfo(ctx, &v)
}

func (s *ChunkService) StopServer(ctx context.Context) error {
	v := zbsv1.Void{}
	_, err := s.client.StopServer(ctx, &v)

	return err
}

func (s *ChunkService) GetChunkServiceStat(ctx context.Context) (*chunkv1.ChunkServiceStat, error) {
	v := zbsv1.Void{}
	return s.client.GetChunkServiceStat(ctx, &v)
}

func (s *ChunkService) StartAurora(ctx context.Context) error {
	v := zbsv1.Void{}
	_, err := s.client.StartAurora(ctx, &v)

	return err
}

func (s *ChunkService) StopAurora(ctx context.Context) error {
	v := zbsv1.Void{}
	_, err := s.client.StopAurora(ctx, &v)

	return err
}

func (s *ChunkService) GetZbsAddressV2(ctx context.Context) (*chunkv1.ZbsAddressV2, error) {
	return s.client.GetZbsAddressV2(ctx, &zbsv1.ChunkInstance{})
}

func (s *ChunkService) ListPartitionV2(ctx context.Context) (*chunkv1.ListPartitionResponseV2, error) {
	return s.client.ListPartitionV2(ctx, &zbsv1.ChunkInstance{})
}

func (s *ChunkService) ListJournalV2(ctx context.Context) (*chunkv1.ListJournalResponseV2, error) {
	return s.client.ListJournalV2(ctx, &zbsv1.ChunkInstance{})
}

func (s *ChunkService) ListCacheV2(ctx context.Context) (*chunkv1.ListCacheResponseV2, error) {
	return s.client.ListCacheV2(ctx, &zbsv1.ChunkInstance{})
}

func (s *ChunkService) ListRecoverV2(ctx context.Context) (*zbsv1.ListRecoverResponseV2, error) {
	return s.client.ListRecoverV2(ctx, &zbsv1.ChunkInstance{})
}

func (s *ChunkService) SummaryInfoV2(ctx context.Context) (*chunkv1.SummaryInfoResponseV2, error) {
	return s.client.SummaryInfoV2(ctx, &zbsv1.ChunkInstance{})
}
