package zbs

import (
	"context"
	"crypto/rand"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
)

const TestVolumeID = "92cad1cb-60c9-447f-bd28-ddc059f560c5"

func NewMockDatachannelServer(t *testing.T) (*DataChannelService, *dc.Server) {
	server := dc.NewServer("127.0.0.1:0")
	d := dc.NewMockService()
	err := server.RegisterName("DataChannel", d)
	assert.Nil(t, err)
	err = server.Run()
	assert.Nil(t, err)
	endpoint, err := server.Addr()
	assert.Nil(t, err)

	config := dc.DefaultClientConfig()
	service, err := NewDataChannelService(endpoint, config)
	assert.Nil(t, err)

	return service, server
}

func TestDataChannelService(t *testing.T) {
	s, server := NewMockDatachannelServer(t)

	defer func() {
		err := server.Stop()
		assert.Nil(t, err)

		err = s.Close()
		assert.Nil(t, err)
	}()

	ctx := context.Background()
	err := s.Ping(ctx)
	assert.Nil(t, err)

	length := uint32(256) // 256kb
	offset := uint64(0)
	writeBuf := make([]byte, length)

	_, err = rand.Read(writeBuf)
	assert.Nil(t, err)

	flags := dc.Flags(0)
	preferredCid := dc.Cid(0)

	err = s.VolumeWrite(ctx, TestVolumeID, writeBuf, length, offset, flags, preferredCid)
	assert.Nil(t, err)

	readBuf := make([]byte, length)

	err = s.VolumeRead(ctx, TestVolumeID, readBuf, length, offset, flags)
	assert.Nil(t, err)
	assert.Equal(t, len(writeBuf), len(readBuf))
	assert.Equal(t, writeBuf, readBuf)

	unexistedVolumeID := "83f04df2-65a9-466c-acb0-f697a21b3804"
	err = s.VolumeRead(ctx, unexistedVolumeID, readBuf, length, offset, flags)
	assert.EqualError(t, err, "[ENotFound] volume [83f04df2-65a9-466c-acb0-f697a21b3804] do not exist")
}

func TestDataChannelServiceConcurrency(t *testing.T) {
	s, server := NewMockDatachannelServer(t)

	defer func() {
		err := server.Stop()
		assert.Nil(t, err)

		err = s.Close()
		assert.Nil(t, err)
	}()

	ctx := context.TODO()
	count := 10
	length := 256 // 256kb
	writeBuf := make([]byte, length*count)
	flags := dc.Flags(0)
	preferredCid := dc.Cid(0)

	_, err := rand.Read(writeBuf)
	assert.Nil(t, err)

	wg := sync.WaitGroup{}

	for i := 0; i < count; i++ {
		offset := i * length

		wg.Add(1)

		go func(offset int) {
			err1 := s.VolumeWrite(ctx, TestVolumeID, writeBuf[offset:offset+length], uint32(length), uint64(offset), flags, preferredCid)
			assert.Nil(t, err1)
			wg.Done()
		}(offset)
	}

	wg.Wait()

	readBuf := make([]byte, length*count)
	err = s.VolumeRead(ctx, TestVolumeID, readBuf, uint32(length*count), 0, flags)
	assert.Nil(t, err)

	assert.Equal(t, len(writeBuf), len(readBuf))
	assert.Equal(t, writeBuf, readBuf)
}

func TestDataChannelServiceTimeoutCancel(t *testing.T) {
	server := dc.NewServer("127.0.0.1:0")

	defer func() {
		err := server.Stop()
		assert.Nil(t, err)
	}()

	d := dc.NewMockService()
	timeout := 6
	d.TimeoutHook = func() {
		fmt.Printf("sleeping for %d secs\n", timeout)
		time.Sleep(time.Duration(timeout) * time.Second)
	}
	err := server.RegisterName("DataChannel", d)
	assert.Nil(t, err)
	err = server.Run()
	assert.Nil(t, err)
	endpoint, err := server.Addr()
	assert.Nil(t, err)

	config := dc.DefaultClientConfig()
	config.RpcTimeoutMS = 2000
	s, err := NewDataChannelService(endpoint, config)
	assert.Nil(t, err)

	defer func() {
		err = s.Close()
		assert.Nil(t, err)
	}()

	ctx := context.TODO()

	length := uint32(256) // 256kb
	offset := uint64(0)
	writeBuf := make([]byte, length)

	_, err = rand.Read(writeBuf)
	assert.Nil(t, err)

	flags := dc.Flags(0)
	preferredCid := dc.Cid(0)

	err = s.VolumeWrite(ctx, TestVolumeID, writeBuf, length, offset, flags, preferredCid)
	assert.EqualError(t, err, "context deadline exceeded")
}
