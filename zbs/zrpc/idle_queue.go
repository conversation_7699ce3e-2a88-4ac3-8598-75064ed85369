package zrpc

import (
	"sync"
	"time"

	"k8s.io/klog"
)

// hold the obj until holder expired, then call closeFunc to close holding obj
type holder struct {
	obj interface{}

	closeFunc func(obj interface{}) error
	expired   bool
	timer     *time.Timer
	locker    sync.Locker
	cancle    chan struct{}

	next *holder
}

func (h *holder) idle(timeout time.Duration) bool {
	h.locker.Lock()
	defer h.locker.Unlock()

	if h.expired {
		return false
	}

	if h.timer != nil {
		return h.timer.Reset(timeout)
	}

	h.timer = time.NewTimer(timeout)
	h.cancle = make(chan struct{})

	go func() {
		select {
		case <-h.timer.C:
			h.locker.Lock()

			if h.closeFunc != nil {
				err := h.closeFunc(h.obj)

				if err != nil {
					klog.Errorf("closing %v failed: %v", h.obj, err)
				}
			}

			h.expired = true
			h.locker.Unlock()
		case <-h.cancle:
		}
	}()

	return true
}

func (h *holder) recover() bool {
	h.locker.Lock()
	defer h.locker.Unlock()

	if h.expired {
		return false
	}

	if h.timer != nil {
		close(h.cancle)
		return h.timer.Stop()
	}

	return true
}

func (h *holder) isExpired() bool {
	h.locker.Lock()
	defer h.locker.Unlock()

	return h.expired
}

func NewIdleQueue() *idleQueue {
	return &idleQueue{
		locker: new(sync.RWMutex),
	}
}

type idleQueue struct {
	head *holder
	tail *holder

	length int
	locker *sync.RWMutex
}

func (q *idleQueue) Push(obj interface{}, closeFunc func(obj interface{}) error, idleTimeout time.Duration) {
	q.locker.Lock()
	defer q.locker.Unlock()

	ele := &holder{
		obj:       obj,
		closeFunc: closeFunc,
		locker:    new(sync.Mutex),
	}

	if !ele.idle(idleTimeout) {
		return
	}

	if q.head == nil && q.tail == nil {
		q.head = ele
		q.tail = ele
	} else if q.tail != nil {
		q.tail.next = ele
		q.tail = ele
	}

	q.length++
}

func (q *idleQueue) pop() *holder {
	q.locker.Lock()
	defer q.locker.Unlock()

	if q.head == nil {
		return nil
	}

	e := q.head

	if q.head.next == nil {
		q.tail = nil
	}

	q.head = q.head.next
	q.length--

	return e
}

func (q *idleQueue) Pop() (interface{}, bool) {
	e := q.pop()

	if e == nil {
		return nil, false
	}

	return e.obj, !e.isExpired()
}

func (q *idleQueue) PopAlive() interface{} {
	e := q.pop()

	for e != nil {
		if !e.recover() {
			e = q.pop()
			continue
		}

		return e.obj
	}

	return nil
}

func (q *idleQueue) Length() int {
	q.locker.RLock()
	defer q.locker.RUnlock()

	return q.length
}
