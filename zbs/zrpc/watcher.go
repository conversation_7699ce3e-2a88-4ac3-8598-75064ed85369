package zrpc

import (
	"context"
	"fmt"
	"log"
	"net"
	"time"

	"github.com/go-zookeeper/zk"
	"github.com/jjeffery/stringset"

	"github.com/iomesh/zbs-client-go/utils"
)

// ZkWatcher helps to watch ZooKeeper more easily
type ZkWatcher struct {
	Conn           *zk.Conn      // zk client connection
	servers        []string      // zk server address list
	logger         zk.Logger     // zk client logger
	sessionTimeout time.Duration // session timeout
	tickInterval   time.Duration // tick interval

	watchPath      string   // the path to be watched
	cachedValue    []byte   // cached values
	cachedChildren []string // cached children
}

// WatcherConfig can be used for watcher config
type WatcherConfig = func(w *ZkWatcher)

// DefaultZooKeeperLogger is the default logger for ZooKeeper client
type DefaultZooKeeperLogger struct{}

// Printf can redirect format and args to std log
func (l *DefaultZooKeeperLogger) Printf(format string, args ...interface{}) {
	log.Printf(format, args...)
}

// NewZkWatcher creates a new watcher
func NewZkWatcher(servers []string, watchPath string, configs ...WatcherConfig) *ZkWatcher {
	watcher := &ZkWatcher{
		watchPath: watchPath,
		servers:   servers,
	}

	applyDefaultConfig := DefaultWatcherConfig()
	applyDefaultConfig(watcher)

	for _, apply := range configs {
		apply(watcher)
	}

	return watcher
}

// DefaultWatcherConfig creates a default Config
func DefaultWatcherConfig() WatcherConfig {
	return func(w *ZkWatcher) {
		w.tickInterval = 100 * time.Millisecond
		w.sessionTimeout = 16 * time.Second
		w.logger = &DefaultZooKeeperLogger{}
	}
}

// WatchChildren will activate watcher to work
// when children under path changes, receiver will get the new value
func (w *ZkWatcher) WatchChildren(ctx context.Context, receiver chan<- []string) error {
	return w.doWatchChildren(ctx, func(children []string) {
		receiver <- children
	})
}

// WatchChildrenCb will activate watcher to work
// when children under path changes, cb will be called
func (w *ZkWatcher) WatchChildrenCb(ctx context.Context, cb func(val []string)) error {
	return w.doWatchChildren(ctx, cb)
}

// RunChildren will activate watcher to work
// when children under path changes, it'll be cached in watcher
func (w *ZkWatcher) RunChildren(ctx context.Context) error {
	return w.doWatchChildren(ctx, func(children []string) {
		w.cachedChildren = children
	})
}

func (w *ZkWatcher) doWatchChildren(ctx context.Context, cb func(children []string)) error {
	go func() {
		for {
			select {
			case <-ctx.Done():
				w.close()
				return
			default:
			}

			if w.Conn == nil {
				err := w.connect()
				if err != nil {
					w.Log("failed to connect, %v", err)
					continue
				}
			}

			children, _, watchCh, err := w.Conn.ChildrenW(w.watchPath)
			if err == zk.ErrSessionExpired || err == zk.ErrConnectionClosed {
				w.Log("reconnecting due to %v", err)
				w.close()

				continue
			}

			if err == zk.ErrNoNode {
				continue
			}

			if err != nil {
				w.Log("unexpected error during watch children: %v", err)
				continue
			}

			// notify new children
			cb(children)

			// wait for children changed
			select {
			case <-ctx.Done():
				w.close()
				return
			case <-watchCh:
				break
			}
		}
	}()

	return nil
}

// RunValue will activate watcher to work
// when value under path changes, it'll be cached in watcher
func (w *ZkWatcher) RunValue(ctx context.Context) error {
	return w.doWatchValue(ctx, func(val []byte) {
		w.cachedValue = val
	})
}

// WatchValue will activate watcher to work
// when value under path changes, receiver will get the new value
func (w *ZkWatcher) WatchValue(ctx context.Context, receiver chan<- []byte) error {
	return w.doWatchValue(ctx, func(val []byte) {
		receiver <- val
	})
}

// WatchValueCb will activate watcher to work
// when value under path changes, cb will be called
func (w *ZkWatcher) WatchValueCb(ctx context.Context, cb func(val []byte)) error {
	return w.doWatchValue(ctx, cb)
}

func (w *ZkWatcher) doWatchValue(ctx context.Context, cb func(val []byte)) error {
	go func() {
		for {
			select {
			case <-ctx.Done():
				w.close()
				return
			default:
			}

			if w.Conn == nil {
				err := w.connect()
				if err != nil {
					w.Log("failed to connect, %v", err)
					continue
				}
			}

			val, _, watchCh, err := w.Conn.GetW(w.watchPath)
			if err == zk.ErrSessionExpired || err == zk.ErrConnectionClosed {
				w.Log("reconnecting due to %v", err)
				w.close()

				continue
			}

			if err == zk.ErrNoNode {
				continue
			}

			if err != nil {
				w.Log("unexpected error during watch value: %v", err)
				continue
			}

			// notify new value
			cb(val)

			// wait for value changed
			select {
			case <-ctx.Done():
				w.close()
				return
			case <-watchCh:
				break
			}
		}
	}()

	return nil
}

// Value returns the copy of cached value
func (w *ZkWatcher) Value() []byte {
	v := make([]byte, len(w.cachedValue))
	copy(v, w.cachedValue)

	return v
}

// String returns the string typed copy of field
func (w *ZkWatcher) String() string {
	return string(w.cachedValue)
}

// Children returns the copy of watched children
func (w *ZkWatcher) Children() []string {
	v := make([]string, len(w.cachedChildren))
	copy(v, w.cachedChildren)

	return v
}

// WatchPath returns the watch path currently using
func (w *ZkWatcher) WatchPath() string {
	return w.watchPath
}

func (w *ZkWatcher) connect() error {
	var err error
	var conn *zk.Conn

	if w.Conn != nil {
		return fmt.Errorf("zk watcher already connected")
	}

	conn, _, err = zk.Connect(w.servers, w.sessionTimeout,
		zk.WithLogger(w.logger),
		zk.WithHostProvider(NewZDNSProvider(w.Log)),
	)
	if err != nil {
		return fmt.Errorf("failed to connect to ZooKeeper, %v", err)
	}

	w.Conn = conn

	return nil
}

func (w *ZkWatcher) close() {
	if w.Conn == nil {
		return
	}

	w.Conn.Close()
	w.Conn = nil
	w.Log("zk watcher closed")

	return
}

// Log calls watcher logger to log
func (w *ZkWatcher) Log(format string, args ...interface{}) {
	if w.logger != nil {
		w.logger.Printf(format, args...)
	}
}

// ZDNSProvider provides a DNS resolver. When address resolved rollback, ZDNSProvider will flush all cached addresses
type ZDNSProvider struct {
	zkHosts []string
	addrs   []string
	cur     int

	log        func(format string, args ...interface{})
	LookupHost func(host string) (addrs []string, err error) // inject lookup host func for testing
}

func NewZDNSProvider(log func(format string, args ...interface{})) *ZDNSProvider {
	return &ZDNSProvider{
		log: log,
		cur: -1,
	}
}

// Init is called first, with the servers specified in the connection string
func (p *ZDNSProvider) Init(zkHosts []string) error {
	p.setZkHosts(zkHosts)
	p.flushAddrs()

	if len(p.addrs) == 0 {
		return fmt.Errorf("no hosts found for addresses %q", zkHosts)
	}

	return nil
}

// Len returns the number of servers
func (p *ZDNSProvider) Len() int {
	return len(p.addrs)
}

// Next returns the next server to connect to. retryStart will be true if we've looped through
// all known servers without Connected() being called
func (p *ZDNSProvider) Next() (string, bool) {
	p.cur++
	rollover := p.cur >= len(p.addrs)

	if rollover {
		p.cur = 0
		p.flushAddrs()
	}

	return p.addrs[p.cur], rollover
}

// Connected will be notified the HostProvider of a successful connection
func (p *ZDNSProvider) Connected() {}

func (p *ZDNSProvider) setZkHosts(zkHosts []string) {
	hosts := make([]string, len(zkHosts))
	copy(hosts, zkHosts)
	shuffleStrings(hosts)
	p.zkHosts = hosts
}

func (p *ZDNSProvider) flushAddrs() {
	lookupHost := p.LookupHost
	if lookupHost == nil {
		lookupHost = net.LookupHost
	}

	res := stringset.New()

	for _, zkHost := range p.zkHosts {
		host, port, err := net.SplitHostPort(zkHost)
		if err != nil {
			p.log("failed to split host port, %v", err)
			continue
		}

		addrs, err := lookupHost(host)
		if err != nil {
			p.log("failed to lookup host(%s), %v", host, err)
			continue
		}

		for _, addr := range addrs {
			res.Add(net.JoinHostPort(addr, port))
		}
	}

	if res.Len() == 0 {
		p.log("failed to flush addresses, no hosts found for zk_hosts")
		return
	}

	p.addrs = res.Values()
}

func shuffleStrings(strs []string) {
	if len(strs) <= 1 {
		return
	}

	for i := range strs {
		n := utils.RandIntn(len(strs) - 1)
		strs[i], strs[n] = strs[n], strs[i]
	}
}
