package zrpc

import (
	"bytes"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"google.golang.org/protobuf/proto"

	"github.com/google/uuid"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/utils"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

type iSCSIServer struct {
	mutex                 sync.Mutex
	targets               map[string]*meta.ISCSITarget
	lunSecondaryMap       map[string]*meta.ISCSILun
	snapshots             map[string][]*meta.Volume
	snapshotsSecondaryMap map[string]*meta.Volume
}

func NewISCSIServer() *iSCSIServer {
	return &iSCSIServer{
		targets:               make(map[string]*meta.ISCSITarget),
		snapshots:             make(map[string][]*meta.Volume),
		lunSecondaryMap:       make(map[string]*meta.ISCSILun),
		snapshotsSecondaryMap: make(map[string]*meta.Volume),
	}
}

func (i *iSCSIServer) CreateTarget(req *meta.CreateISCSITargetRequest, rsp *meta.ISCSITarget) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	targetName := string(req.GetName())
	if targetName == "" {
		return zbserror.New(zbs.ErrorCode_EBadArgument, "target name is empty")
	}

	_, ok := i.targets[targetName]
	if ok {
		return zbserror.New(zbs.ErrorCode_EDuplicate, fmt.Sprintf("target %v duplicate", targetName))
	}

	rsp.Name = req.GetName()

	rsp.Id = req.GetName()

	rsp.IqnDate = []byte(time.Now().String())

	rsp.Description = req.GetName()

	if len(req.GetDescription()) != 0 {
		rsp.Description = req.GetDescription()
	}

	if req.GetLabels() != nil {
		rsp.Labels = req.GetLabels()
	}

	rsp.InitiatorChap = make([]*meta.InitiatorChapInfo, 0)
	rsp.IqnWhitelistV2 = req.GetIqnWhitelistV2()
	rsp.Luns = make([]*meta.ISCSILun, 0)
	rsp.IqnName = req.GetName()
	rsp.Pool = &meta.Pool{
		Name: req.GetName(),
	}
	i.targets[targetName] = &meta.ISCSITarget{}

	proto.Merge(i.targets[targetName], rsp)

	return returnErrorHook(nil)
}

func (i *iSCSIServer) UpdateTarget(req *meta.UpdateISCSITargetRequest, rsp *meta.ISCSITarget) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	t, err := i.getTarget(req.GetPoolPath())
	if err != nil {
		return returnErrorHook(err)
	}

	if req.Name != nil {
		t.Name = req.Name
		t.Pool.Name = req.Name
	}

	if req.IqnWhitelistV2 != nil {
		t.IqnWhitelistV2 = req.IqnWhitelistV2
	}

	if req.Whitelist != nil {
		t.Pool.Whitelist = req.Whitelist
	}

	proto.Merge(rsp, t)

	return returnErrorHook(nil)
}

func (i *iSCSIServer) GetTarget(req *meta.PoolPath, rsp *meta.ISCSITarget) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	t, err := i.getTarget(req)

	if err != nil {
		return err
	}

	proto.Merge(rsp, t)

	return nil
}

func (i *iSCSIServer) getLunByUUID(uuid string) (*meta.ISCSILun, error) {
	for _, target := range i.targets {
		for _, lun := range target.GetLuns() {
			if string(lun.GetVolumeId()) == uuid {
				return lun, nil
			}
		}
	}

	return nil, zbserror.New(zbs.ErrorCode_ENotFound, "lun not found")
}

func (i *iSCSIServer) getTarget(path *meta.PoolPath) (*meta.ISCSITarget, error) {
	targetName := string(path.GetPoolName())
	if targetName == "" {
		targetName = string(path.GetPoolId())
	}

	t, ok := i.targets[targetName]
	if !ok {
		return nil, zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("target %v not found", targetName))
	}

	return t, nil
}

func (i *iSCSIServer) DeleteTarget(req *meta.PoolPath, rsp *zbs.Void) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	t, err := i.getTarget(req)

	if err != nil {
		return err
	}

	delete(i.targets, string(t.GetName()))

	return nil
}

func (i *iSCSIServer) ListTargets(req *meta.ListTargetsRequest, rsp *meta.ISCSITargetsResponse) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	var totalNum uint32 = uint32(len(i.targets))
	rsp.TotalNum = &totalNum

	for _, t := range i.targets {
		tCopy := &meta.ISCSITarget{}

		proto.Merge(tCopy, t)

		rsp.Targets = append(rsp.Targets, tCopy)
	}

	return returnErrorHook(nil)
}

func (i *iSCSIServer) getLunBySecondaryId(secondaryId string) (*meta.ISCSILun, error) {
	lun, ok := i.lunSecondaryMap[secondaryId]
	if ok {
		return lun, nil
	}

	return nil, zbserror.New(zbs.ErrorCode_ENotFound, "lun not found")
}

func (i *iSCSIServer) getLun(path *meta.LunPath) (*meta.ISCSILun, error) {
	if len(path.GetLunUuid()) > 0 {
		return i.getLunByUUID(string(path.GetLunUuid()))
	}

	if len(path.SecondaryId) > 0 {
		return i.getLunBySecondaryId(string(path.SecondaryId))
	}

	target, err := i.getTarget(path.GetPoolPath())

	if err != nil {
		return nil, err
	}

	for _, lun := range target.Luns {
		if bytes.Compare(lun.GetName(), path.LunName) == 0 {
			return lun, nil
		}

		if lun.GetLunId() == path.GetLunId() {
			return lun, nil
		}
	}

	return nil, zbserror.New(zbs.ErrorCode_ENotFound, "lun not found")
}

type labelSelector struct {
	labels *zbs.Labels
}

func (selector *labelSelector) Matches(targetLabels *zbs.Labels) bool {
	if len(targetLabels.GetLabels()) < len(selector.labels.GetLabels()) {
		return false
	}

	if len(selector.labels.GetLabels()) == 0 && len(targetLabels.GetLabels()) > 0 {
		return false
	}

	labelMap := make(map[string]string)

	for _, label := range targetLabels.GetLabels() {
		labelMap[string(label.GetKey())] = string(label.GetValue())
	}

	for _, label := range selector.labels.GetLabels() {
		expectLabelValue, ok := labelMap[string(label.GetKey())]
		if !ok {
			return false
		}

		if expectLabelValue != string(label.GetValue()) {
			return false
		}
	}

	return true
}

func (i *iSCSIServer) getTargetAndLunWithRequirement(lunPath *meta.LunPath,
	targetRequirement *meta.TargetRequirement) (*meta.ISCSITarget, *uint32) {
	var lunID *uint32

	selector := &labelSelector{
		labels: targetRequirement.GetLabels(),
	}

	for _, target := range i.targets {
		if !selector.Matches(target.GetLabels()) {
			continue
		}

		if targetRequirement.ExternalUse != nil && targetRequirement.GetExternalUse() != target.GetExternalUse() {
			continue
		}

		duplicate := false

		for _, lun := range target.Luns {
			if lun.GetLunId() == lunPath.GetLunId() || string(lunPath.GetLunName()) == string(lun.GetName()) {
				duplicate = true
				break
			}
		}

		if duplicate {
			continue
		}

		if lunPath.GetLunId() == 0 {
			lunID = utils.NewUint32(i.findNextLunId(target))
		}

		return target, lunID
	}

	target := &meta.ISCSITarget{}
	target.Name = []byte(uuid.New().String())
	target.Id = target.Name
	target.IqnDate = []byte(time.Now().String())
	target.Description = target.Name
	target.Labels = targetRequirement.GetLabels()
	target.InitiatorChap = make([]*meta.InitiatorChapInfo, 0)
	target.IqnWhitelistV2 = []byte("*/*")
	target.Luns = make([]*meta.ISCSILun, 0)
	target.IqnName = target.GetName()
	i.targets[string(target.Name)] = target

	lunID = utils.NewUint32(1)

	return target, lunID
}

func (i *iSCSIServer) checkLunDuplicate(lunPath *meta.LunPath, hasTargetRequirement bool) error {
	var err error

	if hasTargetRequirement {
		if len(lunPath.GetLunUuid()) == 0 && len(lunPath.GetSecondaryId()) == 0 {
			return zbserror.New(zbs.ErrorCode_EBadArgument,
				"secondary id / uuid must be specified with target requirement")
		}

		if len(lunPath.GetLunUuid()) > 0 {
			_, err = i.getLunByUUID(string(lunPath.GetLunUuid()))
		}

		if len(lunPath.GetSecondaryId()) > 0 {
			_, err = i.getLunBySecondaryId(string(lunPath.GetSecondaryId()))
		}
	} else {
		_, err = i.getLun(lunPath)
	}

	if err == nil {
		return zbserror.New(zbs.ErrorCode_EDuplicate, "lun duplicate")
	}

	if !zbserror.IsNotFound(err) {
		return err
	}

	return nil
}

func checkForSingleAccess(isSingleAccess bool, allowedInitiatorsBytes []byte) error {
	if !isSingleAccess {
		return nil
	}

	allowedInitiators := strings.Split(string(allowedInitiatorsBytes), ",")

	if len(allowedInitiators) > 1 {
		return errors.New("singleAccess is true but len(allowedInitiators) > 1")
	}

	if len(allowedInitiators) == 1 && allowedInitiators[0] == "*/*" {
		return errors.New("singleAccess is true but `*/*` in allowedInitiators")
	}

	return nil
}

func (i *iSCSIServer) CreateLun(req *meta.CreateISCSILunRequest, rsp *meta.ISCSILun) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	err := i.checkLunDuplicate(req.GetLunPath(), req.GetTargetRequirement() != nil)
	if err != nil {
		return returnErrorHook(err)
	}

	err = checkForSingleAccess(req.GetSingleAccess(), req.GetAllowedInitiators())
	if err != nil {
		return err
	}

	var target *meta.ISCSITarget

	lun := &meta.ISCSILun{}

	if len(req.LunPath.LunName) == 0 {
		lun.Name = []byte(uuid.New().String())
	} else {
		lun.Name = req.LunPath.LunName
	}

	if req.GetTargetRequirement() != nil {
		target, lun.LunId = i.getTargetAndLunWithRequirement(req.GetLunPath(),
			req.GetTargetRequirement())
	} else {
		target, err = i.getTarget(req.LunPath.GetPoolPath())
		if req.LunPath.LunId == nil || *req.LunPath.LunId == 0 {
			lunId := i.findNextLunId(target)
			lun.LunId = &lunId
		} else {
			lun.LunId = req.LunPath.LunId
		}

		if err != nil {
			return returnErrorHook(err)
		}
	}

	lun.Size = req.Size

	if len(req.GetSrcSnapshotId()) > 0 {
		snapshotPath := &meta.ISCSISnapshotPath{
			SnapshotId: req.GetSrcSnapshotId(),
		}

		srcSnapshot, err := i.getSnapshot(snapshotPath)
		if err != nil {
			return returnErrorHook(err)
		}

		lun.Size = srcSnapshot.Size
	} else if req.SrcLunPath != nil {
		srcLun, err := i.getLun(req.SrcLunPath)
		if err != nil {
			return returnErrorHook(err)
		}

		lun.Size = srcLun.Size
	}

	if req.GetAllowedInitiators() != nil {
		lun.AllowedInitiators = req.GetAllowedInitiators()
	}

	lun.PoolId = target.Id

	if len(req.LunPath.LunUuid) == 0 {
		lun.VolumeId = []byte(uuid.New().String())
	} else {
		lun.VolumeId = req.LunPath.LunUuid
	}

	var second int64 = time.Now().Unix()

	var nsecond int64 = 0

	lun.CreatedTime = &zbs.TimeSpec{
		Seconds:  &second,
		Nseconds: &nsecond,
	}

	lun.Description = req.Description
	lun.ReplicaNum = req.ReplicaNum
	lun.ThinProvision = req.ThinProvision
	lun.StripeNum = req.StripeNum
	lun.StripeSize = req.StripeSize
	lun.SingleAccess = req.SingleAccess

	proto.Merge(rsp, lun)

	target.Luns = append(target.Luns, lun)

	if len(req.LunPath.SecondaryId) > 0 {
		i.lunSecondaryMap[string(req.LunPath.SecondaryId)] = lun
	}

	return nil
}

func (i *iSCSIServer) findNextLunId(target *meta.ISCSITarget) uint32 {
	lunIdMap := make(map[uint32]bool)

	for _, lun := range target.Luns {
		lunIdMap[*lun.LunId] = true
	}

	var lunId uint32

	for lunId = 1; lunId <= 255; lunId++ {
		_, ok := lunIdMap[lunId]
		if !ok {
			break
		}
	}

	return lunId
}

func (i *iSCSIServer) GetLun(lunPath *meta.LunPath, rsp *meta.ISCSILun) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	lun, err := i.getLun(lunPath)

	if err != nil {
		return err
	}

	proto.Merge(rsp, lun)

	return nil
}

func (i *iSCSIServer) UpdateLun(req *meta.UpdateISCSILunRequest, rsp *meta.ISCSILun) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	lun, err := i.getLun(req.LunPath)
	if err != nil {
		return returnErrorHook(err)
	}

	if err := checkForSingleAccess(lun.GetSingleAccess(), req.GetNewAllowedInitiators()); err != nil {
		return err
	}

	if err := checkForSingleAccess(req.GetSingleAccess(), req.GetNewAllowedInitiators()); err != nil {
		return err
	}

	if err := checkForSingleAccess(req.GetSingleAccess(), lun.GetAllowedInitiators()); err != nil {
		return err
	}

	if req.Size != nil {
		if *req.Size == 0 {
			return returnErrorHook(zbserror.New(
				zbs.ErrorCode_EInvalidArgument, "Lun size should not be 0"))
		}

		lun.Size = req.Size
	}

	if req.NewName != nil {
		lun.Name = req.NewName
	}

	if req.Description != nil {
		lun.Description = req.Description
	}

	if req.ThinProvision != nil {
		lun.ThinProvision = req.ThinProvision
	}

	if req.ReplicaNum == nil {
		lun.ReplicaNum = req.ReplicaNum
	}

	if req.Throttling != nil {
		lun.Throttling = req.Throttling
	}

	if req.NewAllowedInitiators != nil {
		lun.AllowedInitiators = req.NewAllowedInitiators
	}

	if req.SingleAccess != nil {
		lun.SingleAccess = req.SingleAccess
	}

	proto.Merge(rsp, lun)

	return returnErrorHook(nil)
}

func (i *iSCSIServer) DeleteLun(lunPath *meta.LunPath, rsp *zbs.Void) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	lun, err := i.getLun(lunPath)
	if err != nil {
		return nil
	}

	target := i.targets[string(lun.GetPoolId())]

	luns := make([]*meta.ISCSILun, 0)

	for _, l := range target.Luns {
		if lun == l {
			continue
		}

		luns = append(luns, l)
	}

	target.Luns = luns

	for secondaryId, l := range i.lunSecondaryMap {
		if l == lun {
			delete(i.lunSecondaryMap, secondaryId)
			break
		}
	}

	return nil
}

func (i *iSCSIServer) ListLun(poolPath *meta.PoolPath, rsp *meta.ISCSILunsResponse) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	target, err := i.getTarget(poolPath)

	if err != nil {
		return err
	}

	var totalNum uint32 = uint32(len(target.Luns))

	rsp.TotalNum = &totalNum

	for _, lun := range target.Luns {
		lunCopy := &meta.ISCSILun{}
		proto.Merge(lunCopy, lun)
		rsp.Luns = append(rsp.Luns, lunCopy)
	}

	return nil
}

func (i *iSCSIServer) AddLunAllowedInitiators(req *meta.AddLunAllowedInitiatorsRequest, rsp *meta.ISCSILun) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	lun, err := i.getLun(req.GetLunPath())
	if err != nil {
		return err
	}

	initiators := make([]string, 0)
	if len(lun.AllowedInitiators) > 0 {
		initiators = strings.Split(string(lun.AllowedInitiators), ",")
	}

	newInitiators := strings.Split(string(req.GetNewAllowedInitiators()), ",")

	needToAddInitiator := make(map[string]bool)

	for _, initiator := range initiators {
		needToAddInitiator[initiator] = true
	}

	for _, initiator := range newInitiators {
		needToAddInitiator[initiator] = true
	}

	initiators = make([]string, 0)

	for initiator := range needToAddInitiator {
		initiators = append(initiators, initiator)
	}

	lun.AllowedInitiators = []byte(strings.Join(initiators, ","))

	proto.Merge(rsp, lun)

	return nil
}

func (i *iSCSIServer) RemoveLunAllowedInitiators(req *meta.RemoveLunAllowedInitiatorsRequest, rsp *meta.ISCSILun) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	lun, err := i.getLun(req.GetLunPath())
	if err != nil {
		return err
	}

	initiators := make([]string, 0)
	if len(lun.AllowedInitiators) > 0 {
		initiators = strings.Split(string(lun.AllowedInitiators), ",")
	}

	removeInitiators := strings.Split(string(req.GetInitiators()), ",")

	needToAddInitiator := make(map[string]bool)

	for _, initiator := range initiators {
		needToAddInitiator[initiator] = true
	}

	for _, initiator := range removeInitiators {
		delete(needToAddInitiator, initiator)
	}

	initiators = make([]string, 0)

	for initiator := range needToAddInitiator {
		initiators = append(initiators, initiator)
	}

	lun.AllowedInitiators = []byte(strings.Join(initiators, ","))

	proto.Merge(rsp, lun)

	return nil
}

func (i *iSCSIServer) getSnapshot(path *meta.ISCSISnapshotPath) (*meta.Volume, error) {
	if len(path.SecondaryId) > 0 {
		snapshot, ok := i.snapshotsSecondaryMap[string(path.SecondaryId)]
		if ok {
			return snapshot, nil
		}
	}

	lunPath := path.GetLunPath()
	if lunPath != nil {
		key := string(path.GetLunPath().PoolPath.GetPoolId())

		snapshots, ok := i.snapshots[key]
		if !ok {
			return nil, zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("target %v not found", key))
		}

		for _, snapshot := range snapshots {
			if bytes.Compare(snapshot.GetName(), path.GetSnapshotName()) == 0 {
				return snapshot, nil
			}
		}
	}

	for _, snapshots := range i.snapshots {
		for _, snapshot := range snapshots {
			if bytes.Compare(snapshot.Id, path.GetSnapshotId()) == 0 {
				return snapshot, nil
			}
		}
	}

	return nil, zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("snapshot %+v not found", path))
}

func (i *iSCSIServer) CreateSnapshot(req *meta.CreateISCSISnapshotRequest, rsp *meta.Volume) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	snapshotPath := &meta.ISCSISnapshotPath{
		SnapshotName: req.GetSnapshotName(),
		SecondaryId:  req.GetSecondaryId(),
		LunPath:      req.GetLunPath(),
	}

	_, err := i.getSnapshot(snapshotPath)
	if err == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_EDuplicate, "snapshot duplicate"))
	}

	if !zbserror.IsNotFound(err) {
		return returnErrorHook(err)
	}

	lun, err := i.getLun(req.GetLunPath())
	if err != nil {
		return returnErrorHook(err)
	}

	rsp.Name = req.SnapshotName
	rsp.Size = lun.Size
	rsp.CreatedTime = &zbs.TimeSpec{
		Seconds:  utils.NewInt64(time.Now().Unix()),
		Nseconds: utils.NewInt64(time.Now().UnixNano()),
	}

	rsp.Id = []byte(uuid.New().String())
	rsp.SnapshotPoolId = lun.GetPoolId()
	rsp.ParentId = lun.GetVolumeId()
	rsp.ReplicaNum = utils.NewUint32(lun.GetReplicaNum())
	rsp.ThinProvision = utils.NewBool(lun.GetThinProvision())
	rsp.ReadOnly = utils.NewBool(true)
	rsp.Throttling = lun.GetThrottling()
	rsp.IsSnapshot = utils.NewBool(true)
	rsp.SnapshotPoolId = lun.GetPoolId()

	key := string(lun.GetPoolId())

	_, ok := i.snapshots[key]
	if !ok {
		i.snapshots[key] = make([]*meta.Volume, 0)
	}

	snapshot := &meta.Volume{}

	proto.Merge(snapshot, rsp)

	i.snapshots[key] = append(i.snapshots[key], snapshot)
	if len(req.SecondaryId) > 0 {
		i.snapshotsSecondaryMap[string(req.SecondaryId)] = snapshot
	}

	return returnErrorHook(nil)
}

func (i *iSCSIServer) DeleteSnapshot(req *meta.ISCSISnapshotPath, rsp *zbs.Void) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	snapshot, err := i.getSnapshot(req)
	if err != nil {
		return returnErrorHook(err)
	}

	key := string(snapshot.SnapshotPoolId)
	oldSnapshots, _ := i.snapshots[key]
	snapshots := make([]*meta.Volume, 0)

	for _, s := range oldSnapshots {
		if s != snapshot {
			snapshots = append(snapshots, s)
		}
	}

	for secondaryId, s := range i.snapshotsSecondaryMap {
		if s == snapshot {
			delete(i.snapshotsSecondaryMap, secondaryId)
			break
		}
	}

	i.snapshots[key] = snapshots

	return returnErrorHook(nil)
}

func (i *iSCSIServer) ShowSnapshot(req *meta.ISCSISnapshotPath, rsp *meta.Volume) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	snapshot, err := i.getSnapshot(req)
	if err != nil {
		return returnErrorHook(err)
	}

	proto.Merge(rsp, snapshot)

	return returnErrorHook(nil)
}

func (i *iSCSIServer) ListSnapshot(req *meta.LunPath, rsp *meta.SnapshotsResponse) error {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	key := string(req.GetPoolPath().GetPoolId())

	snapshots, ok := i.snapshots[key]
	if !ok {
		return returnErrorHook(
			zbserror.New(zbs.ErrorCode_ENotFound,
				fmt.Sprintf("target %v not found", key)))
	}

	lun, err := i.getLun(req)
	if err != nil {
		if zbserror.IsNotFound(err) {
			lun = nil
		} else {
			return returnErrorHook(err)
		}
	}

	for _, snapshot := range snapshots {
		if lun != nil {
			if bytes.Compare(snapshot.GetOriginId(), lun.GetVolumeId()) == 0 {
				snapshotCopy := &meta.Volume{}
				proto.Merge(snapshotCopy, snapshot)
				rsp.Snapshots = append(rsp.Snapshots, snapshotCopy)
			}
		} else {
			snapshotCopy := &meta.Volume{}
			proto.Merge(snapshotCopy, snapshot)
			rsp.Snapshots = append(rsp.Snapshots, snapshotCopy)
		}
	}

	return returnErrorHook(nil)
}
