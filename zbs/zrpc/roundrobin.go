package zrpc

import (
	"errors"

	"github.com/iomesh/zbs-client-go/utils"
)

type RoundRobin struct {
	targets []string
	idx     int
}

func NewRoundRobin(targets []string) *RoundRobin {
	stringShuffle(targets)
	return &RoundRobin{targets: targets}
}

func (r *RoundRobin) Get() (string, error) {
	if len(r.targets) == 0 {
		return "", errors.New("empty targets")
	}

	target := r.targets[r.idx]
	r.idx = (r.idx + 1) % len(r.targets)

	return target, nil
}

func (r *RoundRobin) Close() {
	// do nothing
}

func stringShuffle(s []string) {
	for i := len(s) - 1; i > 0; i-- {
		j := utils.RandIntn(i + 1)
		s[i], s[j] = s[j], s[i]
	}
}
