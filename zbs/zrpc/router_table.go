package zrpc

import (
	"fmt"
	"strings"

	"github.com/emirpasic/gods/maps/hashbidimap"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/iomesh/zbs-client-go/gen/serviceinfo"
)

// extractPackageNameFromXXX returns the package name extract from service or method
// eg. extractPackageNameFromMethod("zbs.meta.MetaService.CreatePool") returns "zbs.meta"
func extractPackageNameFromMethod(method string) string {
	fields := strings.Split(method, ".")

	packageName := strings.Join(fields[0:len(fields)-2], ".")
	if _, ok := serviceinfo.ProtoFileDescriptorMap[packageName]; !ok {
		return ""
	}

	return packageName
}

func extractPackageFromService(service string) string {
	fields := strings.Split(service, ".")

	packageName := strings.Join(fields[0:len(fields)-1], ".")
	if _, ok := serviceinfo.ProtoFileDescriptorMap[packageName]; !ok {
		return ""
	}

	return packageName
}

type id struct {
	sid int32
	mid int32
}

type routerTable struct {
	// map<pkg:string, map<method:string, {sid, mid}:id>>
	serviceMethodMappings map[string]*hashbidimap.Map
}

func newRouterTable() *routerTable {
	table := routerTable{
		serviceMethodMappings: make(map[string]*hashbidimap.Map),
	}
	table.generateMethodIDMappings()

	return &table
}

// generateMethodIDMappings generates mapping from protoreflect.FileDescriptor
// Example
//
//	servicePackage:          zbs.meta
//	serviceName:             ISCSIService
//	serviceID:               4
//	ISCSIService method [0]: ListTarget
//
// then we have a route info:
//
//	"zbs.mata" => "zbs.mata.ISCSIService.ListTarget" => id{sid: 4, mid: 0}
func (r *routerTable) generateMethodIDMappings() {
	for pkg, fds := range serviceinfo.ProtoFileDescriptorMap {
		for _, fd := range fds {
			servicesLen := fd.Services().Len()
			for i := 0; i < servicesLen; i++ {
				r.generateServiceMethodIDMapping(pkg, fd.Services().Get(i))
			}
		}
	}
}

func (r *routerTable) generateServiceMethodIDMapping(pkg string, svc protoreflect.ServiceDescriptor) {
	if _, ok := r.serviceMethodMappings[pkg]; !ok {
		r.serviceMethodMappings[pkg] = hashbidimap.New()
	}

	sid, ok := serviceinfo.PackageServiceIds[pkg][string(svc.Name())]
	if !ok {
		return
	}

	methodsLen := svc.Methods().Len()

	for mid := 0; mid < methodsLen; mid++ {
		method := svc.Methods().Get(mid)
		serviceName := pkg + "." + string(svc.Name())
		methodName := string(method.Name())

		// net/rpc uses the last '.'  as separator between service name and method name
		serviceMethod := serviceName + "." + methodName
		v := id{sid: sid, mid: int32(mid)}
		r.serviceMethodMappings[pkg].Put(serviceMethod, v)
	}
}

func (r *routerTable) getId(serviceMethod string) (int32, int32, error) {
	// convert gRPC service method name to net/rpc service method name
	serviceMethod = strings.ReplaceAll(strings.TrimLeft(serviceMethod, "/"), "/", ".")
	packageName := extractPackageNameFromMethod(serviceMethod)

	mapping, ok := r.serviceMethodMappings[packageName]
	if !ok {
		return 0, 0, fmt.Errorf("unknown package name: '%s'", packageName)
	}

	v, ok := mapping.Get(serviceMethod)
	if !ok {
		return 0, 0, fmt.Errorf("unknown service method: '%s'", serviceMethod)
	}

	i, ok := v.(id)
	if !ok {
		return 0, 0, fmt.Errorf("internal error: cast interface{} to struct id failed")
	}

	return i.sid, i.mid, nil
}

func (r *routerTable) getMethod(packageName string, sid int32, mid int32) (string, error) {
	mapping, ok := r.serviceMethodMappings[packageName]
	if !ok {
		return "", fmt.Errorf("unknown package name: '%s'", packageName)
	}

	v, ok := mapping.GetKey(id{sid, mid})
	if !ok {
		return "", fmt.Errorf("failed to get method by ID: %+v", id{sid, mid})
	}

	method, ok := v.(string)
	if !ok {
		return "", fmt.Errorf("internal error: cast interface{} to string failed")
	}

	return method, nil
}

type PortRouter struct {
	metaPort        int
	portOffsetTable map[string]int
}

func NewPortRouter(metaPort *int) *PortRouter {
	return &PortRouter{
		metaPort:        *metaPort,
		portOffsetTable: serviceinfo.ServicePortOffsetTable,
	}
}

func (p *PortRouter) GetPortOffset(method string) int {
	service := strings.Split(method, "/")[1]
	portOffset, ok := p.portOffsetTable[service]

	if !ok {
		panic(fmt.Errorf("failed to get server port offset, unknown service: %s", service))
	}

	return portOffset
}
