package zrpc

// Header memory layout must be consistent with zbs
// a rpc call request =  Header + Body
type Header struct {
	Ec        int32  // error code, not equal to 0 means error
	ServiceId int32  // rpc service id
	MethodId  int32  // rpc service method id
	Timeout   int32  // rpc call timeout in ms
	MessageId uint64 // rpc call message id
	Len       uint64 // body len
}

// RequestHeader, memory layout must be the same as the Header
// a rpc call request =  RequestHeader + Body
type RequestHeader Header

// Response, memory layout must be the same as the Header
// a rpc call response = ResponseHeader + Body
type ResponseHeader Header
