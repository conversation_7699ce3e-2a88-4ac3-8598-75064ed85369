package zrpc

import (
	"context"
	"fmt"
	"math"
	"path/filepath"
	"strconv"
	"strings"
)

// ElectionPrefix is ZBS quorum cluster election prefix in ZooKeeper
const ElectionPrefix = "/zos/service/"

// ZBS quorum cluster election path
const (
	ElectionMetaPath = ElectionPrefix + "meta"  // zbs-metad
	ElectionNtpPath  = ElectionPrefix + "ntp"   // zbs-ntpd
	ElectionTaskPath = ElectionPrefix + "taskd" // zbs-taskd
)

// LeaderWatcher watches meta leader
// When meta leader changes, OnChanged will be trigger
type LeaderWatcher struct {
	OnChanged func(string)

	watcher *ZkWatcher
	cancel  context.CancelFunc
	leader  string
}

// NewLeaderWatcher creates a new leader watcher
func NewLeaderWatcher(servers []string, watchPath string) *LeaderWatcher {
	w := NewZkWatcher(servers, watchPath)

	return &LeaderWatcher{
		watcher: w,
	}
}

// Run will activate leader watcher
func (w *LeaderWatcher) Run() error {
	ctx, cancel := context.WithCancel(context.TODO())
	w.cancel = cancel

	return w.watcher.WatchChildrenCb(ctx, func(children []string) {
		if len(children) == 0 {
			return
		}

		child, err := getLeaderChild(children)
		if err != nil {
			w.watcher.Log("Failed to get leader child, %v\n", err)
			return
		}

		rawLeader, _, err := w.watcher.Conn.Get(filepath.Join(w.watcher.WatchPath(), child))
		if err != nil {
			w.watcher.Log("Failed to get leader, %v\n", err)
			return
		}

		w.leader = string(rawLeader)
		w.OnChanged(w.leader)
	})
}

// Close will shutdown leader watcher
func (w *LeaderWatcher) Close() error {
	w.cancel()
	return nil
}

// Leader returns the cached meta leader
func (w *LeaderWatcher) Leader() string {
	return w.leader
}

// getLeaderChild returns the child with the smallest sequence
func getLeaderChild(children []string) (string, error) {
	if len(children) == 0 {
		return "", fmt.Errorf("failed to get leader child, children is empty")
	}

	var leaderChild string
	var leaderSeq = math.MaxInt32

	for _, child := range children {
		seq, err := getSequenceFromChild(child)
		if err != nil {
			// ignore invalid child
			continue
		}

		if seq < leaderSeq {
			leaderSeq = seq
			leaderChild = child
		}
	}

	return leaderChild, nil
}

// getSequenceFromChild extract sequence from child
func getSequenceFromChild(child string) (int, error) {
	segments := strings.Split(child, "-")
	if len(segments) != 2 {
		return -1, fmt.Errorf("invalid child value, %v", child)
	}

	seq, err := strconv.Atoi(segments[1])
	if err != nil {
		return -1, fmt.Errorf("invalid child sequence, %v", segments[1])
	}

	return seq, nil
}
