package zrpc

import (
	"bytes"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/utils"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

type metaServer struct {
	mutex              sync.Mutex
	poolMap            map[string]*metav1.Pool     // PoolId: string -> *Pool
	poolVolumesMap     map[string][]*metav1.Volume // PoolId: string -> []*Volume
	volumeIdPoolIdMap  map[string]string           // VolumeId: string -> PoolId: string
	volumeSecondaryMap map[string]*metav1.Volume   // volumeSecondaryId: string -> *Volume
}

func NewMetaServer() *metaServer {
	return &metaServer{
		mutex:              sync.Mutex{},
		poolMap:            make(map[string]*metav1.Pool),
		poolVolumesMap:     make(map[string][]*metav1.Volume),
		volumeIdPoolIdMap:  make(map[string]string),
		volumeSecondaryMap: make(map[string]*metav1.Volume),
	}
}

func (m *metaServer) CreatePool(req *metav1.Pool, rsp *metav1.Pool) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	poolId := string(req.GetId())
	if poolId != "" {
		pool := m.getPool(utils.NewPoolPathById(poolId))
		if pool != nil {
			return returnErrorHook(zbserror.New(zbs.ErrorCode_EDuplicate, fmt.Sprintf("pool %s already exists", poolId)))
		}
	} else {
		// if pool ID is empty, assign a valid pool ID
		poolId = uuid.New().String()
		req.Id = []byte(poolId)
	}

	m.poolMap[poolId] = req
	m.poolVolumesMap[poolId] = make([]*metav1.Volume, 0)

	proto.Merge(rsp, req)

	return returnErrorHook(nil)
}

func (m *metaServer) ListPool(req *metav1.ListPoolRequest, rsp *metav1.PoolsResponse) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// copy all pools to rsp, ignore pagination in req
	rsp.Pools = make([]*metav1.Pool, len(m.poolMap))
	i := 0

	for _, pool := range m.poolMap {
		rsp.Pools[i], _ = proto.Clone(pool).(*metav1.Pool)
		i++
	}

	rsp.TotalNum = utils.NewUint32(uint32(len(rsp.Pools)))

	return returnErrorHook(nil)
}

func (m *metaServer) DeletePool(req *metav1.PoolPath, rsp *zbs.Void) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	pool := m.getPool(req)
	if pool == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Pool path %+v not found", req)))
	}

	poolId := string(pool.GetId())
	delete(m.poolMap, poolId)

	// delete all volumes in pool
	volumes := m.poolVolumesMap[poolId]
	for _, volume := range volumes {
		delete(m.volumeIdPoolIdMap, string(volume.GetId()))

		if len(volume.GetSecondaryId()) != 0 {
			delete(m.volumeSecondaryMap, string(volume.GetSecondaryId()))
		}
	}

	delete(m.poolVolumesMap, poolId)

	return returnErrorHook(nil)
}

func (m *metaServer) ShowPool(req *metav1.PoolPath, rsp *metav1.Pool) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	pool := m.getPool(req)

	if pool == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Pool path %+v not found", req)))
	}

	proto.Merge(rsp, pool)

	return returnErrorHook(nil)
}

func (m *metaServer) UpdatePool(req *metav1.UpdatePoolRequest, rsp *metav1.Pool) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	pool := m.getPool(utils.NewPoolPathByName(string(req.GetName())))
	if pool == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Pool %+v not found", req.GetName())))
	}

	proto.Merge(rsp, pool)

	rsp.ReplicaNum = utils.NewUint32(req.GetReplicaNum())
	rsp.Description = req.GetDescription()

	return returnErrorHook(nil)
}

func (m *metaServer) CreateVolume(req *metav1.CreateVolumeRequest, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	volume, err := m.createVolume(req)
	if err != nil {
		return returnErrorHook(err)
	}

	proto.Merge(rsp, volume)

	return returnErrorHook(nil)
}

func (m *metaServer) createVolume(req *metav1.CreateVolumeRequest) (*metav1.Volume, error) {
	volume := req.GetVolume()
	if volume == nil {
		return nil, zbserror.New(zbs.ErrorCode_EBadArgument, "Request.Volume is nil")
	}

	pool := m.getPool(req.GetPoolPath())
	if pool == nil {
		return nil, zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Pool path %+v not found", req.GetPoolPath()))
	}

	volumeName := string(volume.GetName())

	existedVolume := m.getVolume(utils.NewVolumePathByName(req.GetPoolPath(), volumeName))
	if existedVolume != nil {
		return nil, zbserror.New(zbs.ErrorCode_EDuplicate, fmt.Sprintf("Volume %s already exists", volumeName))
	}

	if len(volume.GetId()) == 0 {
		volume.Id = []byte(uuid.New().String())
	}

	poolId := string(pool.GetId())
	volumes := m.poolVolumesMap[poolId]
	volumes = append(volumes, volume)
	m.poolVolumesMap[poolId] = volumes

	volumeId := string(volume.GetId())
	m.volumeIdPoolIdMap[volumeId] = poolId

	if len(volume.SecondaryId) != 0 {
		m.volumeSecondaryMap[string(volume.SecondaryId)] = volume
	}

	return req.GetVolume(), nil
}

func (m *metaServer) ListVolume(req *metav1.ListVolumeRequest, rsp *metav1.VolumesResponse) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	pool := m.getPool(req.GetPoolPath())
	if pool == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Pool path %+v not found", req.GetPoolPath())))
	}

	volumes := m.poolVolumesMap[string(pool.GetId())]
	rsp.Volumes = make([]*metav1.Volume, len(volumes))

	for i, volume := range volumes {
		rsp.Volumes[i], _ = proto.Clone(volume).(*metav1.Volume)
	}

	rsp.TotalNum = utils.NewUint32(uint32(len(rsp.GetVolumes())))

	return returnErrorHook(nil)
}

func (m *metaServer) ShowVolume(req *metav1.VolumePath, rsp *metav1.ShowVolumeResponse) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	volume := m.getVolume(req)
	if volume == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume %+v not found", req.GetVolumeName())))
	}

	rsp.Volume = &metav1.Volume{}
	proto.Merge(rsp.Volume, volume)

	return returnErrorHook(nil)
}

func (m *metaServer) DeleteVolume(req *metav1.VolumePath, rsp *zbs.Void) error {
	return returnErrorHook(m.deleteVolume(req))
}

func (m *metaServer) deleteVolume(path *metav1.VolumePath) error {
	volume := m.getVolume(path)
	if volume == nil {
		return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", path))
	}

	volumeId := string(volume.GetId())
	poolId := m.volumeIdPoolIdMap[volumeId]
	oldVolumes := m.poolVolumesMap[poolId]

	if poolId != "" && oldVolumes != nil {
		newVolumes := make([]*metav1.Volume, 0, len(oldVolumes)-1)

		for i, item := range oldVolumes {
			if !bytes.Equal(item.GetId(), volume.GetId()) {
				newVolumes = append(newVolumes, oldVolumes[i])
			}
		}

		m.poolVolumesMap[poolId] = newVolumes
	}

	delete(m.volumeIdPoolIdMap, volumeId)

	if len(volume.SecondaryId) != 0 {
		delete(m.volumeSecondaryMap, string(volume.SecondaryId))
	}

	return nil
}

func (m *metaServer) UpdateVolume(req *metav1.UpdateVolumeRequest, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	volume := m.getVolume(req.GetPath())
	if volume == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", req.GetPath())))
	}

	poolId := m.volumeIdPoolIdMap[string(volume.GetId())]

	pool := m.getPool(utils.NewPoolPathById(poolId))
	if pool == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Pool %+v not found", poolId)))
	}

	volumes := m.poolVolumesMap[string(pool.GetId())]
	for _, item := range volumes {
		if !bytes.Equal(item.GetName(), volume.GetName()) {
			continue
		}

		item.Name = req.GetName()
		item.ReplicaNum = utils.NewUint32(req.GetReplicaNum())
		item.Description = req.GetDescription()
		proto.Merge(rsp, item)

		return returnErrorHook(nil)
	}

	return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", req.GetPath())))
}

func (m *metaServer) ResizeVolume(req *metav1.ResizeVolumeRequest, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	volume := m.getVolume(req.GetPath())
	if volume == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", req.GetPath())))
	}

	poolId := m.volumeIdPoolIdMap[string(volume.GetId())]

	pool := m.getPool(utils.NewPoolPathById(poolId))
	if pool == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Pool %+v not found", poolId)))
	}

	volumes := m.poolVolumesMap[string(pool.GetId())]
	for _, item := range volumes {
		if !bytes.Equal(item.GetName(), volume.GetName()) {
			continue
		}

		volume.Size = utils.NewUint64(req.GetSize())
		proto.Merge(rsp, volume)

		return returnErrorHook(nil)
	}

	return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", req.GetPath())))
}

func (m *metaServer) GetVolumeSize(req *metav1.VolumeId, rsp *metav1.VolumeSizeResponse) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	volume := m.getVolume(utils.NewVolumePathById(string(req.GetVolumeId())))
	if volume == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume %+v not found", req.GetVolumeId())))
	}

	rsp.Size = volume.Size

	return returnErrorHook(nil)
}

func (m *metaServer) GetVTable(req *metav1.VolumePath, rsp *metav1.GetVTableResponse) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	volume := m.getVolume(req)
	if volume == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", req)))
	}

	// Since this mock server don't have a metadb and can't get vtable,
	// mock one vtable is fine, take easy.
	vextents := make([]*zbs.VExtent, 0)
	vextentLen := utils.RandIntn(10)

	for i := 0; i < vextentLen; i++ {
		vextents = append(vextents, utils.GenerateRandomVExtents())
	}

	rsp.Vextents = vextents

	return returnErrorHook(nil)
}

func (m *metaServer) MoveVolume(req *metav1.MoveVolumeRequest, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	volume := m.getVolume(req.GetVolumePath())
	if volume == nil {
		return returnErrorHook(zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", req.GetVolumePath())))
	}

	err := m.deleteVolume(req.GetVolumePath())
	if err != nil {
		return returnErrorHook(err)
	}

	createVolumeReq := &metav1.CreateVolumeRequest{}
	proto.Merge(createVolumeReq.Volume, volume)
	createVolumeReq.PoolPath = req.GetDstPoolPath()

	newVolume, err := m.createVolume(createVolumeReq)
	if err != nil {
		return returnErrorHook(err)
	}

	proto.Merge(newVolume, rsp)

	return returnErrorHook(nil)
}

func (m *metaServer) CreateSnapshot(req *metav1.CreateSnapshotRequest, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	path := req.GetPath()
	if path == nil {
		return zbserror.New(zbs.ErrorCode_EInvalidArgument, "Request.Path is nil")
	}

	existedSnapshot := m.getSnapshot(path)
	if existedSnapshot != nil {
		return zbserror.New(zbs.ErrorCode_EDuplicate, fmt.Sprintf("Snapshot %+v already exists", path))
	}

	volume := m.getVolume(path.GetVolumePath())
	if volume == nil {
		return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", path.GetVolumePath()))
	}

	proto.Merge(rsp, volume)
	rsp.Name = path.GetSnapshotName()
	rsp.ParentId = volume.GetId()
	rsp.Id = []byte(uuid.New().String())
	rsp.CreatedTime = &zbs.TimeSpec{
		Seconds:  utils.NewInt64(time.Now().Unix()),
		Nseconds: utils.NewInt64(time.Now().UnixNano()),
	}
	rsp.Description = req.GetDescription()
	rsp.IsSnapshot = utils.NewBool(true)
	rsp.ReadOnly = utils.NewBool(true)

	if len(path.GetSecondaryId()) != 0 {
		rsp.SecondaryId = path.GetSecondaryId()
	}

	poolId := m.volumeIdPoolIdMap[string(volume.GetId())]
	m.createSnapshotInDB(poolId, rsp)

	return nil
}

func (m *metaServer) ShowSnapshot(req *metav1.SnapshotPath, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	snapshot := m.getSnapshot(req)
	if snapshot == nil {
		return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Snapshot path %+v not found", req))
	}

	proto.Merge(rsp, snapshot)

	return nil
}

func (m *metaServer) UpdateSnapshot(req *metav1.UpdateSnapshotRequest, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	snapshot := m.getSnapshot(req.GetPath())
	if snapshot == nil {
		return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Snapshot path %+v not found", req))
	}

	if req.NewName != nil {
		snapshot.Name = req.GetNewName()
	}

	if req.NewDescription != nil {
		snapshot.Description = req.GetNewDescription()
	}

	if req.NewAllocEven != nil {
		snapshot.AllocEven = utils.NewBool(req.GetNewAllocEven())
	}

	proto.Merge(rsp, snapshot)

	return nil
}

func (m *metaServer) DeleteSnapshot(req *metav1.SnapshotPath, rsp *zbs.Void) error {
	snapshot := m.getSnapshot(req)
	if snapshot == nil {
		return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Snapshot path %+v not found", req))
	}

	m.deleteSnapshotInDB(snapshot)

	return nil
}

func (m *metaServer) MoveSnapshot(req *metav1.MoveSnapshotRequest, rsp *metav1.Volume) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	snapshot := m.getSnapshot(req.GetPath())
	if snapshot == nil {
		return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Snapshot path %+v not found", req))
	}

	pool := m.getPool(req.GetDstPoolPath())
	if pool == nil {
		return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("pool path %+v not found", req.GetDstPoolPath()))
	}

	m.deleteSnapshotInDB(snapshot)
	m.createSnapshotInDB(string(pool.GetId()), snapshot)

	return nil
}

func (m *metaServer) ListSnapshot(req *metav1.ListSnapshotRequest, rsp *metav1.SnapshotsResponse) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	path := req.GetVolumePath()
	if path == nil {
		return zbserror.New(zbs.ErrorCode_EInvalidArgument, "Request.VolumePath is nil")
	}

	rsp.Snapshots = make([]*metav1.Volume, 0)

	if len(path.GetVolumeName()) == 0 && len(path.GetVolumeId()) == 0 {
		// list all snapshots in the pool
		poolPath := path.GetPoolPath()
		if poolPath == nil {
			return zbserror.New(zbs.ErrorCode_EInvalidArgument, "Request.VolumePath.PoolPath is nil")
		}

		poolId := string(poolPath.GetPoolId())

		snapshots, ok := m.poolVolumesMap[poolId]
		if !ok {
			return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("pool %s not found", poolId))
		}

		for _, snapshot := range snapshots {
			item, _ := proto.Clone(snapshot).(*metav1.Volume)
			rsp.Snapshots = append(rsp.Snapshots, item)
		}
	} else {
		// list all snapshots of the volume
		var volumeId []byte

		switch {
		case len(path.GetVolumeName()) != 0:
			volume := m.getVolume(path)
			if volume == nil {
				return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", path))
			}

			volumeId = volume.GetId()
		case len(path.GetVolumeId()) != 0:
			volumeId = path.GetVolumeId()
		default:
			return zbserror.New(zbs.ErrorCode_ENotFound, fmt.Sprintf("Volume path %+v not found", path))
		}

		for _, snapshots := range m.poolVolumesMap {
			for _, snapshot := range snapshots {
				if bytes.Equal(snapshot.ParentId, volumeId) {
					item, _ := proto.Clone(snapshot).(*metav1.Volume)
					rsp.Snapshots = append(rsp.Snapshots, item)
				}
			}
		}
	}

	return nil
}

func (m *metaServer) ShowClusterInfo(req *zbs.Void, rsp *metav1.ClusterInfo) error {
	rsp.Desc = []byte("mock meta server")
	rsp.Uuid = []byte(uuid.New().String())
	rsp.Name = []byte("mock meta server")

	return returnErrorHook(nil)
}

func (m *metaServer) getPool(path *metav1.PoolPath) *metav1.Pool {
	if path == nil {
		return nil
	}

	poolId := string(path.GetPoolId())
	if poolId != "" {
		pool, ok := m.poolMap[poolId]
		if ok {
			return pool
		}
	}

	poolName := string(path.GetPoolName())

	if poolName != "" {
		for _, pool := range m.poolMap {
			if string(pool.Name) == poolName {
				return pool
			}
		}
	}

	return nil
}

func (m *metaServer) getVolume(path *metav1.VolumePath) *metav1.Volume {
	if path == nil {
		return nil
	}

	// 1. get volume by SecondaryId
	if len(path.SecondaryId) != 0 {
		volume, ok := m.volumeSecondaryMap[string(path.SecondaryId)]
		if ok {
			return volume
		}
	}

	// 2. get volume by VolumeName + SnapshotName
	if len(path.GetVolumeName()) != 0 {
		pool := m.getPool(path.GetPoolPath())
		if pool != nil {
			volumes, ok := m.poolVolumesMap[string(pool.GetId())]
			if ok {
				for _, volume := range volumes {
					if bytes.Equal(volume.GetName(), path.GetVolumeName()) {
						return volume
					}
				}
			}
		}
	}

	// 3. get volume by VolumeId
	for _, volumes := range m.poolVolumesMap {
		for _, volume := range volumes {
			if bytes.Equal(volume.GetId(), path.GetVolumeId()) {
				return volume
			}
		}
	}

	return nil
}

func (m *metaServer) getSnapshot(path *metav1.SnapshotPath) *metav1.Volume {
	if path == nil {
		return nil
	}

	// 1. get snapshot by SecondaryId
	if len(path.SecondaryId) != 0 {
		volume, ok := m.volumeSecondaryMap[string(path.GetSecondaryId())]
		if ok {
			return volume
		}
	}

	// 2. get snapshot by VolumePath + SnapshotName
	// Note: the origin volume must be existed
	volumePath := path.GetVolumePath()
	if volumePath != nil {
		var poolId string

		if len(volumePath.GetVolumeId()) != 0 {
			// 2.1. get poolId by volumeId
			poolId = m.volumeIdPoolIdMap[string(volumePath.GetVolumeId())]
		} else if pool := m.getPool(volumePath.GetPoolPath()); pool != nil {
			// 2.2. get poolId by poolPath
			poolId = string(pool.GetId())
		}

		if len(poolId) == 0 {
			return nil
		}

		snapshots, ok := m.poolVolumesMap[poolId]
		if ok {
			for _, snapshot := range snapshots {
				if bytes.Equal(snapshot.GetName(), path.GetSnapshotName()) {
					return snapshot
				}
			}
		}
	}

	// 3. get snapshot by SnapshotId
	for _, snapshots := range m.poolVolumesMap {
		for _, snapshot := range snapshots {
			if bytes.Equal(snapshot.GetId(), path.GetSnapshotId()) {
				return snapshot
			}
		}
	}

	return nil
}

func (m *metaServer) createSnapshotInDB(poolId string, snapshot *metav1.Volume) {
	snapshot.SnapshotPoolId = []byte(poolId)
	snapshots := m.poolVolumesMap[poolId]
	snapshots = append(snapshots, snapshot)
	m.poolVolumesMap[poolId] = snapshots

	if len(snapshot.GetSecondaryId()) != 0 {
		m.volumeSecondaryMap[string(snapshot.GetSecondaryId())] = snapshot
	}
}

func (m *metaServer) deleteSnapshotInDB(snapshot *metav1.Volume) {
	poolId := string(snapshot.GetSnapshotPoolId())
	oldSnapshots := m.poolVolumesMap[poolId]
	snapshots := make([]*metav1.Volume, 0, len(oldSnapshots)-1)

	for _, item := range oldSnapshots {
		if !bytes.Equal(item.GetId(), snapshot.GetId()) {
			snapshots = append(snapshots, item)
		}
	}

	m.poolVolumesMap[poolId] = snapshots

	if len(snapshot.GetSecondaryId()) != 0 {
		delete(m.volumeSecondaryMap, string(snapshot.GetSecondaryId()))
	}
}
