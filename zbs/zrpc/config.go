package zrpc

type Config struct {
	Addr                string            // meta addr
	IOTimeout<PERSON>         int32             // read / write timeout in ms
	RpcTimeoutMS        int32             // rpc timeout in ms
	MaxInflight         int               // max inflight rpc call
	Watcher             *LeaderWatcher    // leader change watcher, If meta proxy addr is set, watcher will be ignored
	IpRedirectMap       map[string]string // Convert Storage IP to Access IP
	PortRouter          *PortRouter       // port router
	WorkerNum           int32             // number of goroutine which send rpc to zbs
	RetryNum            int32             // number of retry count when zbs return retryable error
	RetryIntervalMS     int32             // interval of rpc retrying
	DialTimeoutMS       int32             // dial timeout in ms
	DialRetryNum        int32             // number of retry count when dial return error
	DialRetryIntervalMS int32             // interval of dial retrying
}

func DefaultClientConfig() *Config {
	return &Config{
		MaxInflight:     1024,
		RpcTimeoutMS:    3000,
		PortRouter:      NewPortRouter(&[]int{10100}[0]),
		WorkerNum:       25,
		RetryNum:        0,
		RetryIntervalMS: 2000,
	}
}
