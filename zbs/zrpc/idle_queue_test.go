package zrpc

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

type client struct {
	closed bool
}

func TestHolder(t *testing.T) {
	h := &holder{
		obj: &client{},
		closeFunc: func(obj interface{}) error {
			obj.(*client).closed = true
			return nil
		},
		locker: new(sync.Mutex),
	}

	h.idle(time.Second)

	if !h.recover() {
		t.Fatalf("must recover success")
	}

	if h.timer.Stop() {
		t.Fatalf("timer must stopped")
	}
}

func TestHolderExpired(t *testing.T) {
	h := &holder{
		obj: &client{},
		closeFunc: func(obj interface{}) error {
			obj.(*client).closed = true
			return nil
		},
		locker: new(sync.Mutex),
	}

	h.idle(time.Millisecond)

	time.Sleep(10 * time.Millisecond)

	if h.recover() {
		t.Fatalf("must recover failed")
	}

	if h.timer.Stop() {
		t.Fatalf("timer must expired")
	}
}

func TestHolderResetTimer(t *testing.T) {
	h := &holder{
		obj: &client{},
		closeFunc: func(obj interface{}) error {
			obj.(*client).closed = true
			return nil
		},
		locker: new(sync.Mutex),
	}

	h.idle(time.Second)

	time.Sleep(10 * time.Millisecond)

	if !h.idle(time.Millisecond) {
		t.Fatalf("must reset timer success")
	}
}

func TestReIdleExpiredHolderFailed(t *testing.T) {
	h := &holder{
		obj: &client{},
		closeFunc: func(obj interface{}) error {
			obj.(*client).closed = true
			return nil
		},
		locker: new(sync.Mutex),
	}

	h.idle(time.Millisecond)

	time.Sleep(10 * time.Millisecond)

	if h.idle(time.Millisecond) {
		t.Fatalf("cannot re-idle expired holder")
	}
}

func TestRunCloseFuncError(t *testing.T) {
	h := &holder{
		obj: &client{},
		closeFunc: func(obj interface{}) error {
			obj.(*client).closed = true
			return fmt.Errorf("error")
		},
		locker: new(sync.Mutex),
	}

	h.idle(time.Millisecond)

	time.Sleep(10 * time.Millisecond)
}

func TestKeepOneItem(t *testing.T) {
	q := NewIdleQueue()
	// test keep one obj
	c1 := &client{}
	q.Push(c1, func(obj interface{}) error {
		obj.(*client).closed = true
		return nil
	}, time.Second)

	c := q.PopAlive()
	if c == nil {
		t.Fatal("PopAlive c1 expect not nil")
	}

	if c != c1 {
		t.Fatal("expect same obj")
	}
}

func TestElementExpired(t *testing.T) {
	q := NewIdleQueue()
	// test holder expired, get nil with PopAlived
	c2 := &client{}
	q.Push(c2, func(obj interface{}) error {
		obj.(*client).closed = true
		return nil
	}, time.Millisecond)

	time.Sleep(10 * time.Millisecond)

	c := q.PopAlive()
	if c != nil {
		t.Fatal("PopAlive c2 expect nil")
	}

	if q.Length() > 0 {
		t.Fatal("Length expect 0")
	}

	// test holder expired, obj closed
	c3 := &client{}
	q.Push(c3, func(obj interface{}) error {
		obj.(*client).closed = true
		return nil
	}, time.Millisecond)

	time.Sleep(10 * time.Millisecond)

	c, ok := q.Pop()
	if ok {
		t.Fatal("Pop c3 expect not ok")
	}

	if c == nil {
		t.Fatal("Pop c3 expect not nil")
	}

	if q.Length() > 0 {
		t.Fatal("Length expect 0")
	}

	if !c.(*client).closed {
		t.Fatal("Pop c3 expect closed")
	}
}

func TestQueueOrder(t *testing.T) {
	q := NewIdleQueue()
	// test queue
	c4 := &client{}
	c5 := &client{}
	c6 := &client{}

	q.Push(c4, func(obj interface{}) error {
		obj.(*client).closed = true
		return nil
	}, time.Minute)

	q.Push(c5, func(obj interface{}) error {
		obj.(*client).closed = true
		return nil
	}, time.Minute)

	q.Push(c6, func(obj interface{}) error {
		obj.(*client).closed = true
		return nil
	}, time.Minute)

	c := q.PopAlive()
	if c == nil {
		t.Fatal("Pop c4 expect not nil")
	}

	if c != c4 {
		t.Fatal("Pop not c4")
	}

	c = q.PopAlive()
	if c == nil {
		t.Fatal("Pop c5 expect not nil")
	}

	if c != c5 {
		t.Fatal("Pop not c5")
	}

	c = q.PopAlive()
	if c == nil {
		t.Fatal("Pop c6 expect not nil")
	}

	if c != c6 {
		t.Fatal("Pop not c6")
	}

	c = q.PopAlive()
	if c != nil {
		t.Fatal("expect empty queue")
	}
}

func TestEmptyIdleQueue(t *testing.T) {
	q := NewIdleQueue()
	// test empty queue
	c, ok := q.Pop()
	if ok {
		t.Fatal("Pop expect not ok")
	}

	if c != nil {
		t.Fatalf("Pop expect nil return")
	}

	c = q.PopAlive()
	if c != nil {
		t.Fatal("PopAlive execpt nil")
	}
}
