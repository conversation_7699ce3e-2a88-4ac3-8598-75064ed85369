package zrpc

import (
	"context"
	"fmt"
	"io"
	"math"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"k8s.io/klog"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/utils"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

const addr = "127.0.0.1:0"

var enableHookFlag atomic.Bool

func enableHook() {
	enableHookFlag.Store(true)
}

func disableHook() {
	enableHookFlag.Store(false)
}

func isHookEnabled() bool {
	return enableHookFlag.Load()
}

func newMockClient(server *Server, t *testing.T) *Client {
	addr, err := server.Addr()
	require.NoError(t, err)

	c, err := NewClient(&Config{
		MaxInflight:     1024,
		RpcTimeoutMS:    3000,
		IOTimeoutMS:     3000,
		PortRouter:      nil,
		Addr:            addr,
		WorkerNum:       25,
		RetryNum:        3,
		RetryIntervalMS: 2000,
	})

	if err != nil {
		t.Fatalf("new mock client, %v", err)
	}

	return c
}

func runMockServer(t *testing.T) *Server {
	server := NewServer(addr)

	err := server.RegisterName("zbs.meta.ISCSIService", NewISCSIServer())
	if err != nil {
		t.Fatalf("failed to register iscsi service, %v", err)
	}

	err = server.Run()
	if err != nil {
		t.Fatalf("failed to create mock server, %v", err)
	}

	return server
}

func stopMockServer(server *Server) {
	err := server.Stop()
	if err != nil {
		panic(err)
	}
}

func callCreateTarget(c *Client, name string) (*meta.ISCSITarget, error) {
	req := &meta.CreateISCSITargetRequest{
		Name: []byte(name),
	}
	client := meta.NewISCSIServiceClient(c)

	return client.CreateTarget(context.TODO(), req)
}

func TestMain(m *testing.M) {
	enableHook()

	exitCode := m.Run()

	disableHook()
	os.Exit(exitCode)
}

func TestClient(t *testing.T) {
	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)

	_, err := callCreateTarget(c, fmt.Sprintf("test-target-%v", utils.RandIntn(math.MaxInt32)))

	assert.Nil(t, err, fmt.Sprintf("%+v", err))
}

func TestRpcTimeout(t *testing.T) {
	lastTimeoutHook := timeoutHook

	defer func() { timeoutHook = lastTimeoutHook }()

	timeoutHook = func() {
		if isHookEnabled() {
			fmt.Println("sleeping for 6 secs")
			time.Sleep(time.Second * 6)
		}
	}

	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)

	_, err := callCreateTarget(c, "test-rpc-timeout")

	assert.EqualError(t, err, "context deadline exceeded")

	time.Sleep(5 * time.Second)

	disableHook()

	defer enableHook()

	_, err = callCreateTarget(c, "test-rpc")
	assert.Nil(t, err, fmt.Sprintf("failed to create target: %+v", err))
}

func TestRpcConnClosed(t *testing.T) {
	origin := connCloseHook

	defer func() { connCloseHook = origin }()

	connCloseHook = func(c io.Closer) {
		if isHookEnabled() {
			err := c.Close()
			if err != nil {
				klog.Errorf("failed to close connection, %v", err)
			}
		}
	}

	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)

	_, err := callCreateTarget(c, "test-rpc-conn-closed")

	errMessage := fmt.Sprintf("%s", err)
	ignoreError := strings.Contains(errMessage, "connection reset by peer") ||
		strings.Contains(errMessage, io.ErrUnexpectedEOF.Error())

	if !ignoreError {
		t.Errorf("unexpected error: %v", err)
		t.Fail()
	}

	disableHook()

	defer enableHook()

	_, err = callCreateTarget(c, "test-rpc-conn-reconnect")

	assert.Nil(t, err, fmt.Sprintf("%+v", err))
}

func TestWriteBadProtocol(t *testing.T) {
	origin := writeBadProtocolHook

	defer func() { writeBadProtocolHook = origin }()

	writeBadProtocolHook = func(w io.Writer) {
		if isHookEnabled() {
			klog.Info("on hook: write bad protocol")

			_, err := w.Write([]byte("1234567812345678\xff\xff\xff\xff\xff\xff\xff\xff"))
			require.NoError(t, err)
		}
	}

	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)

	_, err := callCreateTarget(c, "test-write-bad-protocol")

	if assert.NotNil(t, err) {
		assert.Contains(t, err.Error(), "reading error body", err.Error())
	}
}

func TestReturnBadArgument(t *testing.T) {
	origin := returnErrorHook

	defer func() { returnErrorHook = origin }()

	returnErrorHook = func(err error) error {
		if isHookEnabled() {
			if err == nil {
				return zbserror.New(zbs.ErrorCode_EBadArgument, "bad argument")
			}
		}

		return err
	}

	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)

	_, err := callCreateTarget(c, "test-return-bad-argument")

	if assert.NotNil(t, err) {
		assert.Containsf(t, err.Error(), "[EBadArgument] bad argument", err.Error())
	}
}

func TestConcurrent(t *testing.T) {
	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)

	var wg sync.WaitGroup

	for i := 0; i < 10; i++ {
		wg.Add(1)

		go func(id int) {
			for tid := 0; tid < 10; tid++ {
				targetName := fmt.Sprintf("test-concurrent-%v-%v", id, tid)

				target, err := callCreateTarget(c, targetName)
				if assert.Nil(t, err, fmt.Errorf("%+v", err)) {
					assert.Equal(t, string(target.Name), targetName, "target name not match")
				}
			}
			wg.Done()
		}(i)
	}

	wg.Wait()
}

func TestClosedClient(t *testing.T) {
	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)

	_, err := callCreateTarget(c, "test-target-1")

	assert.Nil(t, err)

	c.Close()

	_, err = callCreateTarget(c, "test-target-shutdown")

	assert.EqualError(t, err, zbserror.ErrShutdown.Error())
}

func TestRetry(t *testing.T) {
	origin := returnErrorHook

	defer func() { returnErrorHook = origin }()

	var rpcCalled atomic.Int32

	returnErrorHook = func(err error) error {
		if isHookEnabled() {
			called := rpcCalled.Add(1)

			if err == nil && called < 3 {
				return zbserror.New(zbs.ErrorCode_ENotLeader, "")
			}
		}

		return err
	}

	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)
	defer c.Close()

	client := meta.NewISCSIServiceClient(c)
	req := &meta.ListTargetsRequest{}
	_, err := client.ListTargets(context.TODO(), req)
	require.NoError(t, err)
	require.Equal(t, rpcCalled.Load(), int32(3))
}

func TestDestroyClient(t *testing.T) {
	origin := returnErrorHook

	defer func() { returnErrorHook = origin }()

	var rpcCalled atomic.Int32
	connectionClosedErrs := []zbs.ErrorCode{
		zbs.ErrorCode_EUnknownMethodId,
		zbs.ErrorCode_EUnknownSeviceId,
		zbs.ErrorCode_ETooLargeMessage,
		zbs.ErrorCode_EBadMessageFormat,
	}

	returnErrorHook = func(err error) error {
		if isHookEnabled() {
			called := int(rpcCalled.Add(1))

			if err == nil && called <= len(connectionClosedErrs) {
				return zbserror.New(connectionClosedErrs[called-1], "")
			}
		}

		return err
	}

	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)
	defer c.Close()

	ctx := context.TODO()
	client := meta.NewISCSIServiceClient(c)
	req := &meta.ListTargetsRequest{}

	for range connectionClosedErrs {
		_, err := client.ListTargets(ctx, req)
		require.Error(t, err)

		zbsErr, ok := err.(*zbserror.Error)
		require.True(t, ok)
		require.True(t, zbserror.IsConnectionClosedRPCError(zbsErr))

		// The client pool destroys the client after receiving a connection closed error,
		// so the free number of pool should be 0.
		require.Equal(t, 0, RpcClientPool().FreeNum())
	}

	_, err := client.ListTargets(ctx, req)
	require.NoError(t, err)
}

func TestReconnectClosedClient(t *testing.T) {
	server := runMockServer(t)
	defer stopMockServer(server)

	c := newMockClient(server, t)
	c.Close()

	assert.Nil(t, c.currentStopCh, fmt.Sprint("currentStopCh must be nil after client is closed"))
	assert.NotPanics(t, func() {
		c.reconnect("")
	}, "reconnect should not close currentStopCh again")
}
