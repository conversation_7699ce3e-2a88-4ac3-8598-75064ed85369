package zrpc

import (
	"fmt"
	"log"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

type mockResolver struct {
	records map[string][]string // host -> addrs
}

func newMockResolver() *mockResolver {
	return &mockResolver{
		records: map[string][]string{
			"a.com": {"*********", "*********", "*********", "*********"},
			"b.com": {"*********", "*********", "*********", "*********"},
		},
	}
}

func (p *mockResolver) LookupHost(host string) ([]string, error) {
	addrs, ok := p.records[host]
	if ok {
		return addrs, nil
	}

	return nil, fmt.Errorf("NXDOMAIN: %s", host)
}

func (p *mockResolver) ReplaceRecords(old string, new string) {
	for _, addrs := range p.records {
		for i, addr := range addrs {
			addrs[i] = strings.ReplaceAll(addr, old, new)
		}
	}
}

func TestZDNSProvider(t *testing.T) {
	resolver := newMockResolver()
	provider := NewZDNSProvider(log.Printf)
	provider.LookupHost = resolver.LookupHost

	err := provider.Init([]string{
		"a.com:10100",
		"ufo-exists-in-the-world:10200",
	})
	require.NoError(t, err)
	assert.NotZero(t, provider.Len())

	ticker := time.NewTicker(300 * time.Millisecond)
	defer ticker.Stop()

	timer := time.After(5 * time.Second)

	replaceTimer := time.After(2 * time.Second)

	const oldAddrPrefix = "198.0.0"
	const newAddrPrefix = "198.1.1"

	ok := false

	func() {
		for {
			select {
			case <-ticker.C:
				addr, retry := provider.Next()
				if strings.HasPrefix(addr, newAddrPrefix) {
					ok = true
				}

				log.Printf("addr: %s, retry: %v\n", addr, retry)
			case <-replaceTimer:
				resolver.ReplaceRecords(oldAddrPrefix, newAddrPrefix)
			case <-timer:
				return
			}
		}
	}()

	assert.True(t, ok, "resolved addrs should begin with new addr prefix")
}
