package zrpc

import (
	"fmt"
	"net/rpc"
	"sync"

	"k8s.io/klog"
)

type rpcClientPool struct {
	mutex        sync.Mutex
	usingClients int
	maxClients   int
	idleClients  map[string]*idleQueue // endpoint -> []*Client
}

var clientPoolInstance *rpcClientPool
var clientPoolOnce sync.Once // use sync.Once to protect singleton client pool instantiation

func newRpcClientPool() *rpcClientPool {
	return &rpcClientPool{
		maxClients:   10,
		usingClients: 0,
		idleClients:  make(map[string]*idleQueue),
	}
}

// get rpc.Client Pool instance
func RpcClientPool() *rpcClientPool {
	clientPoolOnce.Do(func() {
		clientPoolInstance = newRpcClientPool()
	})

	return clientPoolInstance
}

// get clients count
func (p *rpcClientPool) Count() int {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	return p.usingClients + p.freeNum()
}

// get free clients count
func (p *rpcClientPool) FreeNum() int {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	return p.freeNum()
}

// borrow a rpc.Client
func (p *rpcClientPool) Borrow(c *Client, endpoint string) (*rpc.Client, error) {
	if idleClient := p.acquireIdleClient(endpoint); idleClient != nil {
		return idleClient, nil
	}

	// endpoint not exist or clientQueue is empty, try to create new client
	// before create, we need to purge idle client if need
	if err := p.purgeIdleClientIfNeed(); err != nil {
		return nil, err
	}

	// try to create a client
	rpcClient, err := newRpcClient(c, endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to create RpcClient, %v", err)
	}

	return rpcClient, nil
}

func (p *rpcClientPool) acquireIdleClient(endpoint string) *rpc.Client {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	clientQueue, ok := p.idleClients[endpoint]
	if ok {
		freeClient := clientQueue.PopAlive()
		if freeClient != nil {
			p.usingClients++
			return freeClient.(*rpc.Client)
		}
	}

	return nil
}

func (p *rpcClientPool) purgeIdleClientIfNeed() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	freeNum := p.freeNum()

	if p.usingClients+freeNum < p.maxClients {
		return nil
	} else if freeNum <= 0 {
		return fmt.Errorf("client nums reached limit, can't create new rpc.Client")
	}

	// find a free client in clientQueue
	found := false

	for _, q := range p.idleClients {
		if q.Length() <= 0 {
			continue
		}

		fc, alived := q.Pop()
		if alived {
			if err := fc.(*rpc.Client).Close(); err != nil {
				return fmt.Errorf("close free client failed: %v", err)
			}
		}

		found = true
	}

	if !found {
		return fmt.Errorf("failed to find a client to erase")
	}

	return nil
}

// Give client back
// When isClientErr is true, client is closed, otherwise client is connected to remote.
// For an closed client, it should be erased.
func (p *rpcClientPool) Restore(endpoint string, client *rpc.Client, needDestroy bool) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.usingClients--

	_, ok := p.idleClients[endpoint]
	if !ok {
		p.idleClients[endpoint] = NewIdleQueue()
	}

	if needDestroy {
		return client.Close()
	}

	p.idleClients[endpoint].Push(client, func(obj interface{}) error {
		return obj.(*rpc.Client).Close()
	}, idleTimeout)

	return nil
}

// Reset this pool to initial state
func (p *rpcClientPool) Reset() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.closeAll()
	p.idleClients = make(map[string]*idleQueue)
	p.usingClients = 0
}

func (p *rpcClientPool) closeAll() {
	for _, q := range p.idleClients {
		c := q.PopAlive()
		for c != nil {
			if err := c.(*rpc.Client).Close(); err != nil {
				klog.Errorf("close client failed wile closeAll: %v", err)
			}

			c = q.PopAlive()
		}
	}
}

func (p *rpcClientPool) freeNum() int {
	free := 0
	for _, v := range p.idleClients {
		free += v.Length()
	}

	return free
}
