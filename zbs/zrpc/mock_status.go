package zrpc

import (
	"github.com/google/uuid"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/utils"
)

type statusServer struct {
	clusterUuid string
}

func NewStatusServer() *statusServer {
	clusterUuid := uuid.New().String()

	return &statusServer{
		clusterUuid: clusterUuid,
	}
}

func (s *statusServer) GetClusterSummary(_ *zbs.Void, rsp *metav1.ClusterSummary) error {
	rsp.ClusterInfo = &metav1.ClusterInfo{
		Uuid:            []byte(s.clusterUuid),
		Name:            []byte("test-meta"),
		Desc:            []byte("Test server for StatusService"),
		IsStretched:     utils.NewBool(false),
		PreferredZoneId: []byte(nil),
		ZkUuidRecorded:  utils.NewBool(false),
	}
	rsp.ValidDataSpace = utils.NewUint64(0)
	rsp.UsedDataSpace = utils.NewUint64(0)
	rsp.TotalNodes = utils.NewUint32(1)
	rsp.HealthyNodes = utils.NewUint32(1)
	rsp.WarningNodes = utils.NewUint32(1)
	rsp.ErrorNodes = utils.NewUint32(0)
	rsp.ConnectingNodes = utils.NewUint32(0)
	rsp.Leader = utils.NewString("127.0.0.1:20131")
	rsp.UnallocatedChunks = []*zbs.Chunk{}
	rsp.StoragePools = []*zbs.StoragePool{}

	return returnErrorHook(nil)
}

func (s *statusServer) GetMetaSummary(_ *zbs.Void, rsp *metav1.MetaSummary) error {
	metaStatus := metav1.MetaStatus_META_RUNNING
	rsp.MetaStatus = &metaStatus
	rsp.IsLeader = utils.NewBool(true)
	rsp.Leader = utils.NewString("127.0.0.1:20131")
	rsp.AliveMetaHosts = []*zbs.Address{
		{
			Ip:   utils.NewString("127.0.0.1"),
			Port: utils.NewInt32(20131),
		},
	}

	return returnErrorHook(nil)
}

func (s *statusServer) ShowClusterStatus(_ *zbs.Void, rsp *metav1.ClusterStatus) error {
	rsp.Flags = utils.NewUint64(uint64(metav1.ClusterStatus_CLUSTER_HEALTHY))
	return returnErrorHook(nil)
}
