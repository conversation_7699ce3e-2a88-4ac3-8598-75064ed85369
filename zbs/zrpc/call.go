package zrpc

import zbserror "github.com/iomesh/zbs-client-go/zbs/error"

type callObj struct {
	portOffset int
	method     string
	args       interface{}
	rsp        interface{}
	done       chan *callObj
	err        error
}

// needDestroy is used to determine if the client needs to be destroyed after a call.
func (c *callObj) needDestroy() bool {
	if c.err != nil {
		return true
	}

	if rsp, ok := c.rsp.(*ResponseWrapper); ok {
		// The ZBS server has closed the connection, so we need to destroy the client.
		return zbserror.IsConnectionClosedRPCError(rsp.Error)
	}

	return false
}
