package zrpc

import (
	"errors"
	"net"
	"net/rpc"

	"golang.org/x/exp/maps"

	"github.com/iomesh/zbs-client-go/utils"
)

type Server struct {
	RpcServer *rpc.Server

	addr         string
	listener     net.Listener
	packageNames map[string]struct{}
}

func NewServer(addr string) *Server {
	return &Server{
		RpcServer: rpc.NewServer(),

		addr:         addr,
		listener:     nil,
		packageNames: make(map[string]struct{}),
	}
}

func (s *Server) Run() error {
	listener, err := net.Listen("tcp", s.addr)
	if err != nil {
		return err
	}

	s.listener = listener

	go func() {
		_ = s.accept()
	}()

	return nil
}

func (s *Server) accept() error {
	for {
		c, err := s.listener.Accept()
		if err != nil {
			return err
		}

		timeoutHook()

		conn := utils.FromConn(c)

		connCloseHook(conn)

		go s.RpcServer.ServeCodec(newServerCodec(conn, maps.Keys(s.packageNames)))
	}
}

func (s *Server) Stop() error {
	var err error
	if s.listener != nil {
		err = s.listener.Close()
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *Server) Addr() (string, error) {
	if s.listener == nil {
		return "", errors.New("server has not been started")
	}

	return s.listener.Addr().String(), nil
}

func (s *Server) RegisterName(name string, rcvr interface{}) error {
	if _, ok := s.packageNames[name]; ok {
		return errors.New("service already registered")
	}

	err := s.RpcServer.RegisterName(name, rcvr)
	if err != nil {
		return err
	}

	s.packageNames[extractPackageFromService(name)] = struct{}{}

	return nil
}
