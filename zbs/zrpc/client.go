package zrpc

import (
	"context"
	"fmt"
	"net"
	"net/rpc"
	"strconv"
	"strings"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/klog"

	"github.com/iomesh/zbs-client-go/utils"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

const (
	// connection idle timeout
	idleTimeout = time.Minute * 10
	// client max worker num
	workerNumMax = 100
)

type Client struct {
	rpcTimeoutMS      int32             // rpc timeout in ms
	ioTimeoutMS       int32             // read / write timeout in ms
	callCh            chan *callObj     // rpc call chan
	metaLeaderWatcher *LeaderWatcher    // watch zk and handle meta leader change event
	mutex             sync.Mutex        // protect the following members
	shutdown          bool              // client shutdown
	currentStopCh     chan struct{}     // current connection stopch
	portRouter        *PortRouter       // get server port from method name
	ipRedirectMap     map[string]string // Convert Storage IP to Access IP
	workerNum         int32             // number of goroutine which send rpc to zbs
	RetryNum          int32             // number of retry count when zbs return retryable error
	RetryIntervalMS   int32             // interval of rpc retrying

	// Re-dial parameters
	DialTimeoutMS       int32 // dial timeout in ms
	DialRetryNum        int32 // number of retry count when dial return error
	DialRetryIntervalMS int32 // interval of dial retrying

	// RPC server address
	// DO NOT use meta/chunk/task address directly. Instead, use a meta client service address
	addr string
}

func NewClient(config *Config) (*Client, error) {
	var rpcTimeoutMS int32 = 5000
	if config.RpcTimeoutMS > 0 {
		rpcTimeoutMS = config.RpcTimeoutMS
	}

	var ioTimeoutMS int32 = 5000
	if config.IOTimeoutMS > 0 {
		ioTimeoutMS = config.IOTimeoutMS
	}

	if config.WorkerNum <= 0 || config.WorkerNum > workerNumMax {
		klog.Fatalf("worker number must greater than 0 and less than %v", workerNumMax)
	}

	var retryNum int32 = 0
	if config.RetryNum >= 0 {
		retryNum = config.RetryNum
	}

	var retryIntervalMS int32 = 0
	if config.RetryIntervalMS >= 0 {
		retryIntervalMS = config.RetryIntervalMS
	}

	var dialTimeoutMS int32 = 2000
	if config.DialTimeoutMS > 0 {
		dialTimeoutMS = config.DialTimeoutMS
	}

	var dialRetryNum int32 = 5
	if config.DialRetryNum > 0 {
		dialRetryNum = config.DialRetryNum
	}

	var dialRetryIntervalMS int32 = 1000
	if config.DialRetryIntervalMS > 0 {
		dialRetryIntervalMS = config.DialRetryIntervalMS
	}

	c := &Client{
		rpcTimeoutMS:        rpcTimeoutMS,
		ioTimeoutMS:         ioTimeoutMS,
		addr:                config.Addr,
		callCh:              make(chan *callObj, config.MaxInflight), // channel buffer len
		currentStopCh:       make(chan struct{}),
		shutdown:            false,
		portRouter:          config.PortRouter,
		ipRedirectMap:       config.IpRedirectMap,
		workerNum:           config.WorkerNum,
		RetryNum:            retryNum,
		RetryIntervalMS:     retryIntervalMS,
		DialTimeoutMS:       dialTimeoutMS,
		DialRetryNum:        dialRetryNum,
		DialRetryIntervalMS: dialRetryIntervalMS,
	}

	switch {
	case c.addr != "":
		go c.reconnect(c.addr)
	case config.Watcher != nil:
		c.metaLeaderWatcher = config.Watcher
		c.metaLeaderWatcher.OnChanged = c.reconnect

		err := c.metaLeaderWatcher.Run()
		if err != nil {
			return nil, fmt.Errorf("failed to run meta leader watcher, %v", err)
		}
	default:
		panic(fmt.Errorf("please provide a valid way to access meta"))
	}

	return c, nil
}

func (c *Client) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.shutdown {
		return nil
	}

	c.shutdown = true

	if c.currentStopCh != nil {
		// close current connection
		close(c.currentStopCh)
		c.currentStopCh = nil
	}

	c.cancelInflightCall()

	pool := RpcClientPool()
	pool.Reset()

	return nil
}

func (c *Client) reconnect(leader string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.shutdown {
		return
	}

	if c.currentStopCh != nil {
		close(c.currentStopCh)
	}

	c.currentStopCh = make(chan struct{})

	pos := strings.Index(leader, ":")
	if pos < 0 {
		panic("leader addr format error: " + leader)
	}

	leaderIp := leader[:pos]
	leaderPort, err := strconv.Atoi(leader[pos+1:])

	if err != nil {
		panic(err)
	}

	var num int32
	for num = 0; num < c.workerNum; num++ {
		if len(c.ipRedirectMap) > 0 {
			go worker(c, c.ipRedirectMap[leaderIp], leaderPort, c.currentStopCh)
		} else {
			go worker(c, leaderIp, leaderPort, c.currentStopCh)
		}
	}

	klog.V(2).Infof("[zbs-client] starting meta rpc client workers, workerNum = %v", c.workerNum)
}

func (c *Client) cancelInflightCall() {
	for len(c.callCh) > 0 {
		select {
		case call := <-c.callCh:
			{
				call.err = zbserror.ErrShutdown
				call.done <- call
			}
		default:
			{
			}
		}
	}
}

func dial(network, addr string, timeout time.Duration, retryNum, retryIntervalMS int32) (net.Conn, error) {
	var conn net.Conn
	var err error

	dialer := net.Dialer{Timeout: timeout}

	err = utils.Until(func() error {
		conn, err = dialer.Dial(network, addr)
		if err != nil {
			return err
		}

		return nil
	}, int(retryNum), time.Duration(retryIntervalMS)*time.Millisecond)

	if err != nil {
		return nil, err
	}

	return conn, nil
}

func newRpcClient(c *Client, addr string) (*rpc.Client, error) {
	conn, err := dial("tcp", addr, time.Duration(c.DialTimeoutMS)*time.Millisecond, c.DialRetryNum, c.DialRetryIntervalMS)
	if err != nil {
		return nil, err
	}

	return rpc.NewClientWithCodec(newClientCodec(conn, c)), nil
}

func worker(c *Client, leaderIp string, leaderPort int, stopCh chan struct{}) {
	var endpoint string

	pool := RpcClientPool()

	for {
		select {
		case <-stopCh:
			// stop worker
			return

		case call := <-c.callCh: // borrow client
			endpoint = fmt.Sprintf("%s:%v", leaderIp, leaderPort+call.portOffset)

			rpcClient, err := pool.Borrow(c, endpoint)

			if err != nil {
				call.err = err
				call.done <- call

				continue
			}

			// call RPC
			klog.V(2).Infof("[zbs-client] calling, endpoint=%v, method=%s", endpoint, call.method)
			err = rpcClient.Call(call.method, call.args, call.rsp)
			call.err = err
			call.done <- call

			// restore client
			err = pool.Restore(endpoint, rpcClient, call.needDestroy())

			if err != nil {
				klog.Errorf("[zbs-client] failed to restore client to pool, %v", err)
			}
		}
	}
}

func (c *Client) isShutdown() bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	return c.shutdown
}

func (c *Client) invoke(ctx context.Context, method string, args interface{}, rsp interface{}) error {
	if c.isShutdown() {
		return zbserror.ErrShutdown
	}

	var cancel func()
	if _, ok := ctx.Deadline(); !ok {
		ctx, cancel = context.WithTimeout(ctx, time.Duration(c.rpcTimeoutMS+1000)*time.Millisecond)
		defer cancel()
	}

	rspWrapper := &ResponseWrapper{
		Response: rsp,
		Error:    nil,
	}

	portOffset := 0

	if c.portRouter != nil {
		portOffset = c.portRouter.GetPortOffset(method)
	}

	call := &callObj{
		portOffset: portOffset,
		method:     method,
		args:       args,
		rsp:        rspWrapper,
		done:       make(chan *callObj, 1),
	}

	select {
	case c.callCh <- call:
	case <-ctx.Done():
		klog.Warningf("[zbs-client] Invoke reaches the deadline and returns, method=%s", call.method)
		return ctx.Err()
	}

	select {
	// rpc done
	case call = <-call.done:
		// io or other err
		if call.err != nil {
			return call.err
		}
		// server reply error
		if rspWrapper.Error != nil {
			return rspWrapper.Error
		}

		return nil
	case <-ctx.Done():
		klog.Warningf("[zbs-client] Invoke reaches the deadline and returns, method=%s", call.method)
		return ctx.Err()
	}
}

func (c *Client) Invoke(ctx context.Context, method string, args interface{}, rsp interface{}, opts ...grpc.CallOption) error {
	var err error
	retry := int32(0)

	for {
		err = c.invoke(ctx, method, args, rsp)
		if err == nil || !zbserror.IsRetryable(err) {
			return err
		}

		retry++
		if retry > c.RetryNum {
			return err
		}

		time.Sleep(time.Duration(c.RetryIntervalMS) * time.Millisecond)
	}
}

func (c *Client) NewStream(ctx context.Context, desc *grpc.StreamDesc, method string, opts ...grpc.CallOption) (grpc.ClientStream, error) {
	return nil, status.Errorf(codes.Unimplemented, "unimpl method %v", method)
}
