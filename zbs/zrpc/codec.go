package zrpc

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"net/rpc"
	"time"
	"unsafe"

	"google.golang.org/protobuf/proto"
	"k8s.io/klog"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

type ResponseWrapper struct {
	Error    *zbserror.Error
	Response interface{}
}

type clientCodec struct {
	client *Client
	conn   net.Conn
	router *routerTable
	rsp    ResponseHeader
}

func newClientCodec(conn net.Conn, client *Client) *clientCodec {
	return &clientCodec{
		client: client,
		conn:   conn,
		router: newRouterTable(),
	}
}

func (c *clientCodec) WriteRequest(req *rpc.Request, args interface{}) error {
	var header RequestHeader

	header.MessageId = req.Seq
	header.Timeout = c.client.rpcTimeoutMS

	var err error

	header.ServiceId, header.MethodId, err = c.router.getId(req.ServiceMethod)
	if err != nil {
		return err
	}

	data, err := proto.Marshal(args.(proto.Message))
	if err != nil {
		return err
	}

	header.Len = uint64(len(data))

	buf := &bytes.Buffer{}
	if err := binary.Write(buf, binary.LittleEndian, header); err != nil {
		return err
	}

	if err := binary.Write(buf, binary.LittleEndian, data); err != nil {
		return err
	}

	c.setWriteTimeout(c.client.ioTimeoutMS)
	defer c.setWriteTimeout(0)

	if _, err := c.conn.Write(buf.Bytes()); err != nil {
		return err
	}

	return nil
}

func (c *clientCodec) ReadResponseHeader(rsp *rpc.Response) error {
	buf := make([]byte, unsafe.Sizeof(c.rsp))

	if _, err := c.conn.Read(buf); err != nil {
		return err
	}

	if err := binary.Read(bytes.NewBuffer(buf), binary.LittleEndian, &c.rsp); err != nil {
		return err
	}

	rsp.Seq = c.rsp.MessageId
	rsp.Error = ""

	return nil
}

func (c *clientCodec) readResponseErrStatus(rspWrapper *ResponseWrapper, len uint64) error {
	buf := make([]byte, len)
	if _, err := c.conn.Read(buf); err != nil {
		return err
	}

	var status zbs.RpcStatus
	if err := proto.Unmarshal(buf, &status); err != nil {
		return fmt.Errorf("%v, buf: %s", err, buf)
	}

	rspWrapper.Error = &zbserror.Error{St: &status}

	return nil
}

func (c *clientCodec) setReadTimeout(timeoutMS int32) {
	var deadline time.Time
	if timeoutMS > 0 {
		deadline = time.Now().Add(time.Duration(timeoutMS) * time.Millisecond)
	}

	_ = c.conn.SetReadDeadline(deadline)
}

func (c *clientCodec) setWriteTimeout(timeoutMS int32) {
	var deadline time.Time
	if timeoutMS > 0 {
		deadline = time.Now().Add(time.Duration(timeoutMS) * time.Millisecond)
	}

	_ = c.conn.SetWriteDeadline(deadline)
}

func (c *clientCodec) ReadResponseBody(rspWrapperInterface interface{}) error {
	rspWrapper, ok := rspWrapperInterface.(*ResponseWrapper)
	if !ok {
		return fmt.Errorf("unknown response type not ResponseWrapper")
	}

	c.setReadTimeout(c.client.ioTimeoutMS)
	defer c.setReadTimeout(0)

	// server reply error
	if c.rsp.Ec != 0 {
		return c.readResponseErrStatus(rspWrapper, c.rsp.Len)
	}

	buf := make([]byte, c.rsp.Len)
	if _, err := io.ReadFull(c.conn, buf); err != nil {
		return err
	}

	if err := proto.Unmarshal(buf, rspWrapper.Response.(proto.Message)); err != nil {
		return err
	}

	return nil
}

func (c *clientCodec) Close() error {
	return c.conn.Close()
}

type serverCodec struct {
	conn         net.Conn
	header       RequestHeader
	router       *routerTable
	packageNames []string
}

func newServerCodec(conn net.Conn, packageNames []string) rpc.ServerCodec {
	return &serverCodec{
		conn:         conn,
		router:       newRouterTable(),
		packageNames: packageNames,
	}
}

func (sc *serverCodec) ReadRequestHeader(req *rpc.Request) error {
	buf := make([]byte, unsafe.Sizeof(Header{}))
	if _, err := sc.conn.Read(buf); err != nil {
		return err
	}

	if err := binary.Read(bytes.NewBuffer(buf), binary.LittleEndian, &sc.header); err != nil {
		return err
	}

	var serviceMethod string
	var err error

	for _, packageName := range sc.packageNames {
		serviceMethod, err = sc.router.getMethod(packageName, sc.header.ServiceId, sc.header.MethodId)
		if err != nil {
			continue
		}

		break
	}

	if len(serviceMethod) == 0 {
		return fmt.Errorf("failed to get method by ID: {sid: %d, mid: %d}", sc.header.ServiceId, sc.header.MethodId)
	}

	req.ServiceMethod = serviceMethod
	req.Seq = sc.header.MessageId

	return nil
}

func (sc *serverCodec) ReadRequestBody(body interface{}) error {
	buf := make([]byte, sc.header.Len)
	if _, err := sc.conn.Read(buf); err != nil {
		return err
	}

	if body == nil {
		// discard body
		return nil
	}

	return proto.Unmarshal(buf, body.(proto.Message))
}

func (sc *serverCodec) WriteResponse(rsp *rpc.Response, body interface{}) error {
	header := &ResponseHeader{}
	header.MessageId = rsp.Seq

	var data []byte
	var err error
	ec := zbs.ErrorCode_EOK

	if rsp.Error != "" {
		ec = zbserror.ParseEC(rsp.Error)
		data = []byte(rsp.Error)
	} else {
		data, err = proto.Marshal(body.(proto.Message))
		if err != nil {
			// net/rpc ignore this error, so in order to expose the problem, directly panic
			panic(err)
		}
	}

	header.Ec = int32(ec)
	header.Len = uint64(len(data))
	buf := &bytes.Buffer{}

	if err := binary.Write(buf, binary.LittleEndian, header); err != nil {
		return err
	}

	writeBadProtocolHook(sc.conn)

	if err := binary.Write(buf, binary.LittleEndian, data); err != nil {
		return err
	}

	if _, err := sc.conn.Write(buf.Bytes()); err != nil {
		return err
	}

	return nil
}

func (sc *serverCodec) Close() error {
	err := sc.conn.Close()
	if err != nil {
		klog.Errorf("failed to close connection, %v", err)
	}

	return err
}
