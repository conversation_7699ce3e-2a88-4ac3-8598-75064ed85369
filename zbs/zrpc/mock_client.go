// Code generated by MockGen. DO NOT EDIT.
// Source: google.golang.org/grpc (interfaces: ClientConnInterface)
//
// Generated by this command:
//
//	mockgen -destination ./zbs/zrpc/mock_client.go -package zrpc google.golang.org/grpc ClientConnInterface
//

// Package zrpc is a generated GoMock package.
package zrpc

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockClientConnInterface is a mock of ClientConnInterface interface.
type MockClientConnInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientConnInterfaceMockRecorder
}

// MockClientConnInterfaceMockRecorder is the mock recorder for MockClientConnInterface.
type MockClientConnInterfaceMockRecorder struct {
	mock *MockClientConnInterface
}

// NewMockClientConnInterface creates a new mock instance.
func NewMockClientConnInterface(ctrl *gomock.Controller) *MockClientConnInterface {
	mock := &MockClientConnInterface{ctrl: ctrl}
	mock.recorder = &MockClientConnInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientConnInterface) EXPECT() *MockClientConnInterfaceMockRecorder {
	return m.recorder
}

// Invoke mocks base method.
func (m *MockClientConnInterface) Invoke(arg0 context.Context, arg1 string, arg2, arg3 any, arg4 ...grpc.CallOption) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Invoke", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Invoke indicates an expected call of Invoke.
func (mr *MockClientConnInterfaceMockRecorder) Invoke(arg0, arg1, arg2, arg3 any, arg4 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Invoke", reflect.TypeOf((*MockClientConnInterface)(nil).Invoke), varargs...)
}

// NewStream mocks base method.
func (m *MockClientConnInterface) NewStream(arg0 context.Context, arg1 *grpc.StreamDesc, arg2 string, arg3 ...grpc.CallOption) (grpc.ClientStream, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewStream", varargs...)
	ret0, _ := ret[0].(grpc.ClientStream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewStream indicates an expected call of NewStream.
func (mr *MockClientConnInterfaceMockRecorder) NewStream(arg0, arg1, arg2 any, arg3 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewStream", reflect.TypeOf((*MockClientConnInterface)(nil).NewStream), varargs...)
}
