package zrpc

import (
	"fmt"
	"sync"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"

	zbsv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

type cdpServer struct {
	mutex       sync.Mutex
	jobs        map[string]*zbsv1.CDPJobInfo
	groupJobIds map[string][]string
}

func NewCDPServer() *cdpServer {
	return &cdpServer{
		jobs:        make(map[string]*zbsv1.CDPJobInfo),
		groupJobIds: make(map[string][]string),
	}
}

func (s *cdpServer) CreateJobsByGroup(req *zbsv1.CreateCDPJobsByGroupRequest, rsp *zbsv1.CreateCDPJobsByGroupResponse) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	groupId := string(req.GetGroup())
	if len(groupId) == 0 {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_EInvalidArgument, "empty group id"))
	}

	createJobReqs := req.GetCreateReqs()
	jobIds := make([]string, len(createJobReqs))
	rsp.CdpJobs = make([]*zbsv1.CDPJobInfo, len(createJobReqs))

	for i, createJobReq := range createJobReqs {
		createJobReq.Group = req.GetGroup()

		jobInfo := s.generateCDPJobInfo(createJobReq)
		jobId := string(jobInfo.GetId())

		jobIds[i] = jobId
		rsp.CdpJobs[i] = jobInfo
		s.jobs[jobId] = jobInfo
	}

	// `CreateJobsByGroup` can be called multiple times for the same group.
	// The result is to add new jobs to an existing group.
	s.groupJobIds[groupId] = append(s.groupJobIds[groupId], jobIds...)

	return returnErrorHook(nil)
}

func (s *cdpServer) ListJobs(req *zbsv1.ListCDPJobsRequest, rsp *zbsv1.ListCDPJobsResponse) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	groupId := string(req.GetGroup())

	// special case: list all jobs when group id is empty
	if len(groupId) == 0 {
		rsp.Jobs = make([]*zbsv1.CDPJobInfo, 0, len(s.jobs))

		for _, job := range s.jobs {
			rsp.Jobs = append(rsp.Jobs, job)
		}
	} else {
		// Consistent with the behavior of ZBS, if the group doesn't exist, an empty result is returned instead of an error.
		jobIds := s.groupJobIds[groupId]
		rsp.Jobs = make([]*zbsv1.CDPJobInfo, 0, len(jobIds))

		for _, jobId := range jobIds {
			jobCopy := &zbsv1.CDPJobInfo{}

			proto.Merge(jobCopy, s.jobs[jobId])

			rsp.Jobs = append(rsp.Jobs, jobCopy)
		}
	}

	return returnErrorHook(nil)
}

func (s *cdpServer) FinishJobsByGroup(req *zbsv1.FinishCDPJobsByGroupRequest, _ *zbsv1.Void) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	groupId := string(req.GetGroup())
	if len(groupId) == 0 {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_EInvalidArgument, "empty group id"))
	}

	jobIds, ok := s.groupJobIds[groupId]
	if !ok {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_ENotFound, fmt.Sprintf("cdp job group %s not found", groupId)))
	}

	doneStage := zbsv1.CDPJobStage_CDP_STAGE_DONE

	for _, jobId := range jobIds {
		job, ok := s.jobs[jobId]
		if !ok {
			continue
		}

		job.Stage = &doneStage

		if job.GetAutoClean() {
			delete(s.jobs, jobId)
			delete(s.groupJobIds, groupId)
		}
	}

	return returnErrorHook(nil)
}

func (s *cdpServer) CancelJobsByGroup(req *zbsv1.CancelCDPJobsByGroupRequest, _ *zbsv1.Void) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	groupId := string(req.GetGroup())
	if len(groupId) == 0 {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_EInvalidArgument, "empty group id"))
	}

	jobIds, ok := s.groupJobIds[groupId]
	if !ok {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_ENotFound, fmt.Sprintf("cdp job group %s not found", groupId)))
	}

	cancelledStage := zbsv1.CDPJobStage_CDP_STAGE_CANCELLED

	for _, jobId := range jobIds {
		job, ok := s.jobs[jobId]
		if !ok {
			continue
		}

		job.Stage = &cancelledStage

		if job.GetAutoClean() {
			delete(s.jobs, jobId)
			delete(s.groupJobIds, groupId)
		}
	}

	return returnErrorHook(nil)
}

func (s *cdpServer) DeleteJobsByGroup(req *zbsv1.DeleteCDPJobsByGroupRequest, _ *zbsv1.Void) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	groupId := string(req.GetGroup())
	if len(groupId) == 0 {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_EInvalidArgument, "empty group id"))
	}

	jobIds, ok := s.groupJobIds[groupId]
	if !ok {
		return returnErrorHook(nil)
	}

	// The group can only be deleted if all jobs in the group can be deleted.
	for _, jobId := range jobIds {
		job, ok := s.jobs[jobId]
		if !ok {
			continue
		}

		if !s.isJobDeletable(job) {
			return returnErrorHook(zbserror.New(zbsv1.ErrorCode_EBadRequest, fmt.Sprintf("cdp job %s of group %s can not be delete", jobId, groupId)))
		}
	}

	for _, jobId := range jobIds {
		delete(s.jobs, jobId)
	}

	delete(s.groupJobIds, groupId)

	return returnErrorHook(nil)
}

func (s *cdpServer) GetJobByVolume(req *zbsv1.GetCDPJobByVolumeRequest, rsp *zbsv1.CDPJobInfo) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	volumeId := string(req.GetVolumeId())

	for _, job := range s.jobs {
		if string(job.GetLocal().GetVolumeId()) == volumeId {
			proto.Merge(rsp, job)

			return returnErrorHook(nil)
		}
	}

	return returnErrorHook(zbserror.New(zbsv1.ErrorCode_ENotFound, fmt.Sprintf("cdp job of volume %s not found", volumeId)))
}

func (s *cdpServer) SyncJobStage(req *zbsv1.CDPJobStageUpdate, _ *zbsv1.Void) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	jobId := string(req.Id)
	if len(jobId) == 0 {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_EInvalidArgument, "empty job id"))
	}

	job, ok := s.jobs[jobId]
	if !ok {
		return returnErrorHook(zbserror.New(zbsv1.ErrorCode_ENotFound, fmt.Sprintf("cdp job %s not found", jobId)))
	}

	job.Stage = req.Stage

	s.cleanJobIfNeed(job)

	return returnErrorHook(nil)
}

func (s *cdpServer) generateCDPJobInfo(req *zbsv1.CreateCDPJobRequest) *zbsv1.CDPJobInfo {
	initStage := zbsv1.CDPJobStage_CDP_STAGE_INIT

	return &zbsv1.CDPJobInfo{
		Id:              []byte(uuid.New().String()),
		Local:           req.GetLocal(),
		Remote:          req.GetRemote(),
		Group:           req.GetGroup(),
		Cid:             req.Cid,
		Stage:           &initStage,
		SkipFcWriteZero: req.SkipFcWriteZero,
		AutoClean:       req.AutoClean,
	}
}

func (s *cdpServer) cleanJobIfNeed(job *zbsv1.CDPJobInfo) {
	if !job.GetAutoClean() || !s.isJobDeletable(job) {
		return
	}

	jobId := string(job.GetId())
	groupId := string(job.GetGroup())

	delete(s.jobs, jobId)

	groupJobIds, ok := s.groupJobIds[groupId]
	if !ok {
		return
	}

	newGroupJobIds := make([]string, 0)

	for _, groupJobId := range groupJobIds {
		if groupJobId == jobId {
			continue
		}

		newGroupJobIds = append(newGroupJobIds, groupJobId)
	}

	s.groupJobIds[groupId] = newGroupJobIds
}

func (s *cdpServer) isJobDeletable(job *zbsv1.CDPJobInfo) bool {
	stage := job.GetStage()

	return stage == zbsv1.CDPJobStage_CDP_STAGE_CANCELLED ||
		stage == zbsv1.CDPJobStage_CDP_STAGE_DONE ||
		stage == zbsv1.CDPJobStage_CDP_STAGE_ERROR
}
