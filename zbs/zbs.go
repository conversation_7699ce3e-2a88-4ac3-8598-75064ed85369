package zbs

import (
	"github.com/iomesh/zbs-client-go/zbs/zrpc"
)

type Client struct {
	client *zrpc.Client

	ISCSI  *ISCSIService
	NFS    *NFSService
	NVMF   *NVMFService
	Meta   *MetaService
	Status *StatusService
	Chunk  *ChunkService
	VIP    *VIPService
	Block  *BlockService
	CDP    *CDPService
}

func (c *Client) GetRpcClient() *zrpc.Client {
	return c.client
}

func (c *Client) Close() error {
	return c.client.Close()
}

func NewClientWithConfig(config *zrpc.Config) (*Client, error) {
	if config == nil {
		config = zrpc.DefaultClientConfig()
	}

	client, err := zrpc.NewClient(config)
	if err != nil {
		return nil, err
	}

	return &Client{
		client: client,
		ISCSI:  NewISCSIService(client),
		NFS:    NewNFSService(client),
		NVMF:   NewNVMFService(client),
		Meta:   NewMetaService(client),
		Status: NewStatusService(client),
		Chunk:  NewChunkService(client),
		VIP:    NewVIPService(client),
		Block:  NewBlockService(client),
		CDP:    NewCDPService(client),
	}, nil
}

func NewClient(watcher *zrpc.LeaderWatcher) (*Client, error) {
	config := zrpc.DefaultClientConfig()
	config.Watcher = watcher

	return NewClientWithConfig(config)
}

func NewStaticClient(addr string) (*Client, error) {
	config := zrpc.DefaultClientConfig()
	config.Addr = addr

	return NewClientWithConfig(config)
}

func newMockClient(addr string) (*Client, error) {
	config := zrpc.DefaultClientConfig()
	config.PortRouter = nil
	config.IOTimeoutMS = 3000
	config.Addr = addr

	return NewClientWithConfig(config)
}
