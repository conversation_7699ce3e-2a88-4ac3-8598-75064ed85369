package zbs

import (
	"errors"
	"io"
	"testing"
)

const (
	stripeSize = 256 << 10 // 256KB
	stripeNum  = 4
)

func TestCompareVolumeIterClose(t *testing.T) {
	iter := &compareVolumeIterator{
		manager: new(compareVolumeManager),
	}

	// mock have started to compare
	iter.prepareCompareInfo()

	err := iter.Close()
	if err != nil {
		t.Fatalf("TestCompareVolumeIterClose failed: close error: %v", err)
	} else if !iter.haveClosed {
		t.<PERSON><PERSON>("TestCompareVolumeIterClose failed: iterator is not closed")
	}

	_, ok := <-iter.closeCh
	if ok {
		t.Fatal("TestCompareVolumeIterClose failed: iterator closeCh is still open")
	}

	// check multiple close
	err = iter.Close()
	if err != nil {
		t.Fatalf("TestCompareVolumeIterClose failed: double close error: %v", err)
	}
}

func TestCompareVolumeIterCloseWithoutStarting(t *testing.T) {
	iter := &compareVolumeIterator{
		manager: new(compareVolumeManager),
	}

	err := iter.Close()
	if err != nil {
		t.Fatalf("TestCompareVolumeIterClose failed: close error: %v", err)
	} else if !iter.haveClosed {
		t.Fatal("TestCompareVolumeIterClose failed: iterator is not closed")
	}
}

func TestCompareVolumeIterSeekForward(t *testing.T) {
	iter := &compareVolumeIterator{
		manager: &compareVolumeManager{
			stripeSize:            stripeSize,
			stripeNum:             stripeNum,
			baseVolumeExtentIds:   []uint32{0, 1, 0, 1},
			targetVolumeExtentIds: []uint32{1, 1, 1, 1},
			compareMode:           compareModePid,
		},
	}

	err := iter.Seek(0)
	if err != nil {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: seek zero error: %v", err)
	}

	diffRange, err := iter.Next()
	if err != nil {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: next error: %v", err)
	}

	if diffRange.Start != 0 || diffRange.Length != stripeSize {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: unexcepted diff range: %v", diffRange)
	}

	currentCloseCh := iter.closeCh

	// seek forward to the same extent group
	err = iter.Seek(4 * stripeSize)
	if err != nil {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: seek forward to the same extent group error: %v", err)
	}

	select {
	case <-currentCloseCh:
		t.Fatalf("TestCompareVolumeIterSeekForward failed: seek forward to the same extent group shouldn't stop current comparison")
	default:
	}

	diffRange, err = iter.Next()
	if err != nil {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: next error: %v", err)
	}

	// should skip diffRange{start: 2*stripeSize, length: stripSize}
	if diffRange.Start != 4*stripeSize || diffRange.Length != stripeSize {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: unexcepted diff range: %v", diffRange)
	}

	err = iter.Seek(stripeNum * DefaultExtentSize)
	if err != nil {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: seek forward to the other extent group error: %v", err)
	}

	select {
	case <-currentCloseCh:
	default:
		t.Fatalf("TestCompareVolumeIterSeekForward failed: seek forward to the other extent group should stop current comparison")
	}

	_, err = iter.Next()
	if !errors.Is(err, io.EOF) {
		t.Fatalf("TestCompareVolumeIterSeekForward failed: next error should be EOF: %v", err)
	}
}

func TestCompareVolumeIterSeekBackward(t *testing.T) {
	iter := &compareVolumeIterator{
		manager: &compareVolumeManager{
			stripeSize:            stripeSize,
			stripeNum:             stripeNum,
			baseVolumeExtentIds:   []uint32{0, 1, 0, 1},
			targetVolumeExtentIds: []uint32{1, 1, 1, 1},
			compareMode:           compareModePid,
		},
	}

	err := iter.Seek(0)
	if err != nil {
		t.Fatalf("TestComapreVolumeIterSeekBackward failed: seek zero error: %v", err)
	}

	diffRange, err := iter.Next()
	if err != nil {
		t.Fatalf("TestComapreVolumeIterSeekBackward failed: next error: %v", err)
	}

	if diffRange.Start != 0 || diffRange.Length != stripeSize {
		t.Fatalf("TestComapreVolumeIterSeekBackward failed: unexcepted diff range: %v", diffRange)
	}

	currentCloseCh := iter.closeCh

	// seek backward
	err = iter.Seek(iter.offset - 10)
	if err != nil {
		t.Fatalf("TestComapreVolumeIterSeekBackward failed: seek backward error: %v", err)
	}

	select {
	case <-currentCloseCh:
	default:
		t.Fatalf("TestComapreVolumeIterSeekBackward failed: seek backward should stop current comparison")
	}

	diffRange, err = iter.Next()
	if err != nil {
		t.Fatalf("TestComapreVolumeIterSeekBackward failed: next error: %v", err)
	}

	if diffRange.Start != 0 || diffRange.Length != stripeSize {
		t.Fatalf("TestComapreVolumeIterSeekBackward failed: unexcepted diff range: %v", diffRange)
	}
}

func TestClosedCompareVolumeIterSeek(t *testing.T) {
	iter := &compareVolumeIterator{
		manager: &compareVolumeManager{
			stripeSize: stripeSize,
			stripeNum:  stripeNum,
		},
		haveClosed: true,
	}

	err := iter.Seek(0)
	if err == nil {
		t.Fatal("TestClosedCompareVolumeIterSeek failed: closed iterator seek should return error")
	}
}
