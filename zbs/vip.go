package zbs

import (
	"context"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/task"
	"google.golang.org/grpc"
)

type VirtualIPs = task.VirtualIPs

type VIPService struct {
	client task.VIPServiceClient
}

func NewVIPService(client grpc.ClientConnInterface) *VIPService {
	return &VIPService{
		client: task.NewVIPServiceClient(client),
	}
}

func (s *VIPService) UpsertVirtualIP(ctx context.Context, name, ip string) error {
	_, err := s.client.SetVIP(ctx, &task.VirtualIP{
		ServiceName: &name,
		Ip:          &ip,
	})

	return err
}

func (s *VIPService) ListVirtualIPs(ctx context.Context) (*VirtualIPs, error) {
	return s.client.ShowVIP(ctx, &zbs.Void{})
}

func (s *VIPService) DeleteVirtualIP(ctx context.Context, name, ip string) error {
	_, err := s.client.DeleteVIP(ctx, &task.VirtualIP{
		ServiceName: &name,
		Ip:          &ip,
	})

	return err
}
