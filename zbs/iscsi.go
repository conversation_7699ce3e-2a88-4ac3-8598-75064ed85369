package zbs

import (
	"context"
	"strings"

	"github.com/samber/lo"
	"google.golang.org/grpc"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
)

type ISCSITarget = meta.ISCSITarget
type ISCSILun = meta.ISCSILun
type ISCSISnapshot = meta.Volume

type ISCSITargetConfig struct {
	IqnWhiteList        []string
	WhiteList           []string
	CreateInitiatorChap []*InitiatorChapInfo
	UpdateInitiatorChap []*InitiatorChapInfo
	RemoveInitiatorChap []string
}

type InitiatorChapInfo struct {
	Iqn      string
	ChapName string
	Secret   string
	Enable   *bool
}

type LunCloneOptions struct {
	SrcLunPath    *meta.LunPath
	SrcSnapshotId *string
}

type ISCSIService struct {
	client meta.ISCSIServiceClient
}

func NewISCSIService(client grpc.ClientConnInterface) *ISCSIService {
	return &ISCSIService{
		client: meta.NewISCSIServiceClient(client),
	}
}

func (s *ISCSIService) CreateTarget(ctx context.Context, name string, sp *StoragePolicy, config *ISCSITargetConfig) (*ISCSITarget, error) {
	req := &meta.CreateISCSITargetRequest{}
	req.Name = []byte(name)

	if sp == nil {
		sp = NewDefaultStoragePolicy()
	}

	req.ReplicaNum = &sp.ReplicaFactor
	req.ThinProvision = &sp.ThinProvision
	req.StripeNum = &sp.StripeNum
	req.StripeSize = &sp.StripeSize

	if sp.EC != nil {
		req.ReplicaNum = nil
		req.ResiliencyType = lo.ToPtr(zbs.ResiliencyType_RT_EC)
		req.EcParam = &zbs.ECAlgorithmParam{
			Name:   lo.ToPtr(ECAlgorithmName),
			K:      &sp.EC.K,
			M:      &sp.EC.M,
			EcType: lo.ToPtr(zbs.ECAlgorithmType_REED_SOLOMON),
			RsArg:  &zbs.ECReedSolArg{},
		}
	}

	if config != nil {
		if len(config.IqnWhiteList) > 0 {
			req.IqnWhitelistV2 = []byte(strings.Join(config.IqnWhiteList, ","))
		}

		if len(config.WhiteList) > 0 {
			req.Whitelist = []byte(strings.Join(config.WhiteList, ","))
		}
	}

	return s.client.CreateTarget(ctx, req)
}

func (s *ISCSIService) UpdateTarget(ctx context.Context, id string,
	newName *string, config *ISCSITargetConfig) (*ISCSITarget, error) {
	req := &meta.UpdateISCSITargetRequest{
		PoolPath: &meta.PoolPath{
			PoolId: []byte(id),
		},
		CreateInitiatorChap: []*meta.InitiatorChapInfo{},
		UpdateInitiatorChap: []*meta.InitiatorChapInfo{},
		RemoveInitiatorChap: [][]byte{},
	}

	if newName != nil {
		req.Name = []byte(*newName)
	}

	if config.WhiteList != nil {
		req.Whitelist = []byte(strings.Join(config.WhiteList, ","))
	}

	if config.IqnWhiteList != nil {
		req.IqnWhitelistV2 = []byte(strings.Join(config.IqnWhiteList, ","))
	}

	if config.CreateInitiatorChap != nil {
		for _, chapInfo := range config.CreateInitiatorChap {
			req.CreateInitiatorChap = append(req.CreateInitiatorChap,
				convertChapInfo(chapInfo))
		}
	}

	if config.UpdateInitiatorChap != nil {
		for _, chapInfo := range config.UpdateInitiatorChap {
			req.UpdateInitiatorChap = append(req.UpdateInitiatorChap,
				convertChapInfo(chapInfo))
		}
	}

	if config.RemoveInitiatorChap != nil {
		for _, iqn := range config.RemoveInitiatorChap {
			req.RemoveInitiatorChap = append(req.RemoveInitiatorChap, []byte(iqn))
		}
	}

	return s.client.UpdateTarget(ctx, req)
}

func (s *ISCSIService) DescribeTarget(ctx context.Context, id string) (*ISCSITarget, error) {
	req := &meta.PoolPath{}
	req.PoolId = []byte(id)

	return s.client.GetTarget(ctx, req)
}

func (s *ISCSIService) DeleteTarget(ctx context.Context, id string) error {
	req := &meta.PoolPath{}
	req.PoolId = []byte(id)

	_, err := s.client.DeleteTarget(ctx, req)

	return err
}

func (s *ISCSIService) GetTargets(ctx context.Context) ([]*ISCSITarget, error) {
	resp, err := s.client.ListTargets(ctx, &meta.ListTargetsRequest{})

	if err != nil {
		return []*ISCSITarget(nil), err
	}

	return resp.Targets, err
}

func (s *ISCSIService) CreateLunByTargetUUID(ctx context.Context, targetId string, lunName string, lunId uint32, size uint64,
	opts ...LunOptionSetter) (*ISCSILun, error) {
	lunPath := &meta.LunPath{
		PoolPath: &meta.PoolPath{
			PoolId: []byte(targetId),
		},
		LunId:   &lunId,
		LunName: []byte(lunName),
	}

	return s.createLun(ctx, append(opts, WithLunPath(lunPath), WithLunSize(size))...)
}

func (s *ISCSIService) CreateLunBySecondaryId(ctx context.Context, targetId string, lunName string, secondaryId string, size uint64,
	opts ...LunOptionSetter) (*ISCSILun, error) {
	lunPath := &meta.LunPath{
		PoolPath:    &meta.PoolPath{},
		SecondaryId: []byte(secondaryId),
		LunName:     []byte(lunName),
	}

	if targetId != "" {
		lunPath.PoolPath.PoolId = []byte(targetId)
	}

	return s.createLun(ctx, append(opts, WithLunPath(lunPath), WithLunSize(size))...)
}

func (s *ISCSIService) createLun(ctx context.Context, setters ...LunOptionSetter) (*ISCSILun, error) {
	opts := &LunOptions{}

	for _, doSet := range setters {
		doSet(opts)
	}

	req := &meta.CreateISCSILunRequest{}

	req.LunPath = opts.LunPath
	if opts.Size > 0 {
		req.Size = &opts.Size
	}

	sp := opts.StoragePolicy
	if sp == nil {
		sp = NewDefaultStoragePolicy()
	}

	req.ReplicaNum = &sp.ReplicaFactor
	req.ThinProvision = &sp.ThinProvision
	req.StripeNum = &sp.StripeNum
	req.StripeSize = &sp.StripeSize

	if sp.EC != nil {
		req.ReplicaNum = nil
		req.ResiliencyType = lo.ToPtr(zbs.ResiliencyType_RT_EC)
		req.EcParam = &zbs.ECAlgorithmParam{
			Name:   lo.ToPtr(ECAlgorithmName),
			K:      &sp.EC.K,
			M:      &sp.EC.M,
			EcType: lo.ToPtr(zbs.ECAlgorithmType_REED_SOLOMON),
			RsArg:  &zbs.ECReedSolArg{},
		}
	}

	if opts.Throttling != nil {
		req.Throttling = opts.Throttling
	}

	if opts.CloneOpts != nil {
		if opts.CloneOpts.SrcSnapshotId != nil {
			req.SrcSnapshotId = []byte(*opts.CloneOpts.SrcSnapshotId)
		} else {
			req.SrcLunPath = opts.CloneOpts.SrcLunPath
		}
	}

	req.AllowedInitiators = opts.AllowedInitiators
	req.TargetRequirement = opts.TargetRequirement
	req.SingleAccess = &opts.SingleAccess
	req.Description = opts.Description
	req.Prioritized = opts.Prioritized
	req.InodePath = opts.InodePath
	req.ChunkInstancesBits = opts.ChunkInstancesBits

	return s.client.CreateLun(ctx, req)
}

func (s *ISCSIService) LunConvertFromNFSFile(
	ctx context.Context,
	targetName string,
	lunName string,
	lunId uint32,
	nfsInodePath string,
	opts ...LunOptionSetter,
) (*ISCSILun, error) {
	lunPath := &meta.LunPath{
		PoolPath: &meta.PoolPath{
			PoolName: []byte(targetName),
		},
		LunName: []byte(lunName),
		LunId:   &lunId,
	}

	return s.createLun(ctx, append(opts, WithLunPath(lunPath), WithLunInodePath(nfsInodePath))...)
}

func (s *ISCSIService) CreateLun(ctx context.Context, targetName string, lunName string, lunId uint32, size uint64,
	opts ...LunOptionSetter) (*ISCSILun, error) {
	lunPath := &meta.LunPath{
		PoolPath: &meta.PoolPath{
			PoolName: []byte(targetName),
		},
		LunName: []byte(lunName),
		LunId:   &lunId,
	}

	return s.createLun(ctx, append(opts, WithLunPath(lunPath), WithLunSize(size))...)
}

func (s *ISCSIService) updateLun(ctx context.Context, setters ...LunOptionSetter) (*ISCSILun, error) {
	opts := &LunOptions{}

	for _, doSet := range setters {
		doSet(opts)
	}

	req := &meta.UpdateISCSILunRequest{}

	req.LunPath = opts.LunPath
	req.NewName = opts.NewName
	req.Description = opts.Description
	req.Size = opts.NewSize
	req.ReplicaNum = opts.ReplicaNum
	req.ThinProvision = opts.ThinProvision
	req.Throttling = opts.Throttling
	req.NewAllowedInitiators = opts.NewAllowedInitiators
	req.SingleAccess = opts.NewSingleAccess
	req.Prioritized = opts.Prioritized
	req.ChunkInstancesBits = opts.ChunkInstancesBits

	return s.client.UpdateLun(ctx, req)
}

func (s *ISCSIService) UpdateLun(ctx context.Context, lunUuid string,
	opts ...LunOptionSetter) (*ISCSILun, error) {
	lunPath := &meta.LunPath{
		LunUuid:  []byte(lunUuid),
		PoolPath: &meta.PoolPath{},
	}

	return s.updateLun(ctx, append(opts, WithLunPath(lunPath))...)
}

func (s *ISCSIService) DescribeLun(ctx context.Context, lunUuid string) (*ISCSILun, error) {
	req := &meta.LunPath{
		PoolPath: &meta.PoolPath{},
	}

	req.LunUuid = []byte(lunUuid)

	return s.client.GetLun(ctx, req)
}

func (s *ISCSIService) DescribeLunBySecondaryId(ctx context.Context, secondaryId string) (*ISCSILun, error) {
	req := &meta.LunPath{
		PoolPath: &meta.PoolPath{},
	}

	req.SecondaryId = []byte(secondaryId)

	return s.client.GetLun(ctx, req)
}

func (s *ISCSIService) DeleteLun(ctx context.Context, lunUuid string) error {
	return s.DeleteLunV2(ctx, lunUuid, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (s *ISCSIService) DeleteLunV2(ctx context.Context, lunUuid string, deletePermanently bool) error {
	req := &meta.DeleteLunRequest{
		PoolPath:          &meta.PoolPath{},
		LunUuid:           []byte(lunUuid),
		DeletePermanently: &deletePermanently,
	}
	_, err := s.client.DeleteLun(ctx, req)

	return err
}

func (s *ISCSIService) DeleteLunBySecondaryId(ctx context.Context, secondaryId string) error {
	return s.DeleteLunBySecondaryIdV2(ctx, secondaryId, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (s *ISCSIService) DeleteLunBySecondaryIdV2(ctx context.Context, secondaryId string, deletePermanently bool) error {
	req := &meta.DeleteLunRequest{
		PoolPath:          &meta.PoolPath{},
		SecondaryId:       []byte(secondaryId),
		DeletePermanently: &deletePermanently,
	}
	_, err := s.client.DeleteLun(ctx, req)

	return err
}

func (s *ISCSIService) GetLuns(ctx context.Context, targetId string) ([]*ISCSILun, error) {
	req := &meta.PoolPath{}
	req.PoolId = []byte(targetId)

	resp, err := s.client.ListLun(ctx, req)
	if err != nil {
		return []*ISCSILun(nil), err
	}

	return resp.Luns, nil
}

func (s *ISCSIService) AddLunAllowedInitiators(ctx context.Context, lunUuId string, initiators []string) (*meta.ISCSILun, error) {
	req := &meta.AddLunAllowedInitiatorsRequest{
		LunPath: &meta.LunPath{
			PoolPath: &meta.PoolPath{},
			LunUuid:  []byte(lunUuId),
		},
		NewAllowedInitiators: []byte(strings.Join(initiators, ",")),
	}

	return s.client.AddLunAllowedInitiators(ctx, req)
}

func (s *ISCSIService) RemoveLunAllowedInitiators(ctx context.Context, lunUuId string, initiators []string) (*meta.ISCSILun, error) {
	req := &meta.RemoveLunAllowedInitiatorsRequest{
		LunPath: &meta.LunPath{
			PoolPath: &meta.PoolPath{},
			LunUuid:  []byte(lunUuId),
		},
		Initiators: []byte(strings.Join(initiators, ",")),
	}

	return s.client.RemoveLunAllowedInitiators(ctx, req)
}

func (s *ISCSIService) CreateSnapshot(ctx context.Context, lunUuid string, name string, desc string) (*ISCSISnapshot, error) {
	req := &meta.CreateISCSISnapshotRequest{
		SnapshotName: []byte(name),
		SnapshotDesc: []byte(desc),
		LunPath: &meta.LunPath{
			PoolPath: &meta.PoolPath{},
			LunUuid:  []byte(lunUuid),
		},
	}

	return s.client.CreateSnapshot(ctx, req)
}

func (s *ISCSIService) CreateSnapshotBySecondaryId(ctx context.Context, lunUuid string,
	secondaryId string, name string, desc string) (*ISCSISnapshot, error) {
	req := &meta.CreateISCSISnapshotRequest{
		SnapshotName: []byte(name),
		SnapshotDesc: []byte(desc),
		SecondaryId:  []byte(secondaryId),
		LunPath: &meta.LunPath{
			PoolPath: &meta.PoolPath{},
			LunUuid:  []byte(lunUuid),
		},
	}

	return s.client.CreateSnapshot(ctx, req)
}

func (s *ISCSIService) DescribeSnapshot(ctx context.Context, snapshotId string) (*ISCSISnapshot, error) {
	req := &meta.ISCSISnapshotPath{
		SnapshotId: []byte(snapshotId),
	}

	return s.client.ShowSnapshot(ctx, req)
}

func (s *ISCSIService) DescribeSnapshotBySecondaryId(ctx context.Context, secondaryId string) (*ISCSISnapshot, error) {
	req := &meta.ISCSISnapshotPath{
		SecondaryId: []byte(secondaryId),
	}

	return s.client.ShowSnapshot(ctx, req)
}

func (s *ISCSIService) DeleteSnapshot(ctx context.Context, snapshotId string) error {
	return s.DeleteSnapshotV2(ctx, snapshotId, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (s *ISCSIService) DeleteSnapshotV2(ctx context.Context, snapshotId string, deletePermanently bool) error {
	req := &meta.DeleteISCSISnapshotRequest{
		SnapshotId:        []byte(snapshotId),
		DeletePermanently: &deletePermanently,
	}
	_, err := s.client.DeleteSnapshot(ctx, req)

	return err
}

func (s *ISCSIService) DeleteSnapshotBySecondaryId(ctx context.Context, secondaryId string) error {
	return s.DeleteSnapshotBySecondaryIdV2(ctx, secondaryId, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (s *ISCSIService) DeleteSnapshotBySecondaryIdV2(ctx context.Context, secondaryId string,
	deletePermanently bool) error {
	req := &meta.DeleteISCSISnapshotRequest{
		SecondaryId:       []byte(secondaryId),
		DeletePermanently: &deletePermanently,
	}

	_, err := s.client.DeleteSnapshot(ctx, req)

	return err
}

func (s *ISCSIService) ListTargetSnapshot(ctx context.Context, targetId string) ([]*ISCSISnapshot, error) {
	req := &meta.LunPath{
		PoolPath: &meta.PoolPath{
			PoolId: []byte(targetId),
		},
	}

	rsp, err := s.client.ListSnapshot(ctx, req)
	if err != nil {
		return nil, err
	}

	return rsp.Snapshots, nil
}

func (s *ISCSIService) ListLunSnapshot(ctx context.Context, lunUuid string) ([]*ISCSISnapshot, error) {
	req := &meta.LunPath{
		PoolPath: &meta.PoolPath{},
		LunUuid:  []byte(lunUuid),
	}

	rsp, err := s.client.ListSnapshot(ctx, req)
	if err != nil {
		return nil, err
	}

	return rsp.Snapshots, nil
}

// covert chapInfo to proto format, used in IscsiTarget RPC
func convertChapInfo(chapInfo *InitiatorChapInfo) *meta.InitiatorChapInfo {
	metaChap := &meta.InitiatorChapInfo{}

	metaChap.Iqn = []byte(chapInfo.Iqn)
	metaChap.ChapName = []byte(chapInfo.ChapName)
	metaChap.Secret = []byte(chapInfo.Secret)
	metaChap.Enable = chapInfo.Enable

	return metaChap
}
