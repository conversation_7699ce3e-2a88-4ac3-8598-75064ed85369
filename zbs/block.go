package zbs

import (
	"context"

	blockv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/block"
	"google.golang.org/grpc"
)

type BlockService struct {
	client blockv1.CompareExtentServiceClient
}

func NewBlockService(client grpc.ClientConnInterface) *BlockService {
	return &BlockService{
		client: blockv1.NewCompareExtentServiceClient(client),
	}
}

func (s *BlockService) CompareLExtent(ctx context.Context, lid1 uint32, lid2 uint32) (*blockv1.CompareExtentResponse, error) {
	req := &blockv1.CompareLExtentRequest{
		Lid1: &lid1,
		Lid2: &lid2,
	}

	return s.client.CompareLExtent(ctx, req)
}
