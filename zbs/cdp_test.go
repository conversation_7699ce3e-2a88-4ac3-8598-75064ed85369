package zbs

import (
	"context"
	"testing"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
)

var (
	remoteHost           = "remote-host"
	groupId              = "test-group-id"
	localRemoteVolumeMap = map[string]string{
		"local-volume-1": "remote-volume-1",
		"local-volume-2": "remote-volume-2",
		"local-volume-3": "remote-volume-3",
	}
)

type cdpRequest struct {
	groupId         string
	chunkId         *uint32
	skipFcWriteZero bool
	autoClean       bool
}

// TestCDPSerive is used to wrap the CDPService to provide some methods for testing.
type TestCDPService struct {
	*CDPService
}

func (s *TestCDPService) SyncJobStage(ctx context.Context, jobId string, stage zbs.CDPJobStage) error {
	req := &zbs.CDPJobStageUpdate{
		Id:    []byte(jobId),
		Stage: &stage,
	}

	_, err := s.client.SyncJobStage(ctx, req)

	return err
}

func TestCreateCDPJobByGroup(t *testing.T) {
	client, server := NewMockServerCDP(t)

	defer stopMockServer(server)

	ctx := context.TODO()
	cdpClient := client.CDP
	cId := uint32(1)

	cases := []cdpRequest{
		{
			groupId:         "not-skip-auto-clean",
			skipFcWriteZero: false,
			autoClean:       true,
		},
		{
			groupId:         "skip-not-auto-clean",
			skipFcWriteZero: true,
			autoClean:       false,
		},
		{
			groupId: "have-chunk-id",
			chunkId: &cId,
		},
	}

	for i := range cases {
		createResp, err := cdpClient.CreateJobsByGroup(ctx, localRemoteVolumeMap, remoteHost, cases[i].groupId,
			cases[i].chunkId, cases[i].skipFcWriteZero, cases[i].autoClean)
		if err != nil {
			t.Fatalf("failed to create cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		// check create cdp job response
		checkCDPJobs(t, &cases[i], createResp.CdpJobs)

		listResp, err := cdpClient.ListJobsByGroup(ctx, cases[i].groupId)
		if err != nil {
			t.Fatalf("failed to list cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		// check list cdp job response
		checkCDPJobs(t, &cases[i], listResp.Jobs)
	}
}

func TestCancelCDPJobByGroup(t *testing.T) {
	client, server := NewMockServerCDP(t)

	defer stopMockServer(server)

	ctx := context.TODO()
	cdpClient := client.CDP

	cases := []cdpRequest{
		{
			groupId:   "auto-clean",
			autoClean: true,
		},
		{
			groupId:   "not-auto-clean",
			autoClean: false,
		},
	}
	expectedStage := zbs.CDPJobStage_CDP_STAGE_CANCELLED

	for i := range cases {
		_, err := cdpClient.CreateJobsByGroup(ctx, localRemoteVolumeMap, remoteHost, cases[i].groupId,
			cases[i].chunkId, cases[i].skipFcWriteZero, cases[i].autoClean)
		if err != nil {
			t.Fatalf("failed to create cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		err = cdpClient.CancelJobsByGroup(ctx, cases[i].groupId, true)
		if err != nil {
			t.Fatalf("failed to cancel cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		listResp, err := cdpClient.ListJobsByGroup(ctx, cases[i].groupId)
		if err != nil {
			t.Fatalf("failed to list cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		if cases[i].autoClean {
			if len(listResp.Jobs) != 0 {
				t.Fatalf("unexpected to get cdp jobs, group id: %s", cases[i].groupId)
			}
		} else {
			// check list cdp job response
			checkCDPJobs(t, &cases[i], listResp.Jobs)

			for _, job := range listResp.Jobs {
				if job.GetStage() != expectedStage {
					t.Fatalf("unexpected to get wrong stage, got: %s, expected: %s, group id: %s",
						job.GetStage(), expectedStage, cases[i].groupId)
				}
			}
		}
	}
}

func TestFinishCDPJobByGroup(t *testing.T) {
	client, server := NewMockServerCDP(t)

	defer stopMockServer(server)

	ctx := context.TODO()
	cdpClient := client.CDP

	cases := []cdpRequest{
		{
			groupId:   "auto-clean",
			autoClean: true,
		},
		{
			groupId:   "not-auto-clean",
			autoClean: false,
		},
	}
	expectedStage := zbs.CDPJobStage_CDP_STAGE_DONE

	for i := range cases {
		_, err := cdpClient.CreateJobsByGroup(ctx, localRemoteVolumeMap, remoteHost, cases[i].groupId,
			cases[i].chunkId, cases[i].skipFcWriteZero, cases[i].autoClean)
		if err != nil {
			t.Fatalf("failed to create cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		err = cdpClient.FinishJobsByGroup(ctx, cases[i].groupId, true)
		if err != nil {
			t.Fatalf("failed to finish cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		listResp, err := cdpClient.ListJobsByGroup(ctx, cases[i].groupId)
		if err != nil {
			t.Fatalf("failed to list cdp jobs by group, group id: %s, err: %v", cases[i].groupId, err)
		}

		if cases[i].autoClean {
			if len(listResp.Jobs) != 0 {
				t.Fatalf("unexpected to get cdp jobs, group id: %s", cases[i].groupId)
			}
		} else {
			// check list cdp job response
			checkCDPJobs(t, &cases[i], listResp.Jobs)

			for _, job := range listResp.Jobs {
				if job.GetStage() != expectedStage {
					t.Fatalf("unexpected to get wrong stage, got: %s, expected: %s, group id: %s",
						job.GetStage(), expectedStage, cases[i].groupId)
				}
			}
		}
	}
}

func TestDeleteCDPJobByGroup(t *testing.T) {
	client, server := NewMockServerCDP(t)

	defer stopMockServer(server)

	ctx := context.TODO()
	cdpClient := TestCDPService{client.CDP}

	createResp, err := cdpClient.CreateJobsByGroup(ctx, localRemoteVolumeMap, remoteHost, groupId, nil, false, true)
	if err != nil {
		t.Fatalf("failed to create cdp jobs by group, err: %v", err)
	}

	err = cdpClient.DeleteJobsByGroup(ctx, groupId)
	if err == nil {
		t.Fatal("unexpected to delete jobs by group, when all jobs are not deletable")
	}

	// job 1 isn't deletable
	stages := []zbs.CDPJobStage{
		zbs.CDPJobStage_CDP_STAGE_DELTA_COPY,
		zbs.CDPJobStage_CDP_STAGE_CANCELLED,
		zbs.CDPJobStage_CDP_STAGE_ERROR,
	}

	for i, job := range createResp.GetCdpJobs() {
		err = cdpClient.SyncJobStage(ctx, string(job.GetId()), stages[i])
		if err != nil {
			t.Fatalf("failed to sync cdp job stage, err: %v", err)
		}
	}

	err = cdpClient.DeleteJobsByGroup(ctx, groupId)
	if err == nil {
		t.Fatal("unexpected to delete jobs by group, when some jobs are not deletable")
	}

	err = cdpClient.SyncJobStage(ctx, string(createResp.GetCdpJobs()[0].GetId()), zbs.CDPJobStage_CDP_STAGE_DONE)
	if err != nil {
		t.Fatalf("failed to sync cdp job stage, err: %v", err)
	}

	err = cdpClient.DeleteJobsByGroup(ctx, groupId)
	if err != nil {
		t.Fatal("failed to delete jobs by group")
	}
}

func TestGetJobByVolume(t *testing.T) {
	client, server := NewMockServerCDP(t)

	defer stopMockServer(server)

	ctx := context.TODO()
	cdpClient := client.CDP

	request := cdpRequest{
		groupId:         groupId,
		skipFcWriteZero: true,
		autoClean:       false,
	}

	_, err := cdpClient.CreateJobsByGroup(ctx, localRemoteVolumeMap, remoteHost,
		request.groupId, request.chunkId, request.skipFcWriteZero, request.autoClean)
	if err != nil {
		t.Fatalf("failed to create cdp jobs by group, err: %v", err)
	}

	gotJobs := make([]*zbs.CDPJobInfo, 0, len(localRemoteVolumeMap))

	for localVolume := range localRemoteVolumeMap {
		job, err := cdpClient.GetJobByVolume(ctx, localVolume)
		if err != nil {
			t.Fatalf("failed to get cdp job by volume, err: %v", err)
		}

		gotJobs = append(gotJobs, job)
	}

	checkCDPJobs(t, &request, gotJobs)
}

func TestSyncJobStage(t *testing.T) {
	client, server := NewMockServerCDP(t)

	defer stopMockServer(server)

	ctx := context.TODO()
	cdpClient := TestCDPService{client.CDP}

	createResp, err := cdpClient.CreateJobsByGroup(ctx, localRemoteVolumeMap, remoteHost, groupId, nil, false, true)
	if err != nil {
		t.Fatalf("failed to create cdp jobs by group, err: %v", err)
	}

	expectedStages := []zbs.CDPJobStage{
		zbs.CDPJobStage_CDP_STAGE_FULL_COPY,
		zbs.CDPJobStage_CDP_STAGE_DELTA_COPY,
		zbs.CDPJobStage_CDP_STAGE_MIRROR,
	}

	for i, job := range createResp.GetCdpJobs() {
		err = cdpClient.SyncJobStage(ctx, string(job.GetId()), expectedStages[i])
		if err != nil {
			t.Fatalf("failed to sync cdp job stage, err: %v", err)
		}
	}

	listResp, err := cdpClient.ListJobsByGroup(ctx, groupId)
	if err != nil {
		t.Fatalf("failed to list cdp jobs by group, group id: %s, err: %v", groupId, err)
	}

	for i, job := range listResp.Jobs {
		if job.GetStage() != expectedStages[i] {
			t.Fatalf("unexpected to get wrong stage, got: %s, expected: %s, group id: %s",
				job.GetStage(), expectedStages[i], groupId)
		}
	}
}

func checkCDPJobs(t *testing.T, expectedRequest *cdpRequest, gotCdpJobs []*zbs.CDPJobInfo) {
	if len(gotCdpJobs) != len(localRemoteVolumeMap) {
		t.Fatalf("failed to create expected number of cdp jobs")
	}

	for _, cdpJob := range gotCdpJobs {
		if string(cdpJob.GetGroup()) != expectedRequest.groupId {
			t.Fatalf("unexpected to get wrong group id, got: %s, expected: %s",
				string(cdpJob.GetGroup()), expectedRequest.groupId)
		}

		if expectedRequest.chunkId == nil {
			if cdpJob.Cid != nil {
				t.Fatalf("unexpected to get not nil chunk id, group id: %s", expectedRequest.groupId)
			}
		} else if cdpJob.GetCid() != *expectedRequest.chunkId {
			t.Fatalf("unexpected to get wrong chunk id, got: %v, expected: %v, group id: %s",
				cdpJob.GetCid(), *expectedRequest.chunkId, expectedRequest.groupId)
		}

		if cdpJob.GetSkipFcWriteZero() != expectedRequest.skipFcWriteZero {
			t.Fatalf("unexpected to get wrong skipFcWriteZero, got: %v, expected: %v, group id: %s",
				cdpJob.GetSkipFcWriteZero(), expectedRequest.skipFcWriteZero, expectedRequest.groupId)
		}

		if cdpJob.GetAutoClean() != expectedRequest.autoClean {
			t.Fatalf("unexpected to get wrong auto clean, got: %v, expected: %v, group id: %s",
				cdpJob.GetAutoClean(), expectedRequest.autoClean, expectedRequest.groupId)
		}

		localInfo := cdpJob.GetLocal()
		remoteInfo := cdpJob.GetRemote()

		if localInfo == nil {
			t.Fatalf("unexpected to get empty local info of cdp jobs, group id: %s", expectedRequest.groupId)
		}

		expectedRemoteVolume, ok := localRemoteVolumeMap[string(localInfo.GetVolumeId())]
		if !ok {
			t.Fatalf("failed to get remote volume, local volume: %s, group id: %s", string(localInfo.GetVolumeId()), expectedRequest.groupId)
		}

		if string(remoteInfo.GetVolumeId()) != expectedRemoteVolume {
			t.Fatalf("unexpected to get wrong remote volume, got: %s, expected: %s, group id: %s",
				string(remoteInfo.GetVolumeId()), expectedRemoteVolume, expectedRequest.groupId)
		}

		if string(remoteInfo.GetHosts()) != remoteHost {
			t.Fatalf("unexpected to get wrong remote host, got: %s, expected: %s, group id: %s",
				string(remoteInfo.GetHosts()), remoteHost, expectedRequest.groupId)
		}
	}
}
