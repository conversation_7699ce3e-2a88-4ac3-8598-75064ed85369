package zbs

import (
	"context"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/zbs/zrpc"
)

type VolumePerfService struct {
	client zbs.VolumePerfServiceClient
}

func NewVolumePerfService(client *zrpc.Client) *VolumePerfService {
	return &VolumePerfService{
		client: zbs.NewVolumePerfServiceClient(client),
	}
}

func (s *VolumePerfService) GetAllVolumesPerf(ctx context.Context) (*zbs.VolumesPerf, error) {
	return s.client.GetAllVolumesPerf(ctx, &zbs.Void{})
}

func (s *VolumePerfService) GetVolumePerf(ctx context.Context, volumeId string) (*zbs.VolumePerf, error) {
	req := &zbs.VolumePerfRequest{
		VolumeId: []byte(volumeId),
	}

	return s.client.GetVolumePerf(ctx, req)
}

func (s *VolumePerfService) GetVolumesPerf(ctx context.Context, volumeIds []string) (*zbs.VolumesPerf, error) {
	var volumeIdsList [][]byte
	for _, volumeId := range volumeIds {
		volumeIdsList = append(volumeIdsList, []byte(volumeId))
	}

	req := &zbs.VolumesPerfRequest{
		VolumeIdList: volumeIdsList,
	}

	return s.client.GetVolumesPerf(ctx, req)
}

func (s *VolumePerfService) ProbeVolumes(ctx context.Context, volumeIds []string) error {
	var volumeIdsList [][]byte
	for _, volumeId := range volumeIds {
		volumeIdsList = append(volumeIdsList, []byte(volumeId))
	}

	req := &zbs.ProbeVolumesRequest{
		VolumeIdList: volumeIdsList,
	}

	_, err := s.client.ProbeVolumes(ctx, req)

	return err
}

func (s *VolumePerfService) DisableProbeVolumes(ctx context.Context, volumeIds []string, disableAll bool) error {
	var volumeIdsList [][]byte
	for _, volumeId := range volumeIds {
		volumeIdsList = append(volumeIdsList, []byte(volumeId))
	}

	req := &zbs.DisableProbeVolumesRequest{
		VolumeIdList: volumeIdsList,
		DisableAll:   &disableAll,
	}

	_, err := s.client.DisableProbeVolumes(ctx, req)

	return err
}

type ChunkPerfService struct {
	client zbs.ChunkPerfServiceClient
}

func NewChunkPerfService(client *zrpc.Client) *ChunkPerfService {
	return &ChunkPerfService{
		client: zbs.NewChunkPerfServiceClient(client),
	}
}

func (s *ChunkPerfService) GetUIOPerf(ctx context.Context) (*zbs.UIOPerf, error) {
	req := &zbs.ChunkInstance{}

	return s.client.GetUIOPerf(ctx, req)
}

func (s *ChunkPerfService) GetUIOPerfOfInstance(ctx context.Context, instanceId *uint32) (*zbs.UIOPerf, error) {
	req := &zbs.ChunkInstance{
		InstanceId: instanceId,
	}

	return s.client.GetUIOPerf(ctx, req)
}

func (s *ChunkPerfService) GetAccessPerf(ctx context.Context) (*zbs.AccessPerf, error) {
	req := &zbs.ChunkInstance{}

	return s.client.GetAccessPerf(ctx, req)
}

func (s *ChunkPerfService) GetAccessPerfOfInstance(ctx context.Context, instanceId *uint32) (*zbs.AccessPerf, error) {
	req := &zbs.ChunkInstance{
		InstanceId: instanceId,
	}

	return s.client.GetAccessPerf(ctx, req)
}

func (s *ChunkPerfService) GetLSMPerf(ctx context.Context) (*zbs.LSMPerf, error) {
	req := &zbs.ChunkInstance{}

	return s.client.GetLSMPerf(ctx, req)
}

func (s *ChunkPerfService) GetLSMPerfOfInstance(ctx context.Context, instanceId *uint32) (*zbs.LSMPerf, error) {
	req := &zbs.ChunkInstance{
		InstanceId: instanceId,
	}

	return s.client.GetLSMPerf(ctx, req)
}
