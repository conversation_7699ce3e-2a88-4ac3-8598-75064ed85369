package zbs

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"

	zbsv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/utils"
)

type MetaService struct {
	Chunk *MetaChunkService

	client metav1.MetaServiceClient
}

func NewMetaService(client grpc.ClientConnInterface) *MetaService {
	return &MetaService{
		Chunk:  NewMetaChunkService(client),
		client: metav1.NewMetaServiceClient(client),
	}
}

func (s *MetaService) GetClient() metav1.MetaServiceClient {
	return s.client
}

func (s *MetaService) CreatePool(ctx context.Context, name string, options *metav1.Pool) (*metav1.Pool, error) {
	now := &zbsv1.TimeSpec{
		Seconds:  utils.NewInt64(time.Now().Unix()),
		Nseconds: utils.NewInt64(0),
	}

	if options == nil {
		defaultStoragePolicy := NewDefaultStoragePolicy()

		pool := metav1.Pool{
			Id:            []byte(uuid.New().String()),
			Name:          []byte(name),
			CreatedTime:   now,
			StoragePoolId: []byte(SystemStoragePoolId),
			ReplicaNum:    &defaultStoragePolicy.ReplicaFactor,
			ThinProvision: &defaultStoragePolicy.ThinProvision,
			StripeNum:     &defaultStoragePolicy.StripeNum,
			StripeSize:    &defaultStoragePolicy.StripeSize,
		}

		if defaultStoragePolicy.EC != nil {
			pool.EcParam = &zbsv1.ECAlgorithmParam{
				K:      &defaultStoragePolicy.EC.K,
				M:      &defaultStoragePolicy.EC.M,
				EcType: lo.ToPtr(zbsv1.ECAlgorithmType_REED_SOLOMON),
				RsArg:  &zbsv1.ECReedSolArg{},
			}
		}

		return s.client.CreatePool(ctx, &pool)
	}

	var req metav1.Pool

	proto.Merge(&req, options)

	req.Name = []byte(name)
	req.CreatedTime = now

	res, err := s.client.CreatePool(ctx, &req)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (s *MetaService) ListPool(ctx context.Context, req *metav1.ListPoolRequest) (*metav1.PoolsResponse, error) {
	res, err := s.client.ListPool(ctx, req)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (s *MetaService) DeletePool(ctx context.Context, req *metav1.PoolPath) error {
	_, err := s.client.DeletePool(ctx, req)
	if err != nil {
		return err
	}

	return nil
}

func (s *MetaService) DeletePoolById(ctx context.Context, id string) error {
	return s.DeletePool(ctx, utils.NewPoolPathById(id))
}

func (s *MetaService) DeletePoolByName(ctx context.Context, name string) error {
	return s.DeletePool(ctx, utils.NewPoolPathByName(name))
}

func (s *MetaService) ShowPool(ctx context.Context, req *metav1.PoolPath) (*metav1.Pool, error) {
	res, err := s.client.ShowPool(ctx, req)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (s *MetaService) ShowPoolById(ctx context.Context, id string) (*metav1.Pool, error) {
	return s.ShowPool(ctx, utils.NewPoolPathById(id))
}

func (s *MetaService) ShowPoolByName(ctx context.Context, name string) (*metav1.Pool, error) {
	return s.ShowPool(ctx, utils.NewPoolPathByName(name))
}

func (s *MetaService) UpdatePool(ctx context.Context, req *metav1.UpdatePoolRequest) (*metav1.Pool, error) {
	return s.client.UpdatePool(ctx, req)
}

func (s *MetaService) CreateVolume(
	ctx context.Context,
	poolPath *metav1.PoolPath,
	name string, size uint64,
	options *metav1.CreateVolumeRequest) (*metav1.Volume, error) {
	if options == nil {
		return s.client.CreateVolume(ctx, &metav1.CreateVolumeRequest{
			PoolPath: poolPath,
			Volume: &metav1.Volume{
				Name: []byte(name),
				Size: utils.NewUint64(size),
			},
		})
	}

	var req metav1.CreateVolumeRequest

	proto.Merge(&req, options)

	req.PoolPath = poolPath
	req.Volume.Name = []byte(name)
	req.Volume.Size = utils.NewUint64(size)

	res, err := s.client.CreateVolume(ctx, &req)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (s *MetaService) ListVolume(ctx context.Context, poolPath *metav1.PoolPath) (*metav1.VolumesResponse, error) {
	return s.client.ListVolume(ctx, &metav1.ListVolumeRequest{
		PoolPath: poolPath,
	})
}

func (s *MetaService) ShowVolume(ctx context.Context, volumePath *metav1.VolumePath) (*metav1.ShowVolumeResponse, error) {
	poolPath := volumePath.GetPoolPath()
	volumeName := volumePath.GetVolumeName()
	volumeId := volumePath.GetVolumeId()
	secondaryId := volumePath.GetSecondaryId()

	svr := &metav1.ShowVolumeRequest{
		PoolPath:    poolPath,
		VolumeName:  volumeName,
		VolumeId:    volumeId,
		SecondaryId: secondaryId,
	}

	return s.client.ShowVolume(ctx, svr)
}

func (s *MetaService) DeleteVolume(ctx context.Context, volumePath *metav1.VolumePath) error {
	return s.DeleteVolumeV2(ctx, volumePath, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (s *MetaService) DeleteVolumeV2(ctx context.Context, volumePath *metav1.VolumePath, deletePermanently bool) error {
	req := &metav1.DeleteVolumeRequest{
		PoolPath:          volumePath.GetPoolPath(),
		VolumeName:        volumePath.GetVolumeName(),
		VolumeId:          volumePath.GetVolumeId(),
		SecondaryId:       volumePath.GetSecondaryId(),
		DeletePermanently: &deletePermanently,
	}

	if _, err := s.client.DeleteVolume(ctx, req); err != nil {
		return err
	}

	return nil
}

func (s *MetaService) UpdateVolume(ctx context.Context, req *metav1.UpdateVolumeRequest) (*metav1.Volume, error) {
	return s.client.UpdateVolume(ctx, req)
}

func (s *MetaService) ResizeVolume(ctx context.Context, volumePath *metav1.VolumePath, size uint64) (*metav1.Volume, error) {
	return s.client.ResizeVolume(ctx, &metav1.ResizeVolumeRequest{
		Path: volumePath,
		Size: utils.NewUint64(size),
	})
}

func (s *MetaService) GetVolumeSize(ctx context.Context, id string) (*metav1.VolumeSizeResponse, error) {
	return s.client.GetVolumeSize(ctx, &metav1.VolumeId{
		VolumeId: []byte(id),
	})
}

func (s *MetaService) GetVTable(ctx context.Context, volumePath *metav1.VolumePath) (*metav1.GetVTableResponse, error) {
	return s.client.GetVTable(ctx, volumePath)
}

func (s *MetaService) MoveVolume(ctx context.Context, volumePath *metav1.VolumePath, dest *metav1.PoolPath) (*metav1.Volume, error) {
	return s.client.MoveVolume(ctx, &metav1.MoveVolumeRequest{
		VolumePath:  volumePath,
		DstPoolPath: dest,
	})
}

func (s *MetaService) CreateSnapshot(ctx context.Context, path *metav1.SnapshotPath, options *metav1.CreateSnapshotRequest) (*metav1.Volume, error) {
	if options == nil {
		return s.client.CreateSnapshot(ctx, &metav1.CreateSnapshotRequest{
			Path: path,
		})
	}

	var req metav1.CreateSnapshotRequest

	proto.Merge(&req, options)
	req.Path = path

	return s.client.CreateSnapshot(ctx, &req)
}

func (s *MetaService) ShowSnapshot(ctx context.Context, path *metav1.SnapshotPath) (*metav1.Volume, error) {
	return s.client.ShowSnapshot(ctx, path)
}

func (s *MetaService) UpdateSnapshot(ctx context.Context, path *metav1.SnapshotPath, name string, desc []byte, allocEven *bool) (*metav1.Volume, error) {
	req := &metav1.UpdateSnapshotRequest{
		Path:           path,
		NewName:        []byte(name),
		NewDescription: desc,
		NewAllocEven:   allocEven,
	}

	return s.client.UpdateSnapshot(ctx, req)
}

func (s *MetaService) DeleteSnapshot(ctx context.Context, path *metav1.SnapshotPath) error {
	return s.DeleteSnapshotV2(ctx, path, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (s *MetaService) DeleteSnapshotV2(ctx context.Context, path *metav1.SnapshotPath, deletePermanently bool) error {
	req := &metav1.DeleteSnapshotRequest{
		VolumePath:        path.GetVolumePath(),
		SnapshotName:      path.GetSnapshotName(),
		SnapshotId:        path.GetSnapshotId(),
		SecondaryId:       path.GetSecondaryId(),
		DeletePermanently: &deletePermanently,
	}

	if _, err := s.client.DeleteSnapshot(ctx, req); err != nil {
		return err
	}

	return nil
}

func (s *MetaService) MoveSnapshot(ctx context.Context, snapshot *metav1.SnapshotPath, dest *metav1.PoolPath) (*metav1.Volume, error) {
	return s.client.MoveSnapshot(ctx, &metav1.MoveSnapshotRequest{
		Path:        snapshot,
		DstPoolPath: dest,
	})
}

func (s *MetaService) ListSnapshot(ctx context.Context, path *metav1.VolumePath, pagination *zbsv1.Pagination) (*metav1.SnapshotsResponse, error) {
	return s.client.ListSnapshot(ctx, &metav1.ListSnapshotRequest{
		VolumePath: path,
		Pagination: pagination,
	})
}

func (s *MetaService) CloneVolume(ctx context.Context, req *metav1.CloneVolumeRequest) (*metav1.Volume, error) {
	return s.client.CloneVolume(ctx, req)
}

func (s *MetaService) RollbackVolume(ctx context.Context, snapshot *metav1.SnapshotPath) error {
	if _, err := s.client.RollbackVolume(ctx, snapshot); err != nil {
		return err
	}

	return nil
}

func (s *MetaService) ShowClusterInfo(ctx context.Context) (*metav1.ClusterInfo, error) {
	return s.client.ShowClusterInfo(ctx, &zbsv1.Void{})
}

func (s *MetaService) SetUpgradeMode(ctx context.Context, duration time.Duration) error {
	seconds := duration.Seconds()

	_, err := s.client.SetUpgradeMode(ctx, &metav1.SetUpgradeModeRequest{
		Duration: &[]uint64{uint64(seconds)}[0],
	})

	return err
}

func (s *MetaService) FindPExtent(ctx context.Context, status metav1.PExtentStatus) (*metav1.PExtentsResponse, error) {
	extents, err := s.client.FindPExtent(ctx, &metav1.FindPExtentRequest{
		PextentStatus: &[]metav1.PExtentStatus{status}[0],
	})
	if err != nil {
		return nil, err
	}

	return extents, nil
}

func (s *MetaService) GetVExtentLease(ctx context.Context, vTableId string, vExtentNo uint32) (*zbsv1.VExtentLease, error) {
	lease, err := s.client.GetVExtentLease(ctx, &metav1.GetVExtentLeaseRequest{
		VtableId:  []byte(vTableId),
		VextentNo: &vExtentNo,
	})
	if err != nil {
		return nil, err
	}

	return lease, nil
}

type CID = uint32

type MetaChunkService struct {
	client metav1.ChunkServiceClient
}

func NewMetaChunkService(client grpc.ClientConnInterface) *MetaChunkService {
	return &MetaChunkService{
		client: metav1.NewChunkServiceClient(client),
	}
}

func (s *MetaChunkService) CreateStoragePool(ctx context.Context, name string, chunks ...CID) (*zbsv1.StoragePool, error) {
	return s.client.CreateStoragePool(ctx, &metav1.CreateStoragePoolRequest{Name: []byte(name), ChunkIds: chunks})
}

func (s *MetaChunkService) DeleteStoragePool(ctx context.Context, id string) (*zbsv1.StoragePool, error) {
	return s.client.DeleteStoragePool(ctx, &zbsv1.StoragePoolId{Id: []byte(id)})
}

func (s *MetaChunkService) UpdateStoragePool(ctx context.Context, req *metav1.UpdateStoragePoolRequest) (*zbsv1.StoragePool, error) {
	return s.client.UpdateStoragePool(ctx, req)
}

func (s *MetaChunkService) ListStoragePool(ctx context.Context) (*zbsv1.StoragePools, error) {
	return s.client.ListStoragePool(ctx, &zbsv1.Void{})
}

func (s *MetaChunkService) ShowStoragePool(ctx context.Context, id string) (*zbsv1.StoragePool, error) {
	return s.client.ShowStoragePool(ctx, &zbsv1.StoragePoolId{Id: []byte(id)})
}

func (s *MetaChunkService) AddChunkToStoragePool(ctx context.Context, storagePoolID string, chunkID CID) (*zbsv1.StoragePool, error) {
	return s.client.AddChunkToStoragePool(ctx, &metav1.AddChunkToStoragePoolRequest{
		StoragePoolId: []byte(storagePoolID),
		ChunkId:       &[]CID{chunkID}[0],
	})
}

type RemoveChunkFromStoragePoolOption = func(request *metav1.RemoveChunkFromStoragePoolRequest) *metav1.RemoveChunkFromStoragePoolRequest

func (s *MetaChunkService) RemoveChunkFromStoragePool(
	ctx context.Context, storagePoolID string, chunkID CID, options ...RemoveChunkFromStoragePoolOption) (*zbsv1.StoragePool, error) {
	req := &metav1.RemoveChunkFromStoragePoolRequest{
		StoragePoolId: []byte(storagePoolID),
		ChunkId:       &[]CID{chunkID}[0],
	}

	for _, fn := range options {
		req = fn(req)
	}

	return s.client.RemoveChunkFromStoragePool(ctx, req)
}

type CreateTopoObjOption = func(*metav1.CreateTopoObjRequest) *metav1.CreateTopoObjRequest

func (s *MetaChunkService) CreateTopoObj(ctx context.Context, topoType *zbsv1.TopoType, options ...CreateTopoObjOption) (*zbsv1.TopoObj, error) {
	req := &metav1.CreateTopoObjRequest{Type: topoType}
	for _, fn := range options {
		req = fn(req)
	}

	return s.client.CreateTopoObj(ctx, req)
}

func (s *MetaChunkService) DeleteTopoObj(ctx context.Context, id *zbsv1.TopoObjId) (*zbsv1.TopoObj, error) {
	return s.client.DeleteTopoObj(ctx, id)
}

type UpdateTopoObjOption = func(*metav1.UpdateTopoObjRequest) *metav1.UpdateTopoObjRequest

func (s *MetaChunkService) UpdateTopoObj(ctx context.Context, id *zbsv1.TopoObjId, options ...UpdateTopoObjOption) (*zbsv1.TopoObj, error) {
	req := &metav1.UpdateTopoObjRequest{Id: id.Id}
	for _, fn := range options {
		req = fn(req)
	}

	return s.client.UpdateTopoObj(ctx, req)
}

func (s *MetaChunkService) ListTopoObj(ctx context.Context, id *zbsv1.TopoObjId) (*zbsv1.TopoObjs, error) {
	return s.client.ListTopoObj(ctx, id)
}

func (s *MetaChunkService) ShowTopoObj(ctx context.Context, id *zbsv1.TopoObjId) (*zbsv1.TopoObj, error) {
	return s.client.ShowTopoObj(ctx, id)
}

func (s *MetaChunkService) RegisterChunk(ctx context.Context, chunk *zbsv1.Chunk) (*zbsv1.Chunk, error) {
	return s.client.RegisterChunk(ctx, chunk)
}

func (s *MetaChunkService) ListChunk(ctx context.Context) (*zbsv1.Chunks, error) {
	return s.client.ListChunk(ctx, &zbsv1.Void{})
}

func (s *MetaChunkService) ShowChunk(ctx context.Context, id *zbsv1.ChunkId) (*zbsv1.Chunk, error) {
	return s.client.ShowChunk(ctx, id)
}

func (s *MetaChunkService) ListNode(ctx context.Context) (*zbsv1.Nodes, error) {
	return s.client.ListNode(ctx, &zbsv1.Void{})
}

func (s *MetaChunkService) ShowNode(ctx context.Context, in *metav1.ShowNodeRequest) (*zbsv1.NodeObj, error) {
	return s.client.ShowNode(ctx, in)
}

func (s *MetaChunkService) RemoveChunk(ctx context.Context, id *zbsv1.ChunkId) error {
	_, err := s.client.RemoveChunk(ctx, id)
	return err
}

func (s *MetaChunkService) BanChunk(ctx context.Context, id *zbsv1.ChunkId) error {
	return s.BanChunkV2(ctx, id, false)
}

func (s *MetaChunkService) BanChunkV2(ctx context.Context, id *zbsv1.ChunkId, singeChunk bool) error {
	req := &metav1.BanChunkRequest{
		Id:          id.Id,
		RpcIp:       id.RpcIp,
		RpcPort:     id.RpcPort,
		SingleChunk: &singeChunk,
	}
	_, err := s.client.BanChunk(ctx, req)

	return err
}

func (s *MetaChunkService) UnbanChunk(ctx context.Context, id *zbsv1.ChunkId) error {
	return s.UnbanChunkV2(ctx, id, false)
}

func (s *MetaChunkService) UnbanChunkV2(ctx context.Context, id *zbsv1.ChunkId, singeChunk bool) error {
	req := &metav1.UnbanChunkRequest{
		Id:          id.Id,
		RpcIp:       id.RpcIp,
		RpcPort:     id.RpcPort,
		SingleChunk: &singeChunk,
	}
	_, err := s.client.UnbanChunk(ctx, req)

	return err
}

func (s *MetaChunkService) GetChunkTopology(ctx context.Context, id *zbsv1.ChunkId) (*zbsv1.ChunkTopology, error) {
	return s.client.GetChunkTopology(ctx, id)
}

func (s *MetaChunkService) LeaveService(ctx context.Context, id *zbsv1.ChunkId) error {
	_, err := s.client.LeaveService(ctx, id)
	return err
}

func (s *MetaChunkService) ListPid(ctx context.Context, id *zbsv1.ChunkId) (*zbsv1.ChunkPids, error) {
	return s.client.ListPid(ctx, id)
}

func (s *MetaChunkService) SetMaintenanceMode(ctx context.Context, id *zbsv1.ChunkId, mode bool) (*zbsv1.Chunk, error) {
	return s.SetMaintenanceModeV2(ctx, id, mode, false)
}

func (s *MetaChunkService) SetMaintenanceModeV2(ctx context.Context, id *zbsv1.ChunkId, mode bool, singeChunk bool) (*zbsv1.Chunk, error) {
	req := &metav1.SetMaintenanceModeRequest{
		Id:          id,
		Mode:        &mode,
		SingleChunk: &singeChunk,
	}

	return s.client.SetMaintenanceMode(ctx, req)
}

func (s *MetaChunkService) GetDefaultPrioSpaceRatio(ctx context.Context) (*metav1.DefaultPrioSpaceInfo, error) {
	return s.client.GetDefaultPrioSpaceRatio(ctx, &zbsv1.Void{})
}

func (s *MetaChunkService) SetDefaultPrioSpaceRatio(ctx context.Context, percentage uint32) error {
	req := &metav1.SetDefaultPrioSpaceRatioRequest{
		Percentage: &percentage,
	}

	_, err := s.client.SetDefaultPrioSpaceRatio(ctx, req)

	return err
}

func (s *MetaChunkService) GetChunkPrioSpaceRatio(ctx context.Context, id *zbsv1.ChunkId) (*metav1.ChunkPrioSpaceInfo, error) {
	return s.client.GetChunkPrioSpaceInfo(ctx, id)
}

func (s *MetaChunkService) SetChunkPrioSpaceRatio(ctx context.Context, id *zbsv1.ChunkId, percentage uint32) error {
	req := metav1.SetChunkPrioSpaceRatioRequest{
		ChunkRatios: []*metav1.SetChunkPrioSpaceRatioRequest_CidPercentage{
			{Cid: id, Percentage: &percentage},
		},
	}

	_, err := s.client.SetChunkPrioSpaceRatio(ctx, &req)

	return err
}

func (s *MetaChunkService) ListChunkPrioSpaceInfo(ctx context.Context) (*metav1.ChunkPrioSpaceInfoList, error) {
	return s.client.ListChunkPrioSpaceInfo(ctx, &zbsv1.Void{})
}

func (s *MetaService) ShowLicense(ctx context.Context) (*zbsv1.License, error) {
	return s.client.ShowLicense(ctx, &zbsv1.Void{})
}

func (s *MetaService) UpdateLicense(ctx context.Context, in *zbsv1.LicenseRequest) error {
	_, err := s.client.UpdateLicense(ctx, in)

	return err
}

func (s *MetaService) ParseLicense(ctx context.Context, in *zbsv1.LicenseRequest) (*zbsv1.License, error) {
	return s.client.ParseLicense(ctx, in)
}

func (s *MetaService) UpdateClusterInfo(ctx context.Context, in *metav1.UpdateClusterInfoRequest) error {
	_, err := s.client.UpdateClusterInfo(ctx, in)
	return err
}

func (s *MetaService) SweepRecycleBinImmediate(ctx context.Context) error {
	_, err := s.client.SweepRecycleBinImmediate(ctx, &zbsv1.Void{})
	return err
}

func (s *MetaService) BatchSweepTrashVolumes(ctx context.Context, volumeIds []string) error {
	req := &metav1.BatchSweepTrashVolumesRequest{
		TrashVolumeId: volumeIds,
	}
	_, err := s.client.BatchSweepTrashVolumes(ctx, req)

	return err
}

func (s *MetaService) RestoreTrashVolume(ctx context.Context, in *metav1.RestoreTrashVolumeRequest) (
	*metav1.RestoreTrashVolumeResponse, error) {
	return s.client.RestoreTrashVolume(ctx, in)
}

func (s *MetaService) ShowTrashPool(ctx context.Context) (
	*metav1.Pool, error) {
	return s.client.ShowTrashPool(ctx, &zbsv1.Void{})
}

func (s *MetaService) ShowTrashVolume(ctx context.Context, volumeId *string) (*metav1.Volume, error) {
	req := metav1.ShowTrashVolumeRequest{
		TrashVolumeId: volumeId,
	}

	return s.client.ShowTrashVolume(ctx, &req)
}

func (s *MetaService) ListTrashVolume(ctx context.Context, in *metav1.ListTrashVolumeRequest) (
	*metav1.ListTrashVolumeResponse, error) {
	return s.client.ListTrashVolume(ctx, in)
}
