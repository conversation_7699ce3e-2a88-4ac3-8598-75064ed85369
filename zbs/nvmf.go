package zbs

import (
	"context"
	"strings"

	"github.com/samber/lo"
	"google.golang.org/grpc"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
)

type NVMFNamespace = meta.NVMFDistNamespace
type NVMFSubsystem = meta.NVMFDistSubsystem
type Volume = meta.Volume

type NVMFService struct {
	client meta.NVMFServiceClient
}

func NewNVMFService(client grpc.ClientConnInterface) *NVMFService {
	return &NVMFService{
		client: meta.NewNVMFServiceClient(client),
	}
}

type NsCloneOptions struct {
	SrcNsPath     *meta.DistNamespacePath
	SrcSnapshotId *string
}

type NVMFSubsystemConfig struct {
	NqnWhiteList  []string
	Ipv4Whitelist []string
	ReplicaNum    *uint32
	Description   string
}

func (n *NVMFService) CreateNamespaceBySecondaryId(ctx context.Context, nsName string, secondaryId string,
	size uint64, setters ...NsOptionSetter) (*NVMFNamespace, error) {
	opts := &NsOptions{}

	for _, apply := range setters {
		apply(opts)
	}

	req := &meta.CreateNVMFDistNamespaceRequest{}
	req.NsPath = &meta.DistNamespacePath{
		PoolPath:    &meta.PoolPath{},
		SecondaryId: []byte(secondaryId),
		NsName:      []byte(nsName),
	}

	req.Size = &size

	sp := opts.StoragePolicy
	if sp == nil {
		sp = NewDefaultStoragePolicy()
	}

	req.ReplicaNum = &sp.ReplicaFactor
	req.ThinProvision = &sp.ThinProvision
	req.StripeNum = &sp.StripeNum
	req.StripeSize = &sp.StripeSize

	if sp.EC != nil {
		req.ReplicaNum = nil
		req.ResiliencyType = lo.ToPtr(zbs.ResiliencyType_RT_EC)
		req.EcParam = &zbs.ECAlgorithmParam{
			Name:   lo.ToPtr(ECAlgorithmName),
			K:      &sp.EC.K,
			M:      &sp.EC.M,
			EcType: lo.ToPtr(zbs.ECAlgorithmType_REED_SOLOMON),
			RsArg:  &zbs.ECReedSolArg{},
		}
	}

	if opts.Throttling != nil {
		req.Throttling = opts.Throttling
	}

	if opts.CloneOpts != nil {
		if opts.CloneOpts.SrcSnapshotId != nil {
			req.SrcSnapshotId = []byte(*opts.CloneOpts.SrcSnapshotId)
		} else {
			req.SrcNsPath = opts.CloneOpts.SrcNsPath
		}
	}

	req.NqnWhitelist = opts.NqnWhitelist
	req.SubsystemRequirement = opts.SubsystemRequirement
	req.SingleAccess = opts.SingleAccess
	req.Description = opts.Description
	req.Prioritized = opts.Prioritized
	req.ChunkInstancesBits = opts.ChunkInstancesBits

	return n.client.CreateDistNamespace(ctx, req)
}

func (n *NVMFService) DescribeNamespace(ctx context.Context, nsVolumeId string) (*NVMFNamespace, error) {
	req := &meta.DistNamespacePath{
		PoolPath: &meta.PoolPath{},
		VolumeId: []byte(nsVolumeId),
	}

	return n.client.GetDistNamespace(ctx, req)
}

func (n *NVMFService) DescribeNamespaceBySecondaryId(ctx context.Context, secondaryId string) (*NVMFNamespace, error) {
	req := &meta.DistNamespacePath{
		PoolPath: &meta.PoolPath{},
	}

	req.SecondaryId = []byte(secondaryId)

	return n.client.GetDistNamespace(ctx, req)
}

func (n *NVMFService) UpdateNamespace(ctx context.Context, nsVolumeId string, setters ...NsOptionSetter) (*NVMFNamespace, error) {
	NsPath := &meta.DistNamespacePath{
		PoolPath: &meta.PoolPath{},
		VolumeId: []byte(nsVolumeId),
	}

	opts := &NsOptions{}
	for _, apply := range setters {
		apply(opts)
	}

	req := &meta.UpdateNVMFDistNamespaceRequest{}

	req.NsPath = NsPath
	req.Size = opts.Size
	req.Description = opts.Description
	req.ReplicaNum = opts.ReplicaNum
	req.ThinProvision = opts.ThinProvision
	req.Throttling = opts.Throttling
	req.NewName = opts.NewName
	req.NqnWhitelist = opts.NqnWhitelist
	req.SingleAccess = opts.SingleAccess
	req.Prioritized = opts.Prioritized
	req.ChunkInstancesBits = opts.ChunkInstancesBits

	return n.client.UpdateDistNamespace(ctx, req)
}

func (n *NVMFService) DeleteNamespace(ctx context.Context, nsVolumeId string) error {
	return n.DeleteNamespaceV2(ctx, nsVolumeId, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (n *NVMFService) DeleteNamespaceV2(ctx context.Context, nsVolumeId string, deletePermanently bool) error {
	req := &meta.DeleteDistNamespaceRequest{
		PoolPath:          &meta.PoolPath{},
		VolumeId:          []byte(nsVolumeId),
		DeletePermanently: &deletePermanently,
	}
	_, err := n.client.DeleteDistNamespace(ctx, req)

	return err
}

func (n *NVMFService) AddNamespaceAllowedHost(ctx context.Context, nsVolumeId string, allowedHost []string) (*NVMFNamespace, error) {
	req := &meta.NVMFNamespaceAllowedHost{
		NsPath: &meta.DistNamespacePath{
			PoolPath: &meta.PoolPath{},
			VolumeId: []byte(nsVolumeId),
		},
		HostNqn: []byte(strings.Join(allowedHost, ",")),
	}

	return n.client.AddDistNamespaceAllowedHost(ctx, req)
}

func (n *NVMFService) RemoveNsNqnWhiteList(ctx context.Context, nsVolumeId string, allowedHost []string) (*NVMFNamespace, error) {
	req := &meta.NVMFNamespaceAllowedHost{
		NsPath: &meta.DistNamespacePath{
			PoolPath: &meta.PoolPath{},
			VolumeId: []byte(nsVolumeId),
		},
		HostNqn: []byte(strings.Join(allowedHost, ",")),
	}

	return n.client.RemoveDistNamespaceAllowedHost(ctx, req)
}

func (n *NVMFService) DescribeSubsystem(ctx context.Context, id string) (*NVMFSubsystem, error) {
	poolPath := &meta.PoolPath{
		PoolId: []byte(id),
	}

	return n.client.GetDistSubsystem(ctx, poolPath)
}

func (n *NVMFService) UpdateSubsystem(ctx context.Context, id string, cfg NVMFSubsystemConfig) (*NVMFSubsystem, error) {
	req := &meta.UpdateNVMFDistSubsystemRequest{
		PoolPath: &meta.PoolPath{
			PoolId: []byte(id),
		},
	}

	if cfg.NqnWhiteList != nil {
		req.NqnWhitelist = []byte(strings.Join(cfg.NqnWhiteList, ","))
	}

	if cfg.Ipv4Whitelist != nil {
		req.Ipv4Whitelist = []byte(strings.Join(cfg.Ipv4Whitelist, ","))
	}

	if cfg.ReplicaNum != nil {
		req.ReplicaNum = cfg.ReplicaNum
	}

	if cfg.Description != "" {
		req.Description = []byte(cfg.Description)
	}

	return n.client.UpdateDistSubsystem(ctx, req)
}

func (n *NVMFService) CreateSnapshotBySecondaryId(ctx context.Context, nsVolumeId string,
	secondaryId string, name string, desc string) (*Volume, error) {
	req := &meta.CreateNVMFSnapshotRequest{
		SnapshotName: []byte(name),
		SnapshotDesc: []byte(desc),
		SecondaryId:  []byte(secondaryId),
		NsPath: &meta.DistNamespacePath{
			PoolPath: &meta.PoolPath{},
			VolumeId: []byte(nsVolumeId),
		},
	}

	return n.client.CreateSnapshot(ctx, req)
}

func (n *NVMFService) DeleteSnapshot(ctx context.Context, snapshotId string) error {
	return n.DeleteSnapshotV2(ctx, snapshotId, false)
}

// The deletePermanently variable is intended solely for API callers who explicitly
// know that the volume resource they are operating on does not need to be moved to
// the recycle bin. Setting deletePermanently to true ensures this behavior.
//
// In all other cases, the volume deletion operation should follow the default behavior.
// This default behavior includes:
// - If the recycle bin is enabled, the volume must be temporarily stored in the recycle bin.
// - If the recycle bin is not enabled, the volume is deleted immediately without being stored.
func (n *NVMFService) DeleteSnapshotV2(ctx context.Context, snapshotId string, deletePermanently bool) error {
	req := &meta.DeleteNVMFSnapshotRequest{
		SnapshotId:        []byte(snapshotId),
		DeletePermanently: &deletePermanently,
	}
	_, err := n.client.DeleteSnapshot(ctx, req)

	return err
}

func (n *NVMFService) DescribeSnapshot(ctx context.Context, snapshotId string) (*Volume, error) {
	req := &meta.NVMFSnapshotPath{
		SnapshotId: []byte(snapshotId),
	}

	return n.client.ShowSnapshot(ctx, req)
}

func (n *NVMFService) DescribeSnapshotBySecondaryId(ctx context.Context, secondaryId string) (*Volume, error) {
	req := &meta.NVMFSnapshotPath{
		SecondaryId: []byte(secondaryId),
	}

	return n.client.ShowSnapshot(ctx, req)
}

func (n *NVMFService) ListSubsystems(ctx context.Context) (*meta.NVMFDistSubsystemsResponse, error) {
	return n.client.ListDistSubsystems(ctx, &meta.ListNVMFDistSubsystemsRequest{})
}

func (n *NVMFService) ListNamespaces(ctx context.Context, poolId string) (*meta.NVMFDistNamespacesResponse, error) {
	poolPath := &meta.PoolPath{
		PoolId: []byte(poolId),
	}

	return n.client.ListDistNamespace(ctx, poolPath)
}

func (n *NVMFService) ListTargets(ctx context.Context) (*meta.NVMFTargetsResponse, error) {
	return n.client.ListTargets(ctx, &zbs.Void{})
}
