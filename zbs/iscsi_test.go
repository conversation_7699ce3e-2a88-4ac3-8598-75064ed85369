package zbs

import (
	"context"
	"strings"
	"testing"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	"github.com/iomesh/zbs-client-go/utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

func TestISCSITarget(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer stopMockServer(server)

	testTarget1, err := client.ISCSI.CreateTarget(context.TODO(), "test1", nil, nil)
	if err != nil {
		t.Fatalf("failed to create target, %v", err)
	}

	testTarget2, err := client.ISCSI.CreateTarget(context.TODO(), "test2", NewDefaultStoragePolicy(), nil)
	if err != nil {
		t.Fatalf("failed to create target, %v", err)
	}

	testTarget2Get, err := client.ISCSI.DescribeTarget(context.TODO(), string(testTarget2.GetId()))

	if err != nil {
		t.Fatalf("failed to get target test2, %v", err)
	}

	if !proto.Equal(testTarget2, testTarget2Get) {
		t.Fatalf("target test2 not equal test2Get, %+v != %+v", testTarget2, testTarget2Get)
	}

	targets, err := client.ISCSI.GetTargets(context.TODO())

	if err != nil {
		t.Fatalf("failed to list target, %v", err)
	}

	if len(targets) != 2 {
		t.Fatalf("len(targets) != 2")
	}

	err = client.ISCSI.DeleteTarget(context.TODO(), string(testTarget1.GetId()))

	if err != nil {
		t.Fatalf("failed to delete target test1, %v", err)
	}

	_, err = client.ISCSI.DescribeTarget(context.TODO(), string(testTarget1.GetId()))

	if err == nil {
		t.Fatalf("get a deleted target")
	}
}

func TestISCSILun(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer stopMockServer(server)

	testTarget, err := client.ISCSI.CreateTarget(context.TODO(), "test", NewDefaultStoragePolicy(), nil)
	if err != nil {
		t.Fatalf("failed to create target, %v", err)
	}

	lun1, err := client.ISCSI.CreateLun(context.TODO(), "test", "test-1", 0, 1<<30)

	if err != nil {
		t.Fatalf("failed to create lun in target test, %v", err)
	}

	lunId := *lun1.LunId
	if !(lunId > 0 && lunId < 256) {
		t.Fatal("lunId is invalid")
	}

	lun1Get, err := client.ISCSI.DescribeLun(context.TODO(), string(lun1.VolumeId))
	if err != nil {
		t.Fatalf("failed to get lun from target test, %v", err)
	}

	if !proto.Equal(lun1, lun1Get) {
		t.Fatalf("lun1 not equal lun1Get, %+v != %+v", lun1, lun1Get)
	}

	lun2, err := client.ISCSI.CreateLunByTargetUUID(context.TODO(), string(testTarget.GetId()), "test-2", 0, 1<<30)

	if err != nil {
		t.Fatalf("failed to create lun by target uuid, %v", err)
	}

	lunId = *lun2.LunId
	if lunId == *lun1.LunId || !(lunId > 0 && lunId < 256) {
		t.Fatalf("lun2's lunId is invalid %v", *lun2.LunId)
	}

	luns, err := client.ISCSI.GetLuns(context.TODO(), string(testTarget.GetId()))

	if err != nil {
		t.Fatalf("failed to list target luns, %v", err)
	}

	if len(luns) != 2 {
		t.Fatalf("failed to list target luns, len(luns) != 2")
	}

	err = client.ISCSI.DeleteLun(context.TODO(), string(lun1.GetVolumeId()))
	if err != nil {
		t.Fatalf("failed to delete lun1, %v", err)
	}

	_, err = client.ISCSI.DescribeLun(context.TODO(), string(lun1.GetVolumeId()))

	if err == nil {
		t.Fatalf("get a deleted lun")
	}
}

func TestUpdateLun(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer stopMockServer(server)

	_, err := client.ISCSI.CreateTarget(context.TODO(), "test", NewDefaultStoragePolicy(), nil)
	if err != nil {
		t.Fatalf("failed to create target, %v", err)
	}

	lun1, err := client.ISCSI.CreateLun(context.TODO(), "test", "test-1", 0, 1<<30)
	if err != nil {
		t.Fatalf("failed to create lun in target test, %v", err)
	}

	lun1.Size = utils.NewUint64(256)
	lun1.ReplicaNum = utils.NewUint32(2)
	lun1.ThinProvision = utils.NewBool(true)

	lun2, err := client.ISCSI.UpdateLun(context.TODO(), string(lun1.GetVolumeId()),
		WithLunNewSize(256), WithLunReplicaNum(2), WithLunThinProvision(true))
	if err != nil || !proto.Equal(lun1, lun2) {
		t.Fatalf("failed to update lun %v, %v", string(lun1.GetVolumeId()), err)
	}
}

func TestInitiators(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer stopMockServer(server)

	config := &ISCSITargetConfig{
		IqnWhiteList: []string{"*/*"},
	}

	testTarget, err := client.ISCSI.CreateTarget(context.TODO(), "test", NewDefaultStoragePolicy(), config)
	if err != nil {
		t.Fatalf("failed to create target, %v", err)
	}

	if string(testTarget.GetIqnWhitelistV2()) != "*/*" {
		t.Fatalf("failed to create target with iqn whiteList, %v != */*", testTarget.GetIqnWhitelistV2())
	}

	lun1, err := client.ISCSI.CreateLunByTargetUUID(context.TODO(), string(testTarget.GetId()), "test-1", 1, 1<<30, WithLunAllowedInitiators([]string{"test"}))
	if err != nil {
		t.Fatalf("failed to create lun in target test, %v", err)
	}

	if string(lun1.GetAllowedInitiators()) != "test" {
		t.Fatalf("allowedInitiators not match, %v != %v", string(lun1.GetAllowedInitiators()), "test-1,test-2")
	}

	_, err = client.ISCSI.RemoveLunAllowedInitiators(context.TODO(), string(lun1.GetVolumeId()), []string{"test"})
	if err != nil {
		t.Fatalf("failed to remove allowed initiators, %v", err)
	}

	lun1, err = client.ISCSI.AddLunAllowedInitiators(context.TODO(), string(lun1.GetVolumeId()), []string{"test-1", "test-2"})
	if err != nil {
		t.Fatalf("failed to add lun initiator, %v", err)
	}

	initiators := string(lun1.GetAllowedInitiators())
	if !(strings.Contains(initiators, "test-1") && strings.Contains(initiators, "test-2")) {
		t.Fatalf("failed to add lun initiator, %+v", lun1)
	}

	lun1, err = client.ISCSI.RemoveLunAllowedInitiators(context.TODO(), string(lun1.GetVolumeId()), []string{"test-1"})
	if err != nil {
		t.Fatalf("failed to remove lun initiator, %v", err)
	}

	initiators = string(lun1.GetAllowedInitiators())
	if initiators != "test-2" {
		t.Fatalf("failed to remove lun initiator test-1, %+v", lun1)
	}

	lun1, err = client.ISCSI.RemoveLunAllowedInitiators(context.TODO(), string(lun1.GetVolumeId()), []string{"test-2"})
	if err != nil {
		t.Fatalf("failed to remove lun initiator, %v", err)
	}

	initiators = string(lun1.GetAllowedInitiators())
	if initiators != "" {
		t.Fatalf("failed to remove lun initiator %+v", lun1)
	}
}

func TestISCSISnapshot(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer stopMockServer(server)

	target, err := client.ISCSI.CreateTarget(context.TODO(), "test", NewDefaultStoragePolicy(), nil)
	require.NoErrorf(t, err, "failed to create target, %v", err)

	lun1, err := client.ISCSI.CreateLun(context.TODO(), "test", "test-1", 0, 1<<30)
	require.NoErrorf(t, err, "failed to create lun, %v", err)

	snapshot, err := client.ISCSI.CreateSnapshot(context.TODO(), string(lun1.GetVolumeId()), "test-snapshot", "")
	require.NoErrorf(t, err, "failed to create snapshot, %v", err)

	require.Equal(t, string(snapshot.GetParentId()), string(lun1.GetVolumeId()), "snapshot parent id is not match")

	snapshots, err := client.ISCSI.ListTargetSnapshot(context.TODO(), string(target.GetId()))
	require.NoErrorf(t, err, "failed to create, %v", err)
	require.Len(t, snapshots, 1, "snapshot len is not match")
	require.Truef(t, proto.Equal(snapshots[0], snapshot), "snapshot conflict %+v != %+v", snapshots[0], snapshot)

	err = client.ISCSI.DeleteSnapshot(context.TODO(), string(snapshot.GetId()))
	require.NoErrorf(t, err, "failed to delete snapshot, %v", err)

	_, err = client.ISCSI.DescribeSnapshot(context.TODO(), string(snapshot.GetId()))
	require.Errorf(t, err, "get a deleted snapshot, %v", err)
}

func TestISCSIClone(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer stopMockServer(server)

	target, err := client.ISCSI.CreateTarget(context.TODO(), "test", NewDefaultStoragePolicy(), nil)
	require.NoErrorf(t, err, "failed to create target, %v", err)

	lun1, err := client.ISCSI.CreateLun(context.TODO(), "test", "test-1", 0, 1<<30)
	require.NoErrorf(t, err, "failed to create lun, %v", err)

	snapshot, err := client.ISCSI.CreateSnapshot(context.TODO(), string(lun1.GetVolumeId()), "test-snapshot", "")
	require.NoErrorf(t, err, "failed to create snapshot, %v", err)

	cloneOpts := &LunCloneOptions{
		SrcSnapshotId: utils.NewString(string(snapshot.GetId())),
	}

	lun2, err := client.ISCSI.CreateLunByTargetUUID(context.TODO(), string(target.GetId()), "test-1-clone-snapshot", 0, 0, WithLunCloneOpts(cloneOpts))
	require.NoErrorf(t, err, "failed to clone from snapshot, %v", err)
	assert.Equal(t, *lun2.Size, *lun1.Size, "clone size is not match")

	cloneOpts = &LunCloneOptions{
		SrcLunPath: &meta.LunPath{
			PoolPath: &meta.PoolPath{
				PoolId: target.GetId(),
			},
			LunId: utils.NewUint32(lun1.GetLunId()),
		},
	}

	lun3, err := client.ISCSI.CreateLunByTargetUUID(context.TODO(), string(target.GetId()), "test-1-clone-lun", 0, 0, WithLunCloneOpts(cloneOpts))
	require.NoErrorf(t, err, "failed to clone from lun, %v", err)
	require.Equal(t, *lun3.Size, *lun1.Size, "clone size is not match")
}

func TestSecondaryId(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer stopMockServer(server)

	target, err := client.ISCSI.CreateTarget(context.TODO(), "test", NewDefaultStoragePolicy(), nil)
	assert.Nilf(t, err, "failed to create target, %v", err)

	defer func() {
		_ = client.ISCSI.DeleteTarget(context.TODO(), string(target.GetId()))
	}()

	lun1, err := client.ISCSI.CreateLunBySecondaryId(context.TODO(), string(target.GetId()),
		"test-1", "secondary-id-1", 1<<30)
	assert.Nilf(t, err, "failed to create lun, %v", err)

	_, err = client.ISCSI.CreateLunBySecondaryId(context.TODO(), string(target.GetId()),
		"test-2", "secondary-id-1", 1<<30)
	assert.NotNilf(t, err, "create by secondaryId duplicate, %v", err)

	lun1Get, err := client.ISCSI.DescribeLunBySecondaryId(context.TODO(), "secondary-id-1")
	assert.Nilf(t, err, "failed to describe lun by secondary_id, %v", err)

	assert.Truef(t, proto.Equal(lun1, lun1Get), "lun not match %+v != %+v", lun1, lun1Get)

	snapshot1, err := client.ISCSI.CreateSnapshotBySecondaryId(context.TODO(),
		string(lun1.GetVolumeId()), "secondary-id-1", "test-snapshot", "test")
	assert.Nilf(t, err, "failed to create snapshot by secondary_id %v", err)

	snapshot1Get, err := client.ISCSI.DescribeSnapshotBySecondaryId(context.TODO(), "secondary-id-1")
	assert.Nilf(t, err, "failed to describe snapshot by secondary_id %v", err)

	assert.Truef(t, proto.Equal(snapshot1, snapshot1Get), "snapshot not match %+v != %+v", snapshot1, snapshot1Get)

	err = client.ISCSI.DeleteSnapshotBySecondaryId(context.TODO(), "secondary-id-1")
	assert.Nilf(t, err, "failed to delete snapshot by secondary_id %v", err)

	err = client.ISCSI.DeleteLunBySecondaryId(context.TODO(), "secondary-id-1")
	assert.Nilf(t, err, "failed to delete lun by secondary_id, %v", err)

	_, err = client.ISCSI.DescribeLunBySecondaryId(context.TODO(), "secondary-id-1")
	assert.NotNilf(t, err, "lun not exists, %v", err)
}

func TestUpdateTarget(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer func() { _ = server.Stop() }()

	target, err := client.ISCSI.CreateTarget(context.TODO(), "test", NewDefaultStoragePolicy(), nil)
	assert.Nilf(t, err, "failed to create target, %v", err)

	config := &ISCSITargetConfig{}
	config.IqnWhiteList = []string{"iqn.2020-05.com.iomesh:xxx"}
	config.WhiteList = []string{"127.0.0.1"}
	targetName := "test-updated"
	targetGet, err := client.ISCSI.UpdateTarget(context.TODO(), string(target.GetId()), &targetName, config)
	assert.Nilf(t, err, "failed to update target, %v", err)
	assert.Equalf(t, []byte(config.IqnWhiteList[0]), targetGet.GetIqnWhitelistV2(), "failed to update iqn whitelist")
	assert.Equalf(t, []byte(config.WhiteList[0]), targetGet.GetPool().GetWhitelist(), "failed to update whitelist")
	assert.Equalf(t, []byte(targetName), targetGet.GetName(), "failed to update target name")
}

func TestTargetRequirement(t *testing.T) {
	client, server := NewMockServerISCSI(t)

	defer func() { _ = server.Stop() }()

	requirement := &meta.TargetRequirement{}

	requirement.ExternalUse = utils.NewBool(true)
	requirement.Labels = &zbs.Labels{
		Labels: []*zbs.Label{
			{
				Key:   []byte("test-1"),
				Value: []byte("true"),
			},
		},
	}

	lun1, err := client.ISCSI.CreateLunBySecondaryId(context.TODO(), "", "test-1",
		"secondary-id-1", 1<<30, WithTargetRequirement(requirement))

	assert.Nilf(t, err, "failed to create lun, %v", err)

	lun1Get, err := client.ISCSI.DescribeLun(context.TODO(),
		string(lun1.GetVolumeId()))
	assert.Nilf(t, err, "failed to describe lun, %v", err)

	assert.Truef(t, proto.Equal(lun1Get, lun1), "lun not match %+v != %+v", lun1, lun1Get)

	requirement.Labels.Labels[0].Key = []byte("test-2")
	lun2, err := client.ISCSI.CreateLunBySecondaryId(context.TODO(), "", "",
		"secondary-id-2", 1<<30, WithTargetRequirement(requirement))
	assert.Nilf(t, err, "failed to create lun, %v", err)
	assert.NotEqual(t, string(lun1.GetPoolId()), string(lun2.GetPoolId()))

	_, err = client.ISCSI.CreateLunBySecondaryId(context.TODO(), "", "",
		"secondary-id-3", 1<<30, WithTargetRequirement(&meta.TargetRequirement{}))
	assert.Nilf(t, err, "failed to create lun, %v", err)

	targets, err := client.ISCSI.GetTargets(context.TODO())
	assert.Nilf(t, err, "%+v", err)
	assert.Equal(t, 3, len(targets))
}

func TestSingleAccess(t *testing.T) {
	client, server := NewMockServerISCSI(t)
	defer stopMockServer(server)

	ctx := context.TODO()

	target, err := client.ISCSI.CreateTarget(ctx, "test", NewDefaultStoragePolicy(), nil)
	assert.Nilf(t, err, "failed to create target, %v", err)

	createLun := func(secondaryId string, allowedInitiators []string, singleAccess bool) (*meta.ISCSILun, error) {
		t.Helper()

		return client.ISCSI.CreateLunBySecondaryId(ctx, string(target.Id), secondaryId, secondaryId, 1<<3,
			WithLunAllowedInitiators(allowedInitiators), WithLunSingleAccess(singleAccess))
	}

	updateLun := func(uuid string, allowedInitiators []string, singleAccess bool) (*meta.ISCSILun, error) {
		t.Helper()

		return client.ISCSI.UpdateLun(ctx, uuid, WithLunNewAllowedInitiators(allowedInitiators), WithLunNewSingleAccess(singleAccess))
	}

	_, err = createLun("test-1", []string{"iqn0", "iqn1"}, true)
	assert.NotNil(t, err, "create lun should failed when singleAccess=true and len(allowedInitiators)>1")

	_, err = createLun("test-2", []string{"*/*"}, true)
	assert.NotNil(t, err, "create lun should failed when singleAccess=true and `*/*` in allowedInitiators")

	_, err = createLun("test-3", []string{"iqn0"}, true)
	assert.Nil(t, err, "create lun should successed when singleAccess=true and len(allowedInitiators)<=1")

	lun, err := client.ISCSI.DescribeLunBySecondaryId(ctx, "test-3")
	assert.Nil(t, err, "describe lun should successed when singleAccess=true and len(allowedInitiators)<=1")
	assert.Equal(t, true, lun.GetSingleAccess(), "lun's singleAccess field should be true")

	_, err = createLun("test-4", []string{}, true)
	assert.Nil(t, err, "create lun should successed when singleAccess=true and len(allowedInitiators)<=1")

	lun, err = client.ISCSI.DescribeLunBySecondaryId(ctx, "test-4")
	assert.Nil(t, err, "describe lun should successed when singleAccess=true and len(allowedInitiators)<=1")
	assert.Equal(t, true, lun.GetSingleAccess(), "lun's singleAccess field should be true")

	_, err = createLun("test-5", nil, true)
	assert.Nil(t, err, "create lun should successed when singleAccess=true and len(allowedInitiators)<=1")

	lun, err = client.ISCSI.DescribeLunBySecondaryId(ctx, "test-5")
	assert.Nil(t, err, "describe lun should successed when singleAccess=true and len(allowedInitiators)<=1")
	assert.Equal(t, true, lun.GetSingleAccess(), "lun's singleAccess field should be true")

	lunWithoutSingleAccess, err := createLun("test-6", []string{}, false)
	assert.Nil(t, err, "create lun should successed")

	_, err = updateLun(string(lunWithoutSingleAccess.VolumeId), []string{"iqn1", "iqn2"}, true)
	assert.NotNil(t, err, "update lun to singleAccess should failed when len(allowedInitiators)>1")

	_, err = updateLun(string(lunWithoutSingleAccess.VolumeId), []string{"iqn1"}, true)
	assert.Nil(t, err, "update lun to singleAccess should successed when len(allowedInitiators)<=1")

	lun, err = client.ISCSI.DescribeLunBySecondaryId(ctx, "test-6")
	assert.Nil(t, err, "describe lun should successed")
	assert.Equal(t, true, lun.GetSingleAccess(), "lun's singleAccess field should be true")

	lunWithoutSingleAccess, err = createLun("test-7", []string{"*/*"}, false)
	assert.Nil(t, err, "create lun should successed")

	_, err = updateLun(string(lunWithoutSingleAccess.VolumeId), nil, true)
	assert.NotNil(t, err, "update lun to singleAccess should failed when `*/*` in allowedInitiators")
}
