package zbs

import (
	"container/heap"
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"sort"
	"sync"

	"github.com/pkg/errors"
	"k8s.io/klog"

	blockv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/block"
	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
	zbserror "github.com/iomesh/zbs-client-go/zbs/error"
)

const compareConcurrentLimit = 8
const compareBatchExtentNumber = 128

type DiffRange struct {
	Start  uint64
	Length uint64
}

func (c *Client) CompareVolume(ctx context.Context, baseVolumeId string, targetVolumeId string) ([]DiffRange, error) {
	if len(baseVolumeId) == 0 && len(targetVolumeId) == 0 {
		return nil, nil
	} else if len(targetVolumeId) == 0 {
		baseVolumeId, targetVolumeId = targetVolumeId, baseVolumeId
	}

	manager, err := newCompareVolumeManager(ctx, c, baseVolumeId, targetVolumeId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create compare volume manager, volume %s and %s", baseVolumeId, targetVolumeId)
	}

	defer manager.close()

	diffRanges, err := manager.compareVolume(ctx)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to compare volume %s and %s", baseVolumeId, targetVolumeId)
	}

	return diffRanges, nil
}

type compareMode string

const (
	compareModeLExtent compareMode = "COMPARE_LEXTENT"
	compareModePid     compareMode = "COMPARE_PID"
)

var wholeExtentDiffRange = []DiffRange{{Start: 0, Length: DefaultExtentSize}}

type compareVolumeManager struct {
	baseVolumeId          string
	targetVolumeId        string
	baseVolumeExtentIds   []uint32
	targetVolumeExtentIds []uint32
	stripeNum             uint64
	stripeSize            uint64

	mu                sync.Mutex
	dataChannelHosts  []string
	dataChannelClient DataChannel
	// this func is for unit test, DO NOT alter it in production
	newDataChannel func(endpoint string, config *dc.Config) (DataChannel, error)

	compareMode compareMode
	client      *Client
}

type taskInput struct {
	ctx       context.Context
	vExtentNo uint64
}

type taskOutput struct {
	err        error
	diffRanges []DiffRange
	vExtentNo  uint64
}

func newCompareVolumeManager(ctx context.Context, client *Client, baseVolumeId string, compareVolumeId string) (*compareVolumeManager, error) {
	manager := &compareVolumeManager{
		baseVolumeId:   baseVolumeId,
		targetVolumeId: compareVolumeId,
		client:         client,
		compareMode:    compareModePid,

		newDataChannel: func(endpoint string, config *dc.Config) (DataChannel, error) {
			return NewDataChannelService(endpoint, config)
		},
	}

	err := manager.precheck(ctx)
	if err != nil {
		return nil, err
	}

	err = manager.init(ctx)
	if err != nil {
		return nil, err
	}

	return manager, nil
}

func (m *compareVolumeManager) precheck(ctx context.Context) error {
	if m.targetVolumeId == "" {
		return errors.New("target volume id cannot be empty")
	}

	if m.baseVolumeId == "" {
		return nil
	}

	targetVolume, err := m.client.Meta.ShowVolume(ctx, &metav1.VolumePath{
		VolumeId: []byte(m.targetVolumeId),
	})
	if err != nil {
		return err
	}

	baseVolume, err := m.client.Meta.ShowVolume(ctx, &metav1.VolumePath{
		VolumeId: []byte(m.baseVolumeId),
	})
	if err != nil {
		return err
	}

	if baseVolume.Volume.GetStripeNum() != targetVolume.Volume.GetStripeNum() {
		return errors.New("stripe number of two volumes not match")
	}

	if baseVolume.Volume.GetStripeSize() != targetVolume.Volume.GetStripeSize() {
		return errors.New("stripe size of two volumes not match")
	}

	return nil
}

func (m *compareVolumeManager) init(ctx context.Context) error {
	err := m.initCompareMode(ctx)
	if err != nil {
		return err
	}

	targetVolume, err := m.client.Meta.ShowVolume(ctx, &metav1.VolumePath{
		VolumeId: []byte(m.targetVolumeId),
	})
	if err != nil {
		return err
	}

	m.stripeNum = uint64(targetVolume.Volume.GetStripeNum())
	m.stripeSize = uint64(targetVolume.Volume.GetStripeSize())
	m.targetVolumeExtentIds = m.getVolumeExtentIds(targetVolume)

	// In scenarios such as full backup, the baseVolume can be empty.
	// The extents in compareVolume will be compared to 0 (InvalidExtentId) to get all allocation ranges.
	if m.baseVolumeId == "" {
		for range targetVolume.GetLextents() {
			m.baseVolumeExtentIds = append(m.baseVolumeExtentIds, InvalidExtentId)
		}
	} else {
		baseVolume, err := m.client.Meta.ShowVolume(ctx, &metav1.VolumePath{
			VolumeId: []byte(m.baseVolumeId),
		})
		if err != nil {
			return err
		}

		m.baseVolumeExtentIds = m.getVolumeExtentIds(baseVolume)
	}

	resp, err := m.client.Meta.Chunk.ListChunk(ctx)
	if err != nil {
		return err
	}

	chunks := resp.GetChunks()
	if len(chunks) == 0 {
		return errors.New("unexpected to find no chunk exists")
	}

	m.dataChannelHosts = make([]string, len(chunks))

	for i, chunk := range chunks {
		m.dataChannelHosts[i] = fmt.Sprintf("%s:%d",
			resolveIP(chunk.GetDataIp()), chunk.GetDataPort())
	}

	return nil
}

func (m *compareVolumeManager) getDataChannelClient() (DataChannel, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if isDataChannelClientAvaliable(m.dataChannelClient) {
		return m.dataChannelClient, nil
	} else if m.dataChannelClient != nil {
		_ = m.dataChannelClient.Close()
		m.dataChannelClient = nil
	}

	hostLength := int64(len(m.dataChannelHosts))

	startIndex, err := rand.Int(rand.Reader, big.NewInt(hostLength))
	if err != nil {
		return nil, err
	}

	for i := int64(0); i < hostLength; i++ {
		index := (startIndex.Int64() + i) % hostLength

		dataChannelClient, err := m.newDataChannel(m.dataChannelHosts[index], dc.DefaultClientConfig())
		if err != nil {
			klog.Errorf("[zbs-client] failed to new data channel client, host: %s, err: %v", m.dataChannelHosts[index], err)

			continue
		} else if isDataChannelClientAvaliable(dataChannelClient) {
			m.dataChannelClient = dataChannelClient

			return m.dataChannelClient, nil
		}

		klog.Errorf("[zbs-client] failed to ping data channel client, host: %s", m.dataChannelHosts[index])

		_ = dataChannelClient.Close()
	}

	return nil, errors.New("failed to connect to any data channel")
}

func (m *compareVolumeManager) getVolumeExtentIds(volume *metav1.ShowVolumeResponse) []uint32 {
	lextents := volume.GetLextents()
	volumeExtentIds := make([]uint32, 0, len(lextents))

	for _, lextent := range lextents {
		if m.compareMode == compareModePid && lextent.GetLocation() == 0 {
			volumeExtentIds = append(volumeExtentIds, InvalidExtentId)
		} else {
			volumeExtentIds = append(volumeExtentIds, lextent.GetId())
		}
	}

	return volumeExtentIds
}

func (m *compareVolumeManager) initCompareMode(ctx context.Context) error {
	// Use 0 (InvalidExtentId) comparisons to avoid Lid Not Allocated error.
	// ZBS CompareLExtent handles this special case and will simply return success.
	_, err := m.client.Block.CompareLExtent(ctx, InvalidExtentId, InvalidExtentId)
	if err == nil {
		m.compareMode = compareModeLExtent

		return nil
	} else if !zbserror.IsUnknownServiceId(err) {
		return err
	}

	m.compareMode = compareModePid

	return nil
}

func (m *compareVolumeManager) startWorker(inputCh chan taskInput, outputCh chan *taskOutput, wg *sync.WaitGroup) {
	for i := 0; i < compareConcurrentLimit; i++ {
		wg.Add(1)

		go m.worker(inputCh, outputCh, wg)
	}
}

func (m *compareVolumeManager) worker(inputCh chan taskInput, outputCh chan *taskOutput, wg *sync.WaitGroup) {
	var extentDiffRanges []DiffRange
	var err error

	for input := range inputCh {
		if input.vExtentNo >= uint64(len(m.baseVolumeExtentIds)) {
			extentDiffRanges = wholeExtentDiffRange
			err = nil
		} else {
			extentDiffRanges, err = m.compareExtent(input.ctx, input.vExtentNo,
				m.baseVolumeExtentIds[input.vExtentNo], m.targetVolumeExtentIds[input.vExtentNo])
		}

		outputCh <- &taskOutput{
			vExtentNo:  input.vExtentNo,
			diffRanges: m.transformToStripedRanges(input.vExtentNo, extentDiffRanges),
			err:        err,
		}
	}

	wg.Done()
}

func (m *compareVolumeManager) compareVolume(ctx context.Context) ([]DiffRange, error) {
	return m.compareExtents(ctx, 0, uint64(len(m.targetVolumeExtentIds)))
}

func (m *compareVolumeManager) compareExtents(ctx context.Context, startVExtentNo uint64, endVExtentNo uint64) ([]DiffRange, error) {
	inputCh := make(chan taskInput, compareConcurrentLimit)
	outputCh := make(chan *taskOutput, compareConcurrentLimit)
	workerWg := &sync.WaitGroup{}

	defer func() {
		// consuming all output ensures that all worker can exit
		go func() {
			for range outputCh {
			}
		}()

		close(inputCh)
		workerWg.Wait()
		close(outputCh)
	}()

	m.startWorker(inputCh, outputCh, workerWg)

	batchDiffRanges := make([][]DiffRange, compareBatchExtentNumber)

	lastDiffRange := DiffRange{}
	// length of merged result is unpredictable here
	result := make([]DiffRange, 0)

	for ; startVExtentNo < endVExtentNo; startVExtentNo += compareBatchExtentNumber {
		inputNumber, outputNumber := uint64(0), uint64(0)
		expectedNumber := min(endVExtentNo-startVExtentNo, compareBatchExtentNumber)
		var output *taskOutput

		for inputNumber < expectedNumber || outputNumber < expectedNumber {
			if inputNumber >= expectedNumber {
				output = <-outputCh
			} else {
				select {
				// vExtentNo is the extent serial number inside the volume
				case inputCh <- taskInput{ctx: ctx, vExtentNo: inputNumber + startVExtentNo}:
					inputNumber += 1
					continue
				case output = <-outputCh:
				}
			}

			batchDiffRanges[output.vExtentNo-startVExtentNo] = output.diffRanges

			if output.err != nil {
				return nil, output.err
			}

			outputNumber += 1
		}

		// TODO: reuse sortedRanges
		sortedRanges := SortExtentsDiffRanges(batchDiffRanges[:expectedNumber])
		if len(sortedRanges) == 0 {
			continue
		}

		for _, r := range sortedRanges {
			switch {
			case lastDiffRange.Length == 0: // DiffRange with Length of 0 is considered to be not inited
				lastDiffRange = r
			case lastDiffRange.Start+lastDiffRange.Length == r.Start:
				lastDiffRange.Length += r.Length
			default:
				result = append(result, lastDiffRange)
				lastDiffRange = r
			}
		}
	}

	if lastDiffRange.Length > 0 {
		result = append(result, lastDiffRange)
	}

	return result, nil
}

func (m *compareVolumeManager) compareExtent(ctx context.Context, vExtentNo uint64, baseExtentId uint32, targetExtentId uint32) ([]DiffRange, error) {
	// If the extent id is the same, the extent has not changed.
	if baseExtentId == targetExtentId {
		return nil, nil
	}

	switch m.compareMode {
	case compareModeLExtent:
		if baseExtentId != InvalidExtentId {
			err := m.prepareExtent(ctx, m.baseVolumeId, vExtentNo)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to prepare base lextent %d, vExtentNo %d", baseExtentId, vExtentNo)
			}
		}

		if targetExtentId != InvalidExtentId {
			err := m.prepareExtent(ctx, m.targetVolumeId, vExtentNo)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to prepare target lextent %d, vExtentNo %d", targetExtentId, vExtentNo)
			}
		}

		diffRanges, err := m.compareLExtent(ctx, baseExtentId, targetExtentId)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to compare lextent %d and %d, vExtentNo %d", baseExtentId, targetExtentId, vExtentNo)
		}

		return diffRanges, nil
	case compareModePid:
		return wholeExtentDiffRange, nil
	}

	return nil, errors.Errorf("unsupported compare mode: %s", m.compareMode)
}

// Sending a extent VolumeRead to cluster would trigger a sync generation if the owner of extent is released, the syncing can
// help preventing sending CompareExtent rpc to a owner of an out-of-date replica.
func (m *compareVolumeManager) prepareExtent(ctx context.Context, volumeId string, vExtentNo uint64) error {
	dcService, err := m.getDataChannelClient()
	if err != nil {
		return errors.New("failed to get data channel client")
	}

	length := uint32(4096)
	offset := vExtentNo * DefaultExtentSize
	readBuf := make([]byte, length)

	err = dcService.VolumeRead(ctx, volumeId, readBuf, length, offset, dc.NO_PROMOTION)
	if err != nil {
		return errors.Wrap(err, "failed to read extent")
	}

	return nil
}

func (m *compareVolumeManager) compareLExtent(ctx context.Context, lid1 uint32, lid2 uint32) ([]DiffRange, error) {
	resp, err := m.client.Block.CompareLExtent(ctx, lid1, lid2)
	if err != nil {
		return nil, err
	}

	return m.extractDiffRanges(resp), nil
}

func (m *compareVolumeManager) extractDiffRanges(resp *blockv1.CompareExtentResponse) []DiffRange {
	zbsDiffRanges := resp.GetDiffRanges()
	diffRanges := make([]DiffRange, len(zbsDiffRanges))

	for i := range zbsDiffRanges {
		diffRanges[i] = DiffRange{
			Start:  *zbsDiffRanges[i].Start,
			Length: *zbsDiffRanges[i].Length,
		}
	}

	return diffRanges
}

func (m *compareVolumeManager) transformToStripedRanges(vExtentNo uint64, extentDiffRanges []DiffRange) []DiffRange {
	stripedDiffRanges := make([]DiffRange, 0)

	for _, extentDiffRange := range extentDiffRanges {
		left := extentDiffRange.Start
		right := extentDiffRange.Start + extentDiffRange.Length

		// split range by stripe size
		stripedRight := (left + m.stripeSize) / m.stripeSize * m.stripeSize

		for right > stripedRight {
			stripedDiffRanges = append(stripedDiffRanges, DiffRange{
				Start:  m.transformToStripedOffset(vExtentNo, left),
				Length: stripedRight - left,
			})

			left = stripedRight
			stripedRight += m.stripeSize
		}

		stripedDiffRanges = append(stripedDiffRanges, DiffRange{
			Start:  m.transformToStripedOffset(vExtentNo, left),
			Length: right - left,
		})
	}

	sort.Slice(stripedDiffRanges, func(i, j int) bool {
		return stripedDiffRanges[i].Start < stripedDiffRanges[j].Start
	})

	return stripedDiffRanges
}

func (m *compareVolumeManager) transformToStripedOffset(vExtentNo uint64, offset uint64) uint64 {
	groupStartVExtentNo := vExtentNo / m.stripeNum * m.stripeNum
	groupExtentIdx := vExtentNo - groupStartVExtentNo

	stripeIdx := offset / m.stripeSize
	inStripeOffset := offset - stripeIdx*m.stripeSize

	return groupStartVExtentNo*DefaultExtentSize + (stripeIdx*m.stripeNum+groupExtentIdx)*m.stripeSize + inStripeOffset
}

func (m *compareVolumeManager) close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.dataChannelClient != nil {
		return m.dataChannelClient.Close()
	}

	return nil
}

type heapItem struct {
	value    *DiffRange
	arrayIdx int
	elemIdx  int
}

type minHeap []heapItem

func (h minHeap) Len() int           { return len(h) }
func (h minHeap) Less(i, j int) bool { return h[i].value.Start < h[j].value.Start }
func (h minHeap) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h *minHeap) Push(x interface{}) {
	*h = append(*h, x.(heapItem))
}

func (h *minHeap) Pop() interface{} {
	old := *h
	e := len(old) - 1
	x := old[e]
	*h = old[:e]

	return x
}

// SortExtentsDiffRanges accepts multiple ordered extent diff ranges
// and use k-way merge-sort to obtain an ordered diff range slice.
func SortExtentsDiffRanges(extentsDiffRanges [][]DiffRange) []DiffRange {
	minHeap := &minHeap{}
	heap.Init(minHeap)

	resultSize := 0

	for i, extentDiffRanges := range extentsDiffRanges {
		if len(extentDiffRanges) > 0 {
			heap.Push(minHeap, heapItem{
				value:    &extentDiffRanges[0],
				arrayIdx: i,
				elemIdx:  0,
			})
		}

		resultSize += len(extentDiffRanges)
	}

	result := make([]DiffRange, 0, resultSize)

	for minHeap.Len() > 0 {
		minElement := &(*minHeap)[0]

		result = append(result, *minElement.value)

		nextElemIdx := minElement.elemIdx + 1
		if nextElemIdx < len(extentsDiffRanges[minElement.arrayIdx]) {
			minElement.value = &extentsDiffRanges[minElement.arrayIdx][nextElemIdx]
			minElement.elemIdx = nextElemIdx

			heap.Fix(minHeap, 0)
		} else {
			heap.Pop(minHeap)
		}
	}

	return result
}

func isDataChannelClientAvaliable(dcClient DataChannel) bool {
	if dcClient == nil {
		return false
	}

	return dcClient.Ping(context.TODO()) == nil
}
