package zbs

import (
	"context"
	"math/rand"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs"
	blockv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/block"
	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
	mock_zbs "github.com/iomesh/zbs-client-go/zbs/mock"
	"github.com/iomesh/zbs-client-go/zbs/zrpc"
)

const (
	testStripeSize     = uint64(256 << 10) // 256KB
	testBaseVolumeID   = "volume-id-1"
	testTargetVolumeID = "volume-id-2"
)

// nolint:funlen
func TestTransformToStripedRanges(t *testing.T) {
	ma := new(compareVolumeManager)
	ma.stripeSize = testStripeSize

	cases := []struct {
		name      string
		stripeNum uint64
		vExtentNo uint64
		source    []DiffRange
		expect    []DiffRange
	}{
		{
			name:      "stipeNum=1, start align to stripeSize, length less than or equal to stripeSize",
			stripeNum: 1,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 0, Length: 10},
				{Start: 0, Length: testStripeSize},
				{Start: testStripeSize, Length: 10},
				{Start: testStripeSize, Length: testStripeSize},
			},
			expect: []DiffRange{
				{Start: 5 * DefaultExtentSize, Length: 10},
				{Start: 5 * DefaultExtentSize, Length: testStripeSize},
				{Start: 5*DefaultExtentSize + testStripeSize, Length: 10},
				{Start: 5*DefaultExtentSize + testStripeSize, Length: testStripeSize},
			},
		},
		{
			name:      "stipeNum=1, start not align to stripeSize, length less than or equal to stripeSize",
			stripeNum: 1,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 20, Length: 10},
				{Start: 20, Length: testStripeSize},
			},
			expect: []DiffRange{
				{Start: 5*DefaultExtentSize + 20, Length: 10},
				{Start: 5*DefaultExtentSize + 20, Length: testStripeSize - 20},
				{Start: 5*DefaultExtentSize + testStripeSize, Length: 20},
			},
		},
		{
			name:      "stipeNum=1, start align to stripeSize, length greater than stripeSize",
			stripeNum: 1,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 0, Length: testStripeSize + 10},
				{Start: 2 * testStripeSize, Length: 2 * testStripeSize},
			},
			expect: []DiffRange{
				{Start: 5 * DefaultExtentSize, Length: testStripeSize},
				{Start: 5*DefaultExtentSize + testStripeSize, Length: 10},
				{Start: 5*DefaultExtentSize + 2*testStripeSize, Length: testStripeSize},
				{Start: 5*DefaultExtentSize + 3*testStripeSize, Length: testStripeSize},
			},
		},
		{
			name:      "stipeNum=1, start not align to stripeSize, length greater than stripeSize",
			stripeNum: 1,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 20, Length: 2 * testStripeSize},
			},
			expect: []DiffRange{
				{Start: 5*DefaultExtentSize + 20, Length: testStripeSize - 20},
				{Start: 5*DefaultExtentSize + testStripeSize, Length: testStripeSize},
				{Start: 5*DefaultExtentSize + 2*testStripeSize, Length: 20},
			},
		},
		{
			name:      "stipeNum=4, start align to stripeSize, length less than or equal to stripeSize",
			stripeNum: 4,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 0, Length: 10},
				{Start: 0, Length: testStripeSize},
				{Start: testStripeSize, Length: 10},
				{Start: testStripeSize, Length: testStripeSize},
			},
			expect: []DiffRange{
				{Start: 4*DefaultExtentSize + testStripeSize, Length: 10},
				{Start: 4*DefaultExtentSize + testStripeSize, Length: testStripeSize},
				{Start: 4*DefaultExtentSize + 5*testStripeSize, Length: 10},
				{Start: 4*DefaultExtentSize + 5*testStripeSize, Length: testStripeSize},
			},
		},
		{
			name:      "stipeNum=4, start not align to stripeSize, length less than or equal to stripeSize",
			stripeNum: 4,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 20, Length: 10},
				{Start: 20, Length: testStripeSize},
			},
			expect: []DiffRange{
				{Start: 4*DefaultExtentSize + testStripeSize + 20, Length: 10},
				{Start: 4*DefaultExtentSize + testStripeSize + 20, Length: testStripeSize - 20},
				{Start: 4*DefaultExtentSize + 5*testStripeSize, Length: 20},
			},
		},
		{
			name:      "stipeNum=4, start align to stripeSize, length greater than stripeSize",
			stripeNum: 4,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 0, Length: testStripeSize + 10},
				{Start: 2 * testStripeSize, Length: 2 * testStripeSize},
			},
			expect: []DiffRange{
				{Start: 4*DefaultExtentSize + testStripeSize, Length: testStripeSize},
				{Start: 4*DefaultExtentSize + 5*testStripeSize, Length: 10},
				{Start: 4*DefaultExtentSize + 9*testStripeSize, Length: testStripeSize},
				{Start: 4*DefaultExtentSize + 13*testStripeSize, Length: testStripeSize},
			},
		},
		{
			name:      "stipeNum=4, start not align to stripeSize, length greater than stripeSize",
			stripeNum: 4,
			vExtentNo: 5,
			source: []DiffRange{
				{Start: 20, Length: 2 * testStripeSize},
			},
			expect: []DiffRange{
				{Start: 4*DefaultExtentSize + testStripeSize + 20, Length: testStripeSize - 20},
				{Start: 4*DefaultExtentSize + 5*testStripeSize, Length: testStripeSize},
				{Start: 4*DefaultExtentSize + 9*testStripeSize, Length: 20},
			},
		},
	}

	for _, c := range cases {
		ma.stripeNum = c.stripeNum

		got := ma.transformToStripedRanges(c.vExtentNo, c.source)
		if !isDiffRangeEqual(got, c.expect) {
			t.Errorf("TestTransformToStripedRanges failed: %s, got: %v, expect: %v", c.name, got, c.expect)
		}
	}
}

func isDiffRangeEqual(a, b []DiffRange) bool {
	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i].Start != b[i].Start || a[i].Length != b[i].Length {
			return false
		}
	}

	return true
}

func TestSortExtentsDiffRanges(t *testing.T) {
	extentsDiffRanges := [][]DiffRange{
		{{Start: 0}, {Start: 4}, {Start: 8}},
		{{Start: 2}, {Start: 3}, {Start: 7}},
		{{Start: 1}, {Start: 5}, {Start: 6}},
	}

	results := SortExtentsDiffRanges(extentsDiffRanges)

	if len(results) != 9 {
		t.Fatal("TestMergeExtentsDiffRanges failed: result is not full")
	}

	prevResult := results[0]
	if prevResult.Start != 0 {
		t.Fatal("TestMergeExtentsDiffRanges failed: result is not sorted")
	}

	for _, result := range results[1:] {
		if result.Start != prevResult.Start+1 {
			t.Fatal("TestMergeExtentsDiffRanges failed: result is not sorted")
		}

		prevResult = result
	}
}

func TestGetVolumeExtentIds(t *testing.T) {
	cases := []struct {
		compareMode compareMode
		ids         []uint32
		locations   []uint32
		expectIds   []uint32
	}{
		{
			compareMode: compareModePid,
			ids:         []uint32{1, 2, 3, 4},
			locations:   []uint32{0, 0, 1, 1},
			expectIds:   []uint32{0, 0, 3, 4},
		},
		{
			compareMode: compareModeLExtent,
			ids:         []uint32{1, 2, 3, 4},
			locations:   []uint32{0, 0, 1, 1},
			expectIds:   []uint32{1, 2, 3, 4},
		},
	}

	for _, c := range cases {
		manager := &compareVolumeManager{compareMode: c.compareMode}

		volume := &metav1.ShowVolumeResponse{
			Lextents: make([]*zbs.LExtentResp, len(c.ids)),
		}
		for i := range volume.Lextents {
			volume.Lextents[i] = &zbs.LExtentResp{
				Id:       &c.ids[i],
				Location: &c.locations[i],
			}
		}

		gotIds := manager.getVolumeExtentIds(volume)

		if len(gotIds) != len(c.expectIds) {
			t.Fatal("TestGetVolumeExtentIds failed: gotIds length is not equal to expectIds length")
		}

		for i := range gotIds {
			if gotIds[i] != c.expectIds[i] {
				t.Fatalf("TestGetVolumeExtentIds failed: gotIds #%d=%d is not equal to expectIds #%d=%d", i, gotIds[i], i, c.expectIds[i])
			}
		}
	}
}

func newGoMockClient(client grpc.ClientConnInterface) *Client {
	return &Client{
		ISCSI:  NewISCSIService(client),
		NVMF:   NewNVMFService(client),
		Meta:   NewMetaService(client),
		Status: NewStatusService(client),
		Chunk:  NewChunkService(client),
		VIP:    NewVIPService(client),
		Block:  NewBlockService(client),
	}
}

func handleDataChannelRPC(mockDC *mock_zbs.MockDataChannel, pingErr error, closeErr error) {
	// TODO: strictly check VolumeRead during prepareExtent
	mockDC.EXPECT().
		VolumeRead(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	mockDC.EXPECT().
		Ping(gomock.Any()).Return(pingErr).AnyTimes()

	mockDC.EXPECT().
		Close().Return(closeErr).AnyTimes()
}

// this function mocks the diff range of a volume
// it returns extentIDDiffRangeMap(required by mock LExtent Compare), diff range of the volume and mocked extent ids of this volume
func mockVolumeDiffRange(mgr *compareVolumeManager, extentNumber int) (
	extentIDDiffRangeMap map[uint32][]DiffRange, volumeDiffRanges []DiffRange, volumeExtentIDs []uint32) {
	extentIDDiffRangeMap = make(map[uint32][]DiffRange)
	curExtentID := uint32(1)
	volumeExtentsDiffRanges := make([][]DiffRange, extentNumber)
	volumeExtentIDs = make([]uint32, extentNumber)

	for vExtentNo := 0; vExtentNo < extentNumber; vExtentNo += 1 {
		diffRanges := mockExtentDiffRanges(testStripeSize)
		extentIDDiffRangeMap[curExtentID] = diffRanges
		volumeExtentsDiffRanges[vExtentNo] = mgr.transformToStripedRanges(uint64(vExtentNo), diffRanges)
		volumeExtentIDs[vExtentNo] = curExtentID

		curExtentID += 1
	}

	volumeDiffRanges = SortExtentsDiffRanges(volumeExtentsDiffRanges)

	return
}

// compareRes is the map of lid2 => []Diffrange, it is generated by mockVolumeDiffRange()
func handleLExtentCompare(t *testing.T, mockClient zrpc.MockClientConnInterface, extentNumber int, compareRes map[uint32][]DiffRange) {
	mockClient.EXPECT().
		Invoke(gomock.Any(), blockv1.CompareExtentService_CompareLExtent_FullMethodName, gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ context.Context, method string, args interface{}, reply interface{}, opts ...grpc.CallOption) error {
			t.Helper()

			input, ok := args.(*blockv1.CompareLExtentRequest)
			require.True(t, ok)

			resp, ok := reply.(*blockv1.CompareExtentResponse)
			require.True(t, ok)

			// for simplicity, diff range will be returned by lid2(target extent id), lid1 will be ignored
			// any lid1 compares to specific lid2 will return the same result
			extentID := input.GetLid2()

			diffRange, ok := compareRes[extentID]
			require.True(t, ok, "diff range of extentID=%v not exists", extentID)

			for i := range diffRange {
				resp.DiffRanges = append(resp.DiffRanges, &zbs.RangeU64{
					Start:  &diffRange[i].Start,
					Length: &diffRange[i].Length,
				})
			}

			return nil
		}).Times(extentNumber)
}

func mockExtentDiffRanges(stripeSize uint64) []DiffRange {
	res := make([]DiffRange, 0)

	for startOffset := uint64(0); startOffset < DefaultExtentSize; startOffset += stripeSize {
		// nolint:gosec
		if rand.Intn(2) == 1 {
			res = append(res, DiffRange{Start: startOffset, Length: stripeSize})
		}
	}

	return res
}

func testCompareVolumeByLExtent(t *testing.T, stripeNumber, extentNumber int) {
	ctrl := gomock.NewController(t)
	mockZRPCClient := zrpc.NewMockClientConnInterface(ctrl)
	mockDCClient := mock_zbs.NewMockDataChannel(ctrl)
	mockClient := newGoMockClient(mockZRPCClient)

	mgr := &compareVolumeManager{
		baseVolumeId:      testBaseVolumeID,
		targetVolumeId:    testTargetVolumeID,
		stripeSize:        testStripeSize,
		stripeNum:         uint64(stripeNumber),
		dataChannelClient: mockDCClient,
		compareMode:       compareModeLExtent,
		client:            mockClient,
	}

	extentIDDiffRangeMap, unmergedExpected, targetVolumeExtentIDs := mockVolumeDiffRange(mgr, extentNumber)

	// the mocked lextent comparator returns diff ranges only by target extent id
	// and ignores the base extent id
	mgr.baseVolumeExtentIds = make([]uint32, extentNumber) // dummy extent ids
	mgr.targetVolumeExtentIds = targetVolumeExtentIDs

	handleDataChannelRPC(mockDCClient, nil, nil)
	handleLExtentCompare(t, *mockZRPCClient, extentNumber, extentIDDiffRangeMap)

	actual, err := mgr.compareVolume(context.TODO())
	require.NoError(t, err)

	// the actual is merged, but expected is not merged. An actual range could contains many expected diff range
	var curActual DiffRange
	actualIdx := 0

	for _, e := range unmergedExpected {
		if curActual.Length == 0 {
			curActual = actual[actualIdx]
			actualIdx += 1
		}

		require.Equal(t, e.Start, curActual.Start)
		require.LessOrEqual(t, e.Length, curActual.Length)

		curActual.Length -= e.Length
		curActual.Start += e.Length
	}

	require.Equal(t, len(actual), actualIdx)
	require.EqualValues(t, 0, curActual.Length)
}

func TestCompareVolume(t *testing.T) {
	testCompareVolumeByLExtent(t, 1, 1)
	testCompareVolumeByLExtent(t, 4, 8)
	testCompareVolumeByLExtent(t, 4, 128)
	testCompareVolumeByLExtent(t, 8, 128)
	testCompareVolumeByLExtent(t, 8, 192)
}

func TestPrepareExtentGetDCClient(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockDCClient := mock_zbs.NewMockDataChannel(ctrl)
	mockPingErrorDCClient := mock_zbs.NewMockDataChannel(ctrl)

	handleDataChannelRPC(mockDCClient, nil, nil)
	handleDataChannelRPC(mockPingErrorDCClient, errors.New("ping error"), nil)

	noError := "no"
	createError := "create"
	pingError := "ping"

	ctx := context.TODO()
	volume := "test-volume"
	numOfCallNew := 0

	mgr := &compareVolumeManager{
		dataChannelHosts:  []string{noError, createError, pingError},
		dataChannelClient: mockDCClient,
		newDataChannel: func(endpoint string, config *dc.Config) (DataChannel, error) {
			numOfCallNew += 1

			switch endpoint {
			case noError:
				return mockDCClient, nil
			case createError:
				return nil, errors.New("create error")
			case pingError:
				return mockPingErrorDCClient, nil
			}

			return mockDCClient, nil
		},
	}

	// test: use an existing available dc client
	err := mgr.prepareExtent(ctx, volume, 0)
	require.NoError(t, err)
	require.Zero(t, numOfCallNew)

	// test: existing dc client isn't available, create a new one
	numOfCallNew = 0
	mgr.dataChannelClient = mockPingErrorDCClient

	err = mgr.prepareExtent(ctx, volume, 0)
	require.NoError(t, err)
	require.NotZero(t, numOfCallNew)

	// test: no client, create a new available client
	numOfCallNew = 0
	mgr.dataChannelClient = nil

	err = mgr.prepareExtent(ctx, volume, 0)
	require.NoError(t, err)
	require.NotZero(t, numOfCallNew)

	// test: no avaible client, return error
	numOfCallNew = 0
	mgr.dataChannelClient = nil
	mgr.dataChannelHosts = []string{createError, pingError}

	err = mgr.prepareExtent(ctx, volume, 0)
	require.Error(t, err)
	require.NotZero(t, numOfCallNew)
}

func TestCompareVolumeManagerClose(t *testing.T) {
	ctrl := gomock.NewController(t)
	manager := new(compareVolumeManager)

	// test: no dc client
	err := manager.close()
	require.NoError(t, err)

	// test: have normal dc client
	mockDCClient := mock_zbs.NewMockDataChannel(ctrl)
	manager.dataChannelClient = mockDCClient

	handleDataChannelRPC(mockDCClient, nil, nil)

	err = manager.close()
	require.NoError(t, err)

	// test: have close error dc client
	mockDCClient = mock_zbs.NewMockDataChannel(ctrl)
	manager.dataChannelClient = mockDCClient

	handleDataChannelRPC(mockDCClient, nil, errors.New("close error"))

	err = manager.close()
	require.Error(t, err)
}

func TestParallelGetDCClient(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockDCClient := mock_zbs.NewMockDataChannel(ctrl)

	handleDataChannelRPC(mockDCClient, nil, nil)

	mgr := &compareVolumeManager{
		dataChannelHosts:  []string{"test-host"},
		dataChannelClient: nil,
		newDataChannel: func(endpoint string, config *dc.Config) (DataChannel, error) {
			time.Sleep(time.Second)

			return mockDCClient, nil
		},
	}

	// run multiple getDataChannelClient at the same time
	// and check for data race

	go func() {
		_, err := mgr.getDataChannelClient()
		require.NoError(t, err)
	}()

	go func() {
		_, err := mgr.getDataChannelClient()
		require.NoError(t, err)
	}()
}
