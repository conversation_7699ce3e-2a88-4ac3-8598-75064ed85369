package zbs

import (
	"context"
	"io"
	"sync"

	"github.com/pkg/errors"
)

const iteratorBufferLimit = 512

type CompareVolumeIterator interface {
	Seek(offset uint64) error
	Next() (DiffRange, error)
	Close() error
}

type compareVolumeIterator struct {
	manager *compareVolumeManager

	offset      uint64
	haveStarted bool
	haveClosed  bool
	mu          sync.Mutex

	wg          sync.WaitGroup
	diffRangeCh chan DiffRange
	errCh       chan error
	closeCh     chan struct{}
}

func (c *Client) NewCompareVolumeIter(ctx context.Context, baseVolumeId string, targetVolumeId string) (CompareVolumeIterator, error) {
	if len(targetVolumeId) == 0 {
		return nil, errors.New("unexpected empty target volume id")
	}

	manager, err := newCompareVolumeManager(ctx, c, baseVolumeId, targetVolumeId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create compare volume manager, volume %s and %s", baseVolumeId, targetVolumeId)
	}

	iter := &compareVolumeIterator{
		manager: manager,
	}

	return iter, nil
}

// The iterator defaults to iterating from 0 offset, and we can use the Seek method
// to seek the offset to skip a portion of the region for comparison.
func (i *compareVolumeIterator) Seek(offset uint64) error {
	i.mu.Lock()
	defer i.mu.Unlock()

	if i.haveClosed {
		return errors.New("compare volume iterator is closed")
	} else if i.isSeekForwardToSameExtentGroup(offset) {
		// If it seeks forward to the same extent group, no action is required.
		// Diff ranges smaller than offset will be skipped in the `Next` method.
		goto end
	}

	if i.haveStarted {
		i.stopCompare()
	}

	i.startCompare(i.getExtentGroupIndex(offset))

end:
	i.offset = offset

	return nil
}

func (i *compareVolumeIterator) isSeekForwardToSameExtentGroup(seekOffset uint64) bool {
	if !i.haveStarted {
		return false
	} else if seekOffset < i.offset {
		return false
	}

	return i.getExtentGroupIndex(i.offset) == i.getExtentGroupIndex(seekOffset)
}

func (i *compareVolumeIterator) Next() (DiffRange, error) {
	i.mu.Lock()
	defer i.mu.Unlock()

	if i.haveClosed {
		return DiffRange{}, errors.New("iterator is closed")
	} else if !i.haveStarted {
		i.startCompare(0)
	}

	for {
		select {
		case err := <-i.errCh:
			return DiffRange{}, err
		case diffRange, ok := <-i.diffRangeCh:
			if !ok {
				return DiffRange{}, io.EOF
			} else if endOffset := diffRange.Start + diffRange.Length; diffRange.Start >= i.offset || endOffset > i.offset {
				i.offset = endOffset

				return diffRange, nil
			}
		}
	}
}

func (i *compareVolumeIterator) Close() error {
	i.mu.Lock()
	defer i.mu.Unlock()

	if i.haveClosed {
		return nil
	}

	i.stopCompare()

	i.haveClosed = true

	return i.manager.close()
}

func (i *compareVolumeIterator) startCompare(extentGroupIdx uint64) {
	i.prepareCompareInfo()

	i.haveStarted = true

	i.wg.Add(1)

	go func() {
		defer i.wg.Done()

		for {
			diffRanges, err := i.compareExtentGroup(extentGroupIdx)
			if err != nil {
				if errors.Is(err, io.EOF) {
					close(i.diffRangeCh)
				} else {
					i.errCh <- err
				}

				return
			}

			for _, diffRange := range diffRanges {
				select {
				case <-i.closeCh:
					return
				case i.diffRangeCh <- diffRange:
				}
			}

			extentGroupIdx += 1
		}
	}()
}

func (i *compareVolumeIterator) stopCompare() {
	// the compare is not started yet, no need to stop
	if i.closeCh == nil {
		return
	}

	close(i.closeCh)

	i.wg.Wait()
}

func (i *compareVolumeIterator) prepareCompareInfo() {
	i.wg = sync.WaitGroup{}
	i.closeCh = make(chan struct{})
	i.errCh = make(chan error, 1)
	i.diffRangeCh = make(chan DiffRange, iteratorBufferLimit)
}

func (i *compareVolumeIterator) getExtentGroupIndex(offset uint64) uint64 {
	stripeBlockIndex := offset / i.manager.stripeSize
	stripeRow := stripeBlockIndex * i.manager.stripeSize / DefaultExtentSize
	stripeCol := stripeBlockIndex % i.manager.stripeNum
	extentIndex := (stripeRow/i.manager.stripeNum)*i.manager.stripeNum + stripeCol

	return extentIndex / i.manager.stripeNum
}

func (i *compareVolumeIterator) compareExtentGroup(extentGroupIndex uint64) ([]DiffRange, error) {
	startExtentOfGroup := extentGroupIndex * i.manager.stripeNum
	endExtentOfVolume := uint64(len(i.manager.targetVolumeExtentIds))

	if startExtentOfGroup >= endExtentOfVolume {
		return nil, io.EOF
	}

	endExtentOfGroup := startExtentOfGroup + min(i.manager.stripeNum, endExtentOfVolume-startExtentOfGroup)

	return i.manager.compareExtents(context.TODO(), startExtentOfGroup, endExtentOfGroup)
}
