package zbs

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc"

	zbsv1 "github.com/iomesh/zbs-client-go/gen/proto/zbs"
	metav1 "github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
)

type CDPService struct {
	client metav1.CDPServiceClient
}

func NewCDPService(client grpc.ClientConnInterface) *CDPService {
	return &CDPService{
		client: metav1.NewCDPServiceClient(client),
	}
}

func (s *CDPService) GetClient() metav1.CDPServiceClient {
	return s.client
}

func (s *CDPService) CreateJobsByGroup(ctx context.Context, localRemoteVolumeMap map[string]string, remoteHost string,
	groupId string, chunkId *uint32, skipFcWriteZero bool, autoClean bool) (*zbsv1.CreateCDPJobsByGroupResponse, error) {
	req := &zbsv1.CreateCDPJobsByGroupRequest{
		Group:      []byte(groupId),
		CreateReqs: make([]*zbsv1.CreateCDPJobRequest, 0, len(localRemoteVolumeMap)),
	}

	// only need to be set if the chunkId value is non-zero
	if chunkId != nil && *chunkId == 0 {
		return nil, errors.New("unexpected to find chunk id is zero")
	}

	for localVolume, remoteVolume := range localRemoteVolumeMap {
		req.CreateReqs = append(req.CreateReqs, &zbsv1.CreateCDPJobRequest{
			Local: &zbsv1.CDPVolumeID{VolumeId: []byte(localVolume)},
			Remote: &zbsv1.CDPRemoteInfo{
				VolumeId: []byte(remoteVolume),
				Hosts:    []byte(remoteHost),
			},
			Group:           []byte(groupId),
			Cid:             chunkId,
			SkipFcWriteZero: &skipFcWriteZero,
			AutoClean:       &autoClean,
		})
	}

	return s.client.CreateJobsByGroup(ctx, req)
}

func (s *CDPService) ListJobsByGroup(ctx context.Context, groupId string) (*zbsv1.ListCDPJobsResponse, error) {
	req := &zbsv1.ListCDPJobsRequest{Group: []byte(groupId)}

	return s.client.ListJobs(ctx, req)
}

func (s *CDPService) FinishJobsByGroup(ctx context.Context, groupId string, sync bool) error {
	req := &zbsv1.FinishCDPJobsByGroupRequest{
		Group: []byte(groupId),
		Sync:  &sync,
	}

	_, err := s.client.FinishJobsByGroup(ctx, req)

	return err
}

func (s *CDPService) CancelJobsByGroup(ctx context.Context, groupId string, sync bool) error {
	req := &zbsv1.CancelCDPJobsByGroupRequest{
		Group: []byte(groupId),
		Sync:  &sync,
	}

	_, err := s.client.CancelJobsByGroup(ctx, req)

	return err
}

func (s *CDPService) DeleteJobsByGroup(ctx context.Context, groupId string) error {
	req := &zbsv1.DeleteCDPJobsByGroupRequest{
		Group: []byte(groupId),
	}

	_, err := s.client.DeleteJobsByGroup(ctx, req)

	return err
}

func (s *CDPService) GetJobByVolume(ctx context.Context, volumeId string) (*zbsv1.CDPJobInfo, error) {
	req := &zbsv1.GetCDPJobByVolumeRequest{VolumeId: []byte(volumeId)}

	return s.client.GetJobByVolume(ctx, req)
}
