package zbs

import (
	"context"

	dc "github.com/iomesh/zbs-client-go/zbs/datachannel"
)

type DataChannel interface {
	VolumeRead(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags) error
	VolumeWrite(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags, preferredCid dc.Cid) error
	Ping(ctx context.Context) error
	Close() error
}

type DataChannelService struct {
	client *dc.Client
}

var _ DataChannel = &DataChannelService{}

func NewDataChannelService(endpoint string, config *dc.Config) (*DataChannelService, error) {
	client, err := dc.NewClient(endpoint, config)
	if err != nil {
		return nil, err
	}

	return &DataChannelService{
		client: client,
	}, nil
}

func (dcs *DataChannelService) VolumeRead(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags) error {
	zbsuuid, err := dc.ToZBSUUID(volumeID)
	if err != nil {
		return err
	}

	args := dc.Args{
		Info: &dc.VolumeInfo{
			VolumeID:   zbsuuid,
			DataLen:    length,
			DataOffset: offset,
			Flags:      flags,
		},
	}
	reply := dc.Reply{
		Info: &dc.VolumeInfo{},
		Buf:  buf,
	}

	err = dcs.client.Invoke(ctx, dc.VolumeReadOp.MethodName(), args, &reply)
	if err != nil {
		return err
	}

	return reply.GetError()
}

func (dcs *DataChannelService) VolumeWrite(ctx context.Context, volumeID string, buf []byte, length uint32, offset uint64, flags dc.Flags, preferredCid dc.Cid) error {
	zbsuuid, err := dc.ToZBSUUID(volumeID)
	if err != nil {
		return err
	}

	args := dc.Args{
		Info: &dc.VolumeInfo{
			VolumeID:     zbsuuid,
			DataLen:      length,
			DataOffset:   offset,
			Flags:        flags,
			PreferredCid: preferredCid,
		},
		Buf: buf,
	}
	reply := dc.Reply{
		Info: &dc.VolumeInfo{},
	}

	err = dcs.client.Invoke(ctx, dc.VolumeWriteOp.MethodName(), args, &reply)
	if err != nil {
		return err
	}

	return reply.GetError()
}

func (dcs *DataChannelService) Ping(ctx context.Context) error {
	args := dc.Args{
		Info: &dc.Padding{},
	}
	reply := dc.Reply{
		Info: &dc.Padding{},
	}

	err := dcs.client.Invoke(ctx, dc.PingOp.MethodName(), args, &reply)
	if err != nil {
		return err
	}

	return reply.GetError()
}

func (dcs *DataChannelService) Close() error {
	return dcs.client.Close()
}
