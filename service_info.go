// This file is generated by service_info_generator.py, DO NOT MODIFY!

package serviceinfo

import (
    "google.golang.org/protobuf/reflect/protoreflect"

    ""
)


var PackageServiceIds = map[string]map[string]int32{
,
}

var ServicePortOffsetTable = map[string]int{
	"zbs.meta.MetaService":   0,
	"zbs.meta.ISCSIService":  0,
	"zbs.meta.StatusService": 1,
	"zbs.meta.ChunkService":  0,
	"zbs.chunk.ChunkService": 0,
	"zbs.VolumePerfService": 3,
	"zbs.ChunkPerfService": 3,
    "zbs.task.VIPService": 0,
}

var ProtoFileDescriptorMap = map[string][]protoreflect.FileDescriptor {
}
