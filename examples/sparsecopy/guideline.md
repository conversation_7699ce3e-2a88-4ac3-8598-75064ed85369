# Sparse-Copy Guideline

## examples

### run the examples

```shell
# replace the source_zbs_host\source_volume\target_zbs_host\target_volume with actual environment.
go run examples/sparsecopy/main.go -source_zbs_host ********** -source_volume 1380f793-6576-4cc4-95a6-2458915da28a \
	-target_zbs_host ********** -target_volume 36b50388-5f53-46ed-a831-1203290af5c1

# output
[2023-10-31 15:25:42]sparse_copy: start copy from **********/6f03545b-34ed-4b97-86eb-566e16fe43a8 to **********/00a6d86f-c7da-4a75-bb70-313e59966c7f
[2023-10-31 15:27:37]reader: readBytes=40814MB, bandiWidth=332MB/s, averageBandiWidth=354MB/s, eta=0.411378seconds, progress=99.643555%
[2023-10-31 15:27:38]sparse_copy: copied 42949672960 bytes in 116.089109 seconds, average bandiwidth 352.832408 MB/s
```

### args
| args | description | required ? |
| :-- | :--------- | :--------|
| source_zbs_host | source zbs host | true | 
| source_volume   | source volume id | true |
| target_zbs_host | target zbs host | true |
| target_volume_id | target volume id | true |
| source_meta_port | source zbs meta port | false, by default is 10206 |
| source_dc_port | source zbs data channel port | false, by default is 10201 |
| target_dc_port | target zbs data channel port | fasle, by default is 10201 |

### check the result

Check data in the source and the target volume is the same by http://**************:30081/#.

## usage

```golang
import sparseio "github.com/iomesh/zbs-client-go/sparse/io"

err := sparseio.SparseCopy(ctx, reader, writer, IOSize, csCh)
```

The reader and writer need to implement sparseio.SparseReader & sparseio.SparseWriter interface.

## sparse reader and writer for zbs volume

### reader & writer

zbs-client-go implements zbs volume sparse reader & writer in zbs-client-go/sparse/zbs.

Create the reader and writer by:

```golang
import sparsezbs "github.com/iomesh/zbs-client-go/sparse/zbs"

reader, err := sparsezbs.NewReader(dc, volumeId, volumeSize, validOffsetIter)
writer, err := sparsezbs.NewWriter(dc, volumeId)
```

The reader & writer use zbs.DataChannel to handle data with zbs.

### validOffsetIter

ValidOffsetIter indicates the strategy to skip some extent during sparse reading.

```golang
func NewValidOffsetIter(volume *metav1.Volume, skipExtentBitmap *roaring.Bitmap, extentSize uint64) *ValidOffsetIter
```

In the skipExtentBitmap, every bit represents an extent. If a bit is set true, the correspond extent should be skipped.

For example, in backup, we generate the bitmap with following codes. It skips unalloc and unmodified extent.
```golang
// CreateSkipExtentBitmap creates a skip bitmap for ValidOffsetIter from extents & parent extents.
// When read volume data from zbs in an incremental backup,
// we only fetch from alloc extent and modify extent, others should be skipped.
func CreateSkipExtentBitmap(extents []*zbs.PExtentResp, parentExtents []*zbs.PExtentResp) *roaring.Bitmap {
	skipExtentBitmap := roaring.NewBitmap()

	for i := 0; i < len(extents); i++ {
		if extents[i].GetLocation() == 0 ||
			(i < len(parentExtents) && 
			parentExtents[i].GetLocation() != 0 && 
			extents[i].GetPid() == parentExtents[i].GetPid()) {
			skipExtentBitmap.AddInt(i)
		}
	}

	return skipExtentBitmap
}
```

If we don't want to skip any extent, just pass a nil or an empty bitmap into the ValidOffsetIter
```golang
offsetIter := sparsezbs.NewValidOffsetIter(volume, nil, extentSize)


offsetIter := sparsezbs.NewValidOffsetIter(volume, roaring.NewBitmap(), extentSize)
```


