package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"github.com/go-playground/validator/v10"

	"github.com/iomesh/zbs-client-go/gen/proto/zbs/meta"
	sparseio "github.com/iomesh/zbs-client-go/sparse/io"
	sparsezbs "github.com/iomesh/zbs-client-go/sparse/zbs"
	"github.com/iomesh/zbs-client-go/zbs"
	"github.com/iomesh/zbs-client-go/zbs/datachannel"
)

const (
	KB = 1024
	MB = KB * KB

	// zbs default settings
	defaultMetaPort   = "10206"
	defaultDcPort     = "10201"
	defaultExtentSize = 256 * MB
	defaultIODepth    = 8
	defaultStripeSize = 256 * KB
	ioSize            = defaultIODepth * defaultStripeSize // 2 MB

	logDuration = 1 * time.Second // print a log every seconds
	timeLayout  = "2006-01-02 15:04:05"
)

type Options struct {
	SourceZbsHost  string `validate:"required,ip|url"`
	SourceVolumeId string `validate:"required,uuid"`
	SourceMetaPort string `validate:"required,gte=1,lte=65535"`
	SourceDcPort   string `validate:"required,gte=1,lte=65535"`

	TargetZbsHost  string `validate:"required,ip|url"`
	TargetVolumeId string `validate:"required,uuid"`
	TargetDcPort   string `validate:"required,gte=1,lte=65535"`
}

func (o *Options) validate() error {
	validate := validator.New(validator.WithRequiredStructEnabled())

	return validate.Struct(o)
}

func getOptions() *Options {
	o := &Options{}

	flag.StringVar(&o.SourceZbsHost, "source_zbs_host", "", "source zbs host")
	flag.StringVar(&o.SourceVolumeId, "source_volume", "", "source volume id")
	flag.StringVar(&o.SourceMetaPort, "source_meta_port", defaultMetaPort, "source zbs meta port")
	flag.StringVar(&o.SourceDcPort, "source_dc_port", defaultDcPort, "source zbs data channel port")

	flag.StringVar(&o.TargetZbsHost, "target_zbs_host", "", "target zbs host")
	flag.StringVar(&o.TargetVolumeId, "target_volume", "", "target volume id")
	flag.StringVar(&o.TargetDcPort, "target_dc_port", defaultDcPort, "target zbs data channel port")

	flag.Parse()

	return o
}

func main() {
	opt := getOptions()

	err := opt.validate()
	if err != nil {
		fmt.Println(err)

		return
	}

	ctx := context.Background()

	// preapare reader and writer
	sourceVolume := getVolume(ctx, opt.SourceZbsHost, opt.SourceMetaPort, opt.SourceVolumeId)
	reader := createReader(ctx, opt.SourceZbsHost, opt.SourceDcPort, sourceVolume)
	statsReader := NewMonitorReader(reader) // stats read progress

	writer := createWriter(ctx, opt.TargetZbsHost, opt.TargetDcPort, opt.TargetVolumeId)

	wg := sync.WaitGroup{}
	stopCh := make(chan (struct{}))

	wg.Add(2)

	// run a routine to give feedback of sparse copy progress
	go func(lastBytesRead uint64, bytesTotal uint64) {
		startTime := time.Now()
		fmt.Printf("[%s]sparse_copy: start copy from %s/%s to %s/%s\n",
			startTime.Format(timeLayout),
			opt.SourceZbsHost, opt.SourceVolumeId,
			opt.TargetZbsHost, opt.TargetVolumeId)

		ticker := time.NewTicker(logDuration)

		for {
			select {
			case <-stopCh: // stopped by the sparse copy routine
				finishTime := time.Now()
				elapseTime := finishTime.Sub(startTime)
				fmt.Printf("\n[%s]sparse_copy: copied %d bytes in %f seconds, average bandiwidth %f MB/s\n",
					finishTime.Format(timeLayout),
					*sourceVolume.Size, elapseTime.Seconds(),
					float64(*sourceVolume.Size)/elapseTime.Seconds()/MB)

				wg.Done()

				return
			case <-ticker.C: // every seconds
				bytesRead := statsReader.GetStats()
				elapseTime := time.Now().Sub(startTime)

				bandiWidth := (bytesRead - lastBytesRead) / uint64(logDuration.Seconds())
				avgBandiWidth := bytesRead / uint64(elapseTime.Seconds())
				eta := float64(bytesTotal-bytesRead) / float64(avgBandiWidth)
				progress := float64(bytesRead) / float64(bytesTotal) * 100

				fmt.Printf("\r[%s]reader: readBytes=%dMB, bandiWidth=%dMB/s, averageBandiWidth=%dMB/s, eta=%fseconds, progress=%f%%\033[K",
					time.Now().Format(timeLayout),
					bytesRead/MB, bandiWidth/MB, avgBandiWidth/MB, eta, progress)

				lastBytesRead = bytesRead
			}
		}
	}(0, *sourceVolume.Size)

	// run sparse copy
	go func() {
		err = sparseio.SparseCopy(ctx, statsReader, writer, ioSize, make(chan sparseio.CopyState))
		if err != nil {
			fmt.Fprintf(os.Stderr, "\n[%s]sparse_copy: error occured, err=%s\n",
				time.Now().Format(timeLayout), err)

			os.Exit(1)
		}

		stopCh <- struct{}{}
		wg.Done()
	}()

	wg.Wait()
}

func getVolume(ctx context.Context, zbsHost string, metaPort string, volumeId string) *meta.Volume {
	client, err := zbs.NewStaticClient(fmt.Sprintf("%s:%s", zbsHost, metaPort))
	if err != nil {
		panic(err)
	}

	resp, err := client.Meta.ShowVolume(ctx, &meta.VolumePath{
		VolumeId: []byte(volumeId),
	})
	if err != nil {
		panic(err)
	}

	return resp.GetVolume()
}

func createReader(ctx context.Context, zbsHost string, dcPort string, volume *meta.Volume) *sparsezbs.Reader {
	dc, err := zbs.NewDataChannelService(fmt.Sprintf("%s:%s", zbsHost, dcPort), datachannel.DefaultClientConfig())
	if err != nil {
		panic(err)
	}

	// create a validOffsetIter with nil bitmap, it will skip no extents
	validOffsetIter := sparsezbs.NewValidOffsetIter(volume, nil, defaultExtentSize)

	reader, err := sparsezbs.NewReader(dc, string(volume.Id), *volume.Size, validOffsetIter)
	if err != nil {
		panic(err)
	}

	return reader
}

func createWriter(ctx context.Context, zbsHost string, dcPort string, volumeId string) *sparsezbs.Writer {
	dc, err := zbs.NewDataChannelService(fmt.Sprintf("%s:%s", zbsHost, dcPort), datachannel.DefaultClientConfig())
	if err != nil {
		panic(err)
	}

	writer, err := sparsezbs.NewWriter(dc, volumeId)
	if err != nil {
		panic(err)
	}

	return writer
}

// StatsReader stats how many bytes has been read
type StatsReader struct {
	reader sparseio.SparseReader

	bytesRead atomic.Uint64
}

func NewMonitorReader(reader sparseio.SparseReader) *StatsReader {
	return &StatsReader{
		reader:    reader,
		bytesRead: atomic.Uint64{},
	}
}

var _ sparseio.SparseReader = (*StatsReader)(nil)

func (r *StatsReader) Read(ctx context.Context, p []byte, offsetLowerBound int64) (int, int64, error) {
	n, offset, err := r.reader.Read(ctx, p, offsetLowerBound)

	r.bytesRead.Store(uint64(offset))

	return n, offset, err
}

func (r *StatsReader) Close() error {
	return r.reader.Close()
}

func (r *StatsReader) GetStats() uint64 {
	return r.bytesRead.Load()
}
