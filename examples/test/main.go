package main

import (
	"context"
	"fmt"

	zbs "github.com/iomesh/zbs-client-go/zbs"
)

func main() {
	const EnvVarZbsHosts = "ZbsHosts"
	client, err := zbs.NewStaticClient("**************:10206")
	if err != nil {
		panic(fmt.Errorf("init client failed"))
	}
	iscsiService := client.ISCSI
	fmt.Println(iscsiService.GetTargets(context.Background()))

	client, err = zbs.NewStaticClient("*************:10206")

	iscsiService = client.ISCSI
	fmt.Println(iscsiService.GetTargets(context.Background()))

	client, err = zbs.NewStaticClient("************:10206")

	iscsiService = client.ISCSI
	fmt.Println(iscsiService.GetTargets(context.Background()))

	// test env setup

}
